classdef TimeStampPlanDB <xls.TimeStampRecordDB
    properties
        m_ActivePlanFilter; 
    end
    
    methods
        function obj = TimeStampPlanDB(dbfolder, varargin)
            obj      = <EMAIL>({'table.uniquekey', 'SOPInstanceUID'},...
                   {'RecordDBName', 'RTPlanDB'}, {'RecordDBFolder', dbfolder}, varargin{:});
            filterfile = obj.getoptioni('activeplanfilter.file'); 
            if exist(filterfile, 'file')
                obj.m_ActivePlanFilter = xls.TableFilter_RTPlan({'FilterFileName', filterfile});
            end
        end
        
        function res = DcmDataFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER');
        end
        
        function num = AddActivePlans(self, instanceT)
              I   = find(ismember(upper(instanceT.Modality), 'RTPLAN'));
              num = numel(I);
              
              if num==0
                  return;
              end
              
              instanceT = instanceT(I, :);
              PatientID = instanceT.PatientID;
              uniquepatids = unique(PatientID); 
              PlanID    = instanceT.SOPInstanceUID; 
              for k=1:numel(uniquepatids )
                  patid = uniquepatids {k};
                  patient= dcmtk.RTPatientTables({'DCMSERVER_FOLDER', DcmDataFolder(self)}, {'PatientID', patid}); 
                  J     = find(ismember(PatientID, patid));
                  planuids = PlanID(J);
                  T     = patient.GetTable('RTPLAN').GetSubRawTable('SOPInstanceUID', planuids);
                  if ~isempty(self.m_ActivePlanFilter)
                      [I, T] =  FilterTable(self.m_ActivePlanFilter, T);
                  end
                  
                  if ~isempty(T)
                     self.AddActiveRecords(T);  
                  end
              end
        end
    end
end

