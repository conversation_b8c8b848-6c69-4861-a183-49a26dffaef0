classdef nnUNETInference <daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = nnUNETInference(varargin)
            options = OptionsMap(varargin{:});
            <EMAIL>(options);
            obj.SetDefaultCfg();
            AddProcess_nnUNET(obj);
        end

        function AddProcess_nnUNET(self)
            optype = 'nnUNETInference';       
            fixedstr=[];
            templateparas='';
            names = {'ModelOptionFile', 'InputImage', 'OutputFile'};
            for k=1:numel(names)
                name=names{k};
                modeloptfile=self.getOption(name);
                if ~isempty(modeloptfile)
                    fixedstr.(name)=modeloptfile;
                else
                    fixedstr.(name)=['[' name ']'];
                end
            end
            AddProcess(self, optype, fixedstr, templateparas);
        end
    end

    methods (Static)
        
       
    end
end