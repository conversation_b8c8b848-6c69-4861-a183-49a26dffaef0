#
#  Copyright (C) 2003-2019, OFFIS e.V.
#  All rights reserved.  See COPYRIGHT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 Oldenburg, Germany
#
#  Module:  dcmnet
#
#  Author:  <PERSON>
#
#  Purpose: Sample configuration file for storescu
#

# ============================================================================
[[TransferSyntaxes]]
# ============================================================================

[Uncompressed]
TransferSyntax1 = LittleEndianExplicit
TransferSyntax2 = BigEndianExplicit
TransferSyntax3 = LittleEndianImplicit

[JPEGBaseline]
TransferSyntax1 = JPEGBaseline

[JPEGLossless]
TransferSyntax1 = JPEGLossless:Non-hierarchical-1stOrderPrediction

[RLE]
TransferSyntax1 = RLELossless

[MPEG2]
TransferSyntax1 = MPEG2MainProfile@MainLevel
#
# commented out the following line since we do not support transcoding of MPEG2
#
#TransferSyntax2 = MPEG2MainProfile@HighLevel

[MPEG4]
TransferSyntax1 = MPEG4HighProfile/Level4.1
#
# commented out the following lines since we do not support transcoding of MPEG4
#
#TransferSyntax2 = MPEG4BDcompatibleHighProfile/Level4.1
#TransferSyntax3 = MPEG4HighProfile/Level4.2For2DVideo
#TransferSyntax4 = MPEG4HighProfile/Level4.2For3DVideo
#TransferSyntax5 = MPEG4StereoHighProfile/Level4.2

# ============================================================================
[[PresentationContexts]]
# ============================================================================

[StorageCompressedAndUncompressed]
#
# For non-image SOP classes, we only support uncompressed transmission.
# For image SOP classes, we support JPEG lossy, JPEG lossless and uncompressed.
# For Ultrasound, we additionally support RLE.
# The retired and standalone SOP classes are not supported with this profile.
#
PresentationContext1   = ComputedRadiographyImageStorage\JPEGBaseline
PresentationContext2   = ComputedRadiographyImageStorage\JPEGLossless
PresentationContext3   = ComputedRadiographyImageStorage\Uncompressed
PresentationContext4   = CTImageStorage\JPEGBaseline
PresentationContext5   = CTImageStorage\JPEGLossless
PresentationContext6   = CTImageStorage\Uncompressed
PresentationContext7   = DigitalIntraOralXRayImageStorageForPresentation\JPEGBaseline
PresentationContext8   = DigitalIntraOralXRayImageStorageForPresentation\JPEGLossless
PresentationContext9   = DigitalIntraOralXRayImageStorageForPresentation\Uncompressed
PresentationContext10  = DigitalIntraOralXRayImageStorageForProcessing\JPEGBaseline
PresentationContext11  = DigitalIntraOralXRayImageStorageForProcessing\JPEGLossless
PresentationContext12  = DigitalIntraOralXRayImageStorageForProcessing\Uncompressed
PresentationContext13  = DigitalMammographyXRayImageStorageForPresentation\JPEGBaseline
PresentationContext14  = DigitalMammographyXRayImageStorageForPresentation\JPEGLossless
PresentationContext15  = DigitalMammographyXRayImageStorageForPresentation\Uncompressed
PresentationContext16  = DigitalMammographyXRayImageStorageForProcessing\JPEGBaseline
PresentationContext17  = DigitalMammographyXRayImageStorageForProcessing\JPEGLossless
PresentationContext18  = DigitalMammographyXRayImageStorageForProcessing\Uncompressed
PresentationContext19  = DigitalXRayImageStorageForPresentation\JPEGBaseline
PresentationContext20  = DigitalXRayImageStorageForPresentation\JPEGLossless
PresentationContext21  = DigitalXRayImageStorageForPresentation\Uncompressed
PresentationContext22  = DigitalXRayImageStorageForProcessing\JPEGBaseline
PresentationContext23  = DigitalXRayImageStorageForProcessing\JPEGLossless
PresentationContext24  = DigitalXRayImageStorageForProcessing\Uncompressed
PresentationContext25  = EnhancedCTImageStorage\JPEGBaseline
PresentationContext26  = EnhancedCTImageStorage\JPEGLossless
PresentationContext27  = EnhancedCTImageStorage\Uncompressed
PresentationContext28  = EnhancedMRImageStorage\JPEGBaseline
PresentationContext29  = EnhancedMRImageStorage\JPEGLossless
PresentationContext30  = EnhancedMRImageStorage\Uncompressed
PresentationContext31  = EnhancedXAImageStorage\JPEGBaseline
PresentationContext32  = EnhancedXAImageStorage\JPEGLossless
PresentationContext33  = EnhancedXAImageStorage\Uncompressed
PresentationContext34  = EnhancedXRFImageStorage\JPEGBaseline
PresentationContext35  = EnhancedXRFImageStorage\JPEGLossless
PresentationContext36  = EnhancedXRFImageStorage\Uncompressed
PresentationContext37  = MRImageStorage\JPEGBaseline
PresentationContext38  = MRImageStorage\JPEGLossless
PresentationContext39  = MRImageStorage\Uncompressed
PresentationContext40  = MultiframeGrayscaleByteSecondaryCaptureImageStorage\JPEGBaseline
PresentationContext41  = MultiframeGrayscaleByteSecondaryCaptureImageStorage\JPEGLossless
PresentationContext42  = MultiframeGrayscaleByteSecondaryCaptureImageStorage\Uncompressed
PresentationContext43  = MultiframeGrayscaleWordSecondaryCaptureImageStorage\JPEGBaseline
PresentationContext44  = MultiframeGrayscaleWordSecondaryCaptureImageStorage\JPEGLossless
PresentationContext45  = MultiframeGrayscaleWordSecondaryCaptureImageStorage\Uncompressed
PresentationContext46  = MultiframeSingleBitSecondaryCaptureImageStorage\JPEGBaseline
PresentationContext47  = MultiframeSingleBitSecondaryCaptureImageStorage\JPEGLossless
PresentationContext48  = MultiframeSingleBitSecondaryCaptureImageStorage\Uncompressed
PresentationContext49  = MultiframeTrueColorSecondaryCaptureImageStorage\JPEGBaseline
PresentationContext50  = MultiframeTrueColorSecondaryCaptureImageStorage\JPEGLossless
PresentationContext51  = MultiframeTrueColorSecondaryCaptureImageStorage\Uncompressed
PresentationContext52  = NuclearMedicineImageStorage\JPEGBaseline
PresentationContext53  = NuclearMedicineImageStorage\JPEGLossless
PresentationContext54  = NuclearMedicineImageStorage\Uncompressed
PresentationContext55  = OphthalmicPhotography16BitImageStorage\JPEGBaseline
PresentationContext56  = OphthalmicPhotography16BitImageStorage\JPEGLossless
PresentationContext57  = OphthalmicPhotography16BitImageStorage\Uncompressed
PresentationContext58  = OphthalmicPhotography8BitImageStorage\JPEGBaseline
PresentationContext59  = OphthalmicPhotography8BitImageStorage\JPEGLossless
PresentationContext60  = OphthalmicPhotography8BitImageStorage\Uncompressed
PresentationContext61  = PositronEmissionTomographyImageStorage\JPEGBaseline
PresentationContext62  = PositronEmissionTomographyImageStorage\JPEGLossless
PresentationContext63  = PositronEmissionTomographyImageStorage\Uncompressed
PresentationContext64  = RTImageStorage\JPEGBaseline
PresentationContext65  = RTImageStorage\JPEGLossless
PresentationContext66  = RTImageStorage\Uncompressed
PresentationContext67  = SecondaryCaptureImageStorage\JPEGBaseline
PresentationContext68  = SecondaryCaptureImageStorage\JPEGLossless
PresentationContext69  = SecondaryCaptureImageStorage\Uncompressed
PresentationContext70  = UltrasoundImageStorage\JPEGBaseline
PresentationContext71  = UltrasoundImageStorage\JPEGLossless
PresentationContext72  = UltrasoundImageStorage\RLE
PresentationContext73  = UltrasoundImageStorage\Uncompressed
PresentationContext74  = UltrasoundMultiframeImageStorage\JPEGBaseline
PresentationContext75  = UltrasoundMultiframeImageStorage\JPEGLossless
PresentationContext76  = UltrasoundMultiframeImageStorage\RLE
PresentationContext77  = UltrasoundMultiframeImageStorage\Uncompressed
PresentationContext78  = VLEndoscopicImageStorage\JPEGBaseline
PresentationContext79  = VLEndoscopicImageStorage\JPEGLossless
PresentationContext80  = VLEndoscopicImageStorage\Uncompressed
PresentationContext81  = VLMicroscopicImageStorage\JPEGBaseline
PresentationContext82  = VLMicroscopicImageStorage\JPEGLossless
PresentationContext83  = VLMicroscopicImageStorage\Uncompressed
PresentationContext84  = VLPhotographicImageStorage\JPEGBaseline
PresentationContext85  = VLPhotographicImageStorage\JPEGLossless
PresentationContext86  = VLPhotographicImageStorage\Uncompressed
PresentationContext87  = VLSlideCoordinatesMicroscopicImageStorage\JPEGBaseline
PresentationContext88  = VLSlideCoordinatesMicroscopicImageStorage\JPEGLossless
PresentationContext89  = VLSlideCoordinatesMicroscopicImageStorage\Uncompressed
PresentationContext90  = XRayAngiographicImageStorage\JPEGBaseline
PresentationContext91  = XRayAngiographicImageStorage\JPEGLossless
PresentationContext92  = XRayAngiographicImageStorage\Uncompressed
PresentationContext93  = XRayRadiofluoroscopicImageStorage\JPEGBaseline
PresentationContext94  = XRayRadiofluoroscopicImageStorage\JPEGLossless
PresentationContext95  = XRayRadiofluoroscopicImageStorage\Uncompressed
#
# the following presentation contexts are for non-image SOP classes
#
PresentationContext96  = AmbulatoryECGWaveformStorage\Uncompressed
PresentationContext97  = BasicTextSRStorage\Uncompressed
PresentationContext98  = BasicVoiceAudioWaveformStorage\Uncompressed
PresentationContext99  = CardiacElectrophysiologyWaveformStorage\Uncompressed
PresentationContext100 = ChestCADSRStorage\Uncompressed
PresentationContext101 = ColonCADSRStorage\Uncompressed
PresentationContext102 = ColorSoftcopyPresentationStateStorage\Uncompressed
PresentationContext103 = ComprehensiveSRStorage\Uncompressed
PresentationContext104 = EncapsulatedPDFStorage\Uncompressed
PresentationContext105 = EnhancedSRStorage\Uncompressed
PresentationContext106 = GeneralECGWaveformStorage\Uncompressed
PresentationContext107 = GrayscaleSoftcopyPresentationStateStorage\Uncompressed
PresentationContext108 = HemodynamicWaveformStorage\Uncompressed
PresentationContext109 = KeyObjectSelectionDocumentStorage\Uncompressed
PresentationContext110 = MammographyCADSRStorage\Uncompressed
PresentationContext111 = MRSpectroscopyStorage\Uncompressed
PresentationContext112 = ProcedureLogStorage\Uncompressed
PresentationContext113 = PseudoColorSoftcopyPresentationStateStorage\Uncompressed
PresentationContext114 = RawDataStorage\Uncompressed
PresentationContext115 = RTBeamsTreatmentRecordStorage\Uncompressed
PresentationContext116 = RTBrachyTreatmentRecordStorage\Uncompressed
PresentationContext117 = RTDoseStorage\Uncompressed
PresentationContext118 = RTPlanStorage\Uncompressed
PresentationContext119 = RTStructureSetStorage\Uncompressed
PresentationContext120 = RTTreatmentSummaryRecordStorage\Uncompressed
PresentationContext121 = SpatialFiducialsStorage\Uncompressed
PresentationContext122 = SpatialRegistrationStorage\Uncompressed
PresentationContext123 = StereometricRelationshipStorage\Uncompressed
PresentationContext124 = TwelveLeadECGWaveformStorage\Uncompressed
PresentationContext125 = XRayRadiationDoseSRStorage\Uncompressed
#
# the new video objects are only negotiated with MPEG2 transfer syntax
#
PresentationContext126 = VideoEndoscopicImageStorage\MPEG2
PresentationContext127 = VideoMicroscopicImageStorage\MPEG2
PresentationContext128 = VideoPhotographicImageStorage\MPEG2
#
# the following SOP classes are missing in the above list:
#
# - AcquisitionContextSRStorage
# - AdvancedBlendingPresentationStateStorage
# - ArterialPulseWaveformStorage
# - AutorefractionMeasurementsStorage
# - BasicStructuredDisplayStorage
# - BlendingSoftcopyPresentationStateStorage
# - BreastProjectionXRayImageStorageForPresentation
# - BreastProjectionXRayImageStorageForProcessing
# - BreastTomosynthesisImageStorage
# - CArmPhotonElectronRadiationStorage
# - CompositingPlanarMPRVolumetricPresentationStateStorage
# - Comprehensive3DSRStorage
# - ContentAssessmentResultsStorage
# - CornealTopographyMapStorage
# - CTDefinedProcedureProtocolStorage
# - CTPerformedProcedureProtocolStorage
# - DeformableSpatialRegistrationStorage
# - EncapsulatedCDAStorage
# - EncapsulatedSTLStorage
# - EnhancedMRColorImageStorage
# - EnhancedPETImageStorage
# - EnhancedUSVolumeStorage
# - ExtensibleSRStorage
# - GeneralAudioWaveformStorage
# - GenericImplantTemplateStorage
# - GrayscalePlanarMPRVolumetricPresentationStateStorage
# - ImplantAssemblyTemplateStorage
# - ImplantationPlanSRDocumentStorage
# - ImplantTemplateGroupStorage
# - IntraocularLensCalculationsStorage
# - IntravascularOpticalCoherenceTomographyImageStorageForPresentation
# - IntravascularOpticalCoherenceTomographyImageStorageForProcessing
# - KeratometryMeasurementsStorage
# - LegacyConvertedEnhancedCTImageStorage
# - LegacyConvertedEnhancedMRImageStorage
# - LegacyConvertedEnhancedPETImageStorage
# - LensometryMeasurementsStorage
# - MacularGridThicknessAndVolumeReportStorage
# - MultipleVolumeRenderingVolumetricPresentationStateStorage
# - OphthalmicAxialMeasurementsStorage
# - OphthalmicOpticalCoherenceTomographyBscanVolumeAnalysisStorage
# - OphthalmicOpticalCoherenceTomographyEnFaceImageStorage
# - OphthalmicThicknessMapStorage
# - OphthalmicTomographyImageStorage
# - OphthalmicVisualFieldStaticPerimetryMeasurementsStorage
# - ParametricMapStorage
# - PatientRadiationDoseSRStorage
# - PerformedImagingAgentAdministrationSRStorage
# - PlannedImagingAgentAdministrationSRStorage
# - ProtocolApprovalStorage
# - RadiopharmaceuticalRadiationDoseSRStorage
# - RealWorldValueMappingStorage
# - RespiratoryWaveformStorage
# - RTBeamsDeliveryInstructionStorage
# - RTBrachyApplicationSetupDeliveryInstructionStorage
# - RTIonBeamsTreatmentRecordStorage
# - RTIonPlanStorage
# - RTPhysicianIntentStorage
# - RTRadiationSetStorage
# - RTSegmentAnnotationStorage
# - SegmentationStorage
# - SegmentedVolumeRenderingVolumetricPresentationStateStorage
# - SimplifiedAdultEchoSRStorage
# - SpectaclePrescriptionReportStorage
# - SubjectiveRefractionMeasurementsStorage
# - SurfaceScanMeshStorage
# - SurfaceScanPointCloudStorage
# - SurfaceSegmentationStorage
# - TractographyResultsStorage
# - VisualAcuityMeasurementsStorage
# - VLWholeSlideMicroscopyImageStorage
# - VolumeRenderingVolumetricPresentationStateStorage
# - WideFieldOphthalmicPhotographyStereographicProjectionImageStorage
# - WideFieldOphthalmicPhotography3DCoordinatesImageStorage
# - XAXRFGrayscaleSoftcopyPresentationStateStorage
# - XRay3DAngiographicImageStorage
# - XRay3DCraniofacialImageStorage
#
# - RETIRED_StandaloneCurveStorage
# - RETIRED_StandaloneModalityLUTStorage
# - RETIRED_StandaloneOverlayStorage
# - RETIRED_StandalonePETCurveStorage
# - RETIRED_StandaloneVOILUTStorage
# - RETIRED_StoredPrintStorage
#
# - DRAFT_RTBeamsDeliveryInstructionStorage
# - DRAFT_SRAudioStorage
# - DRAFT_SRComprehensiveStorage
# - DRAFT_SRDetailStorage
# - DRAFT_SRTextStorage
# - DRAFT_WaveformStorage
#
# - DICOS_2DAITStorage
# - DICOS_3DAITStorage
# - DICOS_CTImageStorage
# - DICOS_DigitalXRayImageStorageForPresentation
# - DICOS_DigitalXRayImageStorageForProcessing
# - DICOS_QuadrupoleResonanceStorage
# - DICOS_ThreatDetectionReportStorage
#
# - DICONDE_EddyCurrentImageStorage
# - DICONDE_EddyCurrentMultiframeImageStorage

# ============================================================================
[[Profiles]]
# ============================================================================

#
# For non-image SOP classes, we only support uncompressed transmission.
# For image SOP classes, we support JPEG lossy, JPEG lossless and uncompressed.
# For Ultrasound, we additionally support RLE.
# The retired and standalone SOP classes are not supported with this profile.
#
# No SCP/SCU role negotiation or extended negotiation.
#
[Default]
PresentationContexts = StorageCompressedAndUncompressed
