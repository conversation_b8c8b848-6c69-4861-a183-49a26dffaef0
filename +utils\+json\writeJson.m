function writeJson(cfg, fname, PrettyPrint, varargin)
    if nargin<3
        PrettyPrint=true;
    end
   

    header = jsonencode(cfg, 'PrettyPrint',PrettyPrint); 
    fid = fopen(fname,'wt');
    %fprintf(fid, header);
    fprintf(fid,'%s', header);
    fclose(fid);

    options = OptionsMap(varargin{:});
    emptyAsNull=options.getOption('emptyAsNull', 0);
    if emptyAsNull
        replaceStringInFile(fname, '[]', 'null');
    end

    recoverFieldNames=options.getOption('recoverFieldNames', 0);
    if recoverFieldNames
        recoverJsonFieldNames(fname);
    end
end

function replaceStringInFile(filePath, oldStr, newStr)
    % REPLACESTRINGINFILE Replaces occurrences of oldStr with newStr in a file.
    % 
    % Usage:
    %   replaceStringInFile('example.txt', 'oldText', 'newText')
    %
    % Inputs:
    %   - filePath: Path to the file
    %   - oldStr: String to be replaced
    %   - newStr: Replacement string

    % Check if file exists
    if ~isfile(filePath)
        error('File "%s" not found.', filePath);
    end
    
    % Read file content
    fid = fopen(filePath, 'r');
    if fid == -1
        error('Could not open file "%s" for reading.', filePath);
    end
    fileContents = fread(fid, '*char')';  % Read as a character array
    fclose(fid);

    % Replace occurrences of oldStr with newStr
    fileContents = strrep(fileContents, oldStr, newStr);

    % Write back to the file
    fid = fopen(filePath, 'w');
    if fid == -1
        error('Could not open file "%s" for writing.', filePath);
    end
    fwrite(fid, fileContents);
    fclose(fid);
    
    fprintf('Replaced occurrences of "%s" with "%s" in file: %s\n', oldStr, newStr, filePath);
end

function recoverJsonFieldNames(jsonFilePath)
    % EDITJSONFIELDNAMES Modifies field names in a JSON file.
    % Any field name starting with 'x' will be replaced with '_'.
    %
    % Usage:
    %   editJsonFieldNames('example.json')

    % Check if the file exists
    if ~isfile(jsonFilePath)
        error('File "%s" not found.', jsonFilePath);
    end

    % Read the JSON file as a text string
    fid = fopen(jsonFilePath, 'r');
    if fid == -1
        error('Could not open file "%s" for reading.', jsonFilePath);
    end
    rawText = fread(fid, '*char')';
    fclose(fid);

    % Use regex to replace field names starting with "x" with "_"
    newJsonText = regexprep(rawText, '"x(\w+)":', '"$1":');

    % Write back to the file
    fid = fopen(jsonFilePath, 'w');
    if fid == -1
        error('Could not open file "%s" for writing.', jsonFilePath);
    end
    fwrite(fid, newJsonText);
    fclose(fid);
    
    fprintf('Updated field names in JSON file: %s\n', jsonFilePath);
end
