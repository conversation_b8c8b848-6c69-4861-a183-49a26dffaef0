function str = struct2mapX(str, mapnamepat)
    if isstruct(str)
        fns=fieldnames(str);
        for k=1:numel(fns)
               fn = fns{k};
               str.(fn)=utils.json.struct2mapX(str.(fn), mapnamepat);
        end
        
    elseif iscell(str)
        for k=1:numel(str)
            str{k}=utils.json.struct2mapX(str{k}, mapnamepat);
        end
    end
end

function flag = matchname(fn, pattern)
    flag = ~isempty(regexp(fn, pattern, 'ONCE'));
end