dcmconv(1)                        OFFIS DCMTK                       dcmconv(1)



NAME
       dcmconv - Convert DICOM file encoding


SYNOPSIS
       dcmconv [options] dcmfile-in dcmfile-out

DESCRIPTION
       The  dcmconv  utility  reads  a  DICOM  file  (dcmfile-in), performs an
       encoding conversion and writes the converted data  to  an  output  file
       (dcmfile-out).

PARAMETERS
       dcmfile-in   DICOM input filename to be converted

       dcmfile-out  DICOM output filename to write to

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -l   --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -l   --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

       parsing of file meta information:

         +ml  --use-meta-length
                use file meta information group length (default)

         -ml  --ignore-meta-length
                ignore file meta information group length

       parsing of odd-length attributes:

         +ao  --accept-odd-length
                accept odd length attributes (default)

         +ae  --assume-even-length
                assume real length is one byte larger

       handling of explicit VR:

         +ev  --use-explicit-vr
                use explicit VR from dataset (default)

         -ev  --ignore-explicit-vr
                ignore explicit VR (prefer data dictionary)

       handling of non-standard VR:

         +vr  --treat-as-unknown
                treat non-standard VR as unknown (default)

         -vr  --assume-implicit
                try to read with implicit VR little endian TS

       handling of undefined length UN elements:

         +ui  --enable-cp246
                read undefined len UN as implicit VR (default)

         -ui  --disable-cp246
                read undefined len UN as explicit VR

       handling of defined length UN elements:

         -uc  --retain-un
                retain elements as UN (default)

         +uc  --convert-un
                convert to real VR if known

       handling of private max-length elements (implicit VR):

         -sq  --maxlength-dict
                read as defined in dictionary (default)

         +sq  --maxlength-seq
                read as sequence with undefined length

       handling of wrong delimitation items:

         -rd  --use-delim-items
                use delimitation items from dataset (default)

         +rd  --replace-wrong-delim
                replace wrong sequence/item delimitation items

       handling of illegal undefined length OB/OW elements:

         -oi  --illegal-obow-rej
                reject dataset with illegal element (default)

         +oi  --illegal-obow-conv
                convert undefined length OB/OW element to SQ

       handling of VOI LUT Sequence with OW VR and explicit length:

         -vi  --illegal-voi-rej
                reject dataset with illegal VOI LUT (default)

         +vi  --illegal-voi-conv
                convert illegal VOI LUT to SQ

       handling of explicit length pixel data for encaps. transfer syntaxes:

         -pe  --abort-expl-pixdata
                abort on explicit length pixel data (default)

         +pe  --use-expl-pixdata
                use explicit length pixel data

       general handling of parser errors:

         +Ep  --ignore-parse-errors
                try to recover from parse errors

         -Ep  --handle-parse-errors
                handle parse errors and stop parsing (default)

       other parsing options:

         +st  --stop-after-elem  [t]ag: "gggg,eeee" or dictionary name
                stop parsing after element specified by t

       automatic data correction:

         +dc  --enable-correction
                enable automatic data correction (default)

         -dc  --disable-correction
                disable automatic data correction

       bitstream format of deflated input:

         +bd  --bitstream-deflated
                expect deflated bitstream (default)

         +bz  --bitstream-zlib
                expect deflated zlib bitstream

   processing options
       specific character set:

         # the following options require support from an underlying character
         # encoding library (see output of --version on which one is available)

         +U8  --convert-to-utf8
                convert all element values that are affected
                by Specific Character Set (0008,0005) to UTF-8

         +L1  --convert-to-latin1
                convert affected element values to ISO 8859-1

         +A7  --convert-to-ascii
                convert affected element values to 7-bit ASCII

         +C   --convert-to-charset  [c]harset: string
                convert affected element values to the character
                set specified by the DICOM defined term c

         -Ct  --transliterate
                try to approximate characters that cannot be
                represented through similar looking characters

         -Cd  --discard-illegal
                discard characters that cannot be represented
                in destination character set

       other processing options:

         -ig  --no-invalid-groups
                remove elements with invalid group number

   output options
       output file format:

         +F   --write-file
                write file format (default)

         +Fm  --write-new-meta-info
                write file format with new meta information

         -F   --write-dataset
                write data set without file meta information

       output transfer syntax:

         +t=  --write-xfer-same
                write with same TS as input (default)

         +te  --write-xfer-little
                write with explicit VR little endian TS

         +tb  --write-xfer-big
                write with explicit VR big endian TS

         +ti  --write-xfer-implicit
                write with implicit VR little endian TS

         +td  --write-xfer-deflated
                write with deflated explicit VR little endian TS

         +tg  --write-xfer-ge
                write private GE implicit VR little endian
                with big endian pixel data TS (non-standard)

       post-1993 value representations:

         +u   --enable-new-vr
                enable support for new VRs (UN/UT) (default)

         -u   --disable-new-vr
                disable support for new VRs, convert to OB

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

         +eo  --write-oversized
                write oversized explicit length sequences
                and items with undefined length (default)

         -eo  --abort-oversized
                abort on oversized explicit sequences/items

       data set trailing padding (not with --write-dataset):

         -p=  --padding-retain
                do not change padding (default if not --write-dataset)

         -p   --padding-off
                no padding (implicit if --write-dataset)

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

       deflate compression level (only with --write-xfer-deflated):

         +cl  --compression-level  [l]evel: integer (default: 6)
                0=uncompressed, 1=fastest, 9=best compression

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmconv utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmdump(1)

COPYRIGHT
       Copyright  (C)  1994-2017  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcmconv(1)
