classdef DicomInfoAnon <OptionsWithLog
    properties
        m_Hash; 
    end
    
    methods
        function obj = DicomInfoAnon(varargin)
            obj = obj@OptionsWithLog('DicomInfoAnon', varargin{:});
            obj.m_Hash = anon.Hash(varargin{:});
        end

        function res = AnonDcmDataRoot(self)
            res = self.getoptioni('ANON_DCMSERVER_FOLDER', 'c:\dcmanon\');
        end
        
        function res = AnonDBFile(self)
            res = self.getoptioni_char('ANON_DB_FILE');
        end
        
        function res = AnonDB(self)
            res = self.getoptioni('ANON_DB');
            if isempty(res)
                fname = AnonDBFile(self); 
                if ~isempty(fname)
                    res = xls.RecordKeepTable({'table.filename', fname}, {'table.uniquekey', 'PatientID'});
                    self.setOption('ANON_DB', res); 
                end
            end
        end
        
        function res = FieldsToRemove(self)
            %res = 'Patient|Institution|Manufacturer|Phone|Address|Residence|Private|StudyID';
%             res = 'Institution|Phone|Residence|Private|StudyID';
            res = 'Private_'; 
        end
        
        function res = FieldsToKeep(self)
            %res = 'PatientSex|StudyDescription|PatientSetupSequence|^Image';
            res = 'PatientSex|StudyDescription'; 
            %res = 'PatientSex|StudyDescription|SeriesDescription|ProtocolName|SequenceName';
        end
        
        function [outfiles, infiles] = AnonymizeFiles(self, infiles)
            outfiles={};
            if isempty(infiles)
                return;
            end
            
            if ~iscell(infiles)
                res = DosUtil.rdir(infiles);
                infiles = {res(:).name};
            end
            
            for k=1:numel(infiles)
                outfiles{k} = self.AnonymizeFile(infiles{k}); 
            end
            
           db =  AnonDB(self); 
           if ~isempty(db)
                db.SaveTable();
           end
        end
        
        function outfile = AnonymizeFile(self, infile, outfile, varargin)
            if ~exist('outfile', 'var')
                outfile ='';
            end
            try
            %metadata0 = dicominfo(infile);
            metadata0 = dicominfo(infile, 'UseDictionaryVR', true, 'UseVRHeuristic', false);
            catch err
                outfile =''; 
                return;
            end
            %avoid anonymize multiple times
            if isfield(metadata0, 'PatientIdentityRemoved') && strcmpi(metadata0.('PatientIdentityRemoved'), 'YES')
                if isempty(outfile)
                     root    = AnonDcmDataRoot(self);
                     [destDirectory, outfile] = ai.DcmParser.info2path(root, metadata0); 
                     if ~exist(destDirectory, 'dir')
                        mkdir(destDirectory);
                     end
                end
                copyfile(infile, outfile); 
                return; 
            end
            
            if ~strcmpi(metadata0.Modality, 'RTSTRUCT') || ~strcmpi(metadata0.Modality, 'RTPLAN')
                X = dicomread(metadata0);
            else
                X =[];
            end
            
            %metadata  = ProcessStruct(self, metadata0, varargin{:});
            metadata  = anon.dicomanonmeta(metadata0, 'keep', strsplit(self.FieldsToKeep(), '|'));
            metadata  = ProcessStruct(self, metadata, varargin{:});
            metadata.PatientID =self.m_Hash.HashName(metadata0.PatientID);
            
            try
            birthdate = metadata0.PatientBirthDate; 
            studydate = metadata0.StudyDate; 
            age       = round((datenum(studydate, 'yyyymmdd')-datenum(birthdate, 'yyyymmdd'))/365.25);
            metadata.PatientAge=num2str(age);
            metadata.PatientName=struct('FamilyName',metadata.PatientID, ...
                'GivenName', 'ANON', 'MiddleName', '', 'NamePrefix', ''); 
            end
            
            try
                 physician = metadata0.PhysiciansOfRecord;
                 name = self.m_Hash.HashName([physician.FamilyName ' ' physician.GivenName]);
                 metadata.PhysiciansOfRecord=struct('FamilyName',name, ...
                    'GivenName', 'ANON', 'MiddleName', '', 'NamePrefix', ''); 
            end
            
%             metadata.PatientIdentityRemoved= 'YES';
%             metadata.DeidentificationMethod= 'DICOMANON (rev R2010a) - PS 3.15-2008 Table E.1-1';
            
            if isempty(outfile)
                 root    = AnonDcmDataRoot(self);
                 [destDirectory, outfile] = ai.DcmParser.info2path(root, metadata); 
                 if ~exist(destDirectory, 'dir')
                    mkdir(destDirectory);
                 end
            end
            
%             dicomwrite(X, outfile, metadata, ...
%                'createmode', 'copy', ...
%                'WritePrivate', false, ...
%                'UseMetadataBitDepths', true);
           self.WriteDicom(outfile, metadata, X);
           
           db =  AnonDB(self); 
           if ~isempty(db)
               record =struct('PatientID', metadata0.PatientID, 'AnonPatientID', metadata.PatientID);
               db.UpdateRow(record, 0);
           end
        end
        
        function info = ProcessStruct(self, info, varargin)
            keeps   = FieldsToKeep(self);
            removes = FieldsToRemove(self); 
            fn = fieldnames(info);
            for k=1:numel(fn)
                name = fn{k}; 
                if ~isempty(regexpi(name, keeps, 'ONCE'))
                    % do nothing
                elseif ~isempty(regexpi(name, removes, 'ONCE'))
                    info = rmfield(info, name); 
                    %info.(name)='';
                else
                    val  = info.(name);
                    if isstruct(val)
                        if isfield(val, 'FamilyName')||isfield(val, 'GivenName')
                            %info = rmfield(info, name); 
                            info.(name)=struct('FamilyName', '', 'GivenName', '');
                        else
                            info.(name) = self.ProcessStruct(val, varargin{:});
                        end
                    else
                        info.(name) = self.HashField(name, val);
                    end
                end
            end
        end
        
        function res = HashField(self, name, val)
             str = UIDRegExpStr(self);
             matchuid = regexp(name, str, 'ONCE');
             %matchdate= regexp(name, 'Date$', 'ONCE');
             res = val;
             try
             if ~isempty(matchuid)
                 res = self.m_Hash.HashUID(val);
%              elseif ~isempty(regexp(name, 'Date$', 'ONCE'))
%                  res= self.m_Hash.HashDateStr(val);
%              elseif ~isempty(regexp(name, 'Time$', 'ONCE'))
%                 res = '000000';
             end
             catch
             end
        end
    end
    
    methods (Access = private)
         function uidnames = UIDFiledNames(self)
            uidnames = self.getoptioni('anon.UIDFiledNames');
            if isempty(uidnames)
                uidnames0 = {'StudyInstanceUID',  'SeriesInstanceUID', 'SOPInstanceUID', 'FrameOfReferenceUID'};
                uidnames1 = cellfun(@(x)(['Referenced' x]), uidnames0, 'uniformoutput', false); 
                uidnames  = cat(2, uidnames0, uidnames1);
                uidnames = cat(2, uidnames, {'SynchronizationFrameOfReferenceUID', 'UID'});
                self.setOption('anon.UIDFiledNames', uidnames); 
            end
        end
        
        function str = UIDRegExpStr(self)
            str = self.getoptioni('anon.UIDRegExpStr');
            if isempty(str)
                uidnames = UIDFiledNames(self);
                str = ['(^' uidnames{1} '$)'];
                for k=2:numel(uidnames)
                    str = [str '|' '(^' uidnames{k} '$)'];
                end
            end
        end
    end
    
    methods (Static)
        function anonymize(files, varargin)
            eval = anon.DicomInfoAnon(varargin{:});
            eval.AnonymizeFiles(files);
        end
        
         function WriteDicom(outputfile, dcminfo, data)
%                 [destDirectory, outputfile] = db.info2path(dcminfo);
%                 mkdir(destDirectory); 
%                 if ~exist('data', 'var') 
%                     data = []; 
%                 end
                dicomwrite(data, outputfile, dcminfo, 'CreateMode', 'Copy', 'WritePrivate', false, ...
                    'UseMetadataBitDepths', true);
                
                dcmobj = dcm4che2.read(outputfile);
                if  isempty(data)
                    dcm4che2.tag(dcmobj, 'PixelData', {});
                end
                
                dcm4che2.tag(dcmobj, 'set', 'SOPInstanceUID', dcminfo.SOPInstanceUID);
                dcm4che2.write(dcmobj, outputfile);
        end
    end
end
