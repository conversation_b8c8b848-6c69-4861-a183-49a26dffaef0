classdef TableFilter <OptionsMap   
    properties
        m_Filter; 
    end
    
    methods
        function obj = TableFilter(varargin)
            obj@OptionsMap(varargin{:});
            fname = obj.getoptioni('FilterFileName'); 
            if exist(fname, 'file')
                 LoadFilter_json(obj, fname);
            end
        end
        
        %filter must be a struct
        function AddFilter(obj, filter)
            if isempty(obj.m_Filter)
                obj.m_Filter = {filter};
            else
                N = numel(obj.m_Filter);
                obj.m_Filter{N+1} = filter; 
            end
        end
        
        function AddTableFilter(obj, filter)
            if isa(filter, 'xls.TableFilter') 
                num = filter.NumFilters();
                for k=1:num
                    obj.AddFilter(filter.m_Filter{k});
                end
            elseif isstruct(filter) 
                 filter = arrayfun(@(x)(x), filter, 'uniformoutput', false); 
                 for k=1:numel(filter)
                    obj.AddFilter(filter{k});
                 end
            elseif iscell(filter) 
                 %obj.AddFilter(filter);
                 for k=1:numel(filter)
                    obj.AddFilter(filter{k});
                 end
            elseif ischar(filter)
                 LoadFilter_json(obj, filter);
            end
        end

        function SetFilter(obj, filter)
            obj.m_Filter = filter; 
        end
        
%         function LoadFilter_json(obj, fname)
%             text        = fileread(fname);
%             obj.m_Filter = jsondecode(text);
%             if ~iscell(obj.m_Filter)
%                 obj.m_Filter = arrayfun(@(x)(x), obj.m_Filter, 'uniformoutput', false); 
%             end
%         end
        
         function LoadFilter_json(obj, fname)
            text   = fileread(fname);
            filters = jsondecode(text);
            if ~iscell(filters)
                filters = arrayfun(@(x)(x), filters, 'uniformoutput', false); 
            end
            for k=1:numel(filters)
                filter = filters{k};
                if isfield(filter, 'FilterFileName')
                    fname1 = filter.('FilterFileName');
                    if ~DosUtil.isabsolutepath(fname1)
                        [path] = [fileparts(fname) '\'];
                        fname1 = [path fname1];
                    end
                    filter = xls.TableFilter({'FilterFileName', fname1});
                end
                AddTableFilter(obj, filter);
            end
        end

        function num = NumFilters(obj)
            num = numel(obj.m_Filter);
        end

        function SaveFilter_json(obj, fname)
            text        = jsonencode(obj.m_Filter,'PrettyPrint',true); 
            fid = fopen(fname,'wt');
            fprintf(fid,'%s', text);
            fclose(fid);
        end
        
        function [I, T] = FilterTable(obj, T0)
            N = size(T0, 1);
            I = zeros(N, 1, 'like', true); 
            for n=1:N
                I(n) = FilterStruct(obj, T0(n, :));
            end
            
            I= find(I);
            
            if nargout>1
                T = T0(I, :);
            end
        end
        
        function [I, T] = FilterCell(obj, T0)
            N = numel(T0);
            I = zeros(N, 1, 'like', true); 
            for n=1:N
                I(n) = FilterStruct(obj, T0{n});
            end
            
            I= find(I);
            
            if nargout>1
                T = T0(I);
            end
        end

        function flag = FilterStruct(obj, str)
            if istable(str)
                str = table2struct(str);
            end
            flag = true; 
            for k=1:numel(obj.m_Filter)
                filter   = obj.m_Filter{k};
                
                if isfield(filter, 'FieldName')
                    name     = filter.FieldName;
                else
                    name     = '';
                end
                matchmode= filter.MatchMode; 
                inclusion= StructBase.getfieldx_default(filter, 'Inclusion', true); 
                try
                
                switch lower(matchmode)
                    case 'exist'
                        flag1 = isfield(str, name);
                    case 'notempty'
                        flag1 = ~isempty(str.(name)) && ~strcmpi(str.(name), 'NaN');
                    case 'strcmp'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        flag1 = strcmp(val, filterval);
                    case 'strcmpi'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        flag1 = strcmpi(val, filterval);
                    case 'regexpi'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        flag1 = ~isempty(regexpi(val, filterval, 'ONCE'));
                    case 'regexp'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        flag1 = ~isempty(regexp(val, filterval, 'ONCE'));
                    case 'range'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        if ischar(val)
                            val = str2num(val);
                        end
                        if ischar(filterval)
                            filterval=str2num(filterval);
                        end
                        flag1 = val>=filterval(1) && val<=filterval(2);
                    
                    case 'minvalue'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        if ischar(val)
                            val = str2num(val);
                        end
                        if ischar(filterval)
                            filterval=str2num(filterval);
                        end
                        flag1 = val>=filterval;

                    case 'maxvalue'
                        val   = str.(name);
                        filterval = filter.FieldValue; 
                        if ischar(val)
                            val = str2num(val);
                        end
                        if ischar(filterval)
                            filterval=str2num(filterval);
                        end
                        flag1 = val<=filterval;

                    case 'datetimerange'
                        %val   = str.(name);
                        %filterval = filter.FieldValue; 
                        flag1 = obj.ParseDateTimeRange(filter, str); 
                end
                
                 
                if ~inclusion
                    flag1 = ~flag1; 
                end
                
                flag = flag && flag1; 
                
                if ~flag
                    return
                end
                
                catch err
                    if inclusion
                        flag = false;
                        return;
                    end
                end
            end
        end
       
        function flag = ParseDateTimeRange(obj, filter, str)
            datetimerange = filter.FieldValue;
            mode = filter.datetimemode;
            if strcmpi(mode, 'relative')
                 datetimerange = datetimerange+now; 
            end
            try
            val = datenum([str.StudyDate '.' str.StudyTime], 'yyyymmdd.HHMMSS');
            catch
               val = datenum([str.StudyDate], 'yyyymmdd');
            end
            flag = (val>= datetimerange(1)) && (val<datetimerange(2));
        end
    end
    methods (Static)
        function obj = Factory(filter, varargin)
            obj = xls.TableFilter(varargin{:}); 
            if ~isempty(filter)
                AddTableFilter(obj, filter);
            end
        end

        function Struct2Filter(S, matchmode)
            if ~exist('matchmode', 'var')
                matchmode ='regexp';
            end
            fns = fieldnames(S);
            values = cellfun
        end
    end
end

