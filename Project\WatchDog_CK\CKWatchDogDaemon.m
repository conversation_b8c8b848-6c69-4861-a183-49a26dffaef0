function DcmRTAdminDaemon(varargin)
    if isdeployed
        warning('off','all');
    end
    
    %%%%%%%%%%%%%%%%%%%%%%check license%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    daemon.utils.LicenseTool.ValidateArtDaemonLicense('rtadmin');
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    
    % force the computer to remain awake
    try
    dosutils.insomnia('on','verbose');
    catch
    end
    server = daemon.WatchDogServer_CK(varargin{:});
    server.RunServer; 
end
