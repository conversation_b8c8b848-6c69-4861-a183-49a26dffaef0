classdef RTPatient <OptionsMap  
    properties
        m_RtHist; 
        m_StudyHist;
    end
    
    methods
        function obj = RTPatient(varargin)
            obj = obj@OptionsMap(varargin{:});
        end
        
        function LoadRtHist(self, fname)
            rthist = xls.TableBase.table_cellstr(fname);
            patid = self.getoptioni('PatientID');
            if ~isempty(patid )
                J = ismember(rthist.MRN, patid);
                rthist = rthist(J, :);
            end
            self.m_RtHist=rthist; 
        end
        
        function LoadStudyHist(self, fname)
            self.m_StudyHist = xls.TableBase.table_cellstr(fname,[], 'sheet', 'study');
        end
        
        function res =ShowRtHist(self, varargin)
            treatmentdate = cellfun(@(x)(datenum(x, 'mm/dd/yyyy')), self.m_RtHist.StartDate); 
            elapsedmonth  = round((treatmentdate-treatmentdate(1))/30);
            Technique = self.m_RtHist.Technique;
            Technique=regexprep(Technique, 'Gamma\s*Knife\w*', 'GK', 'ignorecase');
            Technique=regexprep(Technique, 'Cyber\s*knife(\s*)(\w*)', 'CK', 'ignorecase');
            annotext = Technique;
            res =self.TimeLinePlot(elapsedmonth, annotext, varargin{:});
            res.setOption('referencedate', treatmentdate(1));
        end
        
        function res =ShowStudyHist(self, varargin)
            options = OptionsMap(varargin{:});
            T = self.m_StudyHist;
            treatmentdate = cellfun(@(x)(datenum(x, 'yyyymmdd')), T.StudyDate); 
            referencedate = options.getoptioni('referencedate', treatmentdate(1) );
           
            StudyMonth  = round((treatmentdate-referencedate)/30);
            C             = self.ClusterList(StudyMonth, 1);
            StudyMonth= round(cellfun(@(x)(median(StudyMonth(x))), C)); 
            %StudyDate= round(cellfun(@(x)(median(StudyDate(x))), C)); 

            Modalities=T.Modalities; 
            Modalities=regexprep(Modalities, '(\|)PR\w*\d*', '');
            Modalities=regexprep(Modalities, '(\|)SR\w*\d*', '');
            Modalities=regexprep(Modalities, '(\|)REG\w*\d*', '');
            
            Modalities=Modalities';
            Modalities=cellfun(@(x)(strrep(x, '_', '')), Modalities, 'uniformoutput', false);
            Modalities=cellfun(@(x)(strsplit(x, '|'))', Modalities, 'uniformoutput', false);
            Modalities=cellfun(@(x)(cat(1, Modalities{x(:)})), C, 'uniformoutput', false);

            res =self.TimeLinePlot(StudyMonth, Modalities, varargin{:});
        end
        
        function ShowHistory(rtpat)
            options = rtpat.ShowRtHist;
            %rthist = xls.TableBase.table_cellstr('U:\sharedDoc\RTHistory\20210414\RTHistroy.Diagnosis-BM.xlsx');
            rtpat.ShowStudyHist(options, {'annotation.color', 'b'}, {'annotation.ypos', [0.4 0.6]}, {'annotation.markersize', 6}, ...
                {'annotation.marker', 'o'});
        end
    end
    
    methods (Static)
        %val is a sorted list in ascending order
        function C = ClusterList(val, mindist)
            val = val-val(1);
%             mindist = 15;
            C{1} = 1;
            m=1;
            for k=2:numel(val)
                d = val(k)-val(k-1);
                if  d<mindist
                    C{m} = [C{m} k];
                else
                    m=m+1;
                    C{m} = k;
                end
            end
        end
         
        function options = TimeLinePlot(StudyMonth, annotext, varargin)
            options = OptionsMap(varargin{:});
            axH = options.getoptioni('axes');
            if isempty(axH)
                xLim = [StudyMonth(1)-6, StudyMonth(end)+6];
                axH = axes('xlim', xLim, 'ylim', [-1 1]);
                set(axH, 'Position',  [0 0 1 1]);
                set(axH, 'XTick', [0:6:StudyMonth(end)]);
                set(axH, 'xaxislocation', 'origin')
                options.setOption('axes', axH);
            end
            xLim = get(axH, 'xlim');
            color = options.getoptioni('annotation.color', 'r');
            ypos  = options.getoptioni('annotation.ypos', 0.1);
            marksize=options.getoptioni('annotation.markersize', 9);
            mark=options.getoptioni('annotation.marker', '*');
            for k=1:numel(StudyMonth)
                x = StudyMonth(k);
                line(x, 0, 'marker', mark, 'color', color, 'MarkerSize', marksize); hold on;
                xpos = (x-xLim(1))/(xLim(2)-xLim(1));
                if xpos<0||xpos>1
                    continue; 
                end
                N = numel(ypos);
                dim = [xpos ypos(mod(k, N)+1) 0.1 0.3];
                str = annotext{k};
                annotation('textbox',dim,'String',str,'FitBoxToText','on', 'color', color);
            end
        end
        
        function Demo_BM(patid)
            if nargin<1
                patid = '70627348';
            end
            rtpat = RTHistory.RTPatient({'PatientID', patid});
            rtpat.LoadRtHist('U:\sharedDoc\RTHistory\20210414\RTHistroy.Diagnosis-BM.xlsx');
            rtpat.LoadStudyHist(['c:\DicomRTPatient\QUERY\RTPACS_RES\' patid '.xlsx']);
            ShowHistory(rtpat);
        end
    end
end

