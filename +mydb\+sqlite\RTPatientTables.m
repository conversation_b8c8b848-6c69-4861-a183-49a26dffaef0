classdef RTPatientTables <dcmtk.RTPatientTables   
    properties
        m_DcmRTDB; 
    end
    
    methods
        function obj = RTPatientTables(db, varargin)
           options = OptionsMap({'course.followupdates', 90});
           <EMAIL>(options, varargin{:});
           if isa(db, 'mydb.sqlite.DcmRTDB')
               obj.m_DcmRTDB=db;
           elseif isa(db, 'char') 
               obj.m_DcmRTDB=mydb.sqlite.DcmRTDB(db); 
           end
        end

        function T = GetRawTable(self, modality)
            if isempty(self.m_DcmRTDB)
                T = <EMAIL>  (modality);
                return; 
            end
            T = self.m_DcmRTDB.QueryPatient_modality(modality, self.PatientID);
        end

        function info = GetTableRow(self, modality, uid, varargin)
            info = self.m_DcmRTDB.GetTableRow(modality, uid, varargin{:});
        end

        function CloseDB(obj)
            if ~isempty(obj.m_DcmRTDB)
                obj.m_DcmRTDB.CloseDB();
            end
        end
    end

    methods (Static)
        
    end
end

