classdef AppDcmServerGUI < handle
    properties (SetObservable = true)
        queryResults
    end
    properties
        rootPath
        hFigure
        hMenuQueryTools
          hMenuQueryTreatedPatients
          hMenuQueryRtImage
          hMenuQueryRtPlan
          hMenuQueryRtStruct
          hMenuListImagesByPatient
        hResultLabel
        hResultTable
        hViewerHeader
        hViewerImageAxes
        hViewerImage
    end
    methods
        function self = AppDcmServerGUI(rootPath)
            self.rootPath = rootPath;
            %
            self.queryResults = struct();
            %
            self.initWidgets();
            addlistener(self, 'queryResults', 'PostSet', @self.setQueryResultsCallback);
        end
        function initWidgets(self)
            self.hFigure = figure('Name', 'AppDcmServerGUI', 'NumberTitle', 'off', 'MenuBar', 'none',...
                'Color', 'white',...
                'Units', 'pixels', 'Position', [100 50 1440 900]);
            self.hMenuQueryTools = uimenu(self.hFigure, 'Label', 'Query Tools');
            self.hMenuQueryTreatedPatients = uimenu(self.hMenuQueryTools, 'Label', 'Query Treated Patients', 'Callback', @self.wizardQueryTreatedPatients);
            self.hMenuQueryRtImage = uimenu(self.hMenuQueryTools, 'Label', 'Query Portal Image', 'Callback', @self.wizardQueryRtImage);
            self.hMenuQueryRtPlan = uimenu(self.hMenuQueryTools, 'Label', 'Query RTPlan', 'Callback', @self.wizardQueryRtPlan);
            self.hMenuQueryRtStruct = uimenu(self.hMenuQueryTools, 'Label', 'Query RTStruct', 'Callback', @self.wizardQueryRtStruct);
            self.hMenuListImagesByPatient = uimenu(self.hMenuQueryTools, 'Label', 'List images by patient', 'Callback', @self.wizardListImagesByPatient);
            self.hResultLabel = uicontrol(self.hFigure, 'Style', 'text',...
                'BackgroundColor', 'white',...
                'Units', 'pixels', 'Position', [10 860 1440-20 30],...
                'FontSize', 16,...
                'String', 'Number of results: 0');
            self.hResultTable = uitable(self.hFigure,...
                'RowStriping', 'on', 'BackgroundColor', [1 1 1; 221/255 255/255 221/255],...[1 1 1; 228/255 240/255 230/255],...
                'Units', 'pixels', 'Position', [5 405 1440-10 500-50],...
                'ColumnName', fieldnames(self.queryResults), 'RowName', '');
            self.hViewerHeader = uitable(self.hFigure,...
                'RowStriping', 'on', 'BackgroundColor', [1 1 1; 1 1 1],...
                'Units', 'pixels', 'Position', [5 5 900-10 400-10],...
                'ColumnWidth', {440 440},...
                'ColumnFormat', {'char' 'char'},...
                'ColumnName', {'tag' 'value'}, 'RowName', '');
            self.hViewerImageAxes = axes('Parent', self.hFigure,...
                'Units', 'pixels', 'Position', [905 5 540-10 400-10]);
            axis(self.hViewerImageAxes, 'off');
            
            set(self.hResultTable, 'CellSelectionCallback', @self.displaySelectedImage);
        end
        function setQueryResultsCallback(self, src, evt)
            set(self.hResultLabel, 'String', sprintf('Number of results: %d', numel(self.queryResults)));
            set(self.hResultTable, 'ColumnName', fieldnames(self.queryResults), 'Data', squeeze(struct2cell(self.queryResults))');
        end
    end
    methods
        function displaySelectedImage(self, src, evt)
            if ~isempty(evt.Indices)
                data = get(self.hResultTable, 'Data');
                filename = fullfile(self.rootPath, data{evt.Indices(1,1), end});
                try
                    info = dicominfo(filename);
                    data = dicomread(filename);
                    if ~isempty(info)
                        fns = fieldnames(info);
                        fds = cell(size(fns));
                        for k = 1:numel(fns)
                            fds{k} = evalc('disp(info.(fns{k}))');
                        end
                        set(self.hViewerHeader, 'Data', [fns(:) fds(:)]);
                    end
                    if ~isempty(data)
                        imagesc('Parent', self.hViewerImageAxes, 'CData', data);
                        axis(self.hViewerImageAxes, 'off');
                        axis(self.hViewerImageAxes, 'image');
                    else
                        cla(self.hViewerImageAxes);
                    end
                catch
                end
            end
        end
        function wizardQueryRtImage(self, src, evt)
            answerCell = inputdlg({'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'}, 'Query RtImage Wizard', [1 60], {'' '' '' '' '' '' '' '' '' ''}, struct('Resize', 'on'));
            dbid = mksqlite(0, 'open', fullfile(self.rootPath, 'dcmdb1.sqlite'));
            answer = cell2struct(answerCell, {'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'});
            whereStr = cell(0);
            for fn = fieldnames(answer)'
                if ~isempty(answer.(fn{1}))
                    whereStr(end+1) = {sprintf('%s = "%s"', fn{1}, answer.(fn{1}))};
                end
            end
            if isempty(whereStr)
                self.queryResults = mksqlite(dbid, 'SELECT * FROM vRTIMAGE');
            else
                self.queryResults = mksqlite(dbid, sprintf('SELECT * FROM vRTIMAGE WHERE %s', strjoin(whereStr, ' AND ')));
            end
            mksqlite(dbid, 'close');
        end
        
         function wizardQueryRtPlan(self, src, evt)
            answerCell = inputdlg({'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'}, 'Query RtPlan Wizard', [1 60], {'' '' '' '' '' '' '' '' '' ''}, struct('Resize', 'on'));
            dbid = mksqlite(0, 'open', fullfile(self.rootPath, 'dcmdb1.sqlite'));
            answer = cell2struct(answerCell, {'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'});
            whereStr = cell(0);
            for fn = fieldnames(answer)'
                if ~isempty(answer.(fn{1}))
                    whereStr(end+1) = {sprintf('%s = "%s"', fn{1}, answer.(fn{1}))};
                end
            end
            if isempty(whereStr)
                self.queryResults = mksqlite(dbid, 'SELECT * FROM vRTPLAN');
            else
                self.queryResults = mksqlite(dbid, sprintf('SELECT * FROM vRTPLAN WHERE %s', strjoin(whereStr, ' AND ')));
            end
            mksqlite(dbid, 'close');
         end
        
        function wizardQueryRtStruct(self, src, evt)
            answerCell = inputdlg({'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'}, 'Query RtPlan Wizard', [1 60], {'' '' '' '' '' '' '' '' '' ''}, struct('Resize', 'on'));
            dbid = mksqlite(0, 'open', fullfile(self.rootPath, 'dcmdb1.sqlite'));
            answer = cell2struct(answerCell, {'PatientID' 'PatientName' 'StudyInstanceUID' 'StudyDate' 'SeriesInstanceUID' 'SeriesDate' 'ContentDate' 'FrameOfReferenceUID' 'FractionNumber' 'ReferencedBeamNumber'});
            whereStr = cell(0);
            for fn = fieldnames(answer)'
                if ~isempty(answer.(fn{1}))
                    whereStr(end+1) = {sprintf('%s = "%s"', fn{1}, answer.(fn{1}))};
                end
            end
            if isempty(whereStr)
                self.queryResults = mksqlite(dbid, 'SELECT * FROM vRTSTRUCT');
            else
                self.queryResults = mksqlite(dbid, sprintf('SELECT * FROM vRTSTRUCT WHERE %s', strjoin(whereStr, ' AND ')));
            end
            mksqlite(dbid, 'close');
        end
         
        function wizardQueryTreatedPatients(self, src, evt)
            answer = inputdlg({'Start date' 'End date'}, 'Query Treated Patients Wizard', [1 60], {'20140101' '20141231'}, struct('Resize', 'on'));
            % self.queryResults = struct('PatientID', {'123' '456'}, 'TreatmentDate', {'20140101' '20140102'});
            if ~isempty(answer)
                dbid = mksqlite(0, 'open', fullfile(self.rootPath, 'dcmdb1.sqlite'));
                self.queryResults = mksqlite(dbid, sprintf('SELECT PatientID,TreatmentDate,TreatmentTime FROM vRTRECORD WHERE TreatmentDate >= "%s" AND TreatmentDate <= "%s" GROUP BY PatientID', answer{1}, answer{2}));
                mksqlite(dbid, 'close');
            end
        end
        function wizardListImagesByPatient(self, src, evt)
            dbid = mksqlite(0, 'open', fullfile(self.rootPath, 'dcmdb1.sqlite'));
            self.queryResults = mksqlite(dbid, 'SELECT PatientID,COUNT(SOPInstanceUID) AS TOTAL FROM image GROUP BY PatientID');
            numImages = getfield(mksqlite(dbid, 'SELECT COUNT(SOPInstanceUID) AS numImages FROM image'), 'numImages');
            msgbox(sprintf('Total number of images: %d', numImages));
            mksqlite(dbid, 'close');
        end
    end
end
