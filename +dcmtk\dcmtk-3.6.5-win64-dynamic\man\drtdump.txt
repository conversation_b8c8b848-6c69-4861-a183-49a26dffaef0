drtdump(1)                        OFFIS DCMTK                       drtdump(1)



NAME
       drtdump - Dump DICOM RT file and data set


SYNOPSIS
       drtdump [options] drtfile-in...

DESCRIPTION
       The  drtdump  utility  dumps  the contents of a DICOM Radiation Therapy
       (RT) object (file format or raw data set) to stdout  in  textual  form.
       The  output  consists  of  essential  information on the RT object in a
       format that is probably easier to read than the more generic  one  from
       dcmdump.

       If drtdump reads a raw data set (DICOM data without a file format meta-
       header) it will attempt to guess the transfer syntax by  examining  the
       first  few  bytes  of  the file. It is not always possible to correctly
       guess the transfer syntax and it is better to convert a data set  to  a
       file  format  whenever possible (using the dcmconv utility). It is also
       possible to use the -f and -t[ieb] options to force drtdump to  read  a
       dataset with a particular transfer syntax.

PARAMETERS
       drtfile-in  DICOM RT input filename to be dumped

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

   output options
       printing:

         +Pf  --print-filename
                print header with filename for each input file

NOTES
   DICOM Conformance
       The drtdump utility supports the following SOP Classes:

       RTImageStorage                   1.2.840.10008.5.1.4.1.1.481.1
       RTDoseStorage                    1.2.840.10008.5.1.4.1.1.481.2
       RTStructureSetStorage            1.2.840.10008.5.1.4.1.1.481.3
       RTPlanStorage                    1.2.840.10008.5.1.4.1.1.481.5
       RTTreatmentSummaryRecordStorage  1.2.840.10008.5.1.4.1.1.481.7
       RTIonPlanStorage                 1.2.840.10008.5.1.4.1.1.481.8
       RTIonBeamsTreatmentRecordStorage 1.2.840.10008.5.1.4.1.1.481.9

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The drtdump utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmconv(1), dcmdump(1)

COPYRIGHT
       Copyright (C) 2010-2014 by OFFIS e.V. and ICSMED AG, Escherweg 2, 26121
       Oldenburg, Germany.



Version 3.6.5                   Mon Oct 28 2019                     drtdump(1)
