classdef nnUNET_VS < daemon.taskdef.nnUNETInference
    properties
        
    end

    methods
        function obj = nnUNET_VS(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function TempSeg_ch1(tasksdeffile)
            %taskdeffolder = [fileparts(tasksdeffile) '/'];
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.MR_VS\';
            datesetnums ={'111', '121', '141'};
            modalities = {'T1', 'T2', 'T1orT2'};
            TrainerName='nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_VS-MC-GK_' modality '2AcousticNeuroma']), ...
                 datesetnums, modalities, 'UniformOutput',false);
            modeloptfiles=cellfun(@(modelid)([nnUNETModelRoot modelid '#' TrainerName '.opt']), modelids, 'UniformOutput',false);   
            for k=1:numel(modelids)
                modelid= modelids{k};
                taskid =  matlab.lang.makeValidName(modelid);
                tasskdefname = ['tempMR_' taskid];
                modeloptfile=modeloptfiles{k};
                daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, tasskdefname, modeloptfile);
                tasskdefname = ['MR_' taskid];
                daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, tasskdefname, modeloptfile);
            end
        end

        function TempSeg_T1T2(tasksdeffile)
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.MR_VS\';
            modelids   ={'Dataset006_VSMR1Targ', 'Dataset241_VS-MC-GK_T1T22AcousticNeuroma',};
            TrainerNames={'nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0', 'nnUNetTrainerNoMirroring__nnUNetPlans__3d_fullres#fold_0'};
            modeloptfiles=cellfun(@(modelid, TrainerName)([nnUNETModelRoot modelid '#' TrainerName '.opt']), modelids, TrainerNames, ...
                'UniformOutput',false);   
            for k=1:numel(modelids)
                modelid= modelids{k};
                taskid =  matlab.lang.makeValidName(modelid);
                modeloptfile=modeloptfiles{k};
                    
                tasskdefname = ['tempMR_' taskid];
                daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, tasskdefname, modeloptfile);

                [filters] = daemon.taskdef.qMRI.MRSeqFilter({'T2'}, 10);
                filter_FOR= struct("FieldName", 'FrameOfReferenceUID',	'FieldValue', '[FrameOfReferenceUID]', "MatchMode", 'strcmp');
                filters = cat(2, filters, {filter_FOR});
                extrasource = struct('DBTableName', 'MR'); extrasource.Filter = filters; 
                tasskdefname = ['MR_' taskid];
                daemon.taskdef.nnUNET.InferenceDef_ExtraSource(tasksdeffile, tasskdefname, modeloptfile, extrasource);
            end
        end
    end
end

