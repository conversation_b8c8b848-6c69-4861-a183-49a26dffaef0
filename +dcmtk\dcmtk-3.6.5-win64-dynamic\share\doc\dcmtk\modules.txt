Configuration of the different Modules
======================================

The following files in the "config" directory are used for the configuration
of the DCMTK modules:

confmod.in      definition of configuration tests for modules
confmod         creates "configure" for modules
general.m4      general configuration tests (see "config.txt")
Makefile.def    general default Makefile definitions for all modules, generated
                by "configure"
templates/configure.mod
                "configure" for the modules' directories, must be copied to the
                main module directory (see "dirstruc.txt")
include/dcmtk/config/osconfig.h
                definition that must be included into the modules' source
                files, generated by "configure"

"confmod" must be copied to the module's main directory and renamed to
"configure".


Modules configure
=================

This configure script runs the modules' and the main configure script in the
"config" directory (using special options):

Syntax:
        configure [-a|-c] <configure-options>

Purpose:
        Without options, this script calls "confmod" in the "config" directory
        to create the configuration for the Makefiles in the module.
        Using -a mean that first the "configure" in the "config" directory is
        called and then "confmod".  With -c only the "configure" in the
        "config" directory is started.  The "configure" options are passed to
        the called scripts.
        To determine the "config" directory the script searches from the local
        directory to the root to find a directory named "config".  The output
        of the "confmod" script (*.cache, *.status, *.log) is stored in the
        module's directory.  It is not possible to call "confmod" from the
        "config" directory.

"confmod.in" stores the configurations that must be used in the modules and not
in the "config" directory.  This is mainly used to define variables that
specify the "config" directory and the default Makefile template.


Modules Makefiles
=================

All Makefiles in the modules' directories must include the following lines:

@SET_MAKE@

SHELL = /bin/sh
VPATH = @srcdir@:@top_srcdir@/include:@top_srcdir@/@configdir@/include
srcdir = @srcdir@
top_srcdir = @top_srcdir@
configdir = @top_srcdir@/@configdir@

include $(configdir)/@common_makefile@

See the Makefile.* in the config/templates directory.
