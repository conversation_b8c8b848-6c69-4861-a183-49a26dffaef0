classdef TaskDef <utils.json.JsonConfig
    properties
        
    end

    methods
        function obj = TaskDef(varargin)
            <EMAIL>(varargin{:});
            if isempty(obj.m_cfgfile)
                taskdeffolder = obj.getOption('TaskDefFolder');
                taskdefname = obj.getOption('TaskDefName');
                if ~isempty(taskdeffolder) && ~isempty(taskdefname)
                    obj.m_cfgfile=DosUtil.SimplifyPath([taskdeffolder taskdefname '.taskdef']);
                end
            end
        end
        
        function res = TaskDefFileName(obj)
            res = obj.m_cfgfile; 
        end

        function name = TaskDefName(obj)
            [folder, name, ext] = fileparts(TaskDefFileName(obj));
        end

        function folder = TaskDefFolder(obj)
            [folder, name, ext] = fileparts(TaskDefFileName(obj));
            folder = DosUtil.SimplifyPath([folder '\']);
        end

        function SetDef_struct(self, defstr)
            fns = fieldnames(defstr);
            for k=1:numel(fns)
                name = fns{k};
                self.SetConfig(name, defstr.(name));
            end
        end

        function SetDef_options(self, options)
            names = options.getOptionNames; 
            for k=1:numel(names)
                name = names{k};
                self.SetConfig(name, options.getOption(name));
            end
        end

        function AddConfig2List(self, listname,fixedstr, templateparas)
            if ~exist('templateparas', 'var')
                templateparas=[];
            end
            if ~exist('fixedstr', 'var')
                fixedstr=[];
            end
            if ~isempty(templateparas)&&ischar(templateparas)
                templateparas= strsplit(templateparas, '|');
            end
            str = fixedstr;
            for k=1:numel(templateparas)
                para = templateparas{k};
                str.(para)=['[' para ']'];
            end
            self.AddStruct2List_noID(str, listname);
        end

        function AddDependency(self, dependency)
            self.AddStruct2List_noID(dependency, 'Dependency');
        end

        function AddMiscProcess(self, listname, optype,  fixedstr, varargin)
            if ~exist('listname', 'var')
                listname = 'Process';
            end
            process = fixedstr;  
            if exist('optype', 'var') && ~isempty(optype)
                process.('OperationType')=optype;
            end
            self.AddConfig2List(listname, process, varargin{:});
        end

        function AddPostTask(self, varargin)
            self.AddMiscProcess('PostTask', varargin{:});
        end

        function AddPreTask(self, varargin)
            self.AddMiscProcess('PreTask', varargin{:});
        end

        function AddProcess(self, varargin)
            self.AddMiscProcess('Process', varargin{:});
        end

        function AddToTasksDefFile(obj, TasksDefFile)
            if exist(TasksDefFile, 'file')
                tasksdef = utils.json.readJson(TasksDefFile);
            end
            name = TaskDefName(obj);
            tasksdef.(name)=[name '.taskdef'];
            %tasksdef.writeJson(TasksDefFile);
            utils.json.writeJson(tasksdef, TasksDefFile);
        end
    end

    methods (Static)
        function [timestr, timeunit]=ConvertDaysToTimeStr(days)
            timestr=''; timeunit='';
            periods = [365 30 7 1 1/24 1/(24*60) 1/(24*60*60)];
            units = {'year', 'month', 'week', 'day', 'hour', 'minute', 'second'};
            for k=1:numel(periods)
                period = periods(k);
                if days>=period
                    timestr = num2str(round(days/period)); 
                    timeunit = units{k};
                    return;
                end
            end
        end

        function res = MR_regexpstr(modality)
            res ='';
            switch modality
                case {'T1', 'T1w'}
                    res = "^(?!.*(POST|GAD|+C|Flair)).*T1.*";
                case {'T1ce', 'T1c', 'T1Gd'}
                    res = "^(?!.*Flair).*(T1.*(POST|GAD|+C))|((POST|GAD|+C).*T1).*";
                case {'T2', 'T2w'}
                    res = "^(?!.*Flair).*T2.*";
                case {'Flair', 'T2Flair'}
                    res ="^(?!.*T1).*Flair.*";
            end                    
        end

        function Filter = MR_SeriesFilter(modality)
            regexpstr = utils.json.TaskDef.MR_regexpstr(modality);
            Filter=struct("FieldName", "SeriesDescription",...
              "MatchMode", "regexpi", ...
              "FieldValue", regexpstr,...
              "Inclusion", true); 
        end
    end
end