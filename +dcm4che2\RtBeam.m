classdef RtBeam < dcm4che2.RtDicomBase   
    properties
        m_MachineConfig; 
    end
    
    methods
        function obj = RtBeam(jInfo, varargin)
            <EMAIL>(jInfo, varargin{:});
        end
        
        function SetMachineConfig(self, machine)
            self.m_MachineConfig=machine;
            T = machine.BeamLimitingDeviceTable();
            if ~isempty(T)
                self.setOption('BeamLimitingDeviceTable', T);
            end
            ControlPointTagReplacement=machine.GetField('ControlPointTagReplacement'); 
            if ~isempty(ControlPointTagReplacement)
                self.setOption('ControlPointTagReplacement', ControlPointTagReplacement); 
            end
        end
        
        function str = GetMachineConstraintsProperty(obj, name)
            str =[];
            if ~isempty(obj.m_MachineConfig)
                str = obj.m_MachineConfig.GetConstraintsField(name); 
            end
        end
        
        function val = BeamNumber(self)
            val = self.GetTagx('BeamNumber', NaN);
        end
        
        function name = BeamIDName(self)
            name = [self.GetTagx('BeamName', '') '-' self.GetTagx('BeamDescription')];
        end
        
        function name = BeamName(self)
            name = [self.GetTagx('BeamName', '')];
        end
        
        function str = BeamType(obj)
            str =GetMachineConstraintsProperty(obj, 'BeamType');
            if ~isempty(str) && ischar(str)
                return
            end
            
            RadiationType  = obj.GetTagxx('RadiationType'); 
            BeanEnergy     = obj.GetTagxx('ControlPointSequence[1]/NominalBeamEnergy'); 
            %DoseRateSet    = obj.GetTagxx('ControlPointSequence[1]/DoseRateSet'); 
            switch upper(RadiationType)
                case {'PHOTON'}
%                     if  DoseRateSet >=650
%                         str = [num2str(BeanEnergy ) 'FFF'];
%                     else
%                         str = [num2str(BeanEnergy ) 'X'];
%                     end
                    str = [num2str(BeanEnergy ) 'X'];
                    flumode = PrimaryFluenceMode(obj);
                    if ~isempty(flumode)&&~strcmpi(flumode, 'STANDARD')
                         str = [num2str(BeanEnergy ) 'FFF'];
                    end

                case {'ELECTRON'}
                    str = [num2str(BeanEnergy ) 'E'];
            end
        end
        
        function name = TreatmentMachineName(obj)
            name = obj.GetTagxx('TreatmentMachineName'); 
        end

        function name = MachineEnergy(obj)
            machname  = TreatmentMachineName(obj); 
            str = BeamType(obj);
            name= [machname '_' str];
        end

        function res = PrimaryFluenceMode(obj)
            try
            res = obj.GetTagxx('PrimaryFluenceModeSequence[1].FluenceMode'); 
            catch
                res = '';
            end
        end

        function val = BeamDose(self)
            refbeam = self.getoptioni('ReferencedBeam');
            val =  self.getTagx(refbeam, 'BeamDose', NaN);
        end
        
        function val = BeamMeterset(self)
            refbeam = self.getoptioni('ReferencedBeam');
            val =  self.getTagx(refbeam, 'BeamMeterset', NaN);
        end
        
        function SetBeamMeterset(self, mu)
            refbeam = self.getoptioni('ReferencedBeam');
            %val =  self.getTagx(refbeam, 'BeamMeterset', NaN);
            dcm4che2.setTag(refbeam,'BeamMeterset', mu);
        end

        function val = BeamDoseSpecificationPoint(self)
            refbeam = self.getoptioni('ReferencedBeam');
            val =  self.getTagx(refbeam, 'BeamDoseSpecificationPoint', NaN(3, 1));
            val = val(:)';
        end
        
         function val = NumberOfFractionsPlanned(self)
            refbeam = self.getoptioni('ReferencedBeam'); 
            val =  self.getTagx(refbeam,  'NumberOfFractionsPlanned', NaN);
            if isnan(val)
                val = self.getoptioni('RtPlan').NumberOfFractionsPlanned();
            end
            val = double(val);
         end
        
         function res = NumberOfControlPoints(self)
            res = numel(self.GetTagx('ControlPointSequence'));
         end

        function flag  = isArcBeam(obj)
            flag = ~strcmpi(obj.GetTagx('ControlPointSequence[1]/GantryRotationDirection'), 'NONE');
        end
        
        function flag  = isTreatmentBeam(obj)
            flag = strcmpi(TreatmentDeliveryType(obj), 'TREATMENT');
        end
        
        function res  = TreatmentDeliveryType(obj)
            res = obj.GetTagx('TreatmentDeliveryType');
        end
        
        function val = Isocenter(self)
            val = self.GetTagx('ControlPointSequence[1]/IsocenterPosition', NaN(3, 1));
            val = val(:)';
        end

        function val = SourceAxisDistance(self)
            val =GetMachineConstraintsProperty(self, 'SourceAxisDistance');
            if ~isempty(val)
                return
            end
            val = self.GetTagx('SourceAxisDistance', NaN);
        end
        
        function T = BeamLimitingDeviceTable(obj)
            T = obj.getoptioni('BeamLimitingDeviceTable');
            if isempty(T)
                try
                    devseq = obj.GetTagx('BeamLimitingDeviceSequence');
                    T = obj.ParseBeamLimitingDevices(devseq);
                    obj.setOption('BeamLimitingDeviceTable', T); 
                catch err
                    disp(err.message);
                end
            end
        end
        
        function names = XJawName(obj)
            T = BeamLimitingDeviceTable(obj);
            devtypes = T.RTBeamLimitingDeviceType;
            I = ismember(devtypes, {'X', 'ASYMX'});
            names=devtypes(I);
        end

        function names = YJawName(obj)
            T = BeamLimitingDeviceTable(obj);
            devtypes = T.RTBeamLimitingDeviceType;
            I = ismember(devtypes, {'Y', 'ASYMY'});
            names=devtypes(I);
        end

        function [names, numleafpairs] = MLCName(obj)
            T = BeamLimitingDeviceTable(obj);
            devtypes = T.RTBeamLimitingDeviceType;
            I = ismember(devtypes, {'MLCX', 'MLCY', 'MLCX1', 'MLCX2'});
            names=devtypes(I);
            numleafpairs=T.NumberOfLeafJawPairs(I);
        end

        function T = ControlPointsTable(obj, varargin)
            T = obj.getoptioni('ControlPointsTable'); 

            if isempty(T) || nargin>1
                options   = OptionsMap(varargin{:});
                %varnames0 = {'ControlPointIndex', 'GantryAngle', 'BeamLimitingDeviceAngle', 'PatientSupportAngle','TableTopPitchAngle', 'TableTopRollAngle', 'CumulativeMetersetWeight'};
                varnames0 = {'CumulativeMetersetWeight'};
                varnames  = options.getoptioni('controlpoints.varnames', varnames0);
                cpseq     = obj.GetTagx('ControlPointSequence');
                T = obj.sequence2Array(cpseq, varnames, 1);
            end
        end

        function SetCumulativeMetersetWeights(obj, val)
            cpseq   = obj.GetTagx('ControlPointSequence');
            tagname = 'CumulativeMetersetWeight';
            if numel(val)==1
                obj.setTag_seq_unique(cpseq, tagname, val);
            else
                obj.setTag_seq(cpseq, tagname, val);
            end
        end
        
        function res = getTagx_MachineConstraints(obj, cp, name, varargin)
            res = GetMachineConstraintsProperty(obj, name);
            if ~isempty(res) 
                return
            end
            res = obj.getTagx(cp, name, varargin{:});
        end
        
        function res = getTagx_seq_MachineConstraints(obj, cpseq, name, varargin)
            val = GetMachineConstraintsProperty(obj, name);
            if ~isempty(val)
                res = repmat(val, size(cpseq)); 
                return
            end
            res = obj.getTagx_seq(cpseq, name, varargin{:});
        end
        
        function motion = toSourceMotion(obj)
            motion = wtkapp.SourceMotion;
            motion.m_LinearMeasureUnit='mm';
            %T = obj.ControlPointsTable;
            cpseq     = obj.GetTagx('ControlPointSequence');
            numprojs  = numel(cpseq); 
            
            motion.SetNumberProjections(numprojs);
%              res = {'ISOCENTER_X',	'ISOCENTER_Y',	'ISOCENTER_Z',	...
%                 	'TABLE_ANGLE',	'TABLE_ROLL_ANGLE',	'TABLE_PITCH_ANGLE', ...
%                     'GANTRY_ANGLE','COLLIMATOR_ANGLE',	'HEAD_PITCH_ANGLE', ...
%                     'JAW_X1', 	'JAW_X2',		'JAW_Y1', 		'JAW_Y2',		'MONITOR_UNIT'};
            motion.SetTreatmentBeamModel(obj.MachineEnergy);
            motion.SetSAD(obj.SourceAxisDistance);
            motion.SetIsocenter(obj.Isocenter);
            names0 = {'Gantry', 'BeamLimitingDevice', 'PatientSupport', 'TableTopEccentric', 'TableTopPitch', 'TableTopRoll'};
            %names1 = {'GANTRY_ANGLE','COLLIMATOR_ANGLE','HEAD_PITCH_ANGLE', 'TABLE_ANGLE',	'TABLE_ROLL_ANGLE',	'TABLE_PITCH_ANGLE'};
            names1 = {'GANTRY_ANGLE','COLLIMATOR_ANGLE','TABLE_ANGLE','TABLE_PITCH_ANGLE', 'HEAD_PITCH_ANGLE',	'TABLE_ROLL_ANGLE'};
            for k=1:numel(names0)
                name = names0{k};
                dir = obj.getTagx(cpseq{1}, [name 'RotationDirection'], 'NONE');
                if strcmpi(dir, 'NONE')
                    %val = obj.getTagx(cpseq{1}, [name 'Angle'], 0);
                    val = obj.getTagx_MachineConstraints(cpseq{1}, [name 'Angle'], 0);
                    motion.SetParameter_unique(names1{k}, val);
                else
                    %val = obj.getTagx_seq(cpseq, [name 'Angle'], 0);
                    val = obj.getTagx_seq_MachineConstraints(cpseq, [name 'Angle'], 0);
                    motion.SetParameter(1:numprojs, names1{k}, val(:));
                end
            end
            
            cumweight = obj.getTag_seq(cpseq, ['CumulativeMetersetWeight']);
            motion.SetParameter(1:numprojs, 'MONITOR_UNIT', cumweight);
            motion.SetLinearMeasureUnit('cm');

            %motion = wtkapp.SourceMotion_BEV(motion); %Convert2BEV
%             name = 'TABLE_ANGLE'; projs = 1:motion.GetNumberProjections();
%             tableangles = motion.GetParameter(projs, name);
%             tableangles = -tableangles; 
%             motion.SetParameter(projs, name, tableangles(:));
        end
        
        %to do: implement it with dcm4che2 framework
        function [T, res] = getBeamPropertyTable(obj, propNames, varargin)
            options = OptionsMap(varargin{:});
            
            %T = table; 
            if ~exist('propNames', 'var') || isempty(propNames)
                %propNames = {'BeamNumber', 'Energy', 'Isocenter', 'Gantry', 'Couch', 'Collimator', 'NumFraction', 'MU', 'RefPoint', 'BeamDose'};
                propNames = {'BeamNumber','BeamName', 'ID_Name', 'Machine_Energy', 'CP', 'SSD', 'Isocenter', 'Gantry', 'Couch', 'Collimator', 'NumFraction', 'MU', 'RefPoint', 'BeamDose'};
            end
            if ischar(propNames)
                propNames = strsplit(propNames, '|');
            end
            res = struct; 
            for k=1:numel(propNames)
                propName = propNames{k};
                switch propName

                    case 'BeamNumber'
                        res.('BeamNumber')= BeamNumber(obj);
                    case 'BeamName'
                        res.(propName)= BeamName(obj);
 
                    case {'MU', 'BeamMeterset'}
                        res.(propName)= BeamMeterset(obj);  
                    case 'BeamDose'
                        res.('BeamDose')= BeamDose(obj); 
                    case {'RefPoint', 'BeamDoseSpecificationPoint'}
                         res.('RefPoint')=obj.BeamDoseSpecificationPoint;
                    case 'Isocenter'
                        res.('Isocenter')=obj.Isocenter;
                    case {'Gantry', 'GantryAngle'}
                        res.(propName)= obj.GetTagxx('ControlPointSequence[1]/GantryAngle');
                    case {'Couch', 'PatientSupportAngle'}
                        res.(propName) = obj.GetTagxx('ControlPointSequence[1]/PatientSupportAngle');
                    case {'Collimator', 'BeamLimitingDeviceAngle'}
                        res.(propName) = obj.GetTagxx('ControlPointSequence[1]/BeamLimitingDeviceAngle');
    
%                     case 'GantryAngle'
%                          angles = getGantryAngle(obj);
%                          if numel(angles)==1 ||(numel(angles)==2 && angles(2)==angles(1)) 
%                              res.('GantryAngle') = angles(1);
%                          elseif numel(angles)>2
%                               res.('GantryAngle')= angles([1 end]);
%                          end
%                     case {'SSD', 'SourceToSurfaceDistance'}
%                         res.('SSD') = getBeamProperty(obj, 'SourceToSurfaceDistance'); 
%                     case 'PatientPosition'
%                         res.('PatientPosition') = PatientPosition(obj);
                    case {'NumFraction', 'NumberOfFractions'}
                        res.(propName)     = NumberOfFractionsPlanned(obj); 
                    
                    case 'Machine_Energy'
                        res.('Machine_Energy') = obj.MachineEnergy; 

                    case {'BeamType', 'Energy'}
                        res.(propName) = BeamType(obj);
                    
                    case 'ID_Name'
                        res.('ID_Name')   = BeamIDName(obj);
                    
                    case {'X', 'XJaw', 'ASYMX', 'X1', 'X2'}
                        cpseq = obj.GetTagx('ControlPointSequence');
                        cp1   = cpseq{1};
                        try
                            pos   = obj.GetCPLeafJawPositions(cp1, obj.XJawName{1});
                        catch
                            pos = [NaN NaN];
                        end
                        if strcmpi(propName, 'X1')
                            res.(propName) = pos(1);
                        elseif strcmpi(propName, 'X2')
                            res.(propName) = pos(2);
                        else
                            res.(propName) = pos(:)';
                        end
                    
                    case {'YJaw', 'ASYMY', 'Y1', 'Y2'}
                        cpseq = obj.GetTagx('ControlPointSequence');
                        cp1   = cpseq{1};
                        try
                            pos   = obj.GetCPLeafJawPositions(cp1, obj.YJawName{1});
                        catch
                            pos = [NaN NaN];
                        end
                        if strcmpi(propName, 'Y1')
                            res.(propName) = pos(1);
                        elseif strcmpi(propName, 'Y2')
                            res.(propName) = pos(2);
                        else
                            res.(propName) = pos(:)';
                        end
                        
                    case {'CP', 'NumberOfControlPoints'}
                        res.(propName) = obj.GetTagxx('NumberOfControlPoints');
                        
                    case {'SSD', 'SourceToSurfaceDistance'}
                        try
                        res.(propName) = obj.GetTagxx('ControlPointSequence[1]/SourceToSurfaceDistance');  
                        catch
                            res.(propName) = NaN;
                        end
                    otherwise
                        res.(propName) = obj.GetTagxx(propName);
                end
            end
            
            linearunit= options.getoptioni('LinearMeasureUnit', 'cm'); 
            if strcmpi(linearunit, 'cm')
                linprops = {'RefPoint', 'SSD', 'Isocenter','X', 'Y', 'XJaw', 'ASYMX', 'X1', 'X2', 'YJaw', 'ASYMY', 'Y1', 'Y2'}; 
                fns = intersect(fieldnames(res), linprops); 
                for k=1:numel(fns)
                    fn = fns{k}; 
                    res.(fn) = res.(fn)/10; 
                end
            end
            
            T = struct2table(res, 'AsArray', true);
        end
    end
    
    methods (Static)
         function T = ParseBeamLimitingDevices(seq)
            varnames = {'RTBeamLimitingDeviceType', 'NumberOfLeafJawPairs', 'LeafPositionBoundaries'};
            %vartype = {'char', 'double', 'double'};
            ncps = numel(seq); 
            sz      = [ncps, numel(varnames)];
%             T       = table('Size',sz,'VariableTypes',vartype);
%             T.Properties.VariableNames = varnames; 
            T = cell(sz);
            for k=1:numel(seq)
                item = seq{k}; 
                T{k, 1} = dcm4che2.RtDicomBase.getTagx(item, 'RTBeamLimitingDeviceType', '');
                T{k, 2} = dcm4che2.RtDicomBase.getTagx(item, 'NumberOfLeafJawPairs', NaN)';
                T{k, 3} = dcm4che2.RtDicomBase.getTagx(item, 'LeafPositionBoundaries', [])';
            end
            T = cell2table(T, 'VariableNames', varnames); 
         end
        
         function T = ParseBeamLimitingDevicePositionSequence(sequence)
            N = numel(sequence);
            for n=1:N
                subItem = sequence{n};
                type    = dcm4che2.RtDicomBase.getTagx(subItem, 'RTBeamLimitingDeviceType', '');
                T.(type)= dcm4che2.RtDicomBase.getTagx(subItem, 'LeafJawPositions', {})';
            end
         end

         function pos = GetCPLeafJawPositions(cp, devtype)
            try
                leafjawseq  = dcm4che2.getTagx(cp,'BeamLimitingDevicePositionSequence');
                for k=1:numel(leafjawseq)
                    type0 = dcm4che2.getTagx(leafjawseq{k}, 'RTBeamLimitingDeviceType'); 
                    if strcmpi(devtype, type0)
                        pos = dcm4che2.getTag(leafjawseq{k}, 'LeafJawPositions'); 
                        break; 
                    end
                end
            catch
                pos =[]; 
            end
         end

         function SetCPLeafJawPositions(cp, devtype, pos)
            try
                leafjawseq  = dcm4che2.getTagx(cp,'BeamLimitingDevicePositionSequence');
                for k=1:numel(leafjawseq)
                    type0 = dcm4che2.getTagx(leafjawseq{k}, 'RTBeamLimitingDeviceType'); 
                    if strcmpi(devtype, type0)
                        dcm4che2.setTag(leafjawseq{k}, 'LeafJawPositions', pos(:)); 
                        break; 
                    end
                end
            catch
            end
         end
    end
end

