dcmprscu(1)                       OFFIS DCMTK                      dcmprscu(1)



NAME
       dcmprscu - Print spooler for presentation state viewer


SYNOPSIS
       dcmprscu [options] [dcmfile-in...]

DESCRIPTION
       The  dcmprscu  utility  implements  the  DICOM  Basic  Grayscale  Print
       Management Service Class as SCU. It also supports  the  optional  Basic
       Annotation  Box  and  Presentation  LUT  SOP  Classes.  The  utility is
       intended for use within the DICOMscope viewer.

       The dcmprscu utility takes complete print jobs consisting of  a  Stored
       Print object and one or more Hardcopy Grayscale objects and spools them
       to the printer. No attempt is made  to  check  whether  the  attributes
       defined  in  the  Stored  Print  object are supported by the Print SCP.
       However, the print  spooler  will  not  attempt  to  use  the  optional
       Annotation  or  Presentation  LUT services if they are not successfully
       negotiated with the Print SCP.

       The dcmprscu utility  reads  the  characteristics  of  the  printer  to
       communicate   with  from  the  configuration  file.  Depending  on  the
       printer's support for Presentation LUT, any Presentation LUT present in
       the print job will be rendered into the hardcopy images before they are
       spooled to the printer if necessary. If the printer  does  not  support
       image  transmission with 12 bits/pixel (as per the configuration file),
       the grayscale hardcopy images are down-sampled to 8  bits/pixel  before
       transmission to the printer.

       The dcmprscu utility can be run either in 'printer mode', in which case
       the file name of a Stored Print object must be  passed,  or  in  'spool
       mode',  in  which  case  commands  are  read  periodically from a spool
       directory.

PARAMETERS
       dcmfile-in  stored print file(s) to be spooled

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   print options
              --noprint
                do not create print-out (no n-action-rq)

              --session-print
                send film session n-action-rq (instead of film box)

              --monochrome1
                transmit basic grayscale images in MONOCHROME1

   mode options
         +p   --print
                printer mode, print file(s) and terminate (default)

         +s   --spool  [n]ame: string
                spooler mode, use job prefix n

   processing options
         -c   --config  [f]ilename: string
                process using settings from configuration file

         -p   --printer  [n]ame: string (default: 1st printer in cfg file)
                select printer with identifier n from cfg file

         +d   --dump
                dump all DIMSE messages

   spooler options (only with &ndash;spool)
              --sleep  [d]elay: integer (default: 1)
                sleep d seconds between spooler checks

   basic film session options (not with &ndash;spool):
              --copies  [v]alue: integer (1..100, default: 1)
                set number of copies to v

              --medium-type  [v]alue: string
                set medium type to v

              --destination  [v]alue: string
                set film destination to v

              --label  [v]alue: string
                set film session label to v

              --priority  [v]alue: string
                set print priority to v

              --owner  [v]alue: string
                set film session owner ID to v

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  dcmprscu  utility  will  attempt  to  load DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

FILES
       <etcdir>/dcmpstat.cfg,  <etcdir>/printers.cfg  -  sample  configuration
       files

SEE ALSO
       dcmprscp(1)

COPYRIGHT
       Copyright  (C)  1999-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmprscu(1)
