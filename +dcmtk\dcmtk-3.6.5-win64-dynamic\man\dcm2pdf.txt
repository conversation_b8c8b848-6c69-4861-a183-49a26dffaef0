dcm2pdf(1)                        OFFIS DCMTK                       dcm2pdf(1)



NAME
       dcm2pdf - Extract PDF file from DICOM encapsulated PDF


SYNOPSIS
       dcm2pdf [options] dcmfile-in pdffile-out

DESCRIPTION
       The  dcm2pdf utility reads a DICOM file of the Encapsulated PDF Storage
       SOP Class (dcmfile-in), extracts the embedded PDF document  and  writes
       it  to  an  output  file  (pdffile-out).  Optionally  a  command can be
       executed after the creation of the PDF file.

PARAMETERS
       dcmfile-in   DICOM input filename

       pdffile-out  PDF output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

       parsing of odd-length attributes:

         +ao  --accept-odd-length
                accept odd length attributes (default)

         +ae  --assume-even-length
                assume real length is one byte larger

       handling of undefined length UN elements:

         +ui  --enable-cp246
                read undefined len UN as implicit VR (default)

         -ui  --disable-cp246
                read undefined len UN as explicit VR

       handling of defined length UN elements:

         -uc  --retain-un
                retain elements as UN (default)

         +uc  --convert-un
                convert to real VR if known

       automatic data correction:

         +dc  --enable-correction
                enable automatic data correction (default)

         -dc  --disable-correction
                disable automatic data correction

       bitstream format of deflated input:

         +bd  --bitstream-deflated
                expect deflated bitstream (default)

         +bz  --bitstream-zlib
                expect deflated zlib bitstream

   execution options
         -x   --exec  [c]ommand: string
                execute command c after PDF extraction

NOTES
       Option --exec allows for the execution of a certain command line  after
       the  creation  of  the PDF document. The command line to be executed is
       passed to this option as a parameter. The specified  command  line  may
       contain  the  placeholder  '#f',  which  will  be  replaced  by the PDF
       filename at run time. The specified command line  is  executed  in  the
       foreground, i.e. pdf2dcm will be blocked until the command terminates.

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcm2pdf utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       pdf2dcm(1)

COPYRIGHT
       Copyright  (C)  2007-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcm2pdf(1)
