<!-- Document Type Definition for DCMTK tools dcm2xml and xml2dcm.
     Copyright (C) 2002-2013, OFFIS e.V.
     All rights reserved.  See COPYRIGHT file for details. -->

<!ELEMENT file-format ( meta-header, data-set ) >
<!ATTLIST file-format xmlns CDATA #IMPLIED >

<!ELEMENT meta-header ( element+ ) >
<!ATTLIST meta-header xfer NMTOKEN #REQUIRED >
<!ATTLIST meta-header name CDATA #IMPLIED >

<!ELEMENT data-set ( element | sequence )* >
<!ATTLIST data-set xfer NMTOKEN #REQUIRED >
<!ATTLIST data-set name CDATA #IMPLIED >

<!ELEMENT element ( #PCDATA ) >
<!ATTLIST element binary ( yes | no | hidden | base64 | file ) "no" >
<!ATTLIST element len NMTOKEN #IMPLIED >
<!ATTLIST element loaded ( yes | no ) "yes" >
<!ATTLIST element name CDATA #IMPLIED >
<!ATTLIST element tag CDATA #REQUIRED >
<!ATTLIST element vm NMTOKEN #IMPLIED >
<!ATTLIST element vr NMTOKEN #REQUIRED >

<!ELEMENT sequence ( item* | pixel-item* ) >
<!ATTLIST sequence card NMTOKEN #IMPLIED >
<!ATTLIST sequence len NMTOKEN #IMPLIED >
<!ATTLIST sequence name CDATA #IMPLIED >
<!ATTLIST sequence tag CDATA #REQUIRED >
<!ATTLIST sequence vr NMTOKEN #REQUIRED >

<!ELEMENT item ( element | sequence )* >
<!ATTLIST item card NMTOKEN #IMPLIED >
<!ATTLIST item len NMTOKEN #IMPLIED >
<!ATTLIST item offset NMTOKEN #IMPLIED >

<!ELEMENT pixel-item ( #PCDATA ) >
<!ATTLIST pixel-item binary ( yes | hidden | base64 | file ) "yes" >
<!ATTLIST pixel-item len NMTOKEN #IMPLIED >
<!ATTLIST pixel-item loaded ( yes | no ) "yes" >
