function replaceStringInFile(filePath, oldStr, newStr)
    % REPLACESTRING<PERSON>FILE Replaces occurrences of oldStr with newStr in a file.
    % 
    % Usage:
    %   replaceStringInFile('example.txt', 'oldText', 'newText')
    %
    % Inputs:
    %   - filePath: Path to the file
    %   - oldStr: String to be replaced
    %   - newStr: Replacement string

    % Check if file exists
    if ~isfile(filePath)
        error('File "%s" not found.', filePath);
    end
    
    % Read file content
    fid = fopen(filePath, 'r');
    if fid == -1
        error('Could not open file "%s" for reading.', filePath);
    end
    fileContents = fread(fid, '*char')';  % Read as a character array
    fclose(fid);

    % Replace occurrences of oldStr with newStr
    fileContents = strrep(fileContents, oldStr, newStr);

    % Write back to the file
    fid = fopen(filePath, 'w');
    if fid == -1
        error('Could not open file "%s" for writing.', filePath);
    end
    fwrite(fid, fileContents);
    fclose(fid);
    
    fprintf('Replaced occurrences of "%s" with "%s" in file: %s\n', oldStr, newStr, filePath);
end