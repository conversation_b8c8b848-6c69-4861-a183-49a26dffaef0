function DcmServerDaemon(varargin)
    if isdeployed
        warning('off','all');
    end
    %%%%%%%%%%%%%%%%%%%%%%check license%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    daemon.utils.LicenseTool.ValidateArtDaemonLicense('dcmserver');
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    
    % force the computer to remain awake
    try
    dosutils.insomnia('on','verbose');
    catch
    end
    dcmserver = dcmtk.DcmServer(varargin{:});
    dcmserver.RunServer; 
end
