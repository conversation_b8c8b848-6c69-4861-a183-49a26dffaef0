dcmprscp(1)                       OFFIS DCMTK                      dcmprscp(1)



NAME
       dcmprscp - DICOM basic grayscale print management SCP


SYNOPSIS
       dcmprscp [options]

DESCRIPTION
       The  dcmprscp  utility  implements  the  DICOM  Basic  Grayscale  Print
       Management  Service  Class  as  SCP.  It  also  supports  the  optional
       Presentation  LUT SOP Class. The utility is intended for use within the
       DICOMscope viewer.

       The dcmprscp utility accepts print jobs from a  remote  Print  SCU.  It
       does  not  create  real  hardcopies  but stores print jobs in the local
       DICOMscope database as a set of Stored Print objects (one per page) and
       Hardcopy  Grayscale  images  (one  per  film box N-SET). The DICOMscope
       application allows to load a Stored Print object  created  by  dcmprscp
       and  to  render  a screen preview of the hardcopy. The dcmprscp utility
       reads the characteristics of  the  printer  to  be  emulated  from  the
       configuration file.

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

         -l   --logfile
                write a log file (not with --log-config)

                # this option is available for reasons of backward
                # compatibility only

   processing options
         -c  --config  [f]ilename: string
               process using settings from configuration file

         -p  --printer  [n]ame: string (default: 1st printer in config file)
               select printer with identifier n from config file

         +d  --dump
               dump all DIMSE messages

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmprscp utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

FILES
       <etcdir>/dcmpstat.cfg,  <etcdir>/printers.cfg  -  sample  configuration
       files

SEE ALSO
       dcmprscu(1)

COPYRIGHT
       Copyright (C) 1999-2014 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmprscp(1)
