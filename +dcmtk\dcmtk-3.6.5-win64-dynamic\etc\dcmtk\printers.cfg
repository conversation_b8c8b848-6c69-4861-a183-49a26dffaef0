#
#  Copyright (C) 1998-2018, OFFIS e.V.
#  All rights reserved.  See COPYRIGHT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 <PERSON>enburg, Germany
#
#
#  Module:  dcmpstat
#
#  Author:  <PERSON> et al.
#
#  Purpose: This file contains sample configurations for the DICOMscope
#    Print SCP.  Some of the sample configurations attempt to "emulate"
#    as good as possible the capabilities of existing DICOM printers
#    such as AGFA Drystar 3000, Kodak/Imation 8700/9410 etc.
#    The configurations can also be used for the DICOMscope Print SCU.
#
#    This is not a complete configuration file; use the template from
#    dcmpstat.cfg (DICOMscope.cfg in the DICOMscope release) and copy/
#    paste individual printer configurations into that template.
#


# ----------------------------------------------------------------------------
# Print SCP that supports most options of the DICOM Print protocol and
# the IHE technical framework requirements for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - layouts: 1x1, 1x2, 2x2, 2x3, 3x3, 3x4, 3x5, 4x4, 4x5
#   - supports typical defined terms for empty image density, film
#     destination, film size ID, magnification type, medium type, resolution
#     ID, requested decimate/crop behaviour, image size and trim.
# ----------------------------------------------------------------------------

[IHEFULL]
Aetitle = IHEFULL
Description = IHE Full Print SCP
Hostname = localhost
Port = 10005
Type = LOCALPRINTER

BorderDensity = 150\20\BLACK\WHITE
DisableNewVRs = false
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
EmptyImageDensity = 20\BLACK\WHITE
FilmDestination = MAGAZINE\PROCESSOR\BIN_1\BIN_2
FilmSizeID = 8INX10IN\10INX12IN\10INX14IN\11INX14IN\14INX14IN\14INX17IN\24CMX24CM\24CMX30CM
ImplicitOnly  = false
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 32768
MediumType = PAPER\CLEAR FILM\BLUE FILM
MinDensity = 20
OmitSOPClassUIDFromCreateResponse = false
PresentationLUTMatchRequired = false
PresentationLUTinFilmSession = false
ResolutionID = STANDARD\HIGH
SmoothingType = NONE
Supports12Bit = true
SupportsDecimateCrop = true
SupportsImageSize = true
SupportsPresentationLUT = true
SupportsTrim = true

# ----------------------------------------------------------------------------
# Print SCP that only supports the absolute minimum that is required by
# the IHE technical framework for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - supports STANDARD\1,1 layout only
# ----------------------------------------------------------------------------

[IHERESTRICTED]
Aetitle = IHERESTRICTED
Description = IHE Restricted Print SCP
Hostname = localhost
Port = 10006
Type = LOCALPRINTER

DisableNewVRs = true
DisplayFormat=1,1
FilmDestination = STOREDPRINT
FilmSizeID = 8INX10IN\11INX14IN\14INX17IN
ImplicitOnly = true
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 16384
MediumType = STOREDPRINT
OmitSOPClassUIDFromCreateResponse = true
PresentationLUTMatchRequired = true
PresentationLUTinFilmSession = false
Supports12Bit = true
SupportsPresentationLUT = true

# ----------------------------------------------------------------------------
# Print SCP that emulates (as good as possible) an AGFA Drystar 3000
# with Presentation LUT support
# as shown at the RSNA InfoRAD 1999 DICOM Display Consistency Demo
# ----------------------------------------------------------------------------

[AGFA]
hostname = localhost
type = LOCALPRINTER
description = AGFA Drystar 3000
# The original AGFA Drystar 3000 accepts associations on Port 104,
# but we use something different for the simulation.
port = 10008

# This is the AETITLE Agfa uses
aetitle = AGFA_DI3000

# Setting probably doesn't matter anyway
DisableNewVRs = true

# The standard Drystar 3000 (as of 1999) does not support Presentation LUT;
# however, we're working with a non-standard model (as shown at RSNA 1999)
# that does support Presentation LUT. This model, however, requires
# the Presentation LUT to be referenced on Film Session level instead of
# Film Box level.
SupportsPresentationLUT = true
PresentationLUTinFilmSession = true
PresentationLUTMatchRequired = true

# not properly documented in conformance statement, but seems to work
SupportsImageSize = true

Supports12Bit = true
SupportsDecimateCrop = false
SupportsTrim = true

# valid image display formats for 14INX17IN
DisplayFormat=1,1\2,1\1,2\2,2\3,2\2,3\3,3\4,3\5,3\3,4\4,4\5,4\6,4\3,5\4,5\5,5\6,5\4,6\5,6

# depending on dispenser contents
FilmSizeID = 8INX10IN\11INX14IN\14INX14IN\14INX17IN
MediumType = PAPER\CLEAR FILM\BLUE FILM

MagnificationType = REPLICATE\BILINEAR\CUBIC

# The Drystar accepts any value between 0 and 140 for smoothing type,
# but our emulation only supports "0" and "140"
#   "0" - Unsharp smoothing
# "140" - Sharp (Hi-resolution) smoothing
SmoothingType = 0\140

# The Drystar accepts all OD values for density,
# but our emulation only supports the following choice
BorderDensity = BLACK\WHITE\150
EmptyImageDensity = BLACK\WHITE\150
MaxDensity = 320\310\300\290\280\270
MinDensity = 20\25\30\35\40\45\50

# The Drystar supports annotation, but only prints the first 32 characters
# of the annotation text. THE SCP EMULATION DOES NOT SUPPORT ANNOTATION!
Annotation = 2\ANNOTATION

# AGFA uses lots of configuration information strings...
# Multiple values can be sent, separated by '\'.

# These ones select a "Perception LUT" - nice for printers that don't support Presentation LUT.
Configuration_1 = PERCEPTION_LUT=LINEAR
Configuration_2 = PERCEPTION_LUT=KANAMORI

# Both Annotation and File can range [1..6]
# Allows to print a TIFF image that must be pre-loaded in the printer
# into one of six pre-defined locations.
Configuration_3 = ANNOTATION1=FILE1

# Same as above, but the Patient ID is printed. The Drystar 3000 allows
# (0010,0020) PatientID to be present in Basic Grayscale Image Box N-SETs
Configuration_4 = ANNOTATION1=PATID

# allows to define a VOI Window for Basic Print (whatever that is good for...)
# Window width and center must be "in range", whatever that means.
Configuration_5 = WINDOW_WIDTH=256\WINDOW_CENTER=128


# ----------------------------------------------------------------------------
# Print SCP that emulates (as good as possible) a Kodak Imation 8700/9410
# with Presentation LUT support
# as shown at the RSNA InfoRAD 1999 DICOM Display Consistency Demo
# ----------------------------------------------------------------------------

[KODAK]
hostname = localhost
type = LOCALPRINTER
description = Kodak 8700/9410
# The original printer accepted associations on Port 1024,
# but we use something different for the simulation.
port = 10009
aetitle = IMN_9410

# Setting probably doesn't matter anyway
DisableNewVRs = true

# The standard Kodak 8700/9410 (as of 1999) does not support Presentation LUT;
# however, we're working with a non-standard model (as shown at RSNA 1999)
# that does support Presentation LUT.
SupportsPresentationLUT = true
PresentationLUTinFilmSession = false
PresentationLUTMatchRequired = true

Supports12Bit = true
SupportsImageSize = true
SupportsDecimateCrop = false
SupportsTrim = true

# This list of values is incomplete but should do for testing purposes
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
FilmSizeID = 8INX10IN\11INX14IN\14INX17IN\CURRENT
MediumType = CLEAR FILM\BLUE FILM\CURRENT
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
SmoothingType = 0\1\2\3\4\5\6\7\8\9\10\11\12\13\14\15
BorderDensity = BLACK\WHITE
MaxDensity = 320\310\300\290\280\270
MinDensity = 20\25\30\35\40\45\50

# The Kodak printer supports annotation. THE SCP EMULATION DOES NOT!
Annotation = 0\LABEL

# Configuration Information can be used to select a
# Perception LUT Curve Shape 0..15
Configuration_1 = CS000
Configuration_2 = CS001
Configuration_3 = CS002
Configuration_4 = CS003
Configuration_5 = CS004
Configuration_6 = CS005
Configuration_7 = CS006
Configuration_8 = CS007
Configuration_9 = CS008
Configuration_10= CS009
Configuration_11= CS010
Configuration_12= CS011
Configuration_13= CS012
Configuration_14= CS013
Configuration_15= CS014
Configuration_16= CS015


# ----------------------------------------------------------------------------
# Print SCP that emulates (as good as possible) a Merge APS with
# Presentation LUT support and attached Fuji camera
# as shown at the RSNA InfoRAD 1999 DICOM Display Consistency Demo
# ----------------------------------------------------------------------------

[MERGE_APS]
hostname = localhost
type = LOCALPRINTER
description = MERGE_APS
# The original printer accepted associations on Port 1114,
# but we use something different for the simulation.
port = 10010
aetitle = MERGE_APS
Supports12Bit = true

# Setting probably doesn't matter anyway
DisableNewVRs = true

# The standard Merge APS (as of 1999) does not support Presentation LUT;
# however, we're working with a non-standard model (as shown at RSNA 1999)
# that does support Presentation LUT.
SupportsPresentationLUT = true

# This list of values is incomplete but should do for testing purposes
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5


# ----------------------------------------------------------------------------
# Print SCP that emulates (as good as possible) an AYCAN DICOM paper printer
# with limited presentation LUT support (IDENTITY only)
# as shown at the RSNA InfoRAD 1999 DICOM Display Consistency Demo
# ----------------------------------------------------------------------------
[AYCAN]

hostname = localhost
type = LOCALPRINTER
description = Aycan DICOM Print Server
# The original printer accepted associations on Port 2137,
# (a value that is configurable anyway)
# but we use something different for the simulation.
port = 10011
aetitle = AYCAN

# Setting probably doesn't matter anyway
DisableNewVRs = true

# we're working with a non-standard model that supports P-LUT.
# The standard AYCAN DICOM paper printer (as of 1999) does not support
# Presentation LUT; however, we're working with a non-standard model
# (as shown at RSNA 1999) that does support Presentation LUT.
SupportsPresentationLUT = true
PresentationLUTinFilmSession = false
PresentationLUTMatchRequired = true

Supports12Bit = true
SupportsImageSize = false
SupportsDecimateCrop = false
SupportsTrim = false

# The Aycan printer supports all layouts with rows <= 10 and columns <= 10
# We define only the most reasonable combinations
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5\5,6\8,10\10,10

FilmSizeID = 8INX10IN\10INX12IN
MediumType = PAPER
MagnificationType = REPLICATE
BorderDensity = WHITE
EmptyImageDensity = WHITE

# The Aycan printer uses the film session label for annotation
SessionLabelAnnotation = true

# the config info string must begin with "aycan print" followed by one or more
# settings separated by space characters. No space characters in strings allowed, use '_'
# "G="  gamma correction, value must be [0..2]
# "Ix=" print string (max 64 chars) in 1st/2nd/3rd line on top left corner
# "Dx=" print string (max 64 chars) in 1st/2nd/3rd line on top right corner
# "C="  PostScript compression; 0 = no compression, 1 = level 1 compression (default)
# "TI=" select input papertray; 99=manual feed; default: 0
# "TO=" select input papertray; default: 0
# "AYCAN=" is used for communication with aycan DICOM package.
Configuration_1 = aycan print I1=RSNA_1999_Display_Consistency_Demo I2=DICOMscope_2.0
Configuration_2 = aycan print G=0.5
Configuration_3 = aycan print G=2.0
Configuration_4 = aycan print TI=99


# ----------------------------------------------------------------------------
# Print SCP that supports most options of the DICOM Print protocol and
# the IHE technical framework requirements for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - layouts: 1x1, 1x2, 2x2, 2x3, 3x3, 3x4, 3x5, 4x4, 4x5
#   - supports typical defined terms for empty image density, film
#     destination, film size ID, magnification type, medium type, resolution
#     ID, requested decimate/crop behaviour, image size and trim.
# This Print SCP communicates over TLS and requires peer authentication.
# ----------------------------------------------------------------------------

[IHEFULL_TLS]
Aetitle = IHEFULL_TLS
Description = IHE Full Print SCP
Hostname = localhost
# in the IHE Year 2 MESA release, this printer listened on port 10005
Port = 10012
Type = LOCALPRINTER

BorderDensity = 150\20\BLACK\WHITE
DisableNewVRs = false
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
EmptyImageDensity = 20\BLACK\WHITE
FilmDestination = MAGAZINE\PROCESSOR\BIN_1\BIN_2
FilmSizeID = 8INX10IN\10INX12IN\10INX14IN\11INX14IN\14INX14IN\14INX17IN\24CMX24CM\24CMX30CM
ImplicitOnly  = false
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 32768
MediumType = PAPER\CLEAR FILM\BLUE FILM
MinDensity = 20
OmitSOPClassUIDFromCreateResponse = false
PresentationLUTMatchRequired = false
PresentationLUTinFilmSession = false
ResolutionID = STANDARD\HIGH
SmoothingType = NONE
Supports12Bit = true
SupportsDecimateCrop = true
SupportsImageSize = true
SupportsPresentationLUT = true
SupportsTrim = true

# TLS settings
UseTLS = true
TLSProfile = BCP195
RandomSeed = iheprt1.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem


# ----------------------------------------------------------------------------
# Print SCP that only supports the absolute minimum that is required by
# the IHE technical framework for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - supports STANDARD\1,1 layout only
# This Print SCP communicates over TLS and requires peer authentication.
# ----------------------------------------------------------------------------

[IHERESTRICT_TLS]
Aetitle = IHERESTRICT_TLS
Description = IHE Restricted Print SCP
Hostname = localhost
Port = 10013
Type = LOCALPRINTER

DisableNewVRs = true
DisplayFormat=1,1
FilmDestination = STOREDPRINT
FilmSizeID = 8INX10IN\11INX14IN\14INX17IN
ImplicitOnly = true
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 16384
MediumType = STOREDPRINT
OmitSOPClassUIDFromCreateResponse = true
PresentationLUTMatchRequired = true
PresentationLUTinFilmSession = false
Supports12Bit = true
SupportsPresentationLUT = true

# TLS settings
UseTLS = true
TLSProfile = BCP195
RandomSeed = iheprt2.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem


# ----------------------------------------------------------------------------
# Print SCU configuration for communication with OFFIS DCMPRINT print server
# as of 2003-08-27 (3.5.2 plus extensions)
# ----------------------------------------------------------------------------

[DCMPRINT]
Aetitle = PS Print Server
Description = DCMPRINT Print SCP at localhost
Hostname = localhost
Port = 3100
Type = PRINTER
DisableNewVRs = false

ImplicitOnly = false
FilmDestination = MAGAZINE
MagnificationType = BILINEAR
MediumType = PAPER

# DCMPRINT supports any format, this is just an arbitrary choice
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5

# film sizes can be configured in DCMPRINT
FilmSizeID = A4\8INX10IN\11INX14IN\14INX17IN

# max density depends on the characteristic curve of the printer
# which can be configured in DCMPRINT
MaxDensity = 300

MaxPDU = 32768
PresentationLUTMatchRequired = true
PresentationLUTPreferSCPRendering = true
PresentationLUTinFilmSession = false
Supports12Bit = true
SupportsPresentationLUT = true
