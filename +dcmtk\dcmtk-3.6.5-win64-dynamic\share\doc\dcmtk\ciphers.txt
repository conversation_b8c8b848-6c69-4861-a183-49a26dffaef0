
============================
CIPHERSUITE SUPPORT IN DCMTK
============================

When compiled with OpenSSL support, several of the DCMTK network
applications optionally support TLS encapsulated secure DICOM communication
as defined in the DICOM Part 15. TLS (Transport Layer Security) can be
used with different so-called "ciphersuites". Each ciphersuite defines

  - the algorithm to be used for the authentication of peers,
  - the algorithm to be used for key-exchange of session keys,
  - the algorithm to be used for bulk data encryption, and
  - the algorithm to be used for secure hash (message digest).

The ciphersuites to be used for secure DICOM communication can be
controlled in the TLS-enabled DCMTK tools, e.g. with appropriate command
line parameters. The ciphersuite name constants known to DCMTK are
exactly as defined in the RFCs that define the TLS protocol and its
various extensions.

The TLS protocol allows a dynamic negotiation of the ciphersuite to be used.
For this purpose, the TLS-enabled DCMTK tools allow that multiple
ciphersuites be specified. During negotiation, the TLS client proposes
a list of ciphersuites that it supports, and the TLS server selects
from this list. Many systems will select the first entry from the list
that is also know to the server (thus respecting the prioritization
of ciphersuites by the client), but servers may also select based
on their own priorities, e.g. by always selecting the most secure ciphersuite.

In DCMTK, the selection of ciphersuites is based on "TLS profiles", which
follow the secure profiles define in DICOM Part 15:

- BCP 195 TLS Profile (default)
  This profile proposes the following set of ciphersuites:
  - TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_RSA_WITH_AES_128_CBC_SHA
  - TLS_RSA_WITH_3DES_EDE_CBC_SHA

  The first four ciphersuites are modern, state-of the art ciphersuites
  but only work with TLS version 1.2 (or newer). The other two ciphersuites
  offer backward compatibility with older applications implementing the
  AES TLS Secure Transport Connection Profile or the Basic TLS Secure
  Transport Connection Profile (e.g. DCMTK versions older than 3.6.4) and
  can be used with TLS version 1.0 (or newer).

- Non-downgrading BCP 195 TLS Profile
  This profile proposes the following set of ciphersuites:
  - TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)

  Furthermore, when this profile is active, only TLS version 1.2
  or newer will be negotiated.

- Extended BCP 195 TLS Profile
  This profile proposes the following set of ciphersuites:
  - TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)
  - TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 (only TLS 1.2)
  - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256 (only TLS 1.2)

  Furthermore, when this profile is active, only TLS version 1.2
  will be negotiated. In particular, TLS 1.3 will be disabled.

- Basic TLS Secure Transport Connection Profile (retired)
  This profile only proposes a single ciphersuite for use
  with TLS 1.0 or newer:
  - TLS_RSA_WITH_3DES_EDE_CBC_SHA

  When this profile is active, only TLS versions up to 1.2
  will be negotiated. TLS 1.3 will be disabled.

- AES TLS Secure Transport Connection Profile (retired)
  This profile proposes the following set of ciphersuites
  for use with TLS 1.0 or newer:
  - TLS_RSA_WITH_AES_128_CBC_SHA
  - TLS_RSA_WITH_3DES_EDE_CBC_SHA

  When this profile is active, only TLS versions up to 1.2
  will be negotiated. TLS 1.3 will be disabled.

- Authenticated unencrypted communication (retired)
  This profile proposes a single ciphersuite that offers peer
  authentication and integrity protection, but no encryption.
  This profile should only be used in secure network environments.
  This profile was defined in older versions of the IHE ATNA
  (Audit Trail and Node Authentication) integration profile,
  but has been retired.

  When this profile is active, only TLS versions up to 1.2
  will be negotiated. TLS 1.3 will be disabled.

Furthermore, on API level it is also possible to start without
a profile and directly define the set of supported ciphersuites,
but for security reasons this option has not been made available
in the command line tools.

The list of ciphersuites defined by a profile can be extended by
addition additional profiles from the list of supported ciphersuites
(see below). The only exception is the unencrypted ciphersuite
TLS_RSA_WITH_NULL_SHA: For security reasons, this ciphersuite cannot be
added to one of the secure profiles other than "Authenticated
unencrypted communication".

The list of ciphersuites known to DCMTK is shown at the end
of this document. The list of ciphersuites that is actually supported
may also depend on the OpenSSL library with which DCMTK is compiled:

- The CHACHA20 ciphers are only supported starting from OpenSSL 1.1.0

- The 3DES cipher (TLS_RSA_WITH_3DES_EDE_CBC_SHA) is not available
  in OpenSSL 1.1.0 or newer, unless OpenSSL has been configured
  with "Configure enable-weak-ssl-ciphers <os/compiler>".
  In cases where 3DES is not available, the Basic TLS Secure Transport
  Connection Profile cannot be used and the AES TLS Secure Transport
  Connection Profile will not offer backwards compatibility with the
  Basic TLS profile.

OpenSSL versions older than 1.0.1 are not supported anymore, and
we recommend the use of OpenSSL 1.0.2 or newer since some optional
features recommended by RFC 7525 (BCP 195) are only available starting
with this release.


==============================
NOTES ON CIPHERSUITE SELECTION
==============================

1. Unencrypted Ciphersuites

  TLS defines several "unencrypted" ciphersuites, of which only
  TLS_RSA_WITH_NULL_SHA is also supported by DCMTK.
  This ciphersuite does not provide any confidentiality during data
  transmission - all data is transferred in unencrypted plain text
  and can be read by any man-in-the-middle. It does, however, provide
  peer entity authentication and integrity: it prevents
  any unauthorized modification of data during transfer and
  identifies the communicating entities.  The use of this ciphersuite
  MUST be avoided if confidential (e.g. patient) data is transmitted
  over public networks.

2. Anonymous Ciphersuites

  TLS defines a number of ciphersuites which use anonymous Diffie-Hellman
  key exchange without signatures. These ciphersuites can be identified by a
  name starting with "TLS_DH_anon_".  They do not permit an authentication
  of the communicating entities and, therefore, are susceptible to
  man-in-the-middle attacks. DCMTK does not support these ciphersuites.

3. Ciphersuites with Key Length Restrictions

  DCMTK only supports TLS ciphersuite with at least 128 bits of security
  for the symmetric block cipher. Such ciphers can currently be considered
  secure (not breakable by "brute force attacks").

  There are two exceptions, however: The unencrypted ciphersuite
  (TLS_RSA_WITH_NULL_SHA, see above), and the Triple-DES ciphersuite
  (TLS_RSA_WITH_3DES_EDE_CBC_SHA), which is only supported for backwards
  compatibility with older implementations only supporting the
  Basic TLS Secure Transport Connection Profile. While Triple-DES
  has 168 bits key length, the effective security is only 112 bits,
  which is below the threshold chosen for other ciphers.
  Users who want to ensure at least 128 bits of security should use
  the Non-downgrading BCP 195 TLS Profile.

4. Forward Secrecy Ciphersuites

  TLS defines a number of ciphersuites which implement a security feature
  called "forward secrecy", a technique of ensuring that the communicated
  data are only decipherable for a limited time span by the communicating
  parties. After that time the communicating parties typically achieve
  forward secrecy by destroying cryptographic keys. This prevents an
  attacker from coercing the communicating parties into decrypting old
  ciphertext.  The forward secrecy ciphersuites can be identified by
  the name starting with "TLS_DHE_" or "TLS_ECDHE_".

  Forward secrecy makes it harder for attackers to compromise confidential
  information in transit in encrypted form, and so is more secure than the
  use of non-forward secret cipher suites.  However, some national
  legislations prohibit the use of forward secrecy in terms of non
  recoverable encryption.

  In general, forward secrecy ciphersuites should be preferred.


===================================
TLS Ciphersuites Supported by DCMTK
===================================

The following list shows all TLS ciphersuites currently supported by DCMTK.
The list of ciphersuites actually available in a DCMTK-based application
may be smaller depending on the version of OpenSSL used and the way
OpenSSL has been compiled (see above for details).

  - TLS_RSA_WITH_NULL_SHA
  - TLS_RSA_WITH_3DES_EDE_CBC_SHA
  - TLS_RSA_WITH_AES_128_GCM_SHA256
  - TLS_RSA_WITH_AES_128_CBC_SHA
  - TLS_RSA_WITH_AES_128_CBC_SHA256
  - TLS_RSA_WITH_AES_256_GCM_SHA384
  - TLS_RSA_WITH_AES_256_CBC_SHA
  - TLS_RSA_WITH_AES_256_CBC_SHA256
  - TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
  - TLS_RSA_WITH_CAMELLIA_256_CBC_SHA
  - TLS_DHE_DSS_WITH_AES_128_GCM_SHA256
  - TLS_DHE_DSS_WITH_AES_128_CBC_SHA
  - TLS_DHE_DSS_WITH_AES_128_CBC_SHA256
  - TLS_DHE_DSS_WITH_AES_256_GCM_SHA384
  - TLS_DHE_DSS_WITH_AES_256_CBC_SHA
  - TLS_DHE_DSS_WITH_AES_256_CBC_SHA256
  - TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA
  - TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA
  - TLS_DHE_RSA_WITH_AES_128_GCM_SHA256
  - TLS_DHE_RSA_WITH_AES_128_CBC_SHA
  - TLS_DHE_RSA_WITH_AES_128_CBC_SHA256
  - TLS_DHE_RSA_WITH_AES_256_GCM_SHA384
  - TLS_DHE_RSA_WITH_AES_256_CBC_SHA
  - TLS_DHE_RSA_WITH_AES_256_CBC_SHA256
  - TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA
  - TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA
  - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
  - TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA
  - TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256
  - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
  - TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA
  - TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384
  - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
  - TLS_ECDH_RSA_WITH_AES_128_CBC_SHA
  - TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256
  - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
  - TLS_ECDH_RSA_WITH_AES_256_CBC_SHA
  - TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384
  - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
  - TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA
  - TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256
  - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
  - TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA
  - TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
  - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA
  - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
  - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA
  - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384
  - TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256
  - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256
  - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256
