classdef DBFile <OptionsWithLog
     
    properties       
        m_DBFileName; 
    end
    
    methods
        function obj = DBFile(fname,varargin)
           obj       = obj@OptionsWithLog('dbfile', varargin{:}); 
           obj.m_DBFileName = fname; 
        end
        
        function CreateFromXls(self, xlsfile, varargin)
            options   = OptionsMap(varargin{:}); 
            xlssheetnames = options.getoptioni('xls.sheets');
            
            if isempty(xlssheetnames)
                xlssheetnames = sheetnames(xlsfile);
            end
            
            dbtablenames = upper(options.getoptioni('db.tablenames')); 
            if isempty(dbtablenames)
                dbtablenames = xlssheetnames; 
            end
            primarykeys = options.getoptioni('primarykeys');
            numtables = numel(xlssheetnames);
            
            if isempty(primarykeys)
                primarykeys = cell(1, numtables); 
            end
           
            for k=1:numtables 
                name = xlssheetnames{k}; 
                T    = readtable(xlsfile, 'sheet', name);
                varnames = T.Properties.VariableNames;
                primarykey = primarykeys{k};
                if ~isempty(primarykey)
                    I = ismember(varnames, primarykey);
                    varnames = varnames([find(I) find(~I)]);
                end
                tablename = dbtablenames{k};
                self.CreateTextTable(tablename, varnames);
            end
        end
        
        function ImportXls(self,xlsfile, varargin)
            options   = OptionsMap(varargin{:}); 
            xlssheetnames = options.getoptioni('xls.sheets');
            
            if isempty(xlssheetnames)
                xlssheetnames = sheetnames(xlsfile);
            end
            
            dbtablenames = upper(options.getoptioni('db.tablenames')); 
            if isempty(dbtablenames)
                dbtablenames = xlssheetnames; 
            end
            numtables = numel(xlssheetnames);
            db = mydb.sqlite.DataBase(self.m_DBFileName);
            for k=1:numtables 
                try
                tablename = dbtablenames{k};
                %T = readtable(xlsfile, 'sheet', xlssheetnames{k});
                %T = xls.readtable_str(xlsfile, 'sheet', xlssheetnames{k});
                T = xls.TableBase.ReadTable(xlsfile, {'table.sheetname', xlssheetnames{k}});
                if isempty(T)
                    continue;
                end
                db.InsertMatlabTable(T, tablename);
                catch err
                    self.LogInfo(['error:' xlsfile ' '  tablename]);
                    self.LogErr(err);
                end
            end
            db.CloseDB();
        end
        
        function ExportXls(self, xlsfile, dbtablenames)
            db = mydb.sqlite.DataBase(self.m_DBFileName);
            if ~exist('dbtablenames', 'var') ||isempty(dbtablenames)
                dbtablenames = db.ListTables();
            end
            if ischar(dbtablenames)
                dbtablenames = strsplit(dbtablenames, '|');
            end
            numtables = numel(dbtablenames);
            for k=1:numtables 
                tablename = dbtablenames{k};
                T = db.QueryDBTable(tablename);
                if ~isempty(T)
                    writetable(T, xlsfile, 'sheet', tablename);
                end
            end
            db.CloseDB();
        end
        
        function CreateTextTable(self, tablename, varnames, varargin)
           options = OptionsMap(varargin{:});
           schemaname = options.getoptioni('schema-name', 'main');
           PRIMARY_KEY= options.getoptioni('PRIMARY_KEY', varnames{1});
           conn = mksqlite(0, 'open', self.m_DBFileName);
           str   = sprintf('CREATE  TABLE  IF NOT EXISTS "%s"."%s" (', schemaname, tablename);
           N = numel(varnames);
           for k=1:N
                name = varnames{k};
                if strcmpi(name, PRIMARY_KEY)
                    TEXT = 'TEXT PRIMARY KEY  NOT NULL  UNIQUE';
                else
                    TEXT = 'TEXT'; 
                end
                if k<N
                    str1 = [' "' name '" ' TEXT ',']; 
                else
                    str1 = [' "' name '" ' TEXT ')']; 
                end
                str = cat(2, str, str1); 
            end
            mksqlite(conn, str); 
            %mksqlite(conn, 'COMMIT');
            mksqlite(conn, 'close');
        end
    end
    
    methods (Static)
        function Xls2Sqlite(xlsfile, sqlitefile, varargin)
%             xlsfile     =  'E:\ARTDaemon\GKAnon\DataBase\plandb\RTPlanDB.xlsx'; 
%             xlssheets   =  {'RTPlanDB'};
%             primarykeys =  {'SOPInstanceUID'};
%             dbfilename  = 'E:\ARTDaemon\GKAnon\DataBase\plandb\RTPlanDB.sqlite'; 
            dbf         = mydb.sqlite.DBFile(sqlitefile); 
            dbf.CreateFromXls(xlsfile, varargin{:});
            dbf.ImportXls(xlsfile, varargin{:});
        end
        
        function Sqlite2Xls(sqlitefile, xlsfile,  varargin)
            dbf         = mydb.sqlite.DBFile(sqlitefile); 
            dbf.ExportXls(xlsfile, varargin{:});
        end
    end
end

