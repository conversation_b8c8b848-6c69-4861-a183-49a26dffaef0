classdef DcmConverter < OptionsWithLog 
    properties
        
    end
    
    methods
        function obj = DcmConverter(varargin)
            options = OptionsMap(); 
            options.setOption('outputimage.linearmeasureunit', 'mm');
            options.setOption('outputimage.ext', '.nii.gz');
            options.setOption('DcmDataConfigFileName', 'dcmdata.cfg');  
            options.setOption('convertimage.enforcenonnegativity', 1);
            options.setOption('convertrs.labelimage.name', 'targetoarmaskimage');
            options.setOption('convertrs.labelimage.type', 'labelmask');
            obj@OptionsWithLog('DcmConverter', options, varargin{:}); 
        end
        
        function res   = DcmRootFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER');
            if ~isempty(res) && ~DosUtil.isabsolutepath(res)
                res = DosUtil.SimplifyPath([pwd '/' res]); 
                %res = DosUtil.absolutepath(res, pwd);
                self.setOption('DCMSERVER_FOLDER', res);
            end
        end
        
        function res = DcmPatientFolder(self)
            patid    = self.getoptioni('PatientID');
            res = [DcmRootFolder(self) patid filesep];
        end
        
        function rtpat = RTPatientTables(self)
            rtpat = self.getoptioni('RTPatientTables');
            if isempty(rtpat)
                rtpat = LoadRTPatientTables(self);
            end
        end
        
        function rtpat = LoadRTPatientTables(self)
            patid = self.getoptioni('PatientID'); 
            db = self.getoptioni('DcmDataBase'); 
            if ~isempty(db)
                rtpat = mydb.sqlite.RTPatientTables(db, {'DCMSERVER_FOLDER', self.DcmRootFolder}, {'PatientID', patid});
            else
                rtpat = dcmtk.RTPatientTables({'DCMSERVER_FOLDER', self.DcmRootFolder}, {'PatientID', patid});
            end
            self.setOption('RTPatientTables', rtpat); 
        end
        
        function res = OutputRootFolder(self)
            res = self.getoptioni('output.rootfolder'); 
        end
        
        function res = OutputPatientFolder(self)
            patid    = PatientID(self);
            res = DosUtil.mksubdir(OutputRootFolder(self),  patid);
        end
        
        function patid = PatientID(self)
            patid = self.getoptioni('PatientID');
        end
                
        function res   = OutputImageExt(self)
            res = self.getoptioni('outputimage.ext'); 
        end

        function SetPatientID(self, patid)
            self.setOption('PatientID', patid);
            self.LoadRTPatientTables();
        end
        
        function res = DcmDataConfigFileName(self)
            res = self.getoptioni('DcmDataConfigFileName'); 
        end
        
        function [cfgfile, cfg] = ConvertPlan(self, uid, uidtype)
           
        end
        
        function nomen = Nomenclature(self)
            nomen = self.getoptioni('Nomenclature');
        end

        function rsfilter = RtStructFilter(self)
            str = 'ConvertRS.rois.filter';
            if self.isOption(str)
                rsfilter = self.getOption(str);
            else
                rsfilterfile = self.getoptioni_char('ConvertRS.rois.filter.json');
                if ~isempty(rsfilterfile)
                    rsfilter = xls.TableFilter({'FilterFileName', rsfilterfile}); 
                else
                    rsfilter = [];
                end
                self.setOption(str, rsfilter);
            end
        end

        function [cfgfile, cfg] = ConvertRS(self, rsuid)
            rtpat = RTPatientTables(self);

            T = rtpat.ExtendRTStructTable({'SelectedRTStructUID', rsuid});
            
            [~, loc] = ismember(rsuid, T.SOPInstanceUID);

            if loc<=0
                return; 
            end
            
            imageuid    = T.ReferencedImageSeriesUID{loc};

            [imagefolder, modality] = rtpat.GetDcmImageFolder(imageuid);
            
            outimgfolder    = DosUtil.mksubdir(OutputPatientFolder(self), [modality '.' imageuid]);

            outimageext = OutputImageExt(self); 

            outimgfile = [outimgfolder 'image' outimageext ];
            
%             if ~exist(outimgfile, 'file')
%                 ConvertImage(self, imageuid);   
%             end
            
            img = VolHeaderImage.SafeLoad( outimgfile);

            img.setLinearMeasureUnit('cm');

            rsfile= T.RSFile{loc};
     
            rs    = RtStruct2(rsfile, img);

            rsfilter = RtStructFilter(self);

            if ~isempty(rsfilter)
                roinames = rs.ROINames; 
                rois = cellfun(@(x)(rs.rois(x)), roinames, 'uniformoutput',false);
                I    = rsfilter.FilterCell(rois);
                rs.SelectSubROIs(roinames(I));
            end

            nomen = Nomenclature(self);

            if ~isempty(nomen)
                nomen.MapRtStruct(rs);
            end

            %roimask = ROIMaskImage(VolHeader(img));
            maxindex = rs.MaxROIIndex; 
            if maxindex<64
                roimask = ROIMaskImage(VolHeader(img));
                roimask.imageType = 'roimask';
            else
                roimask = ROIMask8Image(VolHeader(img));
            end

            roimask.fromRTStruct(rs);
            
            roimask.setLinearMeasureUnit('mm');

            roimask.ROIStatTable({'ROICenter', 'ROIVolume', 'BoundingBox'}, [],{'modifymetadata', 1});

            outfolder      = DosUtil.mksubdir(OutputPatientFolder(self), ['RS.' rsuid]);

            %outroimaskfile = [outfolder 'oarmaskimage'];
            outroimaskfile  = [outfolder 'roimask'];
            
            imgcfgfile  = [outimgfolder DcmDataConfigFileName(self)];
% 
            cfgfile     = [outfolder DcmDataConfigFileName(self)];
            
            cfg = dcmtk.DcmDataConfig(cfgfile); 
            
            cfg.SetConfig_file('image',    outimgfile, {'filetype',  'VolHeaderImage'}, {'imagetype', modality});
            
            cfg.SetConfig_file('roimask',    [outroimaskfile outimageext], {'filetype',  'ROIMaskImage'}, {'imagetype',  'roimask'});

            labelimagename=self.getOption('convertrs.labelimage.name');
            
            cfg.SetConfig_file(labelimagename,    [outfolder labelimagename '.nii.gz'], {'filetype', 'ROIMaskImage'}, {'imagetype', 'labelmask'});

            %outcfg.('ID') = ['RS.' rsuid];
            outcfg.('ID') = 'RS_targetoar';
            outcfg.('ROIMaskFileName')  = 'roimask';
            outcfg.('LabelMaskFileName')= labelimagename;
            outcfg.('RSFileName') = rsfile; 
            outcfg.('roimask2rs') = 0;
            outcfg.('LabelMaskWithImageContours')=1;
            self.OutputStructSet(roimask, outcfg, outfolder, imgcfgfile);
            
            cfg.writeJson();
        end
        
        function [cfgfile, cfg] = ConvertRP(self, rpinfo)
%             rtpat = RTPatientTables(self);
            uid   = rpinfo.SOPInstanceUID; 
%             T = rtpat.ExtendRTPlanTable({'SelectedRTPlanUID', uid});
%             if isempty(T)
%                 return; 
%             end
            
            outfolder     = DosUtil.mksubdir(OutputPatientFolder(self), ['RP.' uid]);
            imagemodality = rpinfo.ReferencedImageModality;
            imageuid      = rpinfo.ReferencedImageSeriesUID;
            rsuid         = rpinfo.ReferenceStructureSetUID;
            imagefolder   = [outfolder '../' imagemodality '.' imageuid '/'];

            rduid         = rpinfo.ReferencedRTDoseUID;
            dcmfolder     = [self.DcmRootFolder rpinfo.PatientID '\'];
            res = DosUtil.rdir([dcmfolder '*\*\' rduid '.dcm']); 
            rdfile = res(1).name; 
            
            outimageext   = OutputImageExt(self); 

            cfgfile       = [outfolder DcmDataConfigFileName(self)];
                       
            %cfg = dcmtk.DcmDataConfig(cfgfile); 
            %rpinfo = rtpat.GetTableRow('RTPLAN', uid);
            cfg = dcmtk.DcmDataConfig.Factory(cfgfile, rpinfo,  OutputRootFolder(self));
            
            %imageuid    = T.ReferencedImageSeriesUID{1};

            %rsuid = T.ReferenceStructureSetUID{1};
            
            rscfgfile = [outfolder '../RS.' rsuid '/' DcmDataConfigFileName(self)];
            
            rscfg = dcmtk.DcmDataConfig(rscfgfile);
            
            cfg.CloneFields(rscfg, '', {'filename','LabelMaskFileName', 'ROIMaskFileName' } );

            %[imagefolder, modality] = rtpat.GetDcmImageFolder(imageuid);
%             
%             outimgfolder    = DosUtil.mksubdir(OutputPatientFolder(self), [modality '.' imageuid]);

            %outimageext = OutputImageExt(self); 

            %outimgfile = [outimgfolder 'image' outimageext ];
            
            %[imagecfg, vhct]= ConvertImage(self, imageuid);  
            imagecfgfile = [imagefolder DcmDataConfigFileName(self)];
            
            imagecfg = dcmtk.DcmDataConfig(imagecfgfile); 
            imgfname = imagecfg.GetFullFile('image');
            %imagefile = imagecfg.GetFullFile('image'); 
%             dcmvh     = VolHeader; 
%             dcmvh.readJsonHeader(imagecfg.GetFullFile('dcmheader'));
            
            %cfg.CopyConfig(imagecfg, 'image');  cfg.SetConfig_file('image',  imagefile);

            %rdfile      = T.PlanDoseFile{1};
     
            rd  = RtDose2(rdfile);
            
            dose = rd.toVhImage();
         
            %dose.ReformCS(dcmvh);
            
            img = VolHeaderImage(imgfname); 
   
            dose.ReformCS(img);

            outdosefile  = [outfolder 'doseimage'];
            
            dose.SaveImage(outdosefile, outimageext);

            cfg.SetConfig_file('doseimage',    [outdosefile outimageext], {'filetype',  'VolHeaderImage'}, {'imagetype', 'dose'});
            
            try
                %rsuid = T.ReferenceStructureSetUID{1};
%                 targetoarmaskimage = cfg.GetFullFile('targetoarmaskimage'); 
%                 cfg.SetConfig_file('targetmaskimage', ...
%                      targetoarmaskimage, ...
%                      {'filetype', 'ROIMaskImage'},...
%                      {'imagetype', 'labelmask'}... 
%                      );

%                 roimaskfile = [outfolder '..\RS.' rsuid '\roimask.nii.gz'];
%                 
%                 if ~exist(roimaskfile, 'file')
%                     ConvertRS(self, rsuid);
%                 end
                roimaskfile = cfg.GetFullFile('roimask'); 
                if exist(roimaskfile, 'file')
                    roimask = ROIMaskImage(roimaskfile);
                    dvh = DVHCurves.calcDVH(dose, roimask);
                    dvhfile = [outfolder 'dvh.json'];
                    dvh.writeJson(dvhfile);
                    cfg.SetConfig_file('dvh', dvhfile, ...
                        {'filetype', 'json'});
                end
            catch err
                self.LogErr(err);
            end

            str = struct('ID',['RD.' rduid], 'DoseFileName', 'doseimage', 'DVHFileName', 'dvh.json');
            cfg.AddStruct2List(str, 'DoseSet');

            cfg.writeJson();
        end
        
        
        %function [cfg, vhct] = ConvertImage(self, imageuid, varargin)  
        function [cfg, vhct] = ConvertImage(self, task,info, varargin)  
            reformcs   = task.GetTaskConfig('ReformCS');
            reformopts = task.Config2OptionsMap_filter(reformcs, info);
            options = OptionsMap({'ReformOptions', reformopts}, varargin{:});
            imageuid= info.SeriesInstanceUID;
            cfg=[]; vhct=[];
            
            outimageext = OutputImageExt(self); 
            
            rtpat = RTPatientTables(self);
            
            [imagefolder, modality] = rtpat.GetDcmImageFolder(imageuid);
            modality = upper(modality); 
            if isempty(imagefolder) || isempty(modality)
                %return
                error(['image files: '  imageuid ' not exist!'])
            end

            % try
            %imagefolder = DosUtil.absolutepath(imagefolder, pwd);
            
            outfolder    = DosUtil.mksubdir(OutputPatientFolder(self), [modality '.' imageuid]);
            
            cfgfile      = [outfolder DcmDataConfigFileName(self)];
            
            imginfo = rtpat.GetTableRow(modality, imageuid);
            cfg = dcmtk.DcmDataConfig.Factory(cfgfile, imginfo,  OutputRootFolder(self));
            
            outimagename = [outfolder 'image']; 
            outimagefile = [outimagename outimageext];
            origvhfile   = [outimagename '_dcm.json'];

            [vhct, origvh] = VolHeaderImage.LoadDcmVolImage([imagefolder '\*.dcm']);
            nonnegativity = self.getoptioni_numeric('convertimage.enforcenonnegativity'); 
            if (nonnegativity)
                vhct.data(vhct.data<0)=0;
            end
            origvh.writeJsonHeader(origvhfile);

            cfg.SetConfig_file('dcmheader',origvhfile,   {'filetype', 'VolHeader'});
            
            ReformOptions = options.getOption('ReformOptions'); 
            if ~isempty(ReformOptions)
                vhct.ReformCS([], ReformOptions);
            end
            %ReformImage(self, vhct);
            IntensityMapStr = task.GetTaskConfig('IntensityMapData');
            if ~isempty(IntensityMapStr)
                vhct.intensityMapData(IntensityMapStr);
            end

            %vhct.setShort();
            vhct.SaveImage(outimagename, outimageext);
            Image2PatientMatrix = vhct.DicomAffineMatrix;
            cfg.SetConfig_file('image',    outimagefile, {'Image2PatientMatrix', Image2PatientMatrix}, {'filetype',  'VolHeaderImage'}, {'imagetype', modality});
            cfg.AddImage2ImageSet(outimagename, imginfo);
            cfg.writeJson();
            % catch
            %     outimagefile=''; 
            %     error(['error: ConvertImage:' imagefolder]);
            % end
        end
        
        function ReformImage(self, vhct)
            orientation= self.getoptioni('outputimage.orientation');
            resolution = self.getoptioni_numeric('outputimage.resolution');
            if ~isempty(orientation)
                vhct.ReformImageOrientation(orientation, {'interpmethod', 'linear'});
            end
            if ~isempty(resolution)
                vhct.resample(resolution); 
            end
        end
                
        function ConvertTableRow(self, row)
            modality = upper(row.Modality{1}); 
            patid    = row.PatientID{1}; 
            SetPatientID(self, patid);
            switch modality
                case {'CT', 'MR', 'PT'}
                    uid = row.SeriesInstanceUID{1};
                    ConvertImage(self, uid);
                case {'RTSTRUCT'}
                    uid = row.SOPInstanceUID{1};
                    ConvertRS(self, uid);
                case {'RTPLAN'}
                    %uid = row.SOPInstanceUID{1};
                    ConvertRP(self, info);
            end
        end
        
        function ConvertTable(self, T)
            for k=1:size(T, 1)
                ConvertTableRow(self, T(k, :));
            end
        end
    end
    
    methods (Static)
         function [reginfo, regobj, outimg] = RegFOR(task, rootfolder, movinfo, fixinfo, varargin)
%               options  = OptionsMap(varargin{:});
              %regtable = options.getOption('RegTable'); 
              %regtablefile     =  taskinfo.GetTaskConfig('RegTableFile'); 
              reginfo=[]; regobj=[]; outimg=[];
              regsubspace = task.GetTaskConfig('RegSubSpace');
              if isempty(regsubspace)
                   regsubspace='';
              end
              
              affinemode = task.GetTaskConfig('RegAffineMode');
              if isempty(affinemode)
                  affinemode = 1; 
              end
              movname = task.GetTaskConfig('ImageFileName');
              fixname = task.GetTaskConfig('FixedImageFileName');
              movimgid = [movinfo.Modality '.' movinfo.SeriesInstanceUID];
              fiximgid = [fixinfo.Modality '.' fixinfo.SeriesInstanceUID];
              movpatid = movinfo.('PatientID');
              movfile =  DosUtil.SimplifyPath([rootfolder movpatid '/' movimgid '/' regsubspace '/' movname]); 
              fixpatid = fixinfo.('PatientID');
              fixfile =  DosUtil.SimplifyPath([rootfolder fixpatid '/' fiximgid '/' regsubspace '/' fixname]); 
              movimg  = VolHeaderImage.SafeLoad(movfile); 
              fiximg  = VolHeaderImage.SafeLoad(fixfile);
              sameFOR = strcmpi(movinfo.FrameOfReferenceUID, fixinfo.FrameOfReferenceUID);
              reformonly = StructBase.getfieldx_default(task, 'NoRegWithSameFOR', 1);
              if sameFOR && reformonly
                 regmatrix=eye(4);
                 outimg = VolHeaderImage(movimg);
                 outimg.ReformCS(fiximg); 
              else
                %outfor = task.GetTaskConfig('OutputFOR', 'fixed');  
                srcAsFixed = task.GetTaskConfig('SrcImageAsFixed');
                if srcAsFixed
                    [regobj, outimg]  = ai.reg.RegFOR(fiximg, movimg, {'AffineMode', affinemode}, {'OutputFOR', 'moving'});
                    regmatrix = inv(regobj.RegAffineMatrix);
                else 
                    [regobj, outimg]  = ai.reg.RegFOR(movimg, fiximg, {'AffineMode', affinemode}, {'OutputFOR', 'fixed'});
                    regmatrix = regobj.RegAffineMatrix;
                end
             end
            
             try
%                 if ~isempty(regtablefile) 
%                     fname = DosUtil.SimplifyPath([outfolder regtablefile]);
%                     regtable = ai.reg.RegTable({'table.filename', fname}); 
                    reginfo.('PatientID')      = movpatid;
                    reginfo.('FixedPatientID') = fixpatid;
                    reginfo.('FrameOfReferenceUID') = movinfo.('FrameOfReferenceUID');
                    reginfo.('FixedFrameOfReferenceUID')  = fixinfo.('FrameOfReferenceUID');
                    reginfo.('RegSubSpace')= regsubspace;
                    reginfo.('ImageID')= movimgid;
                    reginfo.('FixedImageID') = fiximgid;
                    reginfo.('RegAffineMatrix')=regmatrix(:)';
                    reginfo.('RegAffineMode')=affinemode;
%                     regtable.UpdateRow(reginfo);
%                     regtable.SaveTable();
%                 end
            catch
            end
         end
         
         function [regmatrix,testimg, outcfg] = Reg2Atlas(taskinfo, infolder, refimgfname, varargin)
            options = OptionsMap(varargin{:}); 
            subname = taskinfo.GetTaskConfig('CustomSubFolder'); 
            outFOR  = taskinfo.GetTaskConfig('OutputFOR');
            
            %infolder   = [fileparts(incfgfile) '\']; 
            incfgfile = [infolder 'dcmdata.cfg']; 
            outfolder  = DosUtil.mksubdir(infolder, subname);
            outcfgfile = [outfolder 'dcmdata.cfg'];
            
            incfg  = dcmtk.DcmDataConfig(incfgfile);
            outcfg = dcmtk.DcmDataConfig(outcfgfile);
            outcfg.CopyConfig(incfg, 'MyInfo', 'MyInfo');
            
            imgfname = incfg.GetFullFile('image');
            testimg  = VolHeaderImage.SafeLoad(imgfname);

            atlasname           = taskinfo.GetTaskConfig('AtlasName');
            affinemode = taskinfo.GetTaskConfig('RegAffineMode');
            if isempty(affinemode)
                affinemode = 1; 
            end

            regoptions = OptionsMap({'AffineMode', affinemode}, {'OutputFOR', outFOR});

            regtablefile     =  taskinfo.GetTaskConfig('RegTableFile'); 
            dcminfo =  options.getOption('DcmInfo'); 
            regtable=[]; reginfo=[]; reginfo1=[];
            if ~isempty(regtablefile) && ~isempty(dcminfo)
                fname = DosUtil.SimplifyPath([outfolder regtablefile]);
                regtable = ai.reg.RegTable({'table.filename', fname}); 
                reginfo.('PatientID') = dcminfo.('PatientID');
                reginfo.('FixedPatientID') = atlasname;
                reginfo.('FrameOfReferenceUID') = dcminfo.('FrameOfReferenceUID');
                reginfo.('FixedFrameOfReferenceUID')  = subname;
                reginfo.('RegSubSpace')= subname;
                reginfo.('ImageID')= [dcminfo.('Modality') '.' dcminfo.('SeriesInstanceUID')];
                reginfo.('FixedImageID') = atlasname;
                %reginfo.('RegAffineMatrix')=regmatrix(:)';
                reginfo.('RegAffineMode')=affinemode;
                %regtable.UpdateRegInfo(reginfo);
                %regtable.SaveTable();
                reginfo1=regtable.GetMatchRegInfo(reginfo, {'UseFORReg', 1});
                if ~isempty(reginfo1)
                    regmatrix = reshape(reginfo1.RegAffineMatrix, [4 4]);
                    regoptions.setOption('RegAffineMatrix', regmatrix);
                end
            end

            if exist(refimgfname, 'file')==2
                 [regobj, testimg]  = ai.reg.Reg2Atlas(atlasname, testimg, {'FixedImageFileName', refimgfname},regoptions);
            else
                 [regobj, testimg]  = ai.reg.Reg2Atlas(atlasname, testimg,regoptions);
            end
            regmatrix = regobj.RegAffineMatrix;
            
            Image2PatientMatrix = testimg.DicomAffineMatrix; 
            outimagename = [outfolder 'image'];
            testimg.writeNiftiImage(outimagename);
            %testimg.writeJsonHeader([outimagename '.json']);
            outimagefile = [outimagename '.nii.gz']; 
            if ~isempty(refimgfname)
                fixedimgfname = DosUtil.relativepath(refimgfname, outfolder);
            else
                fixedimgfname = regobj.getoptioni('FixedImageFileName', ''); 
            end
            fixedimgfname = DosUtil.SimplifyPath(fixedimgfname);
            
            outcfg.CopyConfig(incfg, 'image', 'image');
            outcfg.SetConfig_file('image',    outimagefile, ...
                {'Image2PatientMatrix', Image2PatientMatrix},...
                {'FOR', outFOR}, ...
                {'RegistrationMatrix', regmatrix}, ...
                {'FixedImageFileName', fixedimgfname });
            %incfg.writeJson(); 
            outcfg.writeJson();
            try
            % regtablefile     =  taskinfo.GetTaskConfig('RegTableFile'); 
            % dcminfo =  options.getOption('DcmInfo'); 
            % if ~isempty(regtablefile) && ~isempty(dcminfo)
            if ~isempty(regtable)&&isempty(reginfo1)
                reginfo.('RegAffineMatrix')=regmatrix(:)';
                regtable.UpdateRegInfo(reginfo);
                regtable.SaveTable();
            end
            catch
            end
         end
        
         function outcfg=Reform2Atlas(taskinfo, infolder, imgnames, varargin)
            subname = taskinfo.GetTaskConfig('CustomSubFolder'); 
            outFOR  = taskinfo.GetTaskConfig('OutputFOR');
            options  = OptionsMap(varargin{:});             
            outfolder= DosUtil.mksubdir(infolder, subname); 
            incfgfile = [infolder 'dcmdata.cfg']; 
            outcfgfile = [outfolder 'dcmdata.cfg'];           
            incfg   = dcmtk.DcmDataConfig(incfgfile);
            outcfg  = dcmtk.DcmDataConfig(outcfgfile);
            outcfg.CopyConfig(incfg, 'MyInfo', 'MyInfo');
 
            imgfname= incfg.GetFullFile('image'); 
            [imgfolder]= [fileparts(imgfname) '/' subname '/']; 
            imgcfg    = dcmtk.DcmDataConfig([imgfolder 'dcmdata.cfg']); 
            regmatrix = imgcfg.GetConfig('image.RegistrationMatrix'); 
            fixedvhname = imgcfg.GetConfig('image.FixedImageFileName');
%             fixedvh = VolHeader; 
%             fixedvh.readJsonHeader([imgfolder fixedvhname '.json']); 
            fixedvh = VolHeaderImage.SafeLoad(fixedvhname);
            %A = fixedvh.DicomAffineMatrix;
            movingvh = VolHeader(fixedvh); 
            movingvh.ApplyAffineCoorTransform(regmatrix);
            if strcmpi(outFOR, 'Fixed')
                 Image2PatientMatrix = fixedvh.DicomAffineMatrix;
            else
                 Image2PatientMatrix = movingvh.DicomAffineMatrix;
            end
            
            if ischar(imgnames)
                imgnames = strsplit(imgnames, '|'); 
            end
            
            for m=1:numel(imgnames)
                imgname = imgnames{m};
                outcfg.CopyConfig(incfg, imgname, imgname);
                if strcmpi(imgname, 'dvh')
                    infname =incfg.GetFullFile(imgname); 
                    outcfg.SetConfig_file(imgname,  infname);
                    continue; 
                end
                
                infname =incfg.GetFullFile(imgname); 
                if isempty(infname)
                    continue; 
                end
                
                res = infname; res = strrep(res, '.nii.gz', ''); 
                [folder, outname] = fileparts(res); 
                
                outimagefile = DosUtil.SimplifyPath([folder '/' subname '/' outname]); 
                
                if ~exist([outimagefile '.nii.gz'], 'file')
                    filetype = incfg.GetConfig([imgname '.filetype']); 
                    if strcmpi(filetype, 'ROIMaskImage')||strcmpi(filetype, 'labelimage')
                        testimg = ROIMaskImage(infname); 
                        testimg.RemoveROIFields({'xContours','yContours', 'zContours', 'BoundingBox'});
                    else
                        testimg = VolHeaderImage.SafeLoad(infname);
                    end
                    testimg.ReformCS(movingvh);

                    if strcmpi(outFOR, 'Fixed')
                        testimg.ResetDicomAffineMatrix(Image2PatientMatrix);
                    end
                    testimg.writeNiftiImage(outimagefile); 
                end

                outcfg.SetConfig_file(imgname,  [outimagefile '.nii.gz'],  ...
                    {'Image2PatientMatrix', Image2PatientMatrix}, ...
                    {'FOR', outFOR});
            end
            outcfg.writeJson();
        end
        
         %todo: implement this for both DcmConverter and AISeg
         function str   = OutputStructSet(inmask, outcfg, outfolder, imgcfg, rscfg)
            if ~exist("imgcfg", 'var')
                imgcfg = [];
            end
            
            if ischar(imgcfg)&&~isempty(imgcfg)
                imgcfg      = dcmtk.DcmDataConfig(imgcfg); 
            end
            imgrootpath='';
            if ~isempty(imgcfg)
                imgrootpath = imgcfg.RootPath;
            end

            ExtractROIs = StructBase.getfieldx(outcfg,   'RefPlaceHoldROIs');
            if ~isempty(ExtractROIs)
                inmask = inmask.ExtractPlaceHoldROIs(ExtractROIs);
                inmask.ROIStatTable_regionprops();
            end

            %outcfg     = task.GetTaskConfig('OutputSeg');
            roimaskfile = StructBase.getfieldx(outcfg,   'ROIMaskFileName');
            labelmaskfile = StructBase.getfieldx(outcfg, 'LabelMaskFileName');
            rsfile      = StructBase.getfieldx(outcfg,   'RSFileName');
            str.ID      = StructBase.getfieldx(outcfg,   'ID');
    
            inmasktype  = inmask.imageType; 

            roimask = []; labelmask =[]; 

            switch lower(inmasktype)
                case {'roimask', 'roimask8'}
                    roimask   = inmask; 
                case {'labelmask', 'optmask'}
                    labelmask = inmask; 
            end
               
            if ~isempty(roimask) && ~isempty(roimaskfile)
                outmaskfile  = [outfolder roimaskfile];
                roimask.writeNiftiImage(outmaskfile);
                str.("ROIMaskFileName")= DosUtil.relativepath(outmaskfile, imgrootpath,'/');
            end
            
            if ~isempty(labelmaskfile)
                outmaskfile  = [outfolder labelmaskfile];
                LabelMaskWithImageContours = StructBase.getfieldx_default(outcfg,   'LabelMaskWithImageContours', 0);
                ContourSmoothWindow = StructBase.getfieldx_default(outcfg,   'ContourSmoothWindow', 0);
                ContourDirs = StructBase.getfieldx_default(outcfg,   'ContourDirs', 'z');
                extraopt = OptionsMap; extraopt.fromStruct(outcfg); 
                if isempty(labelmask)
                    if strcmpi(roimask.imageType, 'roimask8')
                        extraopt.setOption('keeproiindex', 0);
                    end
                    labelmask = ROIMaskImage.roimask2labelmask(roimask,[], ...
                        {'LabelMaskWithImageContours', LabelMaskWithImageContours}, ...
                        {'ContourSmoothWindow', ContourSmoothWindow}, {'ContourDirs', ContourDirs}, extraopt);
                end
                if isempty(labelmask) 
                    labelmask = ROIMaskImage(VolHeader(roimask)); labelmask.ZeroData(); labelmask.imageType= 'labelmask';
                end
                %if LabelMaskWithImageContours && ~labelmask.With_zContours
                if LabelMaskWithImageContours
                    labelmask.Convert2ImageContours(ContourDirs, ContourSmoothWindow);
                end
                
                labelmask.writeNiftiImage(outmaskfile);
                str.("LabelMaskFileName")= DosUtil.relativepath(outmaskfile, imgrootpath,'/');
                %imgcfg.AddFile2List([outmaskfile '.json'], 'labelmasklist');
                
                roistatfname = StructBase.getfieldx(outcfg,   'SrcROIStatFileName');
                %imgrootpath = imgcfg.RootPath;
                if ~isempty(roistatfname)
                    
                    xlsfname = [imgrootpath roistatfname]; 
                    statT = xls.TableBase.ReadTable(xlsfname); 
                else
                    statT = ROIMaskImage.ROIStatTable_header(labelmask.rois, { 'ROIIndex', 'ROIVolume', 'ROICenter'});
                end
                
                if ~isempty(str.ID)
                    outxlsname = './ROIStat.xlsx';
                    outxlsfile = [imgrootpath outxlsname]; 
                    xls.TableBase.WriteTable(statT, outxlsfile, 'sheet', str.ID); 
                        %str.('ROIStatFileName') = DosUtil.relativepath(xlsfname, imgrootpath,'/');
                    if ~isempty(imgcfg)
                        imgcfg.SetConfig_file('ROIStat', outxlsfile);
                    end
                end
            end
            
            if ~isempty(rsfile)
                roimask2rs= StructBase.getfieldx_default(outcfg, 'roimask2rs', 0);
                if ischar(roimask2rs)
                    roimask2rs=str2double(roimask2rs);
                end
                if roimask2rs
                    origvhfname = imgcfg.GetFullFile('dcmheader');
                    outmask = ROIMaskImage.SafeLoad(inmask);
                    if ~isempty(origvhfname)
                        dcmvh =DcmVolHeader(origvhfname);
                        outmask.ReformCS(dcmvh);
                    end
                    maskname   =  str.ID;
                    outputfile = [outfolder rsfile]; 
                    rsinfo     = dicom.utils.dicom_rtstruct_template;
                    rsinfo.StructureSetLabel = maskname; 
                    rsinfo.StructureSetName  = maskname;  
                    dicom.utils.ROIMaskImage2DicomRS(outmask,  dcmvh, [], {'outputfile.rtstruct', outputfile}, {'rs.dicominfo', rsinfo});
                end
                str.("RSFileName")= DosUtil.relativepath(rsfile, imgcfg.RootPath,'/');
            end
            
            if ~isempty(str.ID) && ~isempty(imgcfg)
                imgcfg.AddStruct2List(str, "StructSet");
            end
            if ~isempty(imgcfg)
                imgcfg.writeJson();
            end

            if exist('rscfg', 'var') && ~isempty(rscfg)
                fns      = {'ROIMaskFileName', 'LabelMaskFileName', 'RSFileName'}; 
                for m=1:numel(fns)
                    fn = fns{m}; 
                    if isfield(str, fn)
                        fname = imgcfg.FullFile(str.(fn)); 
                        fname = DosUtil.relativepath(fname, rscfg.RootPath(),'/');
                        fname = DosUtil.SimplifyPath(fname); 
                        str.(fn)=fname; 
                    end
                end
                rscfg.AddStruct2List(str, "StructSet");
                rscfg.writeJson();
            end
        end

        function targetoarmask = MergeTargetOARMaskImage(targetmask, oarmask, outmaskfile)
            targetoarmask = [];
            if ischar(targetmask) 
                if exist(targetmask, 'file')
                    targetmask = OptMaskImage(targetmask); 
                else 
                    targetmask = OptMaskImage(VolHeaderImage(oarmask)); 
                    targetmask.data(:)=0; 
                end
            end
            
            if ischar(oarmask)
                if exist(oarmask, 'file')
                    oarmask = ROIMaskImage(oarmask); 
                else
                    oarmask = OptMaskImage(VolHeaderImage(targetmask)); 
                    oarmask.data(:)=0;
                end
            end
            
            try
                indexs0 = cellfun(@(x)(x.ROIIndex), targetmask.rois);
                maxtargetindex = max(indexs0);
            catch
                maxtargetindex=0; 
            end
            
            targetoarmask = OptMaskImage(targetmask);
            
            oarnames= oarmask.ROINames;
            
            rois = oarmask.rois; 
            
            for k=1:numel(oarnames)
                roi1 = rois{k}; 
                roi1.ROIIndex = maxtargetindex+k;
                oarname = oarnames{k};
                mask    = oarmask.getROIMask(oarname); 
                mask1 = mask & targetmask.data==0;
                targetoarmask.AddNewROI(mask1, oarname, 0, {'roi', roi1});    
            end
            targetoarmask.SetDistinguishableColors();
            if exist('outmaskfile', 'var')
                targetoarmask.writeNiftiImage(outmaskfile); 
            end
        end
    end
end



