classdef DBTable_User < mydb.sqlite.DBTable
    properties
        
    end

    methods
        function obj = DBTable_User(varargin)
            <EMAIL>({'dbt.PrimaryKey', 'UserID'});
        end
    end

    methods (Static)
        function varnames = TableVaribleNames
             varnames = {'UserID', 'Password', 'LastName', 'FirstName', 'Department', 'Institute',  'Email', 'Phone', 'AccountRoles', 'AccountStatus'};
             varnames = cat(2, varnames, mydb.sqlite.DBTable.DBTableTimeStampVarNames);
        end
    end
end