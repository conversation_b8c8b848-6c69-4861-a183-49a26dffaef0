classdef TimeStampRecordDB <OptionsWithLog
    properties
        m_ActiveDB;
        
        m_FullDB;
    end
    
    methods
        function obj = TimeStampRecordDB(varargin)
            obj = obj@OptionsWithLog('TimeStampRecordDB', varargin{:});
        end

        function fulldb = GetFullDB(obj)
            if isempty(obj.m_FullDB)
                fulldb  = LoadFullDB(obj);
            else
                fulldb  = obj.m_FullDB;
            end
        end
        
        function activedb = GetActiveDB(obj)
            if isempty(obj.m_ActiveDB)
                activedb  = LoadActiveDB(obj);
            else
                activedb  = obj.m_ActiveDB;
            end
        end
        
        function flag = UseXlsFullDB(obj)
            flag = strcmpi(FullRecordDBFormat(obj), '.xlsx');
        end
        
        function db = LoadFullDB(obj)
            db = [];
            switch lower(FullRecordDBFormat(obj))
                case {'.xlsx'}
                    db = LoadRecordTable(obj, FullRecordDBFile(obj));
                    obj.m_FullDB = db;
                case {'.sqlite'}
                    dbfile  = FullRecordDBFile(obj); 
                    %db= mydb.sqlite.DataBase(dbfile);
                    db= mydb.sqlite. DcmRTDB(dbfile);
%                     db      = sqiltedb.GetDBTable(RecordDBName(obj));
%                     db.setOption('PRIMARY_KEY', TableUniqueKey(obj));
                    obj.m_FullDB = db;
            end
        end
        
        function db = LoadActiveDB(obj)
            db = LoadRecordTable(obj, ActiveRecordDBFile(obj));
            obj.m_ActiveDB = db; 
        end
        
        function fulldb = LoadRecordTable(obj, fname)
            uniquekey  = TableUniqueKey(obj);
            fulldb     = xls.RecordKeepTable({'table.filename', fname}, {'table.uniquekey', uniquekey},...
                {'table.sheetname', RecordDBName(obj)});
            if exist(fname,'file')
                fulldb.LoadTable(fname);
            end
        end
        
        function res= TableUniqueKey(obj)
            res     = obj.getoptioni('table.uniquekey');
        end
        
        function res = RecordDBName(obj)
            res = obj.getoptioni_char('RecordDBName');
        end
        
        function res = RecordDBFolder(obj)
            res = obj.getoptioni_char('RecordDBFolder');
        end

        function res = RecordDBHistoryFolder(obj)
            res = obj.getoptioni_char('RecordDBHistoryFolder');
            if isempty(res)
                res = DosUtil.mksubdir(RecordDBFolder(obj), 'History');
                obj.setOption('RecordDBHistoryFolder', res);
            end
        end
        
        function res = FullRecordDBFormat(obj)
            res = obj.getoptioni_char('FullRecordDBFormat', '.xlsx'); 
        end
        
        function res = FullRecordDBFile(obj)
            res = [RecordDBFolder(obj) RecordDBName(obj) FullRecordDBFormat(obj)];
        end
        
        function res  = ActiveRecordDBFile(obj)
            unit = obj.getoptioni_numeric('timestampunit', 1);
            str  = datestr(floor(now/unit)*unit, 'yyyymmddTHHMMSS');
            res  = [RecordDBFolder(obj)  RecordDBName(obj) '_' str '.xlsx'];
        end
        
        function Store2FullDB_xlsx(obj)
            activedb     = obj.GetActiveDB();
            fulldb       = obj.GetFullDB();
            fulldb.UpdateFromTable(activedb.GetRawTable());
            activedb.ClearTable();
            res = DosUtil.rdir([RecordDBFolder(obj) RecordDBName(obj) '_*.xlsx']);
            if ~isempty(res)
                fnames = {res(:).name};
                %uniquekey= TableUniqueKey(obj);
                for k=1:numel(fnames)
                    try
                    fname = fnames{k};
                    db    = LoadRecordTable(obj, fname);
                    fulldb.UpdateFromTable(db.GetRawTable());
                    %delete(fname);
                    
                    [~, name, ext] = fileparts(fname);
                    movefile(fname, [RecordDBHistoryFolder(obj) name ext], 'f');
                    catch err
                        obj.LogErr(err);
                    end
                end
            end
            fulldb.SaveTable();
        end
        
        function ClearActiveDB_xlsx(obj)
            try
            activedb     = obj.GetActiveDB();
            activedb.ClearTable();
            res = DosUtil.rdir([RecordDBFolder(obj) RecordDBName(obj) '_*.xlsx']);
            if ~isempty(res)
                fnames = {res(:).name};
                for k=1:numel(fnames)
                    try
                    fname = fnames{k};
                     [~, name, ext] = fileparts(fname);
                    movefile(fname, [RecordDBHistoryFolder(obj) name ext], 'f');
                    catch err
                        obj.LogErr(err);
                    end
                end
            end
            catch err0
                obj.LogErr(err0);
            end
        end
        
        function AddActiveRecords(obj, T)
            activedbfile = ActiveRecordDBFile(obj);
            activedb     = obj.GetActiveDB();
            if ~exist(activedbfile, 'file')
                if UseXlsFullDB(obj)
                    obj.Store2FullDB_xlsx();
                else
                    obj.ClearActiveDB_xlsx();
                end
                activedb.setOption('table.filename', ActiveRecordDBFile(obj));
            end
            
            [num, T1] = activedb.UpdateFromTable(T, 0);
            
            if num>0
                activedb.SaveTable();
                
                fulldb = GetFullDB(obj);
                if ~isempty(fulldb)
                    if UseXlsFullDB(obj)
                        fulldb.UpdateFromTable(T1);
                    else
                        dbT = fulldb.GetDBTable(RecordDBName(obj));
                        if ~isempty(dbT)
                            dbT.InsertMatlabTable(T1);
                        end
                    end
                end
            end
        end
    end
end

