function map = structToMap(s,  matchnamepat)
    % Recursively converts a MATLAB struct (which may contain nested fields,
    % struct arrays, or struct cells) to a container.Map
    
    % Create an empty container.Map
    map = containers.Map();
    
    % Handle struct arrays directly
    if isstruct(s)
        fields = fieldnames(s);  % Get field names of the struct
        
        for i = 1:numel(fields)
            key = fields{i};
            value = s.(key);
            
            % If the field contains nested structs, recursively convert it
            if isstruct(value)
                if numel(value) > 1
                    % Struct array: handle each element
                    valueMapArray = cell(1, numel(value));
                    for j = 1:numel(value)
                        valueMapArray{j} =  utils.json.structToMap(value(j));
                    end
                    value = valueMapArray;  % Store array of container.Map
                else
                    % Single struct
                    value = utils.json.structToMap(value);
                end
            elseif iscell(value)
                % If the field is a cell array, recursively handle any struct within it
                for j = 1:numel(value)
                    if isstruct(value{j})
                        value{j} =  utils.json.structToMap(value{j});
                    end
                end
            end
            
            % Handle JSON-decoded struct field names starting with digits by removing the 'x' prefix
            % if key(1) == 'x' && isstrprop(key(2), 'digit')
            %     key = key(2:end);  % Remove the 'x' prefix
            % end

            if ~isempty(regexp(key, matchnamepat, 'ONCE'))
                key(1)=[];
            end
            
            % Store the key-value pair in the container.Map
            map(key) = value;
        end
    else
        error('Input must be a struct.');
    end
end
% 
% function flag = matchname(fn, pattern)
%     flag = ~isempty(regexp(fn, pattern, 'ONCE'));
% end