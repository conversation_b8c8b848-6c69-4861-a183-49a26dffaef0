classdef WatchDogServer_CK < daemon.DcmRTServer
    properties
        %m_CKXmlDB; 
    end
    
    methods
        function obj = WatchDogServer_CK(varargin)
            options = OptionsMap({'ServerName', 'WatchDog_CK'});
            options.setOption('CyberKnife.RegisteredXmls', 'GeneralPlan~*.xml|CkPlanDetails~*.xml|reports.xml');

            <EMAIL>(options, varargin{:});
        end
        
        function status = CalcAdminTask(self, task, info, varargin)
            status  = <EMAIL>(task, info, varargin{:});
            taskname = task.getfieldx('TaskName');
            if regexpi(taskname, '^CKXmlImport', 'once')
                Calc_XmlImport(self, task, info);
            end
        end

        function res = XmlDBFile(self)
             str = 'CyberKnife.XmlDBFile';
             res = self.getOption(str);
        end

        function res = XmlDB(self)
            str = 'CyberKnife.XmlDB';
            if self.isOption(str)
                 res = self.getOption(str);
            else
                fname = XmlDBFile(self);
                res = ck.CKXmlDB({'CyberKnife.XmlDBFile', fname});
                self.setOption(str, res);
            end
        end


        function res=JsonRoot(self)
            res = self.getOption_FullPath('CyberKnife.JsonRoot');
        end

        function Calc_XmlImport(self, task, info)
            info.TaskUID = task.getfieldx('TaskUID');
            xmlfolder = task.GetTaskConfig('XmlFolder', '');
            xmlfolder = DosUtil.WildCardRepStr(xmlfolder, info);
            xmlpats   = task.GetTaskConfig('XmlFilePattern');
            OptionsStr = task.GetTaskConfig('Options');
            options= OptionsMap; 
            if isstruct(OptionsStr)
                options.fromStruct(OptionsStr);
            end
            [fnames, success]=ImportXmlFolder(self, xmlfolder, xmlpats, options);
        end

        function [fnames, success]=ImportXmlFolder(self, xmlfolder, xmlpats, varargin)
            if ~exist('xmlpats', 'var')||isempty(xmlpats)
                xmlpats = self.getOption('CyberKnife.RegisteredXmls');
            end
            if ischar(xmlpats)
                xmlpats = strsplit(xmlpats, '|');
            end
            xmlpats = cellfun(@(x)([xmlfolder x]), xmlpats, 'UniformOutput',false);
            [fnames, watches] = DosUtil.ListFile_filter( xmlpats); 
            success=zeros(size(fnames), 'like', false);
            for n=1:2 %try twice
                for k=1:numel(fnames)
                    if ~success(k)
                        success(k)= ImportXmlFile(self, fnames{k}, varargin{:});
                    end
                end
            end
        end

        function updated=ImportXmlFile(self, xmlfile, varargin)
            options = OptionsMap(varargin{:});
            [~, name, ext]=fileparts(xmlfile);
            xmlplan = [];
            xmltype = extractBefore(name, '~');
            if isempty(xmltype) && strcmp(name, 'reports')
                xmltype = 'reports';
            end
            switch xmltype
                case 'GeneralPlan'
                    xmlplan = ck.xml.CKGeneralPlan(xmlfile, self);
                    extraregtable = options.getOption('ExtractDcmRegTable');
                    if ~isempty(extraregtable)
                        regT = xmlplan.ExportDcmRegTable;
                        RegID = regT.GetRawTable().('RegID_Image');
                        RegMapImage = StructBase.getfieldx(extraregtable, "RegMapImage");
                        if ~isempty(RegID) && ~isempty(RegMapImage)
                            if isstruct(RegMapImage)
                                RegMapImage = StructBase.toCell(RegMapImage);
                            end
                            
                            for k=1:numel(RegMapImage)
                                TaskFolder=StructBase.getfieldx(RegMapImage{k}, 'TaskFolder');
                                TaskUIDPattern=StructBase.getfieldx(RegMapImage{k}, 'TaskUIDPattern');
                                I = cellfun(@(x)(~isempty(regexp(x, TaskUIDPattern, 'ONCE'))), RegID);
                                taskuids =  RegID(I);
                                for m=1:numel(taskuids)
                                    taskfile = [TaskFolder taskuids{m} '.tsk'];
                                    fclose(fopen(taskfile, 'wt')); 
                                end
                            end
                        end
                    end

                    CKPlanID = extractAfter(name, 'GeneralPlan~');
                    xmlplan.SetCKPlanID(CKPlanID);
                case 'CkPlanDetails'
                    xmlplan = ck.xml.CKPlanDetails(xmlfile, self);
                    CKPlanID = extractAfter(name, 'CkPlanDetails~');
                    xmlplan.SetCKPlanID(CKPlanID);
                case 'reports'
                    xmlplan = ck.xml.CKPlanReport(xmlfile, self);
            end
            updated=0;
            if ~isempty(xmlplan) && strcmpi(ext, '.xml')
                db = self.XmlDB; 
                if ~isempty(db)
                    [updated, info] = db.AddXmlPlan2DB(xmlplan);
                    savejson = options.getOption('SaveJson', 1);
                    if updated && savejson
                        jsonfolder = DosUtil.mksubdir(JsonRoot(self),  [info.PatientID '/' info.PlanName]);
                        xmlplan.WriteJson([jsonfolder xmltype '.json']);
                    end
                end
            end
            postimport = options.getOption('PostImport');
            if ~isempty(postimport)
                xmldstroot = StructBase.getfieldx(postimport, 'XmlDstRoot');
                if ~isempty(xmldstroot)
                    xmldstfolder = DosUtil.mksubdir(xmldstroot, info.CKPlanID);
                    copyfile(xmlfile, [xmldstfolder name '.xml'], 'f');
                end
                RemoveXml=StructBase.getfieldx(postimport, 'RemoveXml');
                if RemoveXml>=2 || (RemoveXml>=1&& updated)
                    delete(xmlfile);
                end
            end
        end
    end
end

