dcmpssnd(1)                       OFFIS DCMTK                      dcmpssnd(1)



NAME
       dcmpssnd - Network send for presentation state viewer


SYNOPSIS
       dcmpssnd [options] config-file target study [series] [instance]

DESCRIPTION
       The   dcmpssnd   application  is  invoked  by  the  Grayscale  Softcopy
       Presentation State Viewer and is not intended to be  invoked  manually.
       The  application  reads  the  common configuration file which is shared
       between the different components of the viewer, attempts to establish a
       DICOM  association with the selected target and, if successful, selects
       the required DICOM instances from the viewer's database  and  transmits
       them  using the DICOM storage service class, which is supported as SCU.
       When transmission is finished  (successfully  or  unsuccessfully),  the
       application  terminates. This application supports the same storage SOP
       classes as the storescu utility,  but  does  not  support  encapsulated
       transfer  syntaxes.  See  the  full  conformance  statement for further
       details.

PARAMETERS
       config-file  configuration file to be read

       target       symbolic identifier of send target in config file

       study        study instance UID of study in database to be sent

       series       series instance UID (default: send complete study)

       instance     SOP instance UID (default: send complete series)

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  dcmpssnd  utility  will  attempt  to  load DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

FILES
       <etcdir>/dcmpstat.cfg - sample configuration file

SEE ALSO
       dcmpsrcv(1), storescu(1)

COPYRIGHT
       Copyright (C) 1998-2014 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmpssnd(1)
