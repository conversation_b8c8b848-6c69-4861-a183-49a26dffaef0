dcmqrscp(1)                       OFFIS DCMTK                      dcmqrscp(1)



NAME
       dcmqrscp - DICOM image archive (central test node)


SYNOPSIS
       dcmqrscp [options] [port]

DESCRIPTION
       The  Image Central Test Node (dcmqrscp) Application implements a simple
       image archive. It manages a number of storage areas and  allows  images
       to  be  stored  in  these storage areas using the DICOM Storage Service
       Class. It also allows image attributes to be queried and images  to  be
       retrieved  using  the  DICOM Query/Retrieve Service Class. The dcmqrscp
       application  also  implements  access  restriction   rules   to   limit
       operations to specific peer application entities.

       The dcmqrscp application is intended to be used in DICOM demonstrations
       and as a compatibility test tool. In particular, dcmqrscp  will  delete
       images if its quota restrictions have been exceeded.

       Caution:  Note  that  the  format  of  dcmqrdb's  binary index.dat file
       changed on 20130705 due to a bug fix  in  the  corresponding  code.  An
       index.dat file created before that date will not work with dcmqrdb code
       compiled after that date but must be re-created. Use dcmqridx for  that
       task  or  re-send all images to the server after deleting all old files
       (and creating a new empty index.dat file).

PARAMETERS
       port  tcp/ip port number to listen on (default: in config file)

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

         -c    --config  [f]ilename: string
                 use specific configuration file
                 (default: /usr/local/etc/dcmtk/dcmqrscp.cfg)

         # This option allows a specific configuration file to be
         # specified.  By default, the configuration file in the
         # system configuration directory ("etc" on Unix systems and
         # current working directory on Windows systems) with the name
         # "dcmqrscp.cfg" will be used.  It is an error if a valid
         # configuration file is not available.  See the documentation
         # on configuration for more information (dcmqrcnf.txt and the
         # example configuration file dcmqrscp.cfg).

   multi-process options
         -s    --single-process
                 single process mode

         # This option instructs dcmqrscp to run in single process mode.
         # Normally, dcmqrscp will spawn a new copy of itself to handle
         # each association.  This option will prevent such copies being
         # spawned and is particularly useful when running within a
         # debugger.

               --fork
                 fork child process for each association (default)

         # Please note that the multi-process options are only available
         # on systems that support the fork() call, i.e. not on Windows.

   database options
       association negotiation:

               --require-find
                 reject all MOVE/GET presentation contexts for
                 which no corresponding FIND context is proposed

         # This option causes dcmqrscp to enforce a restriction about
         # FIND and MOVE presentation contexts.  The RSNA'93 demonstration
         # required that a CTN refuse to accept a presentation context for
         # a MOVE if a FIND presentation context is not also requested.
         # This option enforces this restriction.

               --no-parallel-store
                 reject multiple simultaneous STORE presentation
                 contexts for one application entity title

         # This option causes dcmqrscp to refuse storage presentations
         # contexts for AE Titles if another association has already
         # successfully negotiated storage presentation contexts for the
         # AE Title.  This option should only be needed for debugging
         # since the dcmqrscp database back-end sequentialises access via
         # file-locking.

               --disable-get
                 disable C-GET support

               --allow-shutdown
                 allow external shutdown via a private SOP class

       checking identifier validity:

         -XF   --check-find
                 check C-FIND identifier validity

         # This option causes dcmqrscp database back-end to perform checks
         # on the Query Identifiers for a C-FIND command.  With this option,
         # dcmqrscp will reject any queries which do not conform to the very
         # strict DICOM rules for Query Identifiers.

               --no-check-find
                 do not check C-FIND identifier validity (default)

         -XM   --check-move
                 check C-MOVE identifier validity


         # This option causes dcmqrscp database back-end to perform checks
         # on the Query Identifiers for a C-MOVE command.  With this option,
         # dcmqrscp will reject any queries which do not conform to the very
         # strict DICOM rules for Query Identifiers.

               --no-check-move
                 do not check C-MOVE identifier validity (default)

       restriction of move targets:

               --move-unrestricted
                 do not restrict move destination (default)

         -ZA   --move-aetitle
                 restrict move destination to requesting AE title

         -ZH   --move-host
                 restrict move destination to requesting host

         -ZV   --move-vendor
                 restrict move destination to requesting vendor

       restriction of query/retrieve models:

         -QP   --no-patient-root
                 do not support Patient Root Q/R models

         -QS   --no-study-root
                 do not support Study Root Q/R models

         -QO   --no-patient-study
                 do not support Patient/Study Only Q/R models

   network options
       association negotiation profiles from configuration file:

         -xf   --assoc-config-file
                 [f]ilename, [i]n-profile, [o]ut-profile: string
                 use profile i from f for incoming associations
                 use profile o from f for outgoing associations

       preferred network transfer syntaxes (incoming associations):

         +x=   --prefer-uncompr
                 prefer explicit VR local byte order (default)

         +xe   --prefer-little
                 prefer explicit VR little endian TS

         +xb   --prefer-big
                 prefer explicit VR big endian TS

         +xs   --prefer-lossless
                 prefer default JPEG lossless TS

         +xy   --prefer-jpeg8
                 prefer default JPEG lossy TS for 8 bit data

         +xx   --prefer-jpeg12
                 prefer default JPEG lossy TS for 12 bit data

         +xv   --prefer-j2k-lossless
                 prefer JPEG 2000 lossless TS

         +xw   --prefer-j2k-lossy
                 prefer JPEG 2000 lossy TS

         +xt   --prefer-jls-lossless
                 prefer JPEG-LS lossless TS

         +xu   --prefer-jls-lossy
                 prefer JPEG-LS lossy TS

         +xm   --prefer-mpeg2
                 prefer MPEG2 Main Profile @ Main Level TS

         +xh   --prefer-mpeg2-high
                 prefer MPEG2 Main Profile @ High Level TS

         +xn   --prefer-mpeg4
                 prefer MPEG4 AVC/H.264 High Profile / Level 4.1 TS

         +xl   --prefer-mpeg4-bd
                 prefer MPEG4 AVC/H.264 BD-compatible HP / Level 4.1 TS

         +x2   --prefer-mpeg4-2-2d
                 prefer MPEG4 AVC/H.264 HP / Level 4.2 TS for 2D Videos

         +x3   --prefer-mpeg4-2-3d
                 prefer MPEG4 AVC/H.264 HP / Level 4.2 TS for 3D Videos

         +xo   --prefer-mpeg4-2-st
                 prefer MPEG4 AVC/H.264 Stereo HP / Level 4.2 TS

         +x4   --prefer-hevc
                 prefer HEVC H.265 Main Profile / Level 5.1 TS

         +x5   --prefer-hevc10
                 prefer HEVC H.265 Main 10 Profile / Level 5.1 TS

         +xr   --prefer-rle
                 prefer RLE lossless TS

         +xd   --prefer-deflated
                 prefer deflated explicit VR little endian TS

         +xi   --implicit
                 accept implicit VR little endian TS only

       proposed transmission transfer syntaxes (outgoing associations):

         -x=   --propose-uncompr
                 propose all uncompressed TS, explicit VR
                 with local byte ordering first (default)

         -xe   --propose-little
                 propose all uncompressed TS, explicit VR little endian first

         -xb   --propose-big
                 propose all uncompressed TS, explicit VR big endian first

         -xi   --propose-implicit
                 propose implicit VR little endian TS only

         -xs   --propose-lossless
                 propose default JPEG lossless TS
                 and all uncompressed transfer syntaxes

         -xy   --propose-jpeg8
                 propose default JPEG lossy TS for 8 bit data
                 and all uncompressed transfer syntaxes

         -xx   --propose-jpeg12
                 propose default JPEG lossy TS for 12 bit data
                 and all uncompressed transfer syntaxes

         -xv   --propose-j2k-lossless
                 propose JPEG 2000 lossless TS
                 and all uncompressed transfer syntaxes

         -xw   --propose-j2k-lossy
                 propose JPEG 2000 lossy TS
                 and all uncompressed transfer syntaxes

         -xt   --propose-jls-lossless
                 propose JPEG-LS lossless TS
                 and all uncompressed transfer syntaxes

         -xu   --propose-jls-lossy
                 propose JPEG-LS lossy TS
                 and all uncompressed transfer syntaxes

         -xm   --propose-mpeg2
                 propose MPEG2 Main Profile @ Main Level TS only

         -xh   --propose-mpeg2-high
                 propose MPEG2 Main Profile @ High Level TS only

         -xn   --propose-mpeg4
                 propose MPEG4 AVC/H.264 High Profile / Level 4.1 TS only

         -xl   --propose-mpeg4-bd
                 propose MPEG4 AVC/H.264 BD-compatible HP / Level 4.1 TS only

         -x2   --propose-mpeg4-2-2d
                 propose MPEG4 AVC/H.264 HP / Level 4.2 TS for 2D Videos only

         -x3   --propose-mpeg4-2-3d
                 propose MPEG4 AVC/H.264 HP / Level 4.2 TS for 3D Videos only

         -xo   --propose-mpeg4-2-st
                 propose MPEG4 AVC/H.264 Stereo HP / Level 4.2 TS only

         -x4   --propose-hevc
                 propose HEVC H.265 Main Profile / Level 5.1 TS only

         -x5   --propose-hevc10
                 propose HEVC H.265 Main 10 Profile / Level 5.1 TS only

         -xr   --propose-rle
                 propose RLE lossless TS
                 and all uncompressed transfer syntaxes

         -xd   --propose-deflated
                 propose deflated explicit VR little endian TS
                 and all uncompressed transfer syntaxes

       network host access control (tcp wrapper):

         -ac   --access-full
                 accept connections from any host (default)

         +ac   --access-control
                 enforce host access control rules

       other network options:

         -to   --timeout  [s]econds: integer (default: unlimited)
                 timeout for connection requests

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes
                 (default: use value from configuration file)

         -dhl  --disable-host-lookup
                 disable hostname lookup

               --refuse
                 refuse association

               --reject
                 reject association if no implementation class UID

               --ignore
                 ignore store data, receive but do not store

         # This option causes dcmqrscp database back-end to ignore
         # all images received by the Storage Service Class.  All images
         # will be received from the network but not stored to disk nor
         # recorded in database.

         -up   --uid-padding
                 silently correct space-padded UIDs

   processing options
       specific character set:

         # the following options require support from an underlying character
         # encoding library (see output of --version on which one is available)

         +Cr   --use-request-charset
                 try to convert all element values that are affected by
                 Specific Character Set (0008,0005) to the one specified
                 in the request data set, fall back to the one specified
                 via --convert-to-xxx if that is not possible
                 (default, unless overridden by config file)

         -Cr   --override-charset
                 convert affected element values to the character set
                 specified via --convert-to-xxx, ignoring the one
                 specified in the request

         +A7   --convert-to-ascii
                 convert affected element values to 7-bit ASCII
                 (default, unless overridden by config file)

         +U8   --convert-to-utf8
                 convert affected element values to UTF-8

         +L1   --convert-to-latin1
                 convert affected element values to ISO 8859-1

         +C    --convert-to-charset  [c]harset: string
                 convert affected element values to the character set
                 specified by the DICOM defined term c

         -Ct   --transliterate
                 try to approximate characters that cannot be
                 represented through similar looking characters

         -Cd   --discard-illegal
                 discard characters that cannot be represented
                 in destination character set

   output options
       bit preserving mode:

         -B    --normal
                 allow implicit format conversions (default)

         +B    --bit-preserving
                 write data exactly as read

       output file format:

         +F    --write-file
                 write file format (default)

         -F    --write-dataset
                 write data set without file meta information

       output transfer syntax:
       (not with --bit-preserving or compressed transmission)

         +t=   --write-xfer-same
                 write with same TS as input (default)

         +te   --write-xfer-little
                 write with explicit VR little endian TS

         +tb   --write-xfer-big
                 write with explicit VR big endian TS

         +ti   --write-xfer-implicit
                 write with implicit VR little endian TS

         +td   --write-xfer-deflated
                 write with deflated explicit VR little endian TS

       post-1993 value representations:

         +u    --enable-new-vr
                 enable support for new VRs (UN/UT) (default)

         -u    --disable-new-vr
                 disable support for new VRs, convert to OB

       group length encoding (not with --bit-preserving):

         +g=   --group-length-recalc
                 recalculate group lengths if present (default)

         +g    --group-length-create
                 always write with group length elements

         -g    --group-length-remove
                 always write without group length elements

       length encoding in sequences and items (not with --bit-preserving):

         +e    --length-explicit
                 write with explicit lengths (default)

         -e    --length-undefined
                 write with undefined lengths

       data set trailing padding (not with --write-dataset or --bit-preserving):

         -p    --padding-off
                 no padding (default)

         +p    --padding-create  [f]ile-pad [i]tem-pad: integer
                 align file on multiple of f bytes
                 and items on multiple of i bytes

       deflate compression level (only with --propose-deflated or
       --write-xfer-deflated/same):

         +cl   --compression-level  [l]evel: integer (default: 6)
                 0=uncompressed, 1=fastest, 9=best compression

NOTES
   Operation
       dcmqrscp waits for another application to connect at  the  presentation
       address   (port   number)  specified  in  its  configuration  file  (or
       overridden  by  a  command  line  option).  When  another   application
       connects,  dcmqrscp  expects  it  to  be a DICOM application and to use
       calling and called AE  Titles  specified  in  dcmqrscp's  configuration
       file.  dcmqrscp will accept associations with Presentation Contexts for
       SOP Classes of the Verification Service Class,  Storage  Service  Class
       and  Query/Retrieve  Service  Class.  Associations  will be rejected or
       Presentation Contexts will be refused if the peer application does  not
       have  appropriate  access rights as specified by dcmqrscp configuration
       file.

       dcmqrscp will receive images on Presentation Contexts  of  the  Storage
       Service  Class,  write  them  to a configurable storage area associated
       with the class AE title, extract attributes from these images and store
       them  in  a database. dcmqrscp will receive query requests and generate
       query responses on Presentation Contexts of the Query/Retrieve  Service
       class.  dcmqrscp  will  receive retrieve requests and generate retrieve
       responses on Presentation Contexts of the Query/Retrieve Service class.
       dcmqrscp   will   also   process   C-CANCEL   messages   to   interrupt
       query/retrieve operations.

       Under normal operations dcmqrscp will never exit, it keeps  on  waiting
       for new associations until killed.

   DICOM Conformance
   SCU Conformance
       The dcmqrscp application supports the following SOP Classes as an SCU:

       VerificationSOPClass                                 1.2.840.10008.1.1

       RETIRED_StoredPrintStorage                           1.2.840.10008.5.1.1.27
       RETIRED_HardcopyGrayscaleImageStorage                1.2.840.10008.5.1.1.29
       RETIRED_HardcopyColorImageStorage                    1.2.840.10008.5.1.1.30
       ComputedRadiographyImageStorage                      1.2.840.10008.*******.1.1
       DigitalXRayImageStorageForPresentation               1.2.840.10008.*******.1.1.1
       DigitalXRayImageStorageForProcessing                 1.2.840.10008.*******.1.1.1.1
       DigitalMammographyXRayImageStorageForPresentation    1.2.840.10008.*******.1.1.2
       DigitalMammographyXRayImageStorageForProcessing      1.2.840.10008.*******.1.1.2.1
       DigitalIntraOralXRayImageStorageForPresentation      1.2.840.10008.*******.1.1.3
       DigitalIntraOralXRayImageStorageForProcessing        1.2.840.10008.*******.1.1.3.1
       CTImageStorage                                       1.2.840.10008.*******.1.2
       EnhancedCTImageStorage                               1.2.840.10008.*******.1.2.1
       LegacyConvertedEnhancedCTImageStorage                1.2.840.10008.*******.1.2.2
       RETIRED_UltrasoundMultiframeImageStorage             1.2.840.10008.*******.1.3
       UltrasoundMultiframeImageStorage                     1.2.840.10008.*******.1.3.1
       MRImageStorage                                       1.2.840.10008.*******.1.4
       EnhancedMRImageStorage                               1.2.840.10008.*******.1.4.1
       MRSpectroscopyStorage                                1.2.840.10008.*******.1.4.2
       EnhancedMRColorImageStorage                          1.2.840.10008.*******.1.4.3
       LegacyConvertedEnhancedMRImageStorage                1.2.840.10008.*******.1.4.4
       RETIRED_NuclearMedicineImageStorage                  1.2.840.10008.*******.1.5
       RETIRED_UltrasoundImageStorage                       1.2.840.10008.*******.1.6
       UltrasoundImageStorage                               1.2.840.10008.*******.1.6.1
       EnhancedUSVolumeStorage                              1.2.840.10008.*******.1.6.2
       SecondaryCaptureImageStorage                         1.2.840.10008.*******.1.7
       MultiframeSingleBitSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.1
       MultiframeGrayscaleByteSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.2
       MultiframeGrayscaleWordSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.3
       MultiframeTrueColorSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.4
       RETIRED_StandaloneOverlayStorage                     1.2.840.10008.*******.1.8
       RETIRED_StandaloneCurveStorage                       1.2.840.10008.*******.1.9
       TwelveLeadECGWaveformStorage                         1.2.840.10008.*******.1.9.1.1
       GeneralECGWaveformStorage                            1.2.840.10008.*******.1.9.1.2
       AmbulatoryECGWaveformStorage                         1.2.840.10008.*******.1.9.1.3
       HemodynamicWaveformStorage                           1.2.840.10008.*******.1.9.2.1
       CardiacElectrophysiologyWaveformStorage              1.2.840.10008.*******.1.9.3.1
       BasicVoiceAudioWaveformStorage                       1.2.840.10008.*******.1.9.4.1
       GeneralAudioWaveformStorage                          1.2.840.10008.*******.1.9.4.2
       ArterialPulseWaveformStorage                         1.2.840.10008.*******.1.9.5.1
       RespiratoryWaveformStorage                           1.2.840.10008.*******.1.9.6.1
       RETIRED_StandaloneModalityLUTStorage                 1.2.840.10008.*******.1.10
       RETIRED_StandaloneVOILUTStorage                      1.2.840.10008.*******.1.11
       GrayscaleSoftcopyPresentationStateStorage            1.2.840.10008.*******.1.11.1
       ColorSoftcopyPresentationStateStorage                1.2.840.10008.*******.1.11.2
       PseudoColorSoftcopyPresentationStateStorage          1.2.840.10008.*******.1.11.3
       BlendingSoftcopyPresentationStateStorage             1.2.840.10008.*******.1.11.4
       XAXRFGrayscaleSoftcopyPresentationStateStorage       1.2.840.10008.*******.1.11.5
       XRayAngiographicImageStorage                         1.2.840.10008.*******.1.12.1
       EnhancedXAImageStorage                               1.2.840.10008.*******.********
       XRayRadiofluoroscopicImageStorage                    1.2.840.10008.*******.1.12.2
       EnhancedXRFImageStorage                              1.2.840.10008.*******.********
       RETIRED_XRayAngiographicBiPlaneImageStorage          1.2.840.10008.*******.1.12.3
       XRay3DAngiographicImageStorage                       1.2.840.10008.*******.********
       XRay3DCraniofacialImageStorage                       1.2.840.10008.*******.********
       BreastTomosynthesisImageStorage                      1.2.840.10008.*******.********
       IntravascularOpt.Coh.Tom.ImageStorageForPresentation 1.2.840.10008.*******.1.14.1
       IntravascularOpt.Coh.Tom.ImageStorageForProcessing   1.2.840.10008.*******.1.14.2
       NuclearMedicineImageStorage                          1.2.840.10008.*******.1.20
       RawDataStorage                                       1.2.840.10008.*******.1.66
       SpatialRegistrationStorage                           1.2.840.10008.*******.1.66.1
       SpatialFiducialsStorage                              1.2.840.10008.*******.1.66.2
       DeformableSpatialRegistrationStorage                 1.2.840.10008.*******.1.66.3
       SegmentationStorage                                  1.2.840.10008.*******.1.66.4
       SurfaceSegmentationStorage                           1.2.840.10008.*******.1.66.5
       RealWorldValueMappingStorage                         1.2.840.10008.*******.1.67
       SurfaceScanMeshStorage                               1.2.840.10008.*******.1.68.1
       SurfaceScanPointCloudStorage                         1.2.840.10008.*******.1.68.2
       RETIRED_VLImageStorage                               1.2.840.10008.*******.1.77.1
       VLEndoscopicImageStorage                             1.2.840.10008.*******.1.77.1.1
       VideoEndoscopicImageStorage                          1.2.840.10008.*******.1.77.1.1.1
       VLMicroscopicImageStorage                            1.2.840.10008.*******.1.77.1.2
       VideoMicroscopicImageStorage                         1.2.840.10008.*******.1.77.1.2.1
       VLSlideCoordinatesMicroscopicImageStorage            1.2.840.10008.*******.********
       VLPhotographicImageStorage                           1.2.840.10008.*******.********
       VideoPhotographicImageStorage                        1.2.840.10008.*******.********.1
       OphthalmicPhotography8BitImageStorage                1.2.840.10008.*******.********.1
       OphthalmicPhotography16BitImageStorage               1.2.840.10008.*******.********.2
       StereometricRelationshipStorage                      1.2.840.10008.*******.********.3
       OphthalmicTomographyImageStorage                     1.2.840.10008.*******.********.4
       VLWholeSlideMicroscopyImageStorage                   1.2.840.10008.*******.********
       RETIRED_VLMultiframeImageStorage                     1.2.840.10008.*******.1.77.2
       LensometryMeasurementsStorage                        1.2.840.10008.*******.1.78.1
       AutorefractionMeasurementsStorage                    1.2.840.10008.*******.1.78.2
       KeratometryMeasurementsStorage                       1.2.840.10008.*******.1.78.3
       SubjectiveRefractionMeasurementsStorage              1.2.840.10008.*******.1.78.4
       VisualAcuityMeasurementsStorage                      1.2.840.10008.*******.1.78.5
       SpectaclePrescriptionReportStorage                   1.2.840.10008.*******.1.78.6
       OphthalmicAxialMeasurementsStorage                   1.2.840.10008.*******.1.78.7
       IntraocularLensCalculationsStorage                   1.2.840.10008.*******.1.78.8
       MacularGridThicknessAndVolumeReportStorage           1.2.840.10008.*******.1.79.1
       OphthalmicVisualFieldStaticPerimetryMeasurementsSt.  1.2.840.10008.*******.1.80.1
       OphthalmicThicknessMapStorage                        1.2.840.10008.*******.1.81.1
       BasicTextSRStorage                                   1.2.840.10008.*******.1.88.11
       EnhancedSRStorage                                    1.2.840.10008.*******.1.88.22
       ComprehensiveSRStorage                               1.2.840.10008.*******.1.88.33
       Comprehensive3DSRStorage                             1.2.840.10008.*******.1.88.34
       ProcedureLogStorage                                  1.2.840.10008.*******.1.88.40
       MammographyCADSRStorage                              1.2.840.10008.*******.1.88.50
       KeyObjectSelectionDocumentStorage                    1.2.840.10008.*******.1.88.59
       ChestCADSRStorage                                    1.2.840.10008.*******.1.88.65
       XRayRadiationDoseSRStorage                           1.2.840.10008.*******.1.88.67
       ColonCADSRStorage                                    1.2.840.10008.*******.1.88.69
       ImplantationPlanSRDocumentStorage                    1.2.840.10008.*******.1.88.70
       EncapsulatedPDFStorage                               1.2.840.10008.*******.1.104.1
       EncapsulatedCDAStorage                               1.2.840.10008.*******.1.104.2
       PositronEmissionTomographyImageStorage               1.2.840.10008.*******.1.128
       LegacyConvertedEnhancedPETImageStorage               1.2.840.10008.*******.1.128.1
       RETIRED_StandalonePETCurveStorage                    1.2.840.10008.*******.1.129
       EnhancedPETImageStorage                              1.2.840.10008.*******.1.130
       BasicStructuredDisplayStorage                        1.2.840.10008.*******.1.131
       RTImageStorage                                       1.2.840.10008.*******.1.481.1
       RTDoseStorage                                        1.2.840.10008.*******.1.481.2
       RTStructureSetStorage                                1.2.840.10008.*******.1.481.3
       RTBeamsTreatmentRecordStorage                        1.2.840.10008.*******.1.481.4
       RTPlanStorage                                        1.2.840.10008.*******.1.481.5
       RTBrachyTreatmentRecordStorage                       1.2.840.10008.*******.1.481.6
       RTTreatmentSummaryRecordStorage                      1.2.840.10008.*******.1.481.7
       RTIonPlanStorage                                     1.2.840.10008.*******.1.481.8
       RTIonBeamsTreatmentRecordStorage                     1.2.840.10008.*******.1.481.9
       RTBeamsDeliveryInstructionStorage                    1.2.840.100********.34.7

       The  dcmqrscp application will propose presentation contexts for all of
       the abovementioned supported SOP Classes using the transfer syntaxes:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2

       This default behavior can be changed with the  --propose  options  (see
       above). Depending on these options, the following transfer syntaxes are
       supported:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax         1.2.840.10008.1.2.1.99
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2
       JPEGProcess1TransferSyntax                           1.2.840.10008.1.2.4.50
       JPEGProcess2_4TransferSyntax                         1.2.840.10008.1.2.4.51
       JPEGProcess14SV1TransferSyntax                       1.2.840.10008.1.2.4.70
       JPEGLSLosslessTransferSyntax                         1.2.840.10008.1.2.4.80
       JPEGLSLossyTransferSyntax                            1.2.840.10008.1.2.4.81
       JPEG2000LosslessOnlyTransferSyntax                   1.2.840.10008.1.2.4.90
       JPEG2000TransferSyntax                               1.2.840.10008.1.2.4.91
       MPEG2MainProfileAtMainLevelTransferSyntax            1.2.840.10008.1.2.4.100
       MPEG2MainProfileAtHighLevelTransferSyntax            1.2.840.10008.1.2.4.101
       MPEG4HighProfileLevel4_1TransferSyntax               1.2.840.10008.1.2.4.102
       MPEG4BDcompatibleHighProfileLevel4_1TransferSyntax   1.2.840.10008.1.2.4.103
       MPEG4HighProfileLevel4_2_For2DVideoTransferSyntax    1.2.840.10008.1.2.4.104
       MPEG4HighProfileLevel4_2_For3DVideoTransferSyntax    1.2.840.10008.1.2.4.105
       MPEG4StereoHighProfileLevel4_2TransferSyntax         1.2.840.10008.1.2.4.106
       HEVCMainProfileLevel5_1TransferSyntax                1.2.840.10008.1.2.4.107
       HEVCMain10ProfileLevel5_1TransferSyntax              1.2.840.10008.1.2.4.108
       RLELosslessTransferSyntax                            1.2.840.10008.1.2.5

       The dcmqrscp application does not support extended negotiation.

       Presentation contexts for other SOP Classes may be configured  via  the
       --assoc-config-file  option  by  selecting  an  appropriate profile for
       outgoing associations. The selected profile  will  completely  override
       the above mentioned default configuration.

   SCP Conformance
       The dcmqrscp application supports the following SOP Classes as an SCP:

       VerificationSOPClass                                 1.2.840.10008.1.1

       FINDPatientRootQueryRetrieveInformationModel         1.2.840.10008.*******.2.1.1
       FINDPatientStudyOnlyQueryRetrieveInformationModel    1.2.840.10008.*******.2.3.1
       FINDStudyRootQueryRetrieveInformationModel           1.2.840.10008.*******.2.2.1
       GETPatientRootQueryRetrieveInformationModel          1.2.840.10008.*******.2.1.3
       GETPatientStudyOnlyQueryRetrieveInformationModel     1.2.840.10008.*******.2.3.3
       GETStudyRootQueryRetrieveInformationModel            1.2.840.10008.*******.2.2.3
       MOVEPatientRootQueryRetrieveInformationModel         1.2.840.10008.*******.2.1.2
       MOVEPatientStudyOnlyQueryRetrieveInformationModel    1.2.840.10008.*******.2.3.2
       MOVEStudyRootQueryRetrieveInformationModel           1.2.840.10008.*******.2.2.2

       RETIRED_StoredPrintStorage                           1.2.840.10008.5.1.1.27
       RETIRED_HardcopyGrayscaleImageStorage                1.2.840.10008.5.1.1.29
       RETIRED_HardcopyColorImageStorage                    1.2.840.10008.5.1.1.30
       ComputedRadiographyImageStorage                      1.2.840.10008.*******.1.1
       DigitalXRayImageStorageForPresentation               1.2.840.10008.*******.1.1.1
       DigitalXRayImageStorageForProcessing                 1.2.840.10008.*******.1.1.1.1
       DigitalMammographyXRayImageStorageForPresentation    1.2.840.10008.*******.1.1.2
       DigitalMammographyXRayImageStorageForProcessing      1.2.840.10008.*******.1.1.2.1
       DigitalIntraOralXRayImageStorageForPresentation      1.2.840.10008.*******.1.1.3
       DigitalIntraOralXRayImageStorageForProcessing        1.2.840.10008.*******.1.1.3.1
       CTImageStorage                                       1.2.840.10008.*******.1.2
       EnhancedCTImageStorage                               1.2.840.10008.*******.1.2.1
       LegacyConvertedEnhancedCTImageStorage                1.2.840.10008.*******.1.2.2
       RETIRED_UltrasoundMultiframeImageStorage             1.2.840.10008.*******.1.3
       UltrasoundMultiframeImageStorage                     1.2.840.10008.*******.1.3.1
       MRImageStorage                                       1.2.840.10008.*******.1.4
       EnhancedMRImageStorage                               1.2.840.10008.*******.1.4.1
       MRSpectroscopyStorage                                1.2.840.10008.*******.1.4.2
       EnhancedMRColorImageStorage                          1.2.840.10008.*******.1.4.3
       LegacyConvertedEnhancedMRImageStorage                1.2.840.10008.*******.1.4.4
       RETIRED_NuclearMedicineImageStorage                  1.2.840.10008.*******.1.5
       RETIRED_UltrasoundImageStorage                       1.2.840.10008.*******.1.6
       UltrasoundImageStorage                               1.2.840.10008.*******.1.6.1
       EnhancedUSVolumeStorage                              1.2.840.10008.*******.1.6.2
       SecondaryCaptureImageStorage                         1.2.840.10008.*******.1.7
       MultiframeSingleBitSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.1
       MultiframeGrayscaleByteSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.2
       MultiframeGrayscaleWordSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.3
       MultiframeTrueColorSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.4
       RETIRED_StandaloneOverlayStorage                     1.2.840.10008.*******.1.8
       RETIRED_StandaloneCurveStorage                       1.2.840.10008.*******.1.9
       DRAFT_WaveformStorage                                1.2.840.10008.*******.1.9.1
       TwelveLeadECGWaveformStorage                         1.2.840.10008.*******.1.9.1.1
       GeneralECGWaveformStorage                            1.2.840.10008.*******.1.9.1.2
       AmbulatoryECGWaveformStorage                         1.2.840.10008.*******.1.9.1.3
       HemodynamicWaveformStorage                           1.2.840.10008.*******.1.9.2.1
       CardiacElectrophysiologyWaveformStorage              1.2.840.10008.*******.1.9.3.1
       BasicVoiceAudioWaveformStorage                       1.2.840.10008.*******.1.9.4.1
       GeneralAudioWaveformStorage                          1.2.840.10008.*******.1.9.4.2
       ArterialPulseWaveformStorage                         1.2.840.10008.*******.1.9.5.1
       RespiratoryWaveformStorage                           1.2.840.10008.*******.1.9.6.1
       RETIRED_StandaloneModalityLUTStorage                 1.2.840.10008.*******.1.10
       RETIRED_StandaloneVOILUTStorage                      1.2.840.10008.*******.1.11
       GrayscaleSoftcopyPresentationStateStorage            1.2.840.10008.*******.1.11.1
       ColorSoftcopyPresentationStateStorage                1.2.840.10008.*******.1.11.2
       PseudoColorSoftcopyPresentationStateStorage          1.2.840.10008.*******.1.11.3
       BlendingSoftcopyPresentationStateStorage             1.2.840.10008.*******.1.11.4
       XAXRFGrayscaleSoftcopyPresentationStateStorage       1.2.840.10008.*******.1.11.5
       GrayscalePlanarMPRVolumetricPresentationStateStorage 1.2.840.10008.*******.1.11.6
       CompositingPlanarMPRVolumetricPresent.StateStorage   1.2.840.10008.*******.1.11.7
       AdvancedBlendingPresentationStateStorage             1.2.840.10008.*******.1.11.8
       VolumeRenderingVolumetricPresentationStateStorage    1.2.840.10008.*******.1.11.9
       SegmentedVolumeRenderingVolumetricPres.StateStorage  1.2.840.10008.*******.1.11.10
       MultipleVolumeRenderingVolumetricPres.StateStorage   1.2.840.10008.*******.1.11.11
       XRayAngiographicImageStorage                         1.2.840.10008.*******.1.12.1
       EnhancedXAImageStorage                               1.2.840.10008.*******.********
       XRayRadiofluoroscopicImageStorage                    1.2.840.10008.*******.1.12.2
       EnhancedXRFImageStorage                              1.2.840.10008.*******.********
       RETIRED_XRayAngiographicBiPlaneImageStorage          1.2.840.10008.*******.1.12.3
       XRay3DAngiographicImageStorage                       1.2.840.10008.*******.********
       XRay3DCraniofacialImageStorage                       1.2.840.10008.*******.********
       BreastTomosynthesisImageStorage                      1.2.840.10008.*******.********
       BreastProjectionXRayImageStorageForPresentation      1.2.840.10008.*******.********
       BreastProjectionXRayImageStorageForProcessing        1.2.840.10008.*******.********
       IntravascularOpt.Coh.Tom.ImageStorageForPresentation 1.2.840.10008.*******.1.14.1
       IntravascularOpt.Coh.Tom.ImageStorageForProcessing   1.2.840.10008.*******.1.14.2
       NuclearMedicineImageStorage                          1.2.840.10008.*******.1.20
       ParametricMapStorage                                 1.2.840.10008.*******.1.30
       RawDataStorage                                       1.2.840.10008.*******.1.66
       SpatialRegistrationStorage                           1.2.840.10008.*******.1.66.1
       SpatialFiducialsStorage                              1.2.840.10008.*******.1.66.2
       DeformableSpatialRegistrationStorage                 1.2.840.10008.*******.1.66.3
       SegmentationStorage                                  1.2.840.10008.*******.1.66.4
       SurfaceSegmentationStorage                           1.2.840.10008.*******.1.66.5
       TractographyResultsStorage                           1.2.840.10008.*******.1.66.6
       RealWorldValueMappingStorage                         1.2.840.10008.*******.1.67
       SurfaceScanMeshStorage                               1.2.840.10008.*******.1.68.1
       SurfaceScanPointCloudStorage                         1.2.840.10008.*******.1.68.2
       RETIRED_VLImageStorage                               1.2.840.10008.*******.1.77.1
       VLEndoscopicImageStorage                             1.2.840.10008.*******.1.77.1.1
       VideoEndoscopicImageStorage                          1.2.840.10008.*******.1.77.1.1.1
       VLMicroscopicImageStorage                            1.2.840.10008.*******.1.77.1.2
       VideoMicroscopicImageStorage                         1.2.840.10008.*******.1.77.1.2.1
       VLSlideCoordinatesMicroscopicImageStorage            1.2.840.10008.*******.********
       VLPhotographicImageStorage                           1.2.840.10008.*******.********
       VideoPhotographicImageStorage                        1.2.840.10008.*******.********.1
       OphthalmicPhotography8BitImageStorage                1.2.840.10008.*******.********.1
       OphthalmicPhotography16BitImageStorage               1.2.840.10008.*******.********.2
       StereometricRelationshipStorage                      1.2.840.10008.*******.********.3
       OphthalmicTomographyImageStorage                     1.2.840.10008.*******.********.4
       WideFieldOphthalmicPhoto.Stereogr.Proj.ImageStorage  1.2.840.10008.*******.********.5
       WideFieldOphthalmicPhotogr.3DCoordinatesImageStorage 1.2.840.10008.*******.********.6
       OphthalmicOpticalCoherenceTomogr.EnFaceImageStorage  1.2.840.10008.*******.********.7
       OphthalmicOpticalCoh.Tomogr.BscanVolumeAnalysisStor. 1.2.840.10008.*******.********.8
       VLWholeSlideMicroscopyImageStorage                   1.2.840.10008.*******.********
       RETIRED_VLMultiframeImageStorage                     1.2.840.10008.*******.1.77.2
       LensometryMeasurementsStorage                        1.2.840.10008.*******.1.78.1
       AutorefractionMeasurementsStorage                    1.2.840.10008.*******.1.78.2
       KeratometryMeasurementsStorage                       1.2.840.10008.*******.1.78.3
       SubjectiveRefractionMeasurementsStorage              1.2.840.10008.*******.1.78.4
       VisualAcuityMeasurementsStorage                      1.2.840.10008.*******.1.78.5
       SpectaclePrescriptionReportStorage                   1.2.840.10008.*******.1.78.6
       OphthalmicAxialMeasurementsStorage                   1.2.840.10008.*******.1.78.7
       IntraocularLensCalculationsStorage                   1.2.840.10008.*******.1.78.8
       MacularGridThicknessAndVolumeReportStorage           1.2.840.10008.*******.1.79.1
       OphthalmicVisualFieldStaticPerimetryMeasurementsSt.  1.2.840.10008.*******.1.80.1
       OphthalmicThicknessMapStorage                        1.2.840.10008.*******.1.81.1
       CornealTopographyMapStorage                          1.2.840.10008.*******.1.82.1
       DRAFT_SRTextStorage                                  1.2.840.10008.*******.1.88.1
       DRAFT_SRAudioStorage                                 1.2.840.10008.*******.1.88.2
       DRAFT_SRDetailStorage                                1.2.840.10008.*******.1.88.3
       DRAFT_SRComprehensiveStorage                         1.2.840.10008.*******.1.88.4
       BasicTextSRStorage                                   1.2.840.10008.*******.1.88.11
       EnhancedSRStorage                                    1.2.840.10008.*******.1.88.22
       ComprehensiveSRStorage                               1.2.840.10008.*******.1.88.33
       Comprehensive3DSRStorage                             1.2.840.10008.*******.1.88.34
       ExtensibleSRStorage                                  1.2.840.10008.*******.1.88.35
       ProcedureLogStorage                                  1.2.840.10008.*******.1.88.40
       MammographyCADSRStorage                              1.2.840.10008.*******.1.88.50
       KeyObjectSelectionDocumentStorage                    1.2.840.10008.*******.1.88.59
       ChestCADSRStorage                                    1.2.840.10008.*******.1.88.65
       XRayRadiationDoseSRStorage                           1.2.840.10008.*******.1.88.67
       RadiopharmaceuticalRadiationDoseSRStorage            1.2.840.10008.*******.1.88.68
       ColonCADSRStorage                                    1.2.840.10008.*******.1.88.69
       ImplantationPlanSRDocumentStorage                    1.2.840.10008.*******.1.88.70
       AcquisitionContextSRStorage                          1.2.840.10008.*******.1.88.71
       SimplifiedAdultEchoSRStorage                         1.2.840.10008.*******.1.88.72
       PatientRadiationDoseSRStorage                        1.2.840.10008.*******.1.88.73
       PlannedImagingAgentAdministrationSRStorage           1.2.840.10008.*******.1.88.74
       PerformedImagingAgentAdministrationSRStorage         1.2.840.10008.*******.1.88.75
       ContentAssessmentResultsStorage                      1.2.840.10008.*******.1.90.1
       EncapsulatedPDFStorage                               1.2.840.10008.*******.1.104.1
       EncapsulatedCDAStorage                               1.2.840.10008.*******.1.104.2
       EncapsulatedSTLStorage                               1.2.840.10008.*******.1.104.3
       PositronEmissionTomographyImageStorage               1.2.840.10008.*******.1.128
       LegacyConvertedEnhancedPETImageStorage               1.2.840.10008.*******.1.128.1
       RETIRED_StandalonePETCurveStorage                    1.2.840.10008.*******.1.129
       EnhancedPETImageStorage                              1.2.840.10008.*******.1.130
       BasicStructuredDisplayStorage                        1.2.840.10008.*******.1.131
       CTPerformedProcedureProtocolStorage                  1.2.840.10008.*******.1.200.2
       RTImageStorage                                       1.2.840.10008.*******.1.481.1
       RTDoseStorage                                        1.2.840.10008.*******.1.481.2
       RTStructureSetStorage                                1.2.840.10008.*******.1.481.3
       RTBeamsTreatmentRecordStorage                        1.2.840.10008.*******.1.481.4
       RTPlanStorage                                        1.2.840.10008.*******.1.481.5
       RTBrachyTreatmentRecordStorage                       1.2.840.10008.*******.1.481.6
       RTTreatmentSummaryRecordStorage                      1.2.840.10008.*******.1.481.7
       RTIonPlanStorage                                     1.2.840.10008.*******.1.481.8
       RTIonBeamsTreatmentRecordStorage                     1.2.840.10008.*******.1.481.9
       RTPhysicianIntentStorage                             1.2.840.10008.*******.1.481.10
       RTSegmentAnnotationStorage                           1.2.840.10008.*******.1.481.11
       RTRadiationSetStorage                                1.2.840.10008.*******.1.481.12
       CArmPhotonElectronRadiationStorage                   1.2.840.10008.*******.1.481.13
       DICOS_CTImageStorage                                 1.2.840.10008.*******.1.501.1
       DICOS_DigitalXRayImageStorageForPresentation         1.2.840.10008.*******.1.501.2.1
       DICOS_DigitalXRayImageStorageForProcessing           1.2.840.10008.*******.1.501.2.2
       DICOS_ThreatDetectionReportStorage                   1.2.840.10008.*******.1.501.3
       DICOS_2DAITStorage                                   1.2.840.10008.*******.1.501.4
       DICOS_3DAITStorage                                   1.2.840.10008.*******.1.501.5
       DICOS_QuadrupoleResonanceStorage                     1.2.840.10008.*******.1.501.6
       DICONDE_EddyCurrentImageStorage                      1.2.840.10008.*******.1.601.1
       DICONDE_EddyCurrentMultiframeImageStorage            1.2.840.10008.*******.1.601.2
       DRAFT_RTBeamsDeliveryInstructionStorage              1.2.840.100********.34.1
       RTBeamsDeliveryInstructionStorage                    1.2.840.100********.34.7
       RTBrachyApplicationSetupDeliveryInstructionStorage   1.2.840.100********.34.10

       PrivateShutdownSOPClass                              1.2.276.0.7230010.3.4.1915765545.18030.*********.0

       The  dcmqrscp  application will accept presentation contexts for all of
       the above mentioned supported SOP Classes using  any  of  the  transfer
       syntaxes:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2

       The  dcmqrscp  application  will  prefer  transfer  syntaxes  having an
       explicit  encoding  over  the  default  implicit  transfer  syntax.  If
       dcmqrscp   is   running   on   big-endian   hardware   it  will  prefer
       BigEndianExplicit to LittleEndianExplicit  transfer  syntax  (and  vice
       versa).  This  behavior  can  be changed with the --prefer options (see
       above). Depending on these options, the following transfer syntaxes are
       supported:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax         1.2.840.10008.1.2.1.99
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2
       JPEGProcess1TransferSyntax                           1.2.840.10008.1.2.4.50
       JPEGProcess2_4TransferSyntax                         1.2.840.10008.1.2.4.51
       JPEGProcess14SV1TransferSyntax                       1.2.840.10008.1.2.4.70
       JPEGLSLosslessTransferSyntax                         1.2.840.10008.1.2.4.80
       JPEGLSLossyTransferSyntax                            1.2.840.10008.1.2.4.81
       JPEG2000LosslessOnlyTransferSyntax                   1.2.840.10008.1.2.4.90
       JPEG2000TransferSyntax                               1.2.840.10008.1.2.4.91
       MPEG2MainProfileAtMainLevelTransferSyntax            1.2.840.10008.1.2.4.100
       MPEG2MainProfileAtHighLevelTransferSyntax            1.2.840.10008.1.2.4.101
       MPEG4HighProfileLevel4_1TransferSyntax               1.2.840.10008.1.2.4.102
       MPEG4BDcompatibleHighProfileLevel4_1TransferSyntax   1.2.840.10008.1.2.4.103
       MPEG4HighProfileLevel4_2_For2DVideoTransferSyntax    1.2.840.10008.1.2.4.104
       MPEG4HighProfileLevel4_2_For3DVideoTransferSyntax    1.2.840.10008.1.2.4.105
       MPEG4StereoHighProfileLevel4_2TransferSyntax         1.2.840.10008.1.2.4.106
       HEVCMainProfileLevel5_1TransferSyntax                1.2.840.10008.1.2.4.107
       HEVCMain10ProfileLevel5_1TransferSyntax              1.2.840.10008.1.2.4.108
       RLELosslessTransferSyntax                            1.2.840.10008.1.2.5

       The dcmqrscp application does not support extended negotiation.

       Presentation  contexts  for other SOP Classes may be configured via the
       --assoc-config-file option by  selecting  an  appropriate  profile  for
       incoming  associations.  The  selected  profile  will override only the
       above mentioned storage SOP classes, dcmqrscp will accept contexts with
       the  above  mentioned  FIND, GET, MOVE and private shutdown SOP Classes
       additionally to the ones defined in the selected profile.  Please  note
       that  an  appropriate  profile for role negotiation must be defined and
       used for enabling GET support.

   Query Keys
       The dcmqrscp application supports the  following  attributes  as  query
       keys.

       Query/Retrieve Level: PATIENT (or STUDY for the Study Root Q/R model)

       (0010,0010) PatientName
       (0010,0020) PatientID
       (0010,0021) IssuerOfPatientID
       (0010,0030) PatientBirthDate
       (0010,0032) PatientBirthTime
       (0010,0040) PatientSex
       (0010,1000) OtherPatientIDs (retired)
       (0010,1001) OtherPatientNames
       (0010,2160) EthnicGroup
       (0010,4000) PatientComments

       Query/Retrieve Level: STUDY

       (0008,0020) StudyDate
       (0008,0030) StudyTime
       (0008,0050) AccessionNumber
       (0008,0090) ReferringPhysicianName
       (0008,1030) StudyDescription
       (0008,1060) NameOfPhysiciansReadingStudy
       (0008,1080) AdmittingDiagnosesDescription
       (0010,1010) PatientAge
       (0010,1020) PatientSize
       (0010,1030) PatientWeight
       (0010,2180) Occupation
       (0010,21B0) AdditionalPatientHistory
       (0020,000D) StudyInstanceUID
       (0020,0010) StudyID
       (0020,1070) RETIRED_OtherStudyNumbers

       Query/Retrieve Level: SERIES

       (0008,0060) Modality
       (0020,000E) SeriesInstanceUID
       (0020,0011) SeriesNumber

       Query/Retrieve Level: IMAGE

       (0008,0018) SOPInstanceUID
       (0020,0013) InstanceNumber

   Configuration
       The  dcmqrscp  program  uses the same configuration file as the dcmqrti
       program. See the documentation on configuration for more information.

   Access Control
       When compiled on Unix platforms with TCP  wrapper  support,  host-based
       access  control  can  be enabled with the --access-control command line
       option. In this case the access control rules defined in  the  system's
       host  access  control  tables  for  dcmqrscp  are enforced. The default
       locations of the host access control tables  are  /etc/hosts.allow  and
       /etc/hosts.deny. Further details are described in hosts_access(5).

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmqrscp utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

FILES
       <docdir>/dcmqrcnf.txt - configuration information
       <docdir>/dcmqrset.txt - setup information
       <etcdir>/dcmqrscp.cfg - example configuration file
       <etcdir>/dcmqrprf.cfg  -  example  configuration  file  for association
       profiles

SEE ALSO
       dcmqridx(1), dcmqrti(1)

COPYRIGHT
       Copyright (C) 1993-2019 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmqrscp(1)
