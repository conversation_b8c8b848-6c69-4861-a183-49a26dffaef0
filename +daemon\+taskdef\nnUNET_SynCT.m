classdef nnUNET_SynCT < daemon.taskdef.nnUNETInference
    properties
        
    end

    methods
        function obj = nnUNET_SynCT(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function Factory_PT2SynCT(taskdeffolder)
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.hector2022\';
            datesetnums ={'456'};
            modalities = {'CTm'};
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_HNREOS_PT2' modality TrainerName]), ...
                 datesetnums, modalities, 'UniformOutput',false);
            
            tasksdeffile = [taskdeffolder 'tasks_PT2CT.def'];
            subfoldername = 'nnUNET_PT2CTm';
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                InputImage = "../image.nii.gz";
                OutputFile ='../SynCT';
                dependtask ="../DcmConverter/DCMCONVERT_PT/PT.[SeriesInstanceUID].tsk";
                dependency = struct("filename", InputImage,...
                    "taskfilename", dependtask);
                taskdefname = ['PT_nnUNET_HNREOS_PT2' modality];
                obj = daemon.taskdef.nnUNET_BraTS2021_GBMPreOp({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname}, ...
                    {'ModelOptionFile', ModelOptionFile}, ...
                    {'InputImage',  InputImage}, ...
                    {'OutputFile', OutputFile});
                TaskDoneIndicator=[modality '.cfg'];
                defaultcfg = struct("DBTableName", "PT",...
                  "CustomSubFolder", [subfoldername], ...
                  "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
                obj.SetDef_struct(defaultcfg);
                obj.AddDependency(dependency);
                obj.writeJson;
                obj.AddToTasksDefFile( tasksdeffile);
            end
        end
    end
end