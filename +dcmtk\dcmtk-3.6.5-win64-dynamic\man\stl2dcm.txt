stl2dcm(1)                        OFFIS DCMTK                       stl2dcm(1)



NAME
       stl2dcm - Encapsulate STL file into DICOM file format


SYNOPSIS
       stl2dcm [options] stlfile-in dcmfile-out

DESCRIPTION
       The  stl2dcm  utility  reads  a STL file (stlfile-in), converts it to a
       DICOM Encapsulated STL Storage SOP instance and  stores  the  converted
       data to an output file (dcmfile-out).

PARAMETERS
       stlfile-in   STL input filename to be encapsulated

       dcmfile-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   DICOM document options
       document title:

         +t   --title  [t]itle: string (default: empty)
                document title

         +cn  --concept-name  [CSD] [CV] [CM]: string (default: empty)
                coded representation of document title defined by coding
                scheme designator CSD, code value CV and code meaning CM

       patient data:

         +pn  --patient-name  [n]ame: string
                patient's name in DICOM PN syntax

         +pi  --patient-id  [i]d: string
                patient identifier

         +pb  --patient-birthdate  [d]ate: string (YYYYMMDD)
                patient's birth date

         +ps  --patient-sex  [s]ex: string (M, F or O)
                patient's sex

       study and series:

         +sg  --generate
                generate new study and series UIDs (default)

         +st  --study-from  [f]ilename: string
                read patient/study data from DICOM file

         +se  --series-from  [f]ilename: string
                read patient/study/series data from DICOM file

       instance number:

         +i1  --instance-one
                use instance number 1 (default, not with +se)

         +ii  --instance-inc
                increment instance number (only with +se)

         +is  --instance-set [i]nstance number: integer
                use instance number i

       burned-in annotation:

         +an  --annotation-yes
                document contains patient identifying data (default)

         -an  --annotation-no
                document does not contain patient identifying data

       enhanced general equipment:

         +mn  --manufacturer  [n]ame: string
                manufacturer's name

         +mm  --manufacturer-model  [n]ame: string
                manufacturer's model name

         +ds  --device-serial  [n]umber: string
                device serial number

         +sv  --software-versions  [v]ersions: string
                software versions

       3d model measurement units:

         +mu  --measurement-units  [CSD] [CV] [CM]: string
                measurement units with coding scheme designator CSD,
                code value CV and code meaning CM (default: UCUM, um, um)

   processing options
       other processing options:

         -k   --key  [k]ey: gggg,eeee="str", path or dictionary name="str"
                add further attribute

   output options
       output file format:

         +F   --write-file
                write file format (default)

         -F   --write-dataset
                write data set without file meta information

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

       data set trailing padding (not with --write-dataset):

         -p   --padding-off
                no padding (implicit if --write-dataset)

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

EXIT CODES
       The stl2dcm utility uses the following  exit  codes  when  terminating.
       This  enables  the  user  to  check  for the reason why the application
       terminated.

   general
       EXITCODE_NO_ERROR                 0
       EXITCODE_COMMANDLINE_SYNTAX_ERROR 1
       EXITCODE_MEMORY_EXHAUSTED         4

   input file errors
       EXITCODE_CANNOT_READ_INPUT_FILE   20
       EXITCODE_NO_INPUT_FILES           21
       EXITCODE_INVALID_INPUT_FILE       22

   output file errors
       EXITCODE_CANNOT_WRITE_OUTPUT_FILE 40

ENVIRONMENT
       The stl2dcm utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

COPYRIGHT
       Copyright  (C)  2018  by  OFFIS  e.V.,  Escherweg  2,  26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     stl2dcm(1)
