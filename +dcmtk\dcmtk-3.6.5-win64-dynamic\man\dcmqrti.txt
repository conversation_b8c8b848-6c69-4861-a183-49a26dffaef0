dcmqrti(1)                        OFFIS DCMTK                       dcmqrti(1)



NAME
       dcmqrti - The Terminal Initiator Telnet Client Program


SYNOPSIS
       dcmqrti [options] peer...

DESCRIPTION
       The  dcmqrti  program  (telnet  initiator)  is an interactive character
       based program intended to be used  for  examining  the  dcmqrscp  image
       databases and sending images contained within these databases to Vendor
       nodes.  During  a  DICOM  Demonstration  the  dcmqrti  program  can  be
       activated by a Vendor by logging onto the computer running the dcmqrscp
       program. Each vendor will have their own login account and the  dcmqrti
       program will be started instead of a normal shell when they login.

       The  dcmqrti  program  takes  one main argument, the hostname or vendor
       symbolic name (from the VendorTable in the  configuration  file)  of  a
       Vendor.  It  then  searches in the configuration file for all AE titles
       associated with this hostname or vendor name,  and  all  storage  areas
       accessible  to  these AE titles. Thus only the accessible databases and
       peer applications discovered in the configuration file are available as
       choices within the dcmqrti user interface.

PARAMETERS
       peer  peer host name or symbolic name from cfg file

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

         -c    --config  [f]ilename: string
                 use specific configuration file
                 (default: /usr/local/etc/dcmqrscp.cfg)

   network options
         -to   --timeout  [s]econds: integer (default: unlimited)
                 timeout for connection requests

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -xi   --propose-implicit
                 propose implicit VR little endian TS only

         -aet  --aetitle  [a]etitle: string
                 set my AE title (default: TELNET_INITIATOR)

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes
                 (default: use value from configuration file)

   other options
         -u    --disable-new-vr
                 disable support for new VRs, convert to OB

         -rmt  --remote  [t]itle: string
                 connect to remote database defined in cfg file

dcmqrti_NOTES
   Commands
       All  commands can be abbreviated. An abbreviation is allowed if it does
       not conflict with another command.

   'help' Command
       The 'help' command gives a  summary  of  all  available  commands.  Its
       output  is  shown  underneath.  In  order to separate the examples from
       surrounding text, all examples are bracketed  by  dashed  lines.  These
       lines do not appear when running the program.

       ------------------------------------------------------------
       Command Summary:
       help                list this summary
       ?                   short help
       title [#]           list [set] current peer AE title
       database [#]        list [set] current database
       study [#]           list [set] current study
       series [#]          list [set] current series
       image [#]           list [set] current image
       display [#]         display current [specific] image
       send study [#]      send current [specific] study
       send series [#]     send current [specific] series
       send image [#]      send current [specific] image
       echo [#]            verify connectivity [# times]
       quit                quit program
       exit                synonym for quit
       ------------------------------------------------------------

   'title' Command
       The  'title'  command  without  an argument allows the user to list the
       known remote Application Entities (AE). An example  output  might  look
       like:

       ------------------------------------------------------------
       Peer AE Titles:
            Peer AE          HostName:PortNumber
       * 0) ACME1            (swallow:2001)
         1) ACME2            (swallow:2002)
         2) UNITED1          (kolibri:2001)
         3) UNITED2          (kolibri:2002)
       ------------------------------------------------------------

       The current peer AE is marked with an asterisk (*). Each peer AE has an
       index (second column) which can be used as an argument to  the  'title'
       command in order to set the current peer AE. The third column gives the
       AE title of the peer AE. The  fourth  column  shows  the  hostname  and
       TCP/IP port number of the peer AE.

       When  invoked  with an argument index, the 'title' command will set the
       current peer AE. The  dcmqrti  program  will  attempt  to  initiate  an
       association   to   the   current   peer   AE   when  asked  to  send  a
       study/series/image or to send an echo.

   'database' Command
       The 'database' command without an argument allows the user to list  the
       know local databases (these correspond to dcmqrscp's Application Entity
       Titles). An example output might look like:

       ------------------------------------------------------------
       Database Titles:
            Database
       * 0) COMMON
         1) ACME_QUERY
         2) ACME_STORE
         3) UNITED_QUERY
       ------------------------------------------------------------

       The current database is marked with an asterisk (*). Each database  has
       an  index  (second  column)  which  can  be  used as an argument to the
       'database' command in order to set  the  current  database.  The  third
       column  shows  the  name of the database (i.e. the dcmqrscp Application
       Entity title for the particular storage area).

       When invoked with an argument index, the 'database'  command  will  set
       the  current  database.  The  current database is used as the basis for
       further database specific actions.

   'study' Command
       The 'study' command with no argument lists the studies in  the  current
       database. An example output might look like:

       ------------------------------------------------------------
             Patient                        PatientID    StudyID
       *  0) JACKSON^ANDREW^^^              M4997106     20001
          1) GRANT^MARY^^^                  F4997108     20002
          2) ARTHUR^CHESTER^^^              M4997107     20003
          3) JEFFERSON^THOMAS^^^            M4997096     9465
          4) MADISON^DOLLY^^^               F4997097     9443

       4 Studies in Database: COMMON
       ------------------------------------------------------------

       The  current  study  is  marked with an asterisk (*). Each study has an
       index (second column) which can be used as an argument to  the  'study'
       command  in  order to set the current study. The third column shows the
       patient name, the fourth column the patient ID and the fifth column the
       study ID.

       When  invoked  with an argument index, the 'study' command will set the
       current study. The current study is used as the basis for further study
       specific actions.

   'series' Command
       The  'series'  command with no argument lists the series in the current
       study. An example output might look like:

       ------------------------------------------------------------
             Series Modality SeriesInstanceUID
       *  0) 1      MR       1.2.840.113654.2.3.1993.201
          1) 2      MR       1.2.840.113654.2.3.1993.202
          2) 3      MR       1.2.840.113654.2.3.1993.203
          3) 4      MR       1.2.840.113654.2.3.1993.204
          4) 5      MR       1.2.840.113654.2.3.1993.205

       5 Series in StudyID 05381,
         Patient: MONROE^JAMES^^^ (Database: COMMON)
       ------------------------------------------------------------

       The current series is marked with an asterisk (*). Each series  has  an
       index  (second column) which can be used as an argument to the 'series'
       command in order to set the current series. The third column shows  the
       series  number,  the  fourth  column the series modality, and the fifth
       column the series instance UID.

       When invoked with an argument index, the 'series' command will set  the
       current  series.  The  current  series is used as the basis for further
       series specific actions.

   'image' Command
       The 'image' command with no argument lists the images  in  the  current
       series. An example output might look like:

       ------------------------------------------------------------
             Image ImageInstanceUID
       *  0) 1     1.2.840.113654.2.3.1993.9.123.6.2674
          1) 2     1.2.840.113654.2.3.1993.9.123.6.2675
          2) 3     1.2.840.113654.2.3.1993.9.123.6.2676
          3) 4     1.2.840.113654.2.3.1993.9.123.6.2677
          4) 5     1.2.840.113654.2.3.1993.9.123.6.2678
          5) 6     1.2.840.113654.2.3.1993.9.123.6.2679
          6) 7     1.2.840.113654.2.3.1993.9.123.6.2680
          7) 8     1.2.840.113654.2.3.1993.9.123.6.2681
          8) 9     1.2.840.113654.2.3.1993.9.123.6.2682
          9) 10    1.2.840.113654.2.3.1993.9.123.6.2683
         10) 11    1.2.840.113654.2.3.1993.9.123.6.2684
         11) 12    1.2.840.113654.2.3.1993.9.123.6.2685
         12) 13    1.2.840.113654.2.3.1993.9.123.6.2686

       13 Images in MR Series, StudyID 05381,
         Patient: MONROE^JAMES^^^ (Database: COMMON)
       ------------------------------------------------------------

       The  current  image  is  marked with an asterisk (*). Each image has an
       index (second column) which can be used as an argument to  the  'image'
       command  in  order to set the current image. The third column shows the
       image number, and  the  fourth  column  the  image  instance  UID  (SOP
       Instance UID).

       When  invoked  with an argument index, the 'image' command will set the
       current image. The current image is used as the basis for further image
       specific actions.

   'display' Command
       The  display command serves no purpose in the current version of DCMTK.
       It was used in prior releases to request the  CTN  Display  Program  to
       display an image.

   'send' Command
       The  'send'  command allows a complete study/series or individual image
       to be stored on a remote AE. When this command is invoked, the  dcmqrti
       program  will  operate  as a SCU of the DICOM Storage Service Class and
       attempt to initiate an association with the current  peer  AE  (defined
       via  the  'title'  command).  Presentation  contexts  for  all relevant
       Storage SOP Classes will be proposed. An attempt will be made to  store
       all  specified  images.  If no association could be negotiated an error
       message will be printed.  If  an  storage  operation  fails  or  if  no
       appropriate presentation context is available and error message will be
       printed.

       The "send" command exists in three forms:

               send study [#]
               send series [#]
               send image [#]

       The 'study' keyword means send all images in the  current  study.  When
       invoked  with  an  argument  index,  the specified study in the current
       database will be stored. The 'series' keyword means send all images  in
       the  current series. When invoked with an argument index, the specified
       series in the current study will be stored. The 'image'  keyword  means
       send  the  current  image.  When  invoked  with  an argument index, the
       specified image in the current series will be stored.

       When an image is stored, a message will be printed of the form:

       ------------------------------------------------------------
       New Association Started (swallow:2001,ACME1)
       [MsgID 1] Store,
         PatientName: JACKSON^ANDREW^^^, StudyID: 20001,
         Series: 2, Modality: CR, Image: 1,
         Image UID: 1.2.840.113654.2.3.1993.9.123.6.1834
         0%________25%_________50%__________75%________100%
         --------------------------------------------------
       [MsgID 1] Complete [Status: Success]
       Released Association (swallow:2001,ACME1)
       ------------------------------------------------------------


   'echo' Command
       The 'echo' command allows the user  to  verify  connectivity  with  the
       current  peer  AE  (defined via the 'title' command). When invoked, the
       dcmqrti program acts as an SCU of the Verification Service Class.

       When invoked without an argument, only one C-ECHO message is generated.
       When  invoked with an argument, the specified number of C-ECHO messages
       will be sent. A message will be printed of the form:

       ------------------------------------------------------------
       New Association Started (localhost:2001,CMOVE)
       [MsgID 1] Echo, Complete [Status: Success]
       Released Association (localhost:2001,CMOVE)
       ------------------------------------------------------------

   'quit', 'exit' Commands
       The 'quit' and 'exit' commands have the same effect. They terminate the
       dcmqrti program.

   DICOM Conformance
       The  dcmqrti application supports the same set of SOP Classes as an SCU
       as the dcmqrscp application - see dcmqrscp documentation.

       The dcmqrti application will propose presentation contexts for  all  of
       the abovementioned supported SOP Classes using the transfer syntaxes:

       LittleEndianImplicitTransferSyntax  1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax  1.2.840.10008.1.2.1
       BigEndianExplicitTransferSyntax     1.2.840.10008.1.2.2

       The dcmqrti application does not support extended negotiation.

   Configuration
       The  dcmqrti  program  uses the same configuration file as the dcmqrscp
       program. See the documentation on configuration  for  more  information
       (dcmqrcnf.txt and the example configuration file dcmqrscp.cfg).

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmqrti utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

FILES
       <docdir>/dcmqrcnf.txt - configuration information
       <docdir>/dcmqrset.txt - setup information
       <etcdir>/dcmqrscp.cfg - example configuration file

SEE ALSO
       dcmqrscp(1)

COPYRIGHT
       Copyright  (C)  1993-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcmqrti(1)
