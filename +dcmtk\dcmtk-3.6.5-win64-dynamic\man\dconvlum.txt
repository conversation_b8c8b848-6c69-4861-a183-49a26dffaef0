dconvlum(1)                       OFFIS DCMTK                      dconvlum(1)



NAME
       dconvlum - Convert VeriLUM files to DCMTK display files


SYNOPSIS
       dconvlum in-file out-file [ambient]

DESCRIPTION
       Command  line  program  that  converts VeriLUM file with characteristic
       curve data (CCx_xx.dat) to a format used for the Barten  transformation
       in  this  toolkit.  VeriLUM  is  a calibration package from ImageSmiths
       consisting of a photometer (with serial RS232 connector) and a  Windows
       NT software.

PARAMETERS
       in-file   VeriLUM characteristic curve file to be converted

       out-file  DCMTK display file to be written

       ambient   ambient light (cd/m^2, floating point value)

NOTES
       The output file describing the characteristic curve of a display system
       (monitor) is a simple text file. Lines starting with a '#' are  treated
       as  comments  and  therefore  skipped  as  well  as  blank  lines. This
       conversion program creates such a file  automatically  (including  some
       comment  lines)  by  transforming the information stored in the VeriLUM
       file. The number following the magic word 'max' defines the maximum DDL
       value (e.g. 255 for 8 bit displays). This definition is required before
       the luminance data can be defined. Optionally the ambient  light  value
       can be specified in this file too. The floating point value (in candela
       per square meter) must follow the word  'amb'.  Finally  the  luminance
       values  are  defined for each corresponding DDL (digital driving level)
       value. The entries do not necessarily have to be ordered  or  complete,
       i.e.  there  can  be  less  than  'max' + 1 entries. Missing values are
       calculated by cubic spline interpolation.

       See DICOM standard part 14 for more details on display calibration  and
       Barten's model (including GSDF).

SEE ALSO
       dcmdspfn(1), dcm2pnm(1)

COPYRIGHT
       Copyright  (C)  1999-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dconvlum(1)
