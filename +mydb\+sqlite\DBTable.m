classdef DBTable  <OptionsWithLog   
    properties (Access = private)
        m_conn; 
    end
    
    methods
        function obj = DBTable(varargin)
           options = OptionsMap({'schema-name', 'main'}); 
           obj     = obj@ OptionsWithLog('dbtable', options, varargin{:});
           %obj.m_conn = conn; 
           dbconn  = obj.getoptioni('sqlite.DBConnection');
           if dbconn>0
               obj.m_conn = dbconn; 
           else
               dbfile = DBFileName(obj); 
               if ~isempty(dbfile)
                   obj.OpenDB();
               end
           end
        end
         
        function res = DBFileName(self)
            res = self.getoptioni('dbfilename');
        end
        
        function varnames = GetColumnNames(self)
            varnames = self.getOption('dbt.ColumnNames'); 
            if isempty(varnames)
                [varnames] = QueryColumnNames(self);
                self.setOption('dbt.ColumnNames', varnames); 
            end
        end
        
        function pk = GetPrimaryKey(self)
            pk = self.getOption('dbt.PrimaryKey'); 
            if isempty(pk)
                pk = QueryPrimaryKey(self);
                self.setOption('dbt.PrimaryKey', pk); 
            end
        end
        
        function OpenDB(self)
            self.CloseDB(); 
            self.m_conn = mksqlite(1, 'open', DBFileName(self));
            self.LogInfo(['open connection to db file--' DBFileName(self)]);
        end
        
        function CloseDB(self)
            if self.m_conn>0
                mksqlite(self.m_conn, 'close');
                self.LogInfo(['close connection to db file--' DBFileName(self)]);
            end
            self.m_conn = -1; 
        end
        
        function res = SchemaName(self)
            res =self.getoptioni('schema-name');
        end
       
        function res = TableName(self)
            res = self.getoptioni('table_name'); 
        end
        
        function SaveTable(self)
            
        end
        
        function UpdateFromTable(self, T)
            if ~isempty(T) && isa(T, 'table') 
                InsertMatlabTable(self, T);
            end
        end
        
        function InsertMatlabTable(self, T)
            if istable(T)
                T = table2struct(T);
            end
            varnames = fieldnames(T); 
            varnames0= QueryColumnNames(self);
            if ~isempty(varnames0)
                varnames = intersect(varnames, varnames0, 'stable'); 
            end
            mksqlite(self.m_conn, 'BEGIN');
            for k=1:numel(T)
                mksqlite(self.m_conn, self.struct2sqlInsert(T(k), varnames));
            end
            mksqlite(self.m_conn, 'COMMIT');
        end
        
        function sql = struct2sqlInsert(self, s, fieldnames)
            schemaname = SchemaName(self);
            tablename  = TableName(self);
            sql = sprintf('INSERT OR REPLACE INTO "%s"."%s" (%s) VALUES (%s)',...
                schemaname,...
                tablename,...
                strjoin(cellfun(@(x)sprintf('"%s"',x), fieldnames, 'UniformOutput', false), ','),...
                strjoin(cellfun(@(x)sprintf('"%s"',num2str(s.(x))), fieldnames, 'UniformOutput', false), ','));
        end
        
        function info = QueryPrimaryUID(self, uid)
            pk  = GetPrimaryKey(self);
            str = struct(pk, uid); 
            info= Query_struct(self, str,0);
        end
        
        function res = Query_struct(self, str, varargin)
            fns = fieldnames(str);
            fn  = fns{1}; val = str.(fn); 
            querystr = [' where ' '"' fn '"= ' '"' val '"'];   
            for k=2:numel(fns)
                fn = fns{k}; val = str.(fn); 
                querystr=[querystr ' and ' '"' fn '"= ' '"' val '"'];
            end
            res = Query(self, querystr, varargin{:});
        end
        
        function T = Query_structs(self, strs, varargin)
            if isempty(strs)||ischar(strs)||isstring(strs)
                T = Query(self, strs, varargin{:});
                return
            end

            if istable(strs)
                strs = table2struct(strs);
            end
            strs = StructBase.toCell(strs);
            T=[];
            for k=1:numel(strs)
                T1 = Query_struct(self, strs{k}, varargin{:});
                if ~isempty(T1)
                    T = cat(1, T, T1);
                end
            end
        end

        function res = Query(self, quertstr, outputtable)
            if ~exist('quertstr', 'var') 
                quertstr=''; 
            end
            
            if ~exist('outputtable', 'var')
                outputtable=1; 
            end
            
            schemaname = SchemaName(self);
            tablename  = TableName(self);
            cmd = sprintf(['SELECT * FROM ' schemaname '.' tablename  ' ' quertstr]);
            res = self.sqlQuery(cmd);
            %T   = [];
            if ~isempty(res) && outputtable
                res = struct2table(res, 'AsArray', true); 
            end
        end
        
        function T = QueryAndFilter(self, filters, quertstr)
            if ~exist('quertstr', 'var') 
                quertstr=''; 
            end
            T0      = Query(self, quertstr);
            if ~istable(filters) 
                filterobj=xls.TableFilter; 
                filterobj.AddTableFilter(filters);
                [I, T] = filterobj.FilterTable(T0);
            else
                S = table2struct(filters);
                N = numel(S);
                M = size(T0, 1);
                I = zeros(M, 1, 'like', false); 
                for n=1:N
                    filterobj=xls.TableFilter.Struct2Filter(S(n));
                    [loc] = filterobj.FilterTable(T0);
                    if ~isempty(loc)
                        I(loc)=true;
                    end
                end
                T = T0(I, :);
            end
        end

        function [varnames, str] = QueryColumnNames(self)
            try
                tablename  = TableName(self);
                %select * from pragma_table_info('tblName') as tblInfo
    %             cmd = sprintf(['SELECT * FROM  pragma_table_info(' tablename ') as tblInfo;']);
    %             res = self.sqlQuery(cmd);
                cmd = ['pragma table_info(' tablename ')']; 
                str = self.sqlQuery(cmd);
                varnames = {str.name};
%                 mksqlite(self.m_conn, 'COMMIT');
            catch
                varnames=[];
            end
        end
        
        function pk = QueryPrimaryKey(self)
            try
                [varnames, str] = QueryColumnNames(self);
                I  = arrayfun(@(x)(x.pk~=0), str); 
                pk = varnames{I}; 
            catch
                pk = [];
            end
        end
        
        function res = sqlQuery(self, sqlcmd)
            res = mksqlite(self.m_conn, sqlcmd);
        end

        function [info] = GetUpdateRecord(self, info1)
            pk  = GetPrimaryKey(self);
            uid = info1.(pk);
            info0= QueryPrimaryUID(self, uid);
            if isempty(info0)
                info = info1; 
            else
                info = info0; 
                fn   = intersect(fieldnames(info), fieldnames(info1));
                for k=1:numel(fn)
                    name = fn{k}; 
                    info.(name) = info1.(name); 
                end
            end
            [info] = self.UpdateDBTimeStamp(info);
        end

        function updated = InsertRecord(self, info)
            try
                [info] = GetUpdateRecord(self, info);

                InsertMatlabTable(self, info);

                updated = 1; 
            catch
                updated = 0; 
            end
        end
        
        function res   = DeleteRows(self, conditionstr)
            schemaname = SchemaName(self);
            tablename  = TableName(self);
            deletestr  = ['WHERE ' conditionstr];
            cmd = sprintf(['DELETE FROM ' schemaname '.' tablename  ' ' deletestr]);
            try
             res = self.sqlQuery(cmd);
             mksqlite(self.m_conn, 'COMMIT');
            catch
                res =[];
            end
        end
        
        function Export2Xls(self, task)
            % taskid = GET_TASK_CONFIG(task,'TaskUID'); 
            % taskid = strrep(taskid, '#', '/');
            dbname  = TableName(self); 
            querystr= GET_TASK_CONFIG(task,'QueryStr', ''); 
            T       = self.Query_structs(querystr);
            if isempty(T)
                return
            end
            outxlsfile = GET_TASK_CONFIG(task,'DstXlsFile');
            outsheetname = GET_TASK_CONFIG(task,'OutputSheetName', dbname);
            xls.TableBase.WriteTable(T, outxlsfile, 'sheet', outsheetname);
        end
    end

    methods (Static)
        function [info] = UpdateDBTimeStamp(info)
            timenow = now; tsnow = datestr(timenow,  'yyyymmddTHHMMSS');
            ts      = StructBase.getfieldx(info, 'DBCreationTimeStamp');
            if isempty(ts)
                info.('DBCreationTimeStamp')=tsnow; 
                if isempty(StructBase.getfieldx(info, 'DBCreationUser'))
                    info.('DBCreationUser')='auto'; 
                end
            end
            if isempty(StructBase.getfieldx(info, 'DBUpdateUser'))
                info.('DBUpdateUser')='auto'; 
            end
            info.('DBUpdateTimeStamp')=tsnow; 
            if isempty(StructBase.getfieldx(info, 'DBLastAccessUser'))
                info.('DBLastAccessUser')='auto'; 
            end
            info.('DBLastAccessTimeStamp')=tsnow; 
        end

        function varnames = DBTableTimeStampVarNames()
               varnames = {'DBCreationUser', 'DBCreationTimeStamp','DBUpdateUser', 'DBUpdateTimeStamp', 'DBLastAccessUser',  'DBLastAccessTimeStamp'};
               varnames = cat(2, varnames, {'Tasks', 'Notes', 'Comments', 'Status'});
        end
    end
end

function value = GET_TASK_CONFIG(task, name, varargin)
    if isa(task, 'utils.json.JsonTask')
         value = task.GetTaskConfig(name, varargin{:});
    else
         value = StructBase.getfieldx_default(task, name, varargin{:});
    end
end
