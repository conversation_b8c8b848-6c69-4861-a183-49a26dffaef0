dcmmkcrv(1)                       OFFIS DCMTK                      dcmmkcrv(1)



NAME
       dcmmkcrv - Add 2D curve data to image


SYNOPSIS
       dcmmkcrv [options] dcmfile-in curvedata-in dcmfile-out

DESCRIPTION
       The dcmmkcrv utility allows one to create DICOM images containing curve
       data. Since curve data  is  hardly  used  by  vendors  today,  this  is
       intended  as  a means to test implementations that can read curve data.
       The utility reads an existing DICOM image and a  text  file  containing
       the  curve  data in textual form. A DICOM curve data repeating group is
       created according to the options specified on the command  line,  added
       to  the  existing  image  and  written back to file. The output file is
       encoded with the same transfer syntax used for  the  input  file.  This
       utility only supports the creation of two-dimensional curves.

PARAMETERS
       dcmfile-in    DICOM input image file

       curvedata-in  curve data input file (text)

       dcmfile-out   DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   curve creation options
       curve type:

         -r  --poly
               create as POLY curve (default)

         +r  --roi
               create as ROI curve

       curve value representation:

         +v  --data-vr  [n]umber: integer 0..4 (default: 4)
               select curve data VR: 0=US, 1=SS, 2=FL, 3=FD, 4=SL

         -c  --curve-vr  [n]umber: integer 0..2 (default: 0)
               select VR with which the Curve Data element is written
               0=VR according to --data-vr, 1=OB, 2=OW

       repeating group:

         -g  --group  [n]umber: integer 0..15 (default: 0)
               select repeating group: 0=0x5000, 1=0x5002 etc.

       curve description:

         -l  --label  s: string
               set Curve Label to s (default: absent)

         +d  --description  s: string
               set Curve Description to s (default: absent)

         -a  --axis  x: string, y: string
               set Axis Units to xy (default: absent)

NOTES
   Syntax of the Curve Data File
       The  curve  data  file  is  expected  to  be  a  plain  ASCII text file
       containing numbers (integer or floating point) comprising the values of
       the  point  coordinates.  Numbers  must  be separated by whitespace. No
       checking of the value range or value  range  conversion  is  performed.
       Example:

        256.451947    1.000000
        477.689863  128.822080
        128.822080  477.689863
         35.310137  128.822080
        256.451947    1.000000

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmmkcrv utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

COPYRIGHT
       Copyright  (C)  1998-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmmkcrv(1)
