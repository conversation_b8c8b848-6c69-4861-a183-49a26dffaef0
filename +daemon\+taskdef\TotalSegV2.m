classdef TotalSegV2 < daemon.taskdef.TaskDef
    properties
        
    end

    methods
        function obj = TotalSegV2(varargin)         
            <EMAIL>(varargin{:});
            obj.SetDefaultCfg;
        end
    end

    methods (Static)
        function CreateTaskDef(taskname, tasksdeffile, astemp)
             if ~exist("astemp", 'var')
                 astemp=false;
             end

             taskdeffolder = [fileparts(tasksdeffile) '/'];
             isfast = contains(taskname, '_fast');
             if contains(taskname, '_mr')
                   modality='MR';
             else
                   modality='CT';
             end

             segargument = ' --ml ';
             %taskdefname = ['temp_ts2_' taskname];
             subtaskname = ['ts2_' taskname];
             if isfast
                 segargument = [segargument '--fast'];
                 taskname = strrep(taskname, '_fast', '');
                 subtaskname = ['ts2_' taskname '_fast'];
             end

             if ~astemp
                 CustomSubFolder=subtaskname; 
                 InputImage='../image.nii.gz';
                 OutputFile='labelmask';
                 taskdefname = [modality '_' subtaskname];
                 dependency= struct("filename", InputImage, "taskfilename", ['../DcmConverter/DCMCONVERT_' modality '/[Modality].[SeriesInstanceUID].tsk']);

             else
                 %TaskOutputFolder='[TaskOutputFolder]'; 
                 InputImage='[InputImage]';
                 OutputFile='[OutputFile]';
                 taskdefname = ['temp_' subtaskname];
                 CustomSubFolder=''; 
                 dependency= struct("filename", InputImage);
             end
             
             % obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
             %        {'TaskDefName', taskdefname});
             % % ModelOptionFile= modeloptfile;
             % InputImage='[InputImage]';
             % OutputFile='[OutputFile]';
             % dependency = struct("filename", InputImage);
             % %RemoveTiny = struct('OperationType', 'RemoveROIs', 'OperationName', 'RemoveTiny');
             % m=0;   Process=[];
             % m=m+1; Process{m}=struct('OperationType', 'TotalSeg','SegTaskName', taskname, 'SegArgument', segargument,...
             %     'InputImage', InputImage, 'OutputFile', OutputFile); 
             % obj.Generate(tasksdeffile, dependency, Process);

            
             MergeROIs='';
             if strcmpi(taskname, 'total')
                  MergeROIs='Bones|Muscles|Vessels|Lung_L|Lung_R|Kidney_L|Kidney_R';
             elseif strcmpi(taskname, 'total_mr')
                  MergeROIs='Bones|Muscles|Vessels';
             elseif strcmpi(taskname, 'body') ||strcmpi(taskname, 'body_mr')
                  MergeROIs='Body';
             end
             
             obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            
             m=0;   Process=[];
             m=m+1; Process{m}=struct('OperationType', 'TotalSeg','SegTaskName', taskname, 'SegArgument', segargument,...
                 'InputImage', InputImage, 'OutputFile', OutputFile, 'MergeROIs', MergeROIs);         
            
             if ~astemp
                 strinfo = struct('ID', subtaskname, 'LabelMaskFileName', [CustomSubFolder '/' OutputFile]);
                 process = struct('OperationType', 'AssociateStruct',...
                     'AssociateFileName','../dcmdata.cfg', 'ListName', 'StructSet', 'Info', strinfo);
                 m=m+1; Process{m}=process; 
             end
            obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end

        function Factory_totalseg(tasksdeffile, varargin)
             taskdefname = ['temp_totalseg'];
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             % ModelOptionFile= modeloptfile;
             dependency = struct("filename", '[InputImage]');
             %RemoveTiny = struct('OperationType', 'RemoveROIs', 'OperationName', 'RemoveTiny');
             m=0;   Process=[];
             m=m+1; Process{m}=struct('OperationType', 'TotalSeg','SegTaskName', '[SegTaskName]',...
                 'SegArgument', '[SegArgument]',...
                 'InputImage', '[InputImage]', 'OutputFile', '[OutputFile]', 'MergeROIs', '[MergeROIs]'); 
             obj.Generate(tasksdeffile, dependency, Process);

           % tasknames = {'total', 'total_fast', 'total_mr', 'total_fast_mr',...
           %               'lung_vessels', 'cerebral_bleed',...
           %               'hip_implant', 'coronary_arteries', 'pleural_pericard_effusion',...
           %               'body', 'body_fast', 'body_mr', 'body_mr_fast', 'vertebrae_mr',...
           %               'vertebrae_body',...
           %               'heartchambers_highres', 'appendicular_bones',...
           %               'tissue_types', 'tissue_types_mr', 'tissue_4_types', 'face', 'face_mr',...
           %               'head_glands_cavities', 'head_muscles', 'headneck_bones_vessels',...
           %               'headneck_muscles', 'liver_vessels', 'brain_structures',...
           %               'lung_nodules', 'kidney_cysts', 'breasts',...
           %               'thigh_shoulder_muscles', 'thigh_shoulder_muscles_mr'};
            [Tasks] = ai.seg.TotalSegDef.SegTaskNames;
            fns = fieldnames(Tasks);
            for m=1:numel(fns)
                tasknames=Tasks.(fns{m});
                for k=1:numel(tasknames)
                    daemon.taskdef.TotalSegV2.CreateTaskDef(tasknames{k}, tasksdeffile, varargin{:});
                end
            end
        end

        function TempMergeSeg(tasksdeffile, taskdefname0, submodels,dbname, varargin)
            if ~exist("dbname", 'var')
                 dbname='';
            end

             if ischar(submodels)
                 submodels = strsplit(submodels, '|');
             end

             DBTableName=dbname;
             options = OptionsMap;
              m=0; dependency=[];
             if ~isempty(dbname)
                 InputImage ='../image.nii.gz';
                 taskdefname=[dbname '_' taskdefname0];
                 TaskOutputFolder1 = [taskdefname0 '/'];
                 options.setOption('CustomSubFolder', TaskOutputFolder1);
                 TaskUID = [dbname '.' '[SeriesInstanceUID]'];
                 TaskOutputFolder = ['[PatientID]/' TaskUID '/' taskdefname0 '/'];
                 modality = dbname; 
                 m=m+1; dependency{m}= struct("filename", InputImage, ...
                     "taskfilename", ['../DcmConverter/DCMCONVERT_' modality '/' modality '.[SeriesInstanceUID].tsk']);
             else
                 InputImage ='[InputImage]';
                 taskdefname= ['temp_' taskdefname0];
                 TaskOutputFolder = '[TaskOutputFolder]';
                 TaskUID = '[TaskUID]';
                 m=m+1; dependency{m}= struct("filename", InputImage);
             end
            
             
             for k=1:numel(submodels)
                 submodel = submodels{k};
                 subname = ['ts2_' submodel];
                 labelmaskname = ['../' subname '/labelmask.nii.gz'];
                 taskoutfolder = DosUtil.SimplifyPath([TaskOutputFolder '../' subname '/']);
                 taskinfofile  = ['temp_' subname  '/' TaskUID '.tskinfo'];
                 Info = struct('TaskUID', TaskUID, 'InputImage', InputImage, 'OutputFile', 'labelmask', 'TaskOutputFolder', taskoutfolder);
                 TaskInfo = struct('FileName', taskinfofile, 'Info', Info);
                 m=m+1; dependency{m}= struct("filename", labelmaskname, 'TaskInfo', TaskInfo);
                 mergeops{k}=struct("SrcRoiMaskFile", labelmaskname);
             end
             OutputStructSet=struct("LabelMaskFileName", 'labelmask', "ROIMaskFileName", 'roimask');
             m=0; Process=[];
             m=m+1; Process{m} = struct('OperationType', 'MergeExportROIMask', 'OutMaskImageType', 'roimask8',...
                 'OutputStructSet', OutputStructSet);
             Process{m}.MergeOperation = mergeops; 
            
             if ~isempty(dbname)
                 strinfo = struct('ID', taskdefname0, 'LabelMaskFileName', [taskdefname0  '/labelmask']);
                 process = struct('OperationType', 'AssociateStruct',...
                     'AssociateFileName','../dcmdata.cfg', 'ListName', 'StructSet', 'Info', strinfo);
                 m=m+1; Process{m}=process; 
             end
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             obj.Generate(tasksdeffile, dependency, Process, options, varargin{:});
        end

        function MergeSeg(tasksdeffile, taskdefname, modality, submodels, submasks, varargin)
             for k=1:numel(submodels)
                 submodel = submodels{k}; 
                 InputImage = ['../' submodel '/' submasks{k} '.nii.gz'];
                 Dependency{k}= struct("filename", InputImage, "taskfilename", [modality '_' submodel '/' modality '.[SeriesInstanceUID].tsk']);
                 MergeOperation{k} =struct('SrcRoiMaskFile',  InputImage);
             end
             
            CustomSubFolder=taskdefname;
                
            OutputStructSet=struct(...
		        "ID", taskdefname, ...
		        "ROIMaskFileName", "roimask",...
		        "LabelMaskFileName", "labelmask",...
		        "LabelMaskWithImageContours", 1,...
		        "ContourSmoothWindow", 3);
            DcmExport=struct(...
	            "OrigImageHeaderFile", "../image_dcm.json",...
	    	    "OutRSFileName", "RS_Segman.dcm",...
	    	    "OutRSSetName", taskdefname);
            taskdeffolder = [fileparts(tasksdeffile) '/'];

            % Process = struct('OperationType', 'MergeExportROIMask','OutputStructSet', OutputStructSet, ...
            %     'DcmExport', DcmExport);
            % Process.MergeOperation=MergeOperation; 
            Process='';
            obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, Process,{'DBTableName', modality}, {'CustomSubFolder', CustomSubFolder}, ...
                {'MergeOperation', MergeOperation},{'OutputStructSet', OutputStructSet}, {'DcmExport', DcmExport}, varargin{:});
        end
    end
end