classdef DcmDb < OptionsWithLog
% DcmDb DICOM file repository with basic indexing
%
% Purpose:
% DICOM file repository for direct and ease of access.
%
% Features (responsibilities):
% 1. (Auto) store incoming files.
% 2. Validate DICOM file integrity.
% 3. Index tags to database.
% 4. Data consistency between files and indices.
% 5. Clients read only.

    properties (Constant = true)
        version = 1     % integer, matches user_version in sqlite
    end
    properties 
        rootPath        % root directory of storage
%         m_SeriesRecordTable;  % 
        
        m_DcmParser; 
    end

    methods
        function self = DcmDb(rootPath, varargin)
            self@OptionsWithLog('DcmDB', varargin{:}); 
            if nargin >= 1 && ischar(rootPath)
                self.rootPath = rootPath;
                mkdir(rootPath);
                %mkdir(fullfile(rootPath, 'incoming'));  
                str = 'DCMSERVER_FOLDER.incoming'; 
                incomingFolders = self.getOption(str, 'incoming');
                if ischar(incomingFolders)
                    incomingFolders=strsplit(incomingFolders, '|'); 
                    for k=1:numel(incomingFolders)
                        incomingFolders{k} = DosUtil.toFullFile(incomingFolders{k}, [], rootPath);
                        try
                        DosUtil.mksubdir(incomingFolders{k});
                        catch
                        end
                    end
                    self.setOption(str, incomingFolders);
                end

%             else
%                 error('MiniPacs:InvalidInput:Constructor', 'No input or input not string');
%                 if UseRecordTable(self)
%                     self.m_SeriesRecordTable  = self.CreateSeriesRecordKeepTable({'table.filename', [rootPath 'DcmDB_Series.xlsx']});
%                 end
                
                self.m_DcmParser = ai.DcmParser(self);
            end
        end
        
        function res = IsAnonymousDB(self)
            res = self.getoptioni_numeric('IsAnonymousDB', 0); 
        end
        
        function anonymizer = DcmAnonymizer(self)
            anonymizer = self.getoptioni('DcmServer.Anonymizer'); 
            if isempty(anonymizer)
%                 folder = self.getoptioni('ANON_DCMSERVER_FOLDER');
%                 dbfile = self.getoptioni('ANON_DB_FILE', [DcmDataFolder(self) 'anonDB.xlsx']);
                folder = DosUtil.mksubdir('c:\temp\', 'dcmanon'); 
                dbfile = [folder 'anonDB.xlsx'];
                anonymizer = anon.DicomInfoAnon({'ANON_DCMSERVER_FOLDER', folder}, {'ANON_DB_FILE', dbfile});
                self.setOption('DcmServer.Anonymizer', anonymizer); 
            end
        end
        
%         function res = UseRecordTable(self)
%             res = self.getoptioni_numeric('dcmdb.UseRecordTable', 0);
%         end
        

        %storemode: 0: copy, 1: move, 2: symbolic link 
        function [newInstanceT, instanceT] = store(self, inpFiles, storemode)
            if ischar(inpFiles)
                inpFiles = {inpFiles};
            elseif iscellstr(inpFiles)
                % pass
            else
                error('MiniPacs:store:InvalidInput', 'Unknown input type');
            end
            
            newInstanceT  = self.CreateInstanceRecordKeepTable();
            
            %if nargout>=2
                instanceT = self.CreateInstanceRecordKeepTable();
            %end
            isanon = IsAnonymousDB(self);
            if isanon
               %anoncalc   = anon.DicomInfoAnon();
               anoncalc   = DcmAnonymizer(self);
               [outfiles] = anoncalc.AnonymizeFiles(inpFiles);
               if storemode==1 %move
                   cellfun(@(x)(delete(x)), inpFiles); 
               end
                   
               inpFiles   = outfiles;
               storemode  = 1; 
            end
            
            for k = 1:numel(inpFiles)
                try
                    inpFile = inpFiles{k};
                    
                    % extract info
                    %inpInfo = self.file2info(inpFile);
                    inpInfo = self.m_DcmParser.file2info_basic(inpFile);
                    if isempty(inpInfo)
                        if storemode==1
                            delete(inpFile); 
                        end
                        continue;
                    end
                    
                    %if nargout>=2
                        instanceT.UpdateRow(inpInfo);
                    %end
                    
                    [fileexist, destFile, destDirectory] =  self.ExistImage(inpInfo);
                     if fileexist
                        if storemode==1
                            delete(inpFile); 
                        end
                        continue;
                     end
                    
                    % file copy
                    if exist(destDirectory, 'dir') ~= 7 && ~isempty(destDirectory)
                        mkdir(destDirectory);
                    end
                    
                    newInstanceT.UpdateRow(inpInfo);
                    
                    if ~strcmpi(inpFile, destFile)
                        switch storemode
                            case 1 %move
                                movefile(inpFile, destFile, 'f');
                            case 0 %copy
                                copyfile(inpFile, destFile, 'f');
                            case 2 %symbolic link
                                system(['mklink  "' destFile, '" "' inpFile '"']);
                        end
                    end
                catch err
                end
            end
            
            if newInstanceT.NumRows()>0  
                self.LogInfo(['Store ' num2str(newInstanceT.NumRows()) ' of ' num2str(instanceT.NumRows()) ' instances']);
            end
%             if ~isempty(newInstanceT) && UseRecordTable(self)
%                 self.m_SeriesRecordTable.UpdateFromTable(newInstanceT.GetRawTable());
%                 self.m_SeriesRecordTable.SaveTable();
%             end
        end
        
        function [res, destFile, destDirectory] =  ExistImage(self, inpInfo)
               [destDirectory, destFile] = self.info2path(inpInfo); 
               res = exist(destFile, 'file');
        end
        
        function [res, destFile, destDirectory] =  ExistSeries(self, inpInfo)
               [destDirectory, destFile] = self.info2path(inpInfo); 
               res = exist(destDirectory, 'folder');
        end
    end
    
    methods (Access = private)

    end
    
    methods (Access = public)
%         function info = file2info(self, filepath)
%             % Read DICOM tags to Matlab struct.
%             % Note:
%             % 1. Had to use dcm4che2 for several reasons
%             %    a) Broken image can crash Matlab, see XiO segfault files
%             %    b) Faster, no need to parse all tags
%             % 2. Had to manually go through needed tags instead of simply read all tags because certain tags could be missing
%             info = [];
%             try
%                 dcm = dcm4che2.read(filepath);
%                 for t = self.tags.common
%                     info = getOptionalTag(dcm, t{1}, info);
%                 end
%                 for t = self.tags.(info.Modality)
%                     info = getOptionalTag(dcm, t{1}, info);
%                 end
%                 
%                 if isfield(self.tagsxx, info.Modality)
%                     t = self.tagsxx.(info.Modality);
%                     for k=2:2:numel(t)
%                         name = t{k-1}; 
%                         path = t{k};
%                         info = getOptionalTagxx(dcm, name, path, info);
%                     end
%                 end
%                 
%             catch exc
%                 %if ~isempty(self.log)
%                     self.LogWarning(exc.message);
%                 %end
%                 %info = [];
%             end
%             if isstruct(info)
%                 fn = fieldnames(info); 
%                 for k = 1:numel(fn)
%                     name = fn{k};
%                     if isnumeric(info.(name))
%                         info.(name) = num2str(info.(name));
%                     end
%                 end
%             end
%         end
%         function [destDirectory, destFile] = info2path(self, inpInfo)
%             % Returns full path of managed file.
%             % Note:
%             %   Need to be consistent with database view.
%             if isempty(inpInfo)
%                 destDirectory = [];
%                 destFile = [];
%             else
%                 destDirectory = fullfile(self.rootPath,...
%                     inpInfo.PatientID,...
%                     inpInfo.StudyInstanceUID,...
%                     [inpInfo.Modality '.' inpInfo.SeriesInstanceUID]);
%                 destFile = fullfile(destDirectory, [inpInfo.SOPInstanceUID '.dcm']);
%             end
%         end

         function info = file2info(self, filepath)
             info = self.m_DcmParser.file2info(filepath); 
         end
         
         function [destDirectory, destFile] = info2path(self, inpInfo)
             [destDirectory, destFile] = self.m_DcmParser.info2path(self.rootPath, inpInfo);
         end
    end
    
    methods % public API
        function incomingFolders = IncomingFolder(self)
            %incomingFolders = self.getOption_cell('IncomingFolder', fullfile(self.rootPath, 'incoming'));
            %incomingFolders = self.getOption('IncomingFolder', fullfile(self.rootPath, 'incoming'));
            str = 'DCMSERVER_FOLDER.incoming'; 
            incomingFolders = self.getOption(str, 'incoming');
            if ischar(incomingFolders)
                incomingFolders=strsplit(incomingFolders, '|'); 
                self.setOption('IncomingFolder', incomingFolders);
            end
        end
        
        function [newInstanceT, instanceT] =storeIncoming(self, deleteOnSuccess)
%             inpFiles  = DosUtil.rdir(fullfile(self.rootPath, 'incoming', '**/*'), '~isdir');
%             self.store(arrayfun(@(x)x.name, inpFiles, 'UniformOutput', false), deleteOnSuccess);
            incomingFolders = self.IncomingFolder; 
            for k=1:numel(incomingFolders)
                inpFiles  = DosUtil.rdir(fullfile(incomingFolders{k}, '**/*'), '~isdir');
                [newInstanceT, instanceT] =self.store(arrayfun(@(x)x.name, inpFiles, 'UniformOutput', false), deleteOnSuccess);
            end
        end
        
        function removeEmptyDirsInIncoming(self)
            removeEmptyDirs(fullfile(self.rootPath, 'incoming'));
        end
        
        function names = ModalityTags(self, modality)
            names = self.tags.(modality); 
            if isfield(self.tagsxx, modality)
                names1 = self.tagsxx.(modality)(1:2:end);
                names = cat(2, names, names1); 
            end
        end
        
    end
    
    methods (Static)
        function [newInstanceT, instanceT] = StoreDCMFiles(dstrootpath, srcfiles,  varargin)
            options = OptionsMap(varargin{:}); 
            deleteOnSuccess = options.getoptioni_numeric('dcm.storemode', 0); 
            if ischar(srcfiles)
                res = DosUtil.rdir(srcfiles); 
                srcfiles = {res(:).name};
            end
            eval = dcmtk.DcmDb(dstrootpath);
            [newInstanceT, instanceT] = eval.store(srcfiles, deleteOnSuccess);
        end
        
        function T = CreateSeriesRecordKeepTable(varargin)
            T   = xls.RecordKeepTable({'table.uniquekey', 'SeriesInstanceUID'}, varargin{:});
            fns = {'PatientID' 'StudyInstanceUID' 'Modality' 'SeriesInstanceUID' 'SeriesDate' 'SeriesDescription'};
            T.SetVariableNames(fns);
        end
        
        function T = CreateInstanceRecordKeepTable(varargin)
            T   = xls.RecordKeepTable({'table.uniquekey', 'SOPInstanceUID'}, varargin{:});
            fns = {'PatientID' 'StudyInstanceUID' 'Modality' 'SeriesInstanceUID' 'SeriesDate' 'SeriesDescription' 'SOPInstanceUID'};
            T.SetVariableNames(fns);
        end
        
        function instanceT = RetrieveInstanceRecordKeepTable(dcmfiles, varargin)
            instanceT = dcmtk.DcmDb.CreateInstanceRecordKeepTable(varargin{:});
            tags = instanceT.GetVariableNames();
            for k=1:numel(dcmfiles)
                inpInfo = dcm4che2.dcmfile2info(dcmfiles{k}, tags); 
                instanceT.UpdateRow(inpInfo);
            end
        end
    end
end

function removeEmptyDirs(rootDir)
    dirlist = DosUtil.rdir(rootDir);
    % recursively clean subdirectories
    if any(cell2mat({dirlist.isdir}))
        arrayfun(@(x)removeEmptyDirs(x.name), dirlist(cell2mat({dirlist.isdir})));
    end
    % rmdir self
    if isempty(DosUtil.rdir(rootDir))
        rmdir(rootDir);
    end
end

% function info = getOptionalTag(dcm, tagPath, info)
%     if dcm4che2.hasTag(dcm, tagPath)
%         info.(tagPath) = dcm4che2.getTag(dcm, tagPath);
%     else
%         info.(tagPath) = [];
%     end
% end
% 
% function info = getOptionalTagxx(dcm, tagName, tagPath, info)
%    info.(tagName) = dcm4che2.getTagxx(dcm, tagPath);
% end