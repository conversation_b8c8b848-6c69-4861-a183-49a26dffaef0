classdef SqLiteTable  <OptionsMap   
    properties (Access = private)
        m_conn; 
    end
    
    methods
        function obj = SqLiteTable(conn,varargin)
           options = OptionsMap({'schema-name', 'main'}); 
           obj = obj@ OptionsMap(options, varargin{:});
           obj.m_conn = conn; 
        end
         
        function res = SchemaName(self)
            res =self.getoptioni('schema-name');
        end
       
        function res = TableName(self)
            res = self.getoptioni('table_name'); 
        end
        
        function res = PrimaryKey(self)
            res = self.getoptioni('PRIMARY_KEY'); 
        end
        
        function FromMatlabTable(self, T, varargin)
            %str = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" ("SOPInstanceUID" TEXT PRIMARY KEY  NOT NULL  UNIQUE', modality); 
           %str   = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" ("%s" TEXT PRIMARY KEY  NOT NULL  UNIQUE', tablename, PRIMARY_KEY); 
           names = T.Properties.VariableNames; 
           %names = self.ModalityTags(modality);
           schemaname = SchemaName(self);
           tablename  = TableName(self);
           PRIMARY_KEY= self.PrimaryKey(self);
           str   = sprintf('CREATE  TABLE  IF NOT EXISTS "%s"."%s" (', schemaname, tablename);
           N = numel(names);
           for k=1:N
                name = names{k};
                if strcmpi(name, PRIMARY_KEY)
                    TEXT = 'TEXT PRIMARY KEY  NOT NULL  UNIQUE';
                else
                    TEXT = 'TEXT'; 
                end
                if k<N
                    str1 = [' "' name '" ' TEXT ',']; 
                else
                    str1 = [' "' name '" ' TEXT ')']; 
                end
                str = cat(2, str, str1); 
            end
           
            if isempty(self.m_conn)
                self.OpenDB();
            end
            mksqlite(self.m_conn, str); 
            
            InsertMatlabTable(self, T);
        end
        
        function InsertMatlabTable(self, T)
            if istable(T)
                T = table2struct(T);
            end
            varnames = fieldnames(T); 
            mksqlite(self.m_conn, 'BEGIN');
            for k=1:numel(T)
                mksqlite(self.m_conn, self.struct2sqlInsert(T(k), varnames));
            end
             mksqlite(self.m_conn, 'COMMIT');
        end
        
         function sql = struct2sqlInsert(self, s, fieldnames)
            schemaname = SchemaName(self);
            tablename  = TableName(self);
            sql = sprintf('INSERT OR REPLACE INTO "%s"."%s" (%s) VALUES (%s)',...
                schemaname,...
                tablename,...
                strjoin(cellfun(@(x)sprintf('"%s"',x), fieldnames, 'UniformOutput', false), ','),...
                strjoin(cellfun(@(x)sprintf('"%s"',s.(x)), fieldnames, 'UniformOutput', false), ','));
        end
    end
end

