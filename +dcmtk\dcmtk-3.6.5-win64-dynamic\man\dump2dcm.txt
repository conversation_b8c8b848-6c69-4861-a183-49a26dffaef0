dump2dcm(1)                       OFFIS DCMTK                      dump2dcm(1)



NAME
       dump2dcm - Convert ASCII dump to DICOM file


SYNOPSIS
       dump2dcm [options] dumpfile-in dcmfile-out

DESCRIPTION
       The  dump2dcm  utility converts an ASCII dump file to a DICOM file. The
       dump file has the same format as the output  of  dcmdump.  Thus  it  is
       possible  to  capture  the  output  of dcmdump into a file, modify some
       attributes and create a new DICOM file.

PARAMETERS
       dumpfile-in  dump input filename

       dcmfile-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-meta-info
                read meta information if present (default)

         -f   --ignore-meta-info
                ignore file meta information

       file contents byte ordering:

         +rl  --read-file-little
                read OW data with little endian (default)

         +rb  --read-file-big
                read OW data from file with big endian

       other input options:

         +l   --line  [m]ax-length: integer
                maximum line length m (default: 4096)

   processing options
       unique identifiers:

         +Ug  --generate-new-uids
                generate new Study/Series/SOP Instance UID

         -Uo  --dont-overwrite-uids
                do not overwrite existing UIDs (default)

         +Uo  --overwrite-uids
                overwrite existing UIDs

   output options
       output file format:

         +F   --write-file
                write file format (default)

         -F   --write-dataset
                write data set without file meta information

         +Fu  --update-meta-info
                update particular file meta information

       output transfer syntax:

         +t=  --write-xfer-same
                write with same TS as input (default)

         +te  --write-xfer-little
                write with explicit VR little endian

         +tb  --write-xfer-big
                write with explicit VR big endian TS

         +ti  --write-xfer-implicit
                write with implicit VR little endian TS

         +td  --write-xfer-deflated
                write with deflated explicit VR little endian TS

       error handling:

         -E   --stop-on-error
                do not write if dump is damaged (default)

         +E   --ignore-errors
                attempt to write even if dump is damaged

       post-1993 value representations:

         +u   --enable-new-vr
                enable support for new VRs (UN/UT) (default)

         -u   --disable-new-vr
                disable support for new VRs, convert to OB

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

       data set trailing padding (not with --write-dataset):

         -p=  --padding-retain
                do not change padding (default if not --write-dataset)

         -p   --padding-off
                no padding (implicit if --write-dataset)

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

       deflate compression level (only with --write-xfer-deflated):

         +cl  --compression-level  [l]evel: integer (default: 6)
                0=uncompressed, 1=fastest, 9=best compression

NOTES
   Dump File Description
       The input file can be an output of  dcmdump  (default  indented  format
       only).  One  element  (tag,  VR,  value)  must be written into one line
       separated by arbitrary spaces or tab characters. A '#' begins a comment
       that ends at the line end. Empty lines are allowed.

       The individual parts of a line have the following syntax:

       Tag:   (gggg,eeee)
              with gggg and eeee are 4 character hexadecimal values
              representing group and element tag.  Spaces and tabs can be
              anywhere in a tag specification.
       VR:    Value Representation must be written as 2 characters as in
              Part 6 of the DICOM standard.  No spaces or tabs are allowed
              between the two characters.  If the VR can be determined from
              the tag, this part of a line is optional.
       Value: There are several rules for writing values:
              1. US, SS, SL, UL, FD, FL, OD, OF and OL are written as decimal
                 strings that can be read by scanf().
              2. AT is written as '(gggg,eeee)' with additional spaces
                 stripped off automatically and gggg and eeee being decimal
                 strings that can be read by scanf().
              3. OB and OW values are written as byte or word hexadecimal
                 values separated by '\' character.  Alternatively, OB or OW
                 values can be read from a separate file by writing the
                 filename prefixed by a '=' character (e.g. '=largepix.dat').
                 The contents of the file will be read as is.  By default, OW
                 data is expected to be little endian ordered and will be
                 swapped if necessary.  No checks will be made to ensure that
                 the amount of data is reasonable in terms of other attributes
                 such as Rows or Columns.
                 In case of compressed pixel data, the line should start with
                 '(7fe0,0010) OB (PixelSequence' in order to distinguish from
                 uncompressed pixel data.
              4. UI is written as '=Name' in data dictionary or as unique
                 identifier string (see 6.), e.g. '[1.2.840.....]'.
              5. Strings without () <> [] spaces, tabs and # can be written
                 directly.
              6. Other strings must be surrounded by '[' and ']'.  No bracket
                 structure is passed.  The value ends at the last ']' in the
                 line.  Anything after the ']' is interpreted as comment.
              7. '(' and '<' are interpreted special and may not be used when
                 writing an input file by hand as beginning characters of a
                 string.  Multiple Value are separated by '\'.  The lines
                 need not be sorted into ascending tag order.  References in
                 DICOM Directories are not supported.  Semantic errors are
                 not detected.

   Example
        (0008,0020) DA [19921012]            #  8, 1 StudyDate
        (0008,0016) UI =MRImageStorage       # 26, 1 SOPClassUID
        (0002,0012) UI [1.2.276.0.7230010.100.1.1]
        (0020,0032) DS [0.0\0.0]             #  8, 2 ImagePositionPatient
        (0028,0009) AT (3004,000c)           #  4, 1 FrameIncrementPointer
        (0028,0010) US 256                   #  4, 1 Rows
        (0002,0001) OB 01\00

   Limitations
       Please  note  that  dump2dcm  currently does not fully support DICOMDIR
       files. Specifically, the value of the various offset data  elements  is
       not updated automatically by this tool.

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dump2dcm utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmdump(1)

COPYRIGHT
       Copyright  (C)  1996-2016  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dump2dcm(1)
