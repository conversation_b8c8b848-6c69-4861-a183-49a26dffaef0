classdef LicenseTool <OptionsWithLog   
    properties
        
    end
    
    methods
        function obj = LicenseTool(varargin)
           obj@OptionsWithLog('LicenseTool', {'HashMode', 'MD5'}, varargin{:});   
        end

        function code = MyHashString(self, str)
            if iscell(str)
                str = xls.TableBase.Content2Str(str); 
            end
            hashmode = self.getoptioni('HashMode'); 
            code=self.HashString(str, hashmode);
        end
        
        %expdate is in the format of yyyy.mm.dd
        function lic= CreateLicense(self, diskid, softwarename, expdate)
            if ~exist('expdate', 'var')
                expdate = datestr(now+365, 'yyyy.mm.dd');
            end
            str = {diskid softwarename expdate};
            liccode = self.MyHashString(str);
            lic =  struct('LicenseCode', liccode, 'ExpirationDate', expdate);
        end
        
        function [flag, msg]= CheckLicense(self, lics, softwarename)
            self.LogInfo(['Check license for ' softwarename]);
            flag= 0; msg = '';
            try
            if ischar(lics)
                lics= utils.json.readJson(lics); 
            end
            catch
                msg = ['missing license file'];
                self.LogWarning(msg); 
                return; 
            end
            
            
            lic =  StructBase.getfieldx(lics, softwarename);
            
            if isempty(lic)
                msg = ['no license found'];
                self.LogWarning(msg); 
                return; 
            end

            liccode = StructBase.getfieldx(lic, 'LicenseCode');
            if isempty(liccode)
                msg = 'missing license code';
                self.LogWarning(msg); 
                return; 
            end
            
            expdatestr = StructBase.getfieldx(lic, 'ExpirationDate');
            
            diskid = self.DiskSerialNumber('C:');
            str    = {diskid softwarename expdatestr};
            liccode1 = self.MyHashString(str);
            
            if ~strcmp(liccode, liccode1)
                msg = 'invalid license';
                self.LogWarning(msg); 
                return; 
            end
            
            expdate = datenum(expdatestr, 'yyyy.mm.dd');
            curdate = datenum(today);
            if curdate>expdate+1
                msg = ['license expired on ' expdatestr];
                self.LogWarning(msg); 
                return
            end
            
            if curdate>expdate-30
                flag = 1; 
                msg = ['license will be expired in ' expdatestr];
                self.LogWarning(msg);
                return
            end
            
            flag = 1; 
            msg = ['licence validated!'];
            self.LogInfo(msg);
        end
    end
    
    methods (Static)
        function id = WindowsSID(varargin)
            %id = getsid; 
%             cmdLine = '!c:\tomo\DV\bin\psgetsid  /accepteula '; 
            binpath = fileparts(which('LicenseTool'));
            DosUtils.addSystemPath(binpath);
            cmdLine = '!psgetsid  /accepteula '; 
            for n=1:nargin
                cmdLine = [cmdLine ' ' varargin{n}]; 
            end
            
            a = strread(evalc(cmdLine), '%s', 'delimiter','\n');
            pattern = 'SID';
            b = a(strmatch(pattern,a));
            c = strread(b{1}, '%s', 'delimiter',' ');
            d = c(strmatch('S-1-5',c));
            id = d{1}; 
        end
        
        function id = DiskSerialNumber(disk)
            if ~exist('disk', 'var')
                disk = 'c:'; 
            end
            a   = strread(evalc(['!vol ' disk]),'%s','delimiter','\n'); 
            pattern = 'Volume Serial Number is ';
%             b  = a(strmatch(pattern,a));
%             id = b{1}((length(pattern)+1):end); 
            b = extractAfter(a, pattern); 
            [I] = cellfun(@(x)(~isempty(x)), b); 
            id = strtrim(b{I}); 
        end      
        
        function t = atomicTime
            timezone = 'UTC'; 
            URL = 'http://tycho.usno.navy.mil/cgi-bin/timer.pl';
            t = datenum(regexp(urlread(URL), ...
                ['<BR>(.*)\s' timezone],'tokens','once'),'mmm. dd, HH:MM:SS');
        end 
        
        function GenerateLicences(licfile, diskid, softwarenames, days)
            if exist(licfile, 'file')
                lics = utils.json.readJson(licfile);
            end
            lictool = daemon.utils.LicenseTool;
            expdate = datestr(datenum(today)+days, 'yyyy.mm.dd'); 
            if ischar(softwarenames)
                softwarenames = strsplit(softwarenames, '|');
            end
            for k=1:numel(softwarenames)
                name = softwarenames{k};
                lic  = lictool.CreateLicense(diskid, name, expdate);
                lics.(name)=lic; 
            end
            utils.json.writeJson(lics, licfile);
        end
        
        function validated = ValidateDaemonLicense(softwarename, DAEMON_LICENSE_FILE)
            %global DAEMON_LICENSE_FILE
            % DAEMON_LICENSE_FILE=getenv('DAEMON_LICENSE_FILE');
            % if isempty(DAEMON_LICENSE_FILE)
            %     validated =1; 
            %     return;
            % end
            lictool = daemon.utils.LicenseTool;
            validated =  lictool.CheckLicense(DAEMON_LICENSE_FILE, softwarename);
        end
        
        function validated = ValidateArtDaemonLicense(softwarename)
            DAEMON_LICENSE_FILE='c:\ARTDaemon\artdaemon.lic';
            validated = daemon.utils.LicenseTool.ValidateDaemonLicense(softwarename, DAEMON_LICENSE_FILE);
            if ~validated
                error(['ARTDaemon license validation error!']);
            end
        end
        
        function CreateArtDaemonLicenseFile(varargin)
            options = OptionsMap(varargin{:});
            diskid = daemon.utils.LicenseTool.DiskSerialNumber();
            softwarenames={'dcmserver', 'dcmconverter', 'rtadmin', 'rtatlas', 'aiseg', 'group', 'dosedaemon'};
            days    = 366;
            licfile = 'c:\ARTDaemon\artdaemon.lic';

            diskid = options.getOption('DiskSerialNumber', diskid);
            softwarenames = options.getOption('SoftwareName', softwarenames);
            days = options.getOption('LicenseInDays', days);
            licfile = options.getOption('LicenseFile', licfile);
            daemon.utils.LicenseTool.GenerateLicences(licfile, diskid, softwarenames, days)
        end

        function md5hash = HashString(string, hashmode)
            if ~exist('HashMode', 'var')||isempty(hashmode)
                hashmode = 'MD5';
            end
            % Convert the string to ASCII
            asciiString = uint8(string);

            % Compute the MD5 hash
            hash = java.security.MessageDigest.getInstance(hashmode);
            hash.update(asciiString);
            md5hash = typecast(hash.digest, 'uint8');

            % Convert the hash to hexadecimal representation
            md5hash = dec2hex(md5hash)';
            md5hash = lower(md5hash(:)');
        end
    end
end

