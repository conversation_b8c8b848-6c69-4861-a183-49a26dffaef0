classdef JsonTask <utils.json.JsonConfig   
    properties
        
    end
    
    methods
        function obj = JsonTask(varargin)
            <EMAIL>(varargin{:});
        end
        
        function RecursiveReplaceTemplates(self, infos)
            if exist('infos', 'var') &&~isempty(infos)
                self.m_cfg = DosUtil.WildCardRepStr_recursive(self.m_cfg, infos);
            end
            DefaultSettings =  GetTaskConfig(self,'DefaultSettings');
            if ~isempty(DefaultSettings)
                self.m_cfg = DosUtil.WildCardRepStr_recursive(self.m_cfg, DefaultSettings);
            end
        end

        function val= GetTaskConfig(self, name, varargin)
            if isfield(self.m_cfg, name)
                val = StructBase.getfieldx(self.m_cfg, name); 
            else
                val = self.getfieldx(['TaskDef.' name], varargin{:});
            end
        end

        function flag = IsDefined(self, name)
            flag = isfield(self.m_cfg, name);
            if ~flag && isfield(self.m_cfg,'TaskDef')
                flag = isfield(self.m_cfg.TaskDef, name);
            else
                flag = false; 
            end
        end

        function opts = Config2OptionsMap_filter(self, cfgs, info)
            opts = OptionsMap; 
            if isstruct(cfgs)
                cfgs = arrayfun(@(x)(x), cfgs,'uniformoutput', false); 
            end
            for k=1:numel(cfgs)
                cfg = cfgs{k};
                filterstr = StructBase.getfieldx(cfg, 'Filter'); 
                optstr = StructBase.getfieldx(cfg, 'Options'); 
                if isempty(filterstr)
                    if ~isempty(optstr)
                        opts.setOptions(Config2OptionsMap(self, optstr));
                    end
                else
                    filter = xls.TableFilter(); 
                    filter.AddFilter(filterstr); 
                    flag = filter.FilterStruct(info);
                    if flag
                        opts.setOptions(Config2OptionsMap(self,optstr));
                    end
                end
            end
        end
        
        function opts = Config2OptionsMap(self, cfgs)
            opts = OptionsMap; 
            if isstruct(cfgs)
                cfgs = arrayfun(@(x)(x), cfgs,'uniformoutput', false); 
            end
            for k=1:numel(cfgs)
                cfg = cfgs{k};
                optfile = StructBase.getfieldx_default(cfg, 'OptionFile', '');
                if ~isempty(optfile)
                    if ~DosUtil.isabsolutepath(optfile)
                        optfile = [RootPath(self) optfile];
                    end
                    opts.setOptions(optfile); 
                end
                optname = StructBase.getfieldx_default(cfg, 'OptionName', ''); 
                optvalue= StructBase.getfieldx_default(cfg, 'OptionValue', '');
                if ~isempty(optname)
                    opts.setOption(optname, optvalue);
                end
            end
        end
    end
end

