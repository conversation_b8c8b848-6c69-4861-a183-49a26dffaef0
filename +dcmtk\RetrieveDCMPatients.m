function [ res] = RetrieveDCMPatients(list, varargin)
%     if ~exist('modalities', 'var')
% %         modalities = {'CT', 'MR', 'RTPLAN', 'RTDOSE', 'RTSTRUCT', 'REG', 'RTIMAGE', 'RTRECORD'}; 
%         modalities = {'CT', 'MR', 'RTPLAN', 'RTDOSE', 'RTSTRUCT'}; 
%     end
    
    if ischar(list)&& exist(list, 'file')
        T = readtable(list); 
    else
        T = list; 
    end
    
    if istable(T)
        mrns = unique(T.MRN); 
    elseif iscell(T)
        mrns = T; 
    end
    
%     dcm = dcmtk.DCMTKWrapper(cfg); 
    options      = OptionsMap(varargin{:}); 
    dcm          = dcmtk.DCMTKServer(options); 
    imagecounts=[]; seriescounts=[]; studycounts=[];
    
    
    retrievetype = options.getoptioni('retrievetype', 'rtpatient'); 
    modalities = options.getoptioni('modalities',   {'CT', 'MR', 'RTPLAN', 'RTDOSE', 'RTSTRUCT'});   
    
    if ischar(modalities)
        modalities = {modalities};
    end
    
    if ~iscell(mrns)
        mrns = arrayfun(@(x)(num2str(x)), mrns, 'uniformoutput', false); 
    end
    
    for k=1:numel(mrns)
        mrn = mrns{k};
%         if ~exist('modalities', 'var') || isempty(modalities)
%             dcm.RetrieveRTPatient(mrn, varargin{:}); 
%             disp(mrn); 
%         else
%             [imagecounts(k),seriescounts(k), studycounts(k)] = dcm.RetrievePatient(mrn, modalities); 
%             disp([mrn '   ' num2str(imagecounts(k)) '   ' num2str(seriescounts(k)) '   ' num2str(studycounts(k))]); 
%         end
        try
        switch lower(retrievetype)
            case 'rtpatient'
                dcm.RetrieveRTPatient(mrn, options); 
                disp(mrn); 
            case 'modalities'
                [imagecounts(k),seriescounts(k), studycounts(k)] = dcm.RetrievePatient(mrn, modalities); 
                 disp([mrn '   ' num2str(imagecounts(k)) '   ' num2str(seriescounts(k)) '   ' num2str(studycounts(k))]); 
            case 'imageseries'
               imagecounts(k) = dcm.RetrieveSeries(mrn);  
        end
        catch err
            disp(err.message);
        end
            
    end
    
    res.mrns = mrns; 
    res.studycounts = studycounts; 
    res.imagecounts = imagecounts; 
    res.seriescounts= seriescounts; 
end

