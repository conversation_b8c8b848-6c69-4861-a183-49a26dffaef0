classdef JsonConfig <OptionsMap
    properties
        m_cfg; 
        m_cfgfile; 
    end
    
    methods
        %cfg file must be in absolute path
        function obj = JsonConfig(cfgfile, varargin)
            obj@OptionsMap(varargin{:});
            if exist('cfgfile', 'var') && ~isempty(cfgfile) && ischar(cfgfile)
                if ~DosUtil.isabsolutepath(cfgfile)
                    cfgfile= [pwd '/' cfgfile];
                end
                cfgfile =  obj.SimplifyPath(cfgfile);
                obj.m_cfgfile = cfgfile; 
                obj.readJson();
            end
        end
        
        function WildCardRepStr(self, infos)
            self.m_cfg = DosUtil.WildCardRepStr_recursive(self.m_cfg, infos);
        end
        
        function RemoveCommentFileds(obj)
            if isstruct(obj.m_cfg)
                fns = fieldnames(obj.m_cfg);
                [I] = cellfun(@(x)(~isempty(regexp(x, '^x_', 'ONCE'))), fns);
                obj.m_cfg = rmfield(obj.m_cfg, fns(I));
            end
        end

        function readJson(self, fname)
            if ~exist('fname', 'var')
                fname = self.m_cfgfile;
            end
            if exist(fname, 'file')==2
                opt = self.getoptioni('options.convertJson');
                self.m_cfg = self.readJsonConfig(fname, opt);
            end
        end
        
        function writeJson(self, fname)
            if ~exist('fname', 'var')
                fname = self.m_cfgfile;
            end
            self.writeJsonConfig(self.m_cfg, fname);
        end
        
        function SetUndefinedConfig(self, name, val)
            if ~isfield(self.m_cfg, name)
                SetConfig(self, name, val);
            end
        end

        function SetUndefinedOrNullConfig(self, name, val)
            if isempty(StructBase.getfieldx(self, name))
                SetConfig(self, name, val);
            end
        end

        function SetConfig(self, name, val)
            self.m_cfg= StructBase.setfieldx(self.m_cfg, name, val); 
        end
        
        function CopyConfig(self, src, srcname, dstname)
            if ~exist('dstname', 'var')
                dstname = srcname; 
            end
            self.m_cfg= StructBase.setfieldx(self.m_cfg, dstname, src.GetConfig(srcname)); 
        end
        
        function res = ConfigNames(self)
            try
                res = fieldnames(self.m_cfg); 
            catch
                res = []; 
            end
        end
        
        function SetConfig_file(self, name, filename,varargin)
            options      = OptionsMap(varargin{:});
            relativepath = options.getoptioni_numeric('relativepath', 1);
            if relativepath
                filename    = DosUtil.relativepath(filename, RootPath(self),'/');
            end
            
            SetConfig(self, [name '.filename'], filename);
            fns = options.getOptionNames; 
            for k=1:numel(fns)
                fn = fns{k};
                val = options.getOption(fn); 
                SetConfig(self, [name '.' fn], val);
            end
            %SetConfig(self, [name '.filetype'], filetype);
        end
        
        function AddFile2List(self, fname, listname, varargin)
            fname = DosUtil.relativepath(fname, RootPath(self),'/');
            fname = self.SimplifyPath(fname);
            list = self.GetConfig(listname);
            if ismember(fname, list)
                return;
            end
            N = numel(list);
            list{N+1} = fname;
            setfieldx(self, listname, list);
        end

        function AddImage2ImageSet(self, imagefile, info)
            str.ID = [info.Modality '.' info.SeriesInstanceUID]; 
            names = {'Modality', 'SeriesDescription', 'SeriesDate', 'SeriesTime', 'SeriesInstanceUID'};
%             str.Modality = info.Modality; 
%             str.DateTime = [info.SeriesDate 'T' info.SeriesTime]; 
%             str.SeriesInstanceUID = info.SeriesInstanceUID;
%             str.SeriesDescription = info.SeriesDescription;
            for k=1:numel(names)
                name = names{k};
                str.(name) = StructBase.getfieldx_default(info, name, '');
            end
            str.ImageFileName = DosUtil.relativepath(imagefile, RootPath(self),'/');
            self.AddStruct2List(str, "ImageSet");
        end
        
        function AddDocument2DocumentSet(self, docfile,str)
            if ~exist('str', 'var') ||isempty(str)
                str  = struct;
            end
            str.DocFileName = DosUtil.relativepath(docfile, RootPath(self),'/');
            if ~isfield(str, 'ID')
                str.('ID') = str.DocFileName;
            end
            self.AddStruct2List(str, "DocumentSet");
        end
        
        function AddStruct2List(self, str, listname, withid)
            if ~exist('withid', 'var') || isempty(withid)
                withid = true; 
            end
            if withid
                AddStruct2List_withID(self, str, listname);
            else
                AddStruct2List_noID(self, str, listname);
            end
        end

        function AddStruct2List_noID(self, str, listname, varargin)
            list = self.GetConfig(listname);
            if isstruct(list)
                list = arrayfun(@(x)(x), list, 'uniformoutput', false);
            end
            N = numel(list);
            if N==0
                list{1} = str;
            else
               list{N+1}=str; 
            end
            setfieldx(self, listname, list);
        end

        function AddStruct2List_withID(self, str, listname, varargin)
            list = self.GetConfig(listname);
            if isstruct(list)
                list = arrayfun(@(x)(x), list, 'uniformoutput', false);
            end
            N = numel(list);
            if N==0
                list{1} = str;
            else
                ids = cellfun(@(x)(x.ID), list, 'uniformoutput', false);
                if isfield(str, 'ID')
                    [flag, loc] = ismember(str.ID, ids);
                    if flag
                        list{loc}=str; 
                    else
                        list{N+1}=str; 
                    end
                end
            end
            setfieldx(self, listname, list);
        end

        function str = GetStructFromList(self, id, listname)
             str =[];
             list = self.GetConfig(listname);
             if isstruct(list)
                list = arrayfun(@(x)(x), list, 'uniformoutput', false);
             end
             ids = cellfun(@(x)(x.ID), list, 'uniformoutput', false);
             [flag, loc] = ismember(id, ids);
             if flag
                 str = list{loc(1)};
             end
        end
        
        function [str, ids] = GetMatchStructFromList(self, listname, matchname, matchpat)
             str =[];
             list = self.GetConfig(listname);
             if isstruct(list)
                list = arrayfun(@(x)(x), list, 'uniformoutput', false);
             end
             ids = cellfun(@(x)(x.(matchname)), list, 'uniformoutput', false);
             I   = cellfun(@(x)(~isempty(regexpi(x,matchpat, 'ONCE'))), ids);
             str=list(I); ids = ids(I);
        end

        function [fnames, ids] = GetMatchFileFromList(self, listname, matchname, matchpat)
             [str, ids] =GetMatchStructFromList(self, listname, matchname, matchpat);
             fnames = []; 
             if ~isempty(str)
                 fnames = cellfun(@(x)(x.filename),str, 'uniformoutput', false);
                 fnames = cellfun(@(x)(self.FullFile(x)), fnames, 'uniformoutput', false);
             end
        end

        function [fullfile, filetype] = GetFullFile(self, name)
            fullfile     = GetConfig_default(self, [name '.filename'], '');
            filetype     = GetConfig_default(self, [name '.filetype'], '');
            if isempty(fullfile)
                fullfile = GetConfig_default(self, name, '');
            end
            
            if isempty(fullfile)
                return; 
            end
            
            if ~DosUtil.isabsolutepath(fullfile)
                fullfile = [RootPath(self) fullfile];
            end
            fullfile = self.SimplifyPath(fullfile);
        end
        
        function val = GetConfig(self, name)
            val      = StructBase.getfieldx(self.m_cfg, name); 
        end
        
        function val = GetConfig_default(self, name, defaultval)
            val      = getfieldx(self, name, defaultval);
        end
        
        function [val, ISFIELD] = getfieldx(self, name, defaultval)
            [val, ISFIELD]      = StructBase.getfieldx(self.m_cfg, name); 
            if ~ISFIELD && exist('defaultval', 'var')
                val = defaultval;
            end
        end
        
        function rmfields(self,  names)
            self.m_cfg = StructBase.rmfields(self.m_cfg,names);
        end

%         function val = getfieldx_default(self, name, defaultval)
%             [val, ISFIELD]      = self.getfieldx(name);
%             if ~ISFIELD && exist('defaultval', 'var')
%                 val = defaultval;
%             end
%         end

        function [val, ISFIELD] = getfieldxx(self, varargin)
            [val, ISFIELD]      = StructBase.getfieldxx(self.m_cfg, varargin{:}); 
        end
        
        function setfieldx(self, field, value)
            self.m_cfg = StructBase.setfieldx(self.m_cfg, field, value); 
        end
        
        function res = RootPath(self)
            res = self.getoptioni('jsonconfig.rootpath'); 
            if isempty(res)
                res  = [fileparts(self.m_cfgfile) '/']; 
                self.setOption('jsonconfig.rootpath', res); 
            end
        end
        
        function fname = FullFile(self, fname)
            if ~DosUtil.isabsolutepath(fname)
                fname = DosUtil.SimplifyPath([RootPath(self) fname]);
            end
        end
        
        function CloneFields(self, incfg, fns, filenames)
            cfg = incfg.m_cfg; 
            if ~exist('fns', 'var')||isempty(fns)
                fns = fieldnames(cfg);
            end
            
            if ~exist('filenames', 'var')||isempty(filenames)
                filenames='filename';
            end
            if ischar(filenames)
                filenames=strsplit(filenames, '|');
            end
            
            for k=1:numel(fns)
                fn = fns{k}; 
                str = cfg.(fn); 
                if ~iscell(str)
                    for m=1:numel(filenames)
                        filename = filenames{m};
                        fname = StructBase.getfieldx(str, filename); 
                        if ~isempty(fname)
                            fname = incfg.FullFile(fname);
                            fname    = DosUtil.relativepath(fname, RootPath(self),'/');
                            str.(filename) = fname;
                        end
                    end
                else
                    for n=1:numel(str)
                        for m=1:numel(filenames)
                            filename = filenames{m};
                            fname = StructBase.getfieldx(str{n}, filename); 
                            if ~isempty(fname)
                                fname = incfg.FullFile(fname);
                                fname    = DosUtil.relativepath(fname, RootPath(self),'/');
                                str{n}.(filename) = fname;
                            end
                        end
                    end
                end
                self.SetConfig(fn, str);
            end
        end
    end
    
    methods (Static)
        function writeJsonConfig(cfg, fname)
            header = jsonencode(cfg, 'PrettyPrint',true); 
            fid = fopen(fname,'wt');
            %fprintf(fid, header);
            fprintf(fid, '%s\r\n', header);
            fclose(fid);
        end
        
        function cfg = readJsonConfig(fname, varargin)
%             text     = fileread(fname);
%             try
%                 cfg  = jsondecode(text);
%             catch
%                 cfg = []; 
%             end
            cfg = utils.json.readJson(fname, varargin{:});
        end
        
        function islocked = FileLock(cfgfile)
            islocked = false; 
            if exist(cfgfile, 'file') %server as lock
                islocked = true; 
                return; 
            end
            %create an empty file as lock
            fclose(fopen([cfgfile], 'w'));
        end

        % function mrfile = SimplifyPath(mrfile)
        %     isnetpath=~isempty(regexpi(mrfile, '^\\\\', 'once'));
        %     mrfile = strrep(mrfile, '\', '/');
        %     parts  = strsplit(mrfile, '/');
        %     I      = cellfun(@(x)(strcmpi(x, '.')), parts);
        %     parts(I)=[];
        %     loc    = find(cellfun(@(x)(strcmpi(x, '..')), parts), 1);
        %     while(loc>1)
        %         parts([loc-1 loc])=[];
        %         loc      = find(cellfun(@(x)(strcmpi(x, '..')), parts), 1);
        %     end
        %     mrfile=fullfile(parts{:});
        %     mrfile = strrep(mrfile, '\', '/');
        %     if isnetpath
        %         mrfile = ['\\' mrfile];
        %     end
        % end
        
        function mrfile = SimplifyPath(mrfile)
            mrfile = DosUtil.SimplifyPath(mrfile);
        end
        
    end
end


