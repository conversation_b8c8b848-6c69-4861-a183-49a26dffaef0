function qMRIDaemon(varargin)
    if isdeployed
        warning('off','all');
    end
    %%%%%%%%%%%%%%%%%%%%%%check license%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    daemon.utils.LicenseTool.ValidateArtDaemonLicense('rtatlas');
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    
    % force the computer to remain awake
    try
    dosutils.insomnia('on','verbose');
    catch
    end
    server = daemon.qMRIServer(varargin{:});
    server.RunServer; 
end
