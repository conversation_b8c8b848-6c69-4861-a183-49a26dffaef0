function currentDir = getExeDir()
% Returns the full path of a deployed application.
%
% See Matlab solutions:
% http://www.mathworks.com/support/solutions/en/data/1-6OFV37/
% How can I find the directory containing my compiled application?
%
% Author: <PERSON><PERSON>

if isdeployed % Stand-alone mode.
    [~, result] = system('path');
    currentDir = char(regexpi(result, 'Path=(.*?);', 'tokens', 'once'));
else % MATLAB mode.
    currentDir = pwd;
end
