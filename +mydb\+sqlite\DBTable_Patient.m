classdef DBTable_Patient < mydb.sqlite.DBTable
    properties
        
    end

    methods
        function obj = DBTable_Patient(varargin)
            <EMAIL>({'dbt.PrimaryKey', 'PatientID'});
        end
    end

    methods (Static)
        function varnames = TableVaribleNames
             varnames = {'PatientID' 'PatientName' 'PatientBirthDate' 'PatientSex'};
             varnames = cat(2, varnames, mydb.sqlite.DBTable.DBTableTimeStampVarNames);
        end
    end
end