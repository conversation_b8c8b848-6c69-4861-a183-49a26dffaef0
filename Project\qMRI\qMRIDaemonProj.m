classdef qMRIDaemonProj <SAProject  
    properties
    end
    
    methods 
        function self = qMRIDaemonProj(varargin)
            self = self@SAProject(varargin{:}); 
            filepath = fileparts(mfilename('fullpath')); 
            addpath(fullfile(filepath,  '..')); 
        end
        
        function res = BuildType(self)
             res = 'console'; 
%            res = self.getoptioni('buildtype', 'standalone') ;
        end
        
        function res = privateFolder(self)
            res = {}; 
        end
        
        function res = RecursivePath(self)
           SourcePath = [self.SolutionRootPath];
           %res = {[SourcePath 'repo-common\Common\base\'], [SourcePath 'repo-common\Common\model\'], [SourcePath 'repo-common\Common\RT\']};
           res = {[SourcePath 'repo-common\Common\'], [SourcePath 'repo-imviewer\imViewer\']};
        end
        
        function res = ProjectPath(self)
            SourcePath = [self.SolutionRootPath];
            aipath    = [SourcePath 'repo-ai\'];
            imagepath = [SourcePath 'repo-image\'];
            dcmpath   = [SourcePath 'dcmserver\src\'];
            res = {aipath, dcmpath, imagepath}; 
        end
    end
    
    methods (Static)
        function name = AppName()
            name = 'qMRIDaemon'; 
        end
        
        function res = mFiles
            res = {'qMRIDaemon.m'}; 
        end
        
        function res = Toolboxes
            res = {'images', 'matlab', 'stats', 'textanalytics'};
        end  
        
    end
 end

