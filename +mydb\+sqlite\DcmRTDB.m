classdef DcmRTDB <mydb.sqlite.DataBase
    
    properties
        
    end
    
    methods
        function obj = DcmRTDB(dbfile, varargin)
            options = OptionsMap({'course.followupdates', 90});
            <EMAIL>(dbfile, options, varargin{:});
        end
        
        function res = DcmDataRoot(self)
            res = self.getoptioni('DCMSERVER_FOLDER');
        end
        
        function T = QueryPatient_modality(self, modality, patid)
            T   = []; 
            dbT = self.GetDBTable_modality(modality);
            if ~isempty(dbT)
                str = struct('PatientID', patid);
                T = dbT.Query_struct(str);
            end
        end
        
        function CreateDBTable_modality(self, modalities)
            if ischar(modalities)
                modalities = strsplit(modalities);
            end
            for k=1:numel(modalities)
                modality  = upper(modalities{k}); 
                tablename = self.GetTableName_modality(modality);
                PRIMARY_KEY= self.GetTablePrimaryKey_modality(modality);
                switch modality
                    case {'USER'}
                        varnames = mydb.sqlite.DBTable_User.TableVaribleNames;
                    case {'PATIENT'}
                        varnames = mydb.sqlite.DBTable_Patient.TableVaribleNames;
                    case {'STUDY'}
                        varnames = mydb.sqlite.DBTable_Study.TableVaribleNames;
                    case {'COURSE'}
                        varnames = mydb.sqlite.DBTable_Course.TableVaribleNames;
                    otherwise
                        varnames = ai.DcmParser.GatTableVarNames(modality);
                        varnames = cat(2, varnames, mydb.sqlite.DBTable.DBTableTimeStampVarNames);
                end
                self.CreateTextTable(tablename, varnames, {'PRIMARY_KEY', PRIMARY_KEY});
            end
        end
        
        function UpdateDcmRecords(self, T)
             if istable(T)
                T = table2struct(T);
             end
             for k=1:numel(T)
                 UpdateDcmRecord(self, T(k));
             end
        end

        function UpdateUserRecord(self, info)
            modalityDB=self.GetDBTable('USER');
            modalityDB.InsertRecord(info);
        end
        
        function UpdateDcmRecord(self, info)
            try
                 modalityDB=self.GetDBTable_modality(info.Modality);
                 if isempty(modalityDB)
                     return; 
                 end
                 modalityDB.InsertRecord(info);
            catch err 
                self.LogErr(err);
            end

            try
                studyDB = self.GetDBTable_modality('STUDY');
                if ~isempty(studyDB)
                    studyDB.InsertRecord(info);
                end
            catch err
                self.LogErr(err);
            end

            try
                patientDB = self.GetDBTable_modality('PATIENT');
                if ~isempty(patientDB)
                    patientDB.InsertRecord(info);
                end
            catch err
                self.LogErr(err);
            end
        end

        function UpdatePatientStudyTable(self, patid)
            dbT    = self.GetDBTable_modality('STUDY');
            if isempty(dbT)
                return; 
            end
            modalities = {'CT', 'MR', 'PT'}; 
            for k=1:numel(modalities)
                modality = modalities{k};
                modalityT = self.QueryPatient_modality(modality, patid);
                if isempty(modalityT)
                    continue;
                end
                for m=1:size(modalityT, 1)
                    dbT.InsertRecord(table2struct(modalityT(m, :)));
                end
            end
        end

        function UpdatePatientPatientTable(self, patid)
            dbT    = self.GetDBTable_modality('PATIENT');
            if isempty(dbT)
                return; 
            end
            modalities = {'STUDY'}; 
            for k=1:numel(modalities)
                modality = modalities{k};
                modalityT = self.QueryPatient_modality(modality, patid);
                if isempty(modalityT)
                    continue;
                end
                for m=1:size(modalityT, 1)
                    dbT.InsertRecord(table2struct(modalityT(m, :)));
                end
            end
        end

        function UpdatePatientCourseTable(self, patid)
            studyT = self.QueryPatient_modality('STUDY', patid);
            dbT    = self.GetDBTable_modality('COURSE');
            if isempty(studyT) || isempty(dbT)
                return; 
            end

            maxd    = self.getoptioni_numeric('course.followupdates');
            courseT = self.Study2CourseTable(studyT, maxd);
           
            for k=1:size(courseT, 1)
                dbT.InsertRecord(table2struct(courseT(k, :)));
            end
        end

        function UpdatePatientTables(self, patid)
            UpdatePatientStudyTable(self,  patid);
            UpdatePatientPatientTable(self, patid);
            UpdatePatientCourseTable(self, patid);
        end

        function dbT = GetDBTable_modality(self, modality)
            tbname   = self.GetTableName_modality(modality);
            dbT = self.GetDBTable(tbname); 
        end
        
        function info = GetTableRow(self, modality, uid)
            dbT  = GetDBTable_modality(self, modality);
            info = dbT.QueryPrimaryUID(uid);
        end
        
        function T    = GetTable_filter(self, modality, filter)
            dbT = GetDBTable_modality(self, modality);
            
            if isempty(dbT)
                T=table; return;
            end
            
            T   = dbT.Query;
            if exist('filter', 'var')
                [~, T] = filter.FilterTable(T);
            end
        end
        
        function [studyT] = ReconStudyTable(self)
            modalities = {'MR', 'CT', 'PT'};
%             T          = xls.RecordKeepTable({'table.uniquekey', 'StudyInstanceUID'}); 
%             varnames   = self.StudyInfoVarNames;
%             rawT = xls.TableBase.CreateEmptyTable(varnames, 'char');
%             T.SetRawTable(rawT); 
%             for k=1:numel(modalities)
%                 modT   = self.GetTable_filter(modalities{k});
%                 T.UpdateFromTable(modT);
%             end
%             studyT     = T.GetRawTable; 
            T = []; 
            varnames   = self.StudyInfoVarNames;
            groupvar = varnames{1}; copyvarnames = varnames(2:end); 
            subname  = 'Series';
            for k=1:numel(modalities)
                modT   = self.GetTable_filter(modalities{k});
                if ~isempty(modT)
                    T1 = self.GroupTable_DateTime(modT, groupvar, copyvarnames, subname);
                    T  = cat(1, T, T1); 
                end
            end
            studyT     = T; 
            %studyT = self.GroupTable_DateTime2(T, groupvar, copyvarnames, subname);
        end
        
        function [B] = ReconPatientTable(self, studyT, varargin)
            if ~exist('studyT', 'var')
                [studyT] = ReconStudyTable(self);
            end
            groupvar = 'PatientID'; subname = 'Study'; 
            copyvarnames = {'PatientName' 'PatientBirthDate' 'PatientSex'};
            B = self.GroupTable_DateTime(studyT, groupvar, copyvarnames, subname, varargin{:});
        end
        
%         function DeletePatient(self, patid)
%             tbnames = self.ListTables(); 
%             tbnames = setdiff(tbnames, {'USER'}, 'stable'); 
%             for k=1:numel(tbnames)
%                 tbname  = tbnames{k};
%                 condstr = ['PatientID = ' patid]; 
%                 self.DeleteTableRows(tbname,  condstr);
%             end
%         end
%         
%         function DeleteStudy(self, StudyInstanceUID)
%             tbnames = self.ListTables(); 
%             tbnames = setdiff(tbnames, {'USER'}, 'stable'); 
%             for k=1:numel(tbnames)
%                 tbname  = tbnames{k};
%                 condstr = ['StudyInstanceUID = ' StudyInstanceUID]; 
%                 self.DeleteTableRows(tbname,  condstr);
%             end
%         end
%         
%         function DeleteSeries(self, SeriesInstanceUID)
%             tbnames = self.ListTables(); 
%             tbnames = setdiff(tbnames, {'USER'}, 'stable'); 
%             for k=1:numel(tbnames)
%                 tbname  = tbnames{k};
%                 condstr = ['SeriesInstanceUID = ' SeriesInstanceUID]; 
%                 self.DeleteTableRows(tbname,  condstr);
%             end
%         end
%         
%         function DeleteInstance(self, SOPInstanceUID)
%             tbnames = self.ListTables(); 
%             tbnames = setdiff(tbnames, {'USER'}, 'stable'); 
%             for k=1:numel(tbnames)
%                 tbname  = tbnames{k};
%                 condstr = ['SOPInstanceUID = ' SOPInstanceUID]; 
%                 self.DeleteTableRows(tbname,  condstr);
%             end
%         end
        
        function res = RemoveDcmData(self, uid, level, dcmrootfolder)
            if exist('dcmrootfolder', 'var') && ischar(dcmrootfolder)
                try
                [res, isdir] = GetDcmFolderOrFile(self, uid, level, dcmrootfolder);
                if ~isempty(res)
                    if isdir
                        rmdir([res], 's'); 
                    else
                        delete(res); 
                    end
                end
                catch
                end
            end
            
            tbnames = self.ListTables(); 
            tbnames = setdiff(tbnames, {'USER'}, 'stable'); 
            res = [];
            switch upper(level)
                case {'PATIENT'}
                    colname = 'PatientID';
                case {'STUDY'}
                    colname = 'StudyInstanceUID';
                case {'SERIES', 'CT', 'MR', 'PT', 'US', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'REG', 'SEG'}
                    colname = 'SeriesInstanceUID';
                case {'INSTANCE', 'RS', 'RD', 'RP', 'RI', 'RE', 'SE'}
                    colname = 'SOPInstanceUID';
                otherwise
                    return
            end
            condstr = [colname '="' uid '"'];
            for k=1:numel(tbnames)
                tbname  = tbnames{k};
                self.DeleteTableRows(tbname,  condstr);
            end
            
            
        end

        function [res, isdir] = GetDcmFolderOrFile(self, uid, level, dcmrootfolder)
            switch upper(level)
                case {'PATIENT'}
                    res  = [uid '/'];
                    isdir= 1; 
                case {'STUDY'}
                    dbT =  self.GetDBTable_modality(level);
                    info=  dbT.Query(['where StudyInstanceUID="' uid '"'], 0);
                    res =  [info.PatientID '/' uid '/'];
                    isdir= 1; 
                case {'CT', 'MR', 'PT', 'US', 'RTPLAN', 'RTSTRUCT', 'RTDOSE'}
                    dbT =  self.GetDBTable_modality(level);
                    %info=  dbT.QueryPrimaryUID(uid);
                    info=  dbT.Query(['where SeriesInstanceUID="' uid '"'], 0);
                    res =  [info.PatientID '/' info.StudyInstanceUID '/' level '.' uid '/'];
                    isdir= 1; 
                case {'RP', 'RS', 'RD', 'RE', 'SE'}
                    level = strrep(level, 'RP', 'RTPLAN');
                    level = strrep(level, 'RS', 'RTSTRUCT');
                    level = strrep(level, 'RD', 'RTDOSE');
                    level = strrep(level, 'RE', 'REG');
                    level = strrep(level, 'SE', 'SEG');
                    dbT =  self.GetDBTable_modality(level);
                    info=  dbT.Query(['where SOPInstanceUID="' uid '"'], 0);
                    res =  [info.PatientID '/' info.StudyInstanceUID '/' level '.' info.SeriesInstanceUID '/' uid '.dcm'];
                    isdir= 0; 
            end
            
            if exist('dcmrootfolder', 'var') && ischar(dcmrootfolder)
                res = [dcmrootfolder res];
            end
        end

        function ExportSubDB(self, task)
            subdbfile = StructBase.getfieldx(task, 'DstDBFile');
            if ~exist(subdbfile, 'file')
                self.CreateDBTables(subdbfile);
            end
            filters= StructBase.getfieldx(task, 'ExportFilter');
            if ischar(filters) && endsWith(filters, '.xlsx','IgnoreCase',true)
                filters = xls.TableBase.ReadTable(filters);
            end

            subdb   = mydb.sqlite.DcmRTDB(subdbfile, {'DBConnection', 2});
            try
                tbnames = StructBase.getfieldx(task, 'SrcDBTableNames');
                if isempty( tbnames)
                    tbnames = self.ListTables();
                    tbnames = setdiff(tbnames, {'USER', 'PATIENT', 'COURSE', 'STUDY'});
                end
                if ischar(tbnames)
                    tbnames = strsplit(tbnames, '|');
                end
                PatientID =[];
                for k=1:numel(tbnames)
                    tbname = tbnames{k};
                    dbT = self.GetDBTable_modality(tbname);
                    %T = dbT.QueryAndFilter(filters);
                    T = dbT.Query_structs(filters);
                    if isempty(T)
                        continue;
                    end
                    PatientID1= unique(T.PatientID);
                    PatientID = union(PatientID, PatientID1);
                    subdb.InsertMatlabTable(T, tbname);
                end
    
                for m=1:numel(PatientID)
                    patid = PatientID{m};
                    subdb.UpdatePatientTables(patid);
                end
    
                xlsfile = StructBase.getfieldx_default(task, 'DstXlsFile', '');
                if ~isempty(xlsfile)
                    subdb.Export2Xls(task);
                end
                subdb.CloseDB();
            catch
                subdb.CloseDB();
            end
        end
    end
    
    methods (Static)
         function T = GroupTable(rawT, groupvar, copyvarnames, subname, varargin)
             options      = OptionsMap(varargin{:}); 
             T = varfun(@(x)(x(1)), rawT, 'GroupingVariables', groupvar, ...
                'InputVariables', copyvarnames);
             for k=1:numel(copyvarnames)
                varname = copyvarnames{k};
                T.Properties.VariableNames{['Fun_' varname]} = varname;
             end
             %subname = options.getoptioni('subname', 'Group');
             T.Properties.VariableNames{'GroupCount'} = [subname 'Count'];
             T=movevars(T, [subname 'Count'], 'after', size(T, 2));
         end
         
         function T = GroupTable_DateTime(rawT, groupvar, copyvarnames, subname, varargin)
             options       = OptionsMap(varargin{:}); 
             %copyvarnames = options.getoptioni('copyvarnames');
             T = mydb.sqlite.DcmRTDB.GroupTable(rawT, groupvar, copyvarnames, subname, options);
             varnames      = rawT.Properties.VariableNames; 
             firstdatetime = ['First' subname 'DateTime'];
             lastdatetime  = ['Last'  subname 'DateTime'];
             datetimeformat= 'yyyymmddTMMHHSS'; 
             if ismember([subname 'Date'], varnames) && ismember([subname 'Time'], varnames)    
                DataStr    = strcat(rawT.([subname 'Date']), cellfun(@(x)(['T' x]),  rawT.([subname 'Time']), 'uniformoutput', false));
                rawT.DateNum = datenum(DataStr, datetimeformat);  
                T.(firstdatetime)=varfun(@(x)(datestr(min(x), datetimeformat)), rawT, 'GroupingVariables', groupvar, ...
                    'InputVariables', {'DateNum'}, 'OutputFormat', 'cell'); 
                T.(lastdatetime)=varfun(@(x)(datestr(max(x), datetimeformat)), rawT, 'GroupingVariables', groupvar, ...
                    'InputVariables', {'DateNum'}, 'OutputFormat', 'cell');
             end
         end
         
         function T = GroupTable_DateTime2(rawT, groupvar, copyvarnames, subname, varargin)
             options      = OptionsMap(varargin{:}); 
             %copyvarnames = options.getoptioni('copyvarnames');
             T = mydb.sqlite.DcmRTDB.GroupTable(rawT, groupvar, copyvarnames, subname, options);
             varnames      = rawT.Properties.VariableNames;
             datetimeformat = 'yyyymmddTMMHHSS'; 
             firstdatetime = ['First' subname 'DateTime'];
             lastdatetime  = ['Last'  subname 'DateTime'];
             groupcountname = [subname 'Count']; 
             if ismember(groupcountname, varnames)
                 groupcount = varfun(@(x)(sum(x)), rawT, 'GroupingVariables', groupvar, ...
                        'InputVariables', {groupcountname}, 'OutputFormat', 'uniform');  
                 T.(groupcountname)=groupcount;    
                 if ismember(firstdatetime, varnames)
                    DataStr = rawT.(firstdatetime);
                    rawT.DateNum = datenum(DataStr, datetimeformat);  
                    T.(firstdatetime)=varfun(@(x)(datestr(min(x), datetimeformat)), rawT, 'GroupingVariables', groupvar, ...
                        'InputVariables', {'DateNum'}, 'OutputFormat', 'cell');
                 end
                 if ismember(lastdatetime, varnames)
                    DataStr = rawT.(lastdatetime);
                    rawT.DateNum = datenum(DataStr, datetimeformat);  
                    T.(lastdatetime)=varfun(@(x)(datestr(max(x), datetimeformat)), rawT, 'GroupingVariables', groupvar, ...
                        'InputVariables', {'DateNum'}, 'OutputFormat', 'cell');
                 end
             end
         end
         
         
         function res = GetTableName_modality(modality)
            modality = upper(modality); 
            switch modality
                case {'RTPLAN', 'RTPLANDB'}
                    res = 'RTPLANDB';
                otherwise
                    res = modality; 
            end
         end
        
         function res = GetTablePrimaryKey_modality(modality)
            modality = upper(modality); 
            switch modality
                case {'USER'}
                    res = 'UserID';
                case {'CT', 'MR', 'PT'}
                    res = 'SeriesInstanceUID';
                case {'STUDY'}
                    res = 'StudyInstanceUID';
                case {'COURSE'}
                    res = 'CourseUID';
                case {'PATIENT'}
                    res = 'PatientID';
                otherwise
                    res = 'SOPInstanceUID'; 
            end
         end
         
         
         function modalities = DefaultTableNames()
                %modalities = {'USER', 'PATIENT', 'COURSE', 'STUDY', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'CT', 'MR', 'PT', 'REG'};
                modalities = {'USER', 'PATIENT', 'COURSE', 'STUDY', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'CT', 'MR', 'PT', 'US', 'REG', 'SEG'};
         end
         
         function CreateDBTables(dbfile, modalities)
             db = mydb.sqlite.DcmRTDB(dbfile, {'DBConnection', 3});
             if ~exist('modalities', 'var')
                 %modalities = {'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'CT', 'MR', 'PT', 'REG'};
                 modalities = mydb.sqlite.DcmRTDB.DefaultTableNames();
             end
             db.CreateDBTable_modality(modalities);
             db.CloseDB();
         end
         
         function varnames = PatientInfoVarNames
             varnames = {'PatientID' 'PatientName' 'PatientBirthDate' 'PatientSex', 'FirstStudyDateTime', 'LastStudyDateTime'};
         end
         
         function varnames = StudyInfoVarNames
             varnames = {'StudyInstanceUID', 'PatientID' 'PatientName' 'PatientBirthDate' 'PatientSex', 'StudyDate' 'StudyTime' 'StudyDescription'};
         end

         function Xls2Sqlite(xlsfile, sqlitefile, varargin)
            if ~exist(sqlitefile, 'file')
                mydb.sqlite.DcmRTDB.CreateDBTables(sqlitefile);
            end
            dbf         = mydb.sqlite.DcmRTDB(sqlitefile);
            modalities = {'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'CT', 'MR', 'PT', 'US', 'REG', 'SEG'};
            %primarykeys= {'SeriesInstanceUID', 'SOPInstanceUID', 'SOPInstanceUID', 'SOPInstanceUID'};
            primarykeys=  cellfun(@(x)(mydb.sqlite.DcmRTDB.GetTablePrimaryKey_modality(x)), modalities, 'UniformOutput', false); 
            dbtablenames= cellfun(@(x)(mydb.sqlite.DcmRTDB.GetTableName_modality(x)), modalities, 'UniformOutput', false); 
            %dbf.CreateFromXls(xlsfile, varargin{:});
            dbf.ImportXls(xlsfile, {'xls.sheets', modalities},{'db.tablenames', dbtablenames}, {'primarykeys', primarykeys});
         end

         function T = Study2CourseTable(studyT, maxd)
            StudyDate= studyT.('StudyDate');
            %maxd     = self.getoptioni_numeric('course.followupdates');
            clusters = mydb.sqlite.DcmRTDB.ClusterStudyDate(StudyDate, maxd);
            clusters = clusters(:);
            patid    = studyT.PatientID{1};
            N        = numel(clusters);
            CourseNumber=[1:N]';
            PatientID = arrayfun(@(x)(patid), CourseNumber, 'uniformoutput', false); 
            CourseUID  = arrayfun(@(x)([patid '.' num2str(x)]), CourseNumber, 'uniformoutput', false);
            FirstStudyDate = cellfun(@(x)(x{1}), clusters, 'uniformoutput', false); 
            LastStudyDate  = cellfun(@(x)(x{end}), clusters, 'uniformoutput', false); 
            StudyDates = cellfun(@(x)(xls.TableBase.Content2Str(x)), clusters, 'uniformoutput', false); 
            T = table(CourseUID, PatientID, CourseNumber, FirstStudyDate, LastStudyDate, StudyDates);
        end

        function clusters = ClusterStudyDate(StudyDate, maxd)  
            StudyDate = unique(StudyDate);
            I         = cellfun(@(x)(isempty(x)), StudyDate);
            StudyDate(I)=[];
            dateNum = cellfun(@(x)(datenum(x, 'yyyymmdd')), StudyDate);
            [dateNum, J] = sort(dateNum, 'ascend');
            StudyDate = StudyDate(J);
            N = numel(dateNum);
            m       = 1;
            clusters{m} = {StudyDate{1}};
            k=0;
            for n=2:N
                if (dateNum(n)-dateNum(n-1))>maxd
                    m=m+1; k=0; 
                    clusters{m}= {StudyDate{n}};
                else
                    k=k+1;
                    clusters{m}{k}= StudyDate{n};
                end
            end
            if sum(I)>=1
               clusters{m+1} = {''};
            end
        end

        function MigrateDB(srcdbfile, dstdbfile, tablenames, varargin)
            options      = OptionsMap(varargin{:});
            tablefilter0 = options.getoptioni('table.filter.all');
            if  ischar(tablefilter0)&&~isempty(tablefilter0)
                tablefilter0 = xls.TableFilter({'FilterFileName', tablefilter0});
            end

            if ~exist('tablenames', 'var')||isempty(tablenames)
                tablenames =  mydb.sqlite.DcmRTDB.DefaultTableNames();
            end
            
            if ~exist(dstdbfile, 'file')
                mydb.sqlite.DcmRTDB.CreateDBTables(dstdbfile,  tablenames); 
            end

            mydb.sqlite.DataBase.CloseAll();
            srcdb = mydb.sqlite.DcmRTDB(srcdbfile, {'DBConnection', 2});
            dstdb = mydb.sqlite.DcmRTDB(dstdbfile, {'DBConnection', 3});
            %tablenames = srcdb.ListTables();
          
            for k=1:numel(tablenames)
                tablename = tablenames{k};
                T = srcdb.GetTable_filter(tablename); 
                if isempty(T)
                    continue; 
                end

                tablefilter = tablefilter0;                
                if options.isOption(['table.filter.' tablename])
                    tablefilter = options.getoptioni(['table.filter.' tablename]);
                    if  ischar(tablefilter) &&~isempty(tablefilter)
                        tablefilter = xls.TableFilter({'FilterFileName', tablefilter});
                    end
                end
                
                if ~isempty(tablefilter)
                    [~, T] = tablefilter.FilterTable(T);
                end
                
                if isempty(T)
                    continue; 
                end

                dbT = dstdb.GetDBTable_modality(tablename);
                dbT.InsertMatlabTable(T);
            end
            srcdb.CloseDB();
            dstdb.CloseDB();
        end

        function DcmXls2DB(dcmfolder, dstdbfile, tablenames)
            mydb.sqlite.DataBase.CloseAll();
            
            if ~exist('tablenames', 'var')||isempty(tablenames)
                tablenames =  mydb.sqlite.DcmRTDB.DefaultTableNames();
            end
            
            if ~exist(dstdbfile, 'file')
                mydb.sqlite.DcmRTDB.CreateDBTables(dstdbfile,  tablenames); 
            end
            
            res    = dir(dcmfolder); 
            res(~[res(:).isdir])=[]; res(1:2)=[]; 
            MRNs   = {res(:).name};
            xlsfiles = cellfun(@(x)([dcmfolder x '/patient.' x '.xlsx']), MRNs, 'uniformoutput', false);
            I = cellfun(@(x)(exist(x, 'file')==2), xlsfiles); 
            xlsfiles=xlsfiles(I); MRNs = MRNs(I);
            for k=1:numel(MRNs)
                try
                mydb.sqlite.DcmRTDB.Xls2Sqlite(xlsfiles{k}, dstdbfile);
                catch
                    disp(['error: import ' xlsfiles{k}]); 
                end
            end

            
            db = mydb.sqlite.DcmRTDB(dstdbfile);
            for k=1:numel(MRNs)
                try
                db.UpdatePatientTables(MRNs{k});
                catch
                end
            end
            db.CloseDB();
        end

        function rsT = JoinTable_RS_Image(xlsfile, modality)
            T = xls.TableBase.ReadTable(xlsfile, {'table.sheetname', 'RTSTRUCT'}); %1321
            rmvarnames = {...
                'DBCreationUser'   'DBCreationTimeStamp'    'DBUpdateUser'     'DBUpdateTimeStamp' ...
                'DBLastAccessUser'    'DBLastAccessTimeStamp'    'Tasks'      'Notes'     'Comments'    'Status'};
            T = removevars(T, rmvarnames);
            mrT=xls.TableBase.ReadTable(xlsfile, {'table.sheetname', modality}); 
            mrT = removevars(mrT, rmvarnames);
            
            I = ismember(T.ReferencedImageModality,  modality); 
            T = T(I, :); %649
            T=removevars(T,{'StudyDescription'	'Modality' 'SeriesDate'	'SeriesTime'...
                'SeriesDescription'	'Manufacturer'	'ManufacturerModelName'	'InstitutionName' 'StructureSetDate'	'StructureSetTime'	'FrameOfReferenceUID'});
            [I1, loc] = ismember(T.ReferencedImageSeriesUID, mrT.SeriesInstanceUID);
            T = T(I1,:); loc(~I1)=[];
            mrT = mrT(loc, :);
            switch upper(modality)
                case 'MR'
                    varnames = {'FrameOfReferenceUID' 'SeriesDescription'  'Manufacturer' 'ManufacturerModelName' 'ProtocolName'  'SequenceName'  'MRAcquisitionType'    'MagneticFieldStrength'    'BodyPartExamined'    'SliceThickness'    'RescaleType'    'WindowCenter'    'WindowWidth'    'NumberOfSlices'};
                case 'CT'
                    varnames = {'FrameOfReferenceUID' 'SeriesDescription'  'Manufacturer' 'ManufacturerModelName'       'SliceThickness'    'RescaleType'    'WindowCenter'    'WindowWidth'    'NumberOfSlices'};
            end
            rsT= cat(2, T, mrT(:, varnames));
            rsT= renamevars(rsT, 'SOPInstanceUID', 'RSUID'); 
            rsT= movevars(rsT, 'PatientID', 'before', 1); 
        end

        function  varnames = MetaVarNames()
            varnames = {...
                'DBCreationUser'   'DBCreationTimeStamp'    'DBUpdateUser'     'DBUpdateTimeStamp' ...
                'DBLastAccessUser'    'DBLastAccessTimeStamp'    'Tasks'      'Notes'     'Comments'    'Status'};
        end
    end
end

