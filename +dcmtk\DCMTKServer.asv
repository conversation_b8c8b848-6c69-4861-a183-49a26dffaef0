classdef DCMTKServer <dcmtk.DCMTKWrapper   
    properties
        m_DB; 
    end
    
    methods
       

        function self = DCMTKServer(varargin)
            self = <EMAIL>(varargin{:}); 
            openDcmServer(self);
        end
        
        function res = UseSQLite(self)
            res = self.getoptioni('dcmdb.usesqlite', 0); 
            if ischar(res) 
                res = str2num(res); 
            end
        end
        
         function [status, result] = openDcmServer(self)
            status =[]; result =[]; 
            dcmserver_folder = self.DCMSERVER_FOLDER;
            if isempty(dcmserver_folder)
                return;
            end
            
            isanon= self.getoptioni_numeric('IsAnonymousDB');
            
%             if UseSQLite(self)
%                 self.m_DB = dcmtk.DcmDb_sqlite(dcmserver_folder, {'IsAnonymousDB', isanon});
%                 self.setupLogger('DcmServer');
%             else
                str='DCMSERVER_FOLDER.incoming'; 
                incomingfolder = self.getOption(str);
                self.m_DB = dcmtk.DcmDb(dcmserver_folder, {'IsAnonymousDB', isanon}, {str, incomingfolder});

%             end
         end
        
        function [imagecounts, seriescounts, studycounts] = RetrievePatient(self, patid, modalities, varargin)
%             patid = '92827181'; %multimets
            studyids = self.queryStudy(patid); 
            studycounts = numel(studyids); 
            seriescounts = 0; 
            imagecounts = 0; 
            retrievelevel = DefaultRetrieveLevel(self); 
            patfolder = [self.DCMSERVER_FOLDER patid '/'];
            for k=1:studycounts
                studyid = studyids{k}; 
                for m=1:numel(modalities)
                    modality = modalities{m}; 
                    %seriesids = self.querySeries(studyid, modality);
                    [seriesids, rsps] = self.querySeries(studyid, modality);
                    for n=1:numel(seriesids)
                        seriescounts = seriescounts+1; 
                        %seriesid = seriesids{n}; 
                        %self.RetrieveSeries(seriesid, studyid, patid);
                        self.RetrieveSeries(rsps(n));
                    end
                end
            end
        end
         
%         function [status, result] = retrieveImage(self, uid)
%             status = []; result = []; 
%             if isempty(self.m_DB)
%                 [status, result] = <EMAIL>(uid); 
%             else
%                 %if ~self.m_DB.ExistImage(uid)
%                     [status, result] = <EMAIL>(uid); 
%                     self.m_DB.storeIncoming(true);
%                 %end
%             end
%         end
        
        function RetrieveSeries(self, varargin)
            self.retrieveSeries(varargin{:}); 
            if ~isempty(self.m_DB) 
                self.m_DB.storeIncoming(true);
            end
        end

        function RetrieveStudy(self, studyuid, modality)
            self.retrieveStudy(studyuid, modality); 
            if ~isempty(self.m_DB) 
                self.m_DB.storeIncoming(true);
            end
        end

        function RetrieveImage(self, varargin)
            self.retrieveImage(varargin{:}); 
            if ~isempty(self.m_DB) 
                self.m_DB.storeIncoming(true);
            end
        end

%         function [status, result] = retrieveSeries(self,  seriesuid, studyuid, patfolder)
%             status = []; result = [];
%             
%             res = DosUtil.rdir([patfolder studyuid '\*' seriesuid '\']); 
%             if ~isempty(res)
%                 return
%             end
%             
%             [status, result] = <EMAIL>(seriesuid, studyuid); 
%             if ~isempty(self.m_DB) &&~isempty(status)
%                 self.m_DB.storeIncoming(true);
%             end
%         end
        
        function RetrieveRTPatient(self, patid, varargin)
            self.RetrievePatient(patid, {'RTPLAN', 'RTSTRUCT', 'RTDOSE'}, varargin{:});

            options = OptionsMap(varargin{:}); 
            retrieveimage = options.getoptioni_numeric('retrieveplanningimage', 0); 
            patfolder = [self.DCMSERVER_FOLDER patid '/']; 
            if retrieveimage 
                pattablefile =self.HouseKeepMRN(self.DCMSERVER_FOLDER, patid, {'RTPLAN', 'RTSTRUCT', 'RTDOSE'}); 
                if ~isempty(pattablefile)
                    T = xls.TableBase.ReadTable(pattablefile, {'table.sheetname', 'RTPlan'});
                    T1 = T; 
                    T1.SeriesInstanceUID = T.ReferencedImageSeriesUID;
                    T1.Modality = T.ReferencedImageModality;
                    N = size(T1, 1);
                    for n=1:N
                        info = table2struct(T1(n, :));
                        self.RetrieveSeries(info);
                    end
                end
                %self.RetrievePatientPlanningImages(patfolder, varargin{:});
            end
            
            try
                options = OptionsMap(varargin{:}); 
                updatedcmtable = options.getoptioni_numeric('patient.updatedcmtable', 0); 
                if updatedcmtable
                    ai.DcmTable.ProcessRTPatient(patfolder,[], options);
                end
            catch err
                disp(err.message);
            end
        end
        
        function rptable = RetrievePatientPlanningImages(self, patfolder, varargin)
            rptable   = ai.RTPlanTable(varargin{:}); 
%             patfolder = [self.DCMSERVER_FOLDER patid '/']; 
            rptable.ProcessPatient(patfolder);
            if isempty(rptable.m_Table)
                return;
            end
            try
                options = OptionsMap(varargin{:}); 
                outputrptable = options.getoptioni_numeric('outputrptable', 0); 
                if outputrptable
                    rptable.WriteTable([patfolder 'RTPlanTable.xls']); 
                end
            catch err
                disp(err.message);
            end
            
%             retrievelevel = self.DefaultRetrieveLevel();  
            ReferencedImageSeriesUID = rptable.m_Table.ReferencedImageSeriesUID;
            imageseriesuids = unique(ReferencedImageSeriesUID);
            studyids = rptable.m_Table.StudyInstanceUID;
            for k=1:numel(imageseriesuids)
                series = imageseriesuids{k}; 
                if isempty(series)
                    continue; 
                end
                kk = find(ismember(ReferencedImageSeriesUID, series), 1);
                self.RetrieveSeries(series, studyids{kk});
%                 switch lower(retrievelevel)
%                     case 'image'
%                         imgs = self.queryImage(series);
%                         self.retrieveImages(imgs); 
%                     case 'series'
%                         studyids = rptable.m_Table.StudyInstanceUID;
%                         kk = find(ismember(rptable.m_Table.ReferencedImageSeriesUID, series), 1);
%                         status = self.retrieveSeries( series, studyids{kk}, patfolder);
%                         if ~isempty(status)
%                             
%                         end
%                 end
                
            end
        end
    end
    
    methods (Static)
        function setupLogger(appname)
            defaultProperties = strjoin({...
                'log4j.rootLogger=DEBUG, ACONSOLE, AFileAll, AFileInfo, AFileWarn'...
                'log4j.appender.ACONSOLE = org.apache.log4j.ConsoleAppender'...
                'log4j.appender.ACONSOLE.layout = org.apache.log4j.PatternLayout'...
                'log4j.appender.ACONSOLE.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
                ''...
                'log4j.appender.AFileAll = org.apache.log4j.RollingFileAppender'...
                sprintf('log4j.appender.AFileAll.File = %s', strrep(fullfile(getExeDir, [appname '.debug.log']), '\', '/'))...
                'log4j.appender.AFileAll.Append = true'...
                'log4j.appender.AFileAll.layout = org.apache.log4j.PatternLayout'...
                'log4j.appender.AFileAll.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
                ''...
                'log4j.appender.AFileInfo.Threshold = INFO'...
                'log4j.appender.AFileInfo = org.apache.log4j.RollingFileAppender'...
                sprintf('log4j.appender.AFileInfo.File = %s', strrep(fullfile(getExeDir, [appname '.summary.log']), '\', '/'))...
                'log4j.appender.AFileInfo.Append = true'...
                'log4j.appender.AFileInfo.layout = org.apache.log4j.PatternLayout'...
                'log4j.appender.AFileInfo.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
                ''...
                'log4j.appender.AFileWarn.Threshold = WARN'...
                'log4j.appender.AFileWarn = org.apache.log4j.RollingFileAppender'...
                sprintf('log4j.appender.AFileWarn.File = %s', strrep(fullfile(getExeDir, [appname '.error.log']), '\', '/'))...
                'log4j.appender.AFileWarn.Append = true'...
                'log4j.appender.AFileWarn.layout = org.apache.log4j.PatternLayout'...
                'log4j.appender.AFileWarn.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
                }, sprintf('\n'));
            props = java.util.Properties();
            props.load(java.io.StringReader(defaultProperties));
            org.apache.log4j.PropertyConfigurator.configure(props);
        end

    end
end

