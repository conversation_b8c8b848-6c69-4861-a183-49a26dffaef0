classdef DBTable_Study < mydb.sqlite.DBTable
    properties
        
    end

    methods
        function obj = DBTable_Study(varargin)
            <EMAIL>({'dbt.PrimaryKey', 'StudyInstanceUID'});
        end        
    end

    methods (Static)
        function varnames = TableVaribleNames
             varnames = {'StudyInstanceUID', 'PatientID' 'PatientName' 'PatientBirthDate' 'PatientSex', 'StudyDate' 'StudyTime' 'StudyDescription'};
             varnames = cat(2, varnames, mydb.sqlite.DBTable.DBTableTimeStampVarNames);
        end
    end
end