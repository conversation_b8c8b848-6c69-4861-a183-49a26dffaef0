classdef RtDicomBase < OptionsMap
    properties
        jInfo
    end
    
    properties (Constant = true)
          Tags_Common   = {'SOPInstanceUID' 'PatientID' 'PatientName' 'PatientBirthDate' 'PatientSex'  'StudyInstanceUID' 'StudyDate' 'StudyTime' 'StudyDescription' 'Modality' 'SeriesInstanceUID' 'SeriesDate' 'SeriesTime' 'SeriesDescription'};
    end
    
    methods
        function self = RtDicomBase(filepath, varargin)
            self@OptionsMap(varargin{:});
            if nargin >= 1 
                if ischar(filepath)
                dcm = dcm4che2.read(filepath);
                self.jInfo = dcm4che2.clone(dcm);
                elseif isa(filepath, 'org.dcm4che2.data.BasicDicomObject')
                    self.jInfo = filepath; 
                end
            end            
        end
        
        
        function write(self, filepath)
            dcm = dcm4che2.clone(self.jInfo);
            dcm4che2.write(dcm, filepath);
        end
        
        function val = GetTagx(obj, varargin)
            val = obj.getTagx(obj.jInfo, varargin{:});
        end
        
        function val = GetTagxx(obj, field)
            val = dcm4che2.getTag(obj.jInfo, field);
        end
        
        function SetTag(obj, tagPath, val)
            dcm4che2.setTag(obj.jInfo, tagPath, val);
        end
    end
    
    methods (Static)
        function val = getTagx(dcm, tagPath, defaultval)
            if dcm4che2.hasTag(dcm, tagPath)
                val = dcm4che2.getTag(dcm, tagPath);
            elseif exist('defaultval', 'var')
                val = defaultval; 
            else
                val = []; 
            end
        end
        
        function res = getTag_seq(seq, tagname)
            res = cellfun(@(x)(dcm4che2.getTag(x, tagname)), seq); 
        end
        
        function res = getTagx_seq(seq, tagname, defaultval)
            res = cellfun(@(x)(dcm4che2.RtDicomBase.getTagx(x, tagname, defaultval)), seq); 
        end
        
        function setTag_seq_unique(seq, tagname, val)
           cellfun(@(x)(dcm4che2.setTag(x, tagname, val)), seq); 
        end
        
        function setTag_seq(seq, tagname, vals)
           if ~iscell(vals)
               vals = arrayfun(@(x)(x), vals, 'uniformoutput', false);
           end
           cellfun(@(x, val)(dcm4che2.setTag(x, tagname, val)), seq, vals); 
        end
        
        function T = sequence2Array(cpseq,varnames, outputtable)
        % varnames0 = {'ControlPointIndex', 'GantryAngle', 'BeamLimitingDeviceAngle', 'PatientSupportAngle','TableTopPitchAngle', 'TableTopRollAngle', 'CumulativeMetersetWeight'};
        %                 varnames  = options.getoptioni('controlpoints.varnames', varnames0);
        %                 cpseq     = obj.GetTagx('ControlPointSequence');
            ncps = numel(cpseq);
            nvars= numel(varnames);
            T    = NaN(ncps, nvars);
        %     prevT= T(1, :);  

            for m=1:nvars
                T(1, m) = dcm4che2.RtDicomBase.getTagx(cpseq{1}, varnames{m}, NaN);
            end 

            for k=2:ncps
                for m=1:nvars
                    T(k, m) = dcm4che2.RtDicomBase.getTagx(cpseq{k}, varnames{m}, T(k-1, m));
                end
            end

            if exist('outputtable', 'var') && outputtable
               T = array2table(T, 'VariableNames', varnames); 
            end
        end
        
         function info = GetDcmInfo(dcm, tags, info)
            % Read DICOM tags to Matlab struct.
            if ~exist('info', 'var')
                info =[]; 
            end

            try
                if ischar(dcm)
                    dcm = dcm4che2.read(dcm);
                end
                
                for k = 1:numel(tags)
                    t = tags{k};
                    if ischar(t)
                        info = dcm4che2.RtDicomBase.getOptionalTag(dcm, t, info);
                    else
                        info = dcm4che2.RtDicomBase.getOptionalTagxx(dcm,t{1}, t{2}, info);
                    end
                end
                
            catch exc
                info = [];
            end
        end

        function info = getOptionalTag(dcm, tagPath, info)
            if dcm4che2.hasTag(dcm, tagPath)
                info.(tagPath) = num2str(dcm4che2.getTag(dcm, tagPath));
            else
                info.(tagPath) = '';
            end
        end

        function info = getOptionalTagxx(dcm, tagName, tagPath, info)
           info.(tagName) = num2str(dcm4che2.getTagxx(dcm, tagPath));
        end
    end
end
