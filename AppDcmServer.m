function AppDcmServer(rootPath, varargin)

setupLogger('AppDcmServer');

db = BasicDcmDb(rootPath, varargin{:});
while true
    db.storeIncoming(true);
    pause(1);
end

function setupLogger(appname)
defaultProperties = strjoin({...
    'log4j.rootLogger=DEBUG, ACONSOLE, AFileAll, AFileInfo, AFileWarn'...
    'log4j.appender.ACONSOLE = org.apache.log4j.ConsoleAppender'...
    'log4j.appender.ACONSOLE.layout = org.apache.log4j.PatternLayout'...
    'log4j.appender.ACONSOLE.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
    ''...
    'log4j.appender.AFileAll = org.apache.log4j.RollingFileAppender'...
    sprintf('log4j.appender.AFileAll.File = %s', strrep(fullfile(getExeDir, [appname '.debug.log']), '\', '/'))...
    'log4j.appender.AFileAll.Append = true'...
    'log4j.appender.AFileAll.layout = org.apache.log4j.PatternLayout'...
    'log4j.appender.AFileAll.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
    ''...
    'log4j.appender.AFileInfo.Threshold = INFO'...
    'log4j.appender.AFileInfo = org.apache.log4j.RollingFileAppender'...
    sprintf('log4j.appender.AFileInfo.File = %s', strrep(fullfile(getExeDir, [appname '.summary.log']), '\', '/'))...
    'log4j.appender.AFileInfo.Append = true'...
    'log4j.appender.AFileInfo.layout = org.apache.log4j.PatternLayout'...
    'log4j.appender.AFileInfo.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
    ''...
    'log4j.appender.AFileWarn.Threshold = WARN'...
    'log4j.appender.AFileWarn = org.apache.log4j.RollingFileAppender'...
    sprintf('log4j.appender.AFileWarn.File = %s', strrep(fullfile(getExeDir, [appname '.error.log']), '\', '/'))...
    'log4j.appender.AFileWarn.Append = true'...
    'log4j.appender.AFileWarn.layout = org.apache.log4j.PatternLayout'...
    'log4j.appender.AFileWarn.layout.ConversionPattern = %d{ISO8601}	%p	%t	%c	%m%n'...
    }, sprintf('\n'));
props = java.util.Properties();
props.load(java.io.StringReader(defaultProperties));
org.apache.log4j.PropertyConfigurator.configure(props);

