classdef nnUNET_PredDose <daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = nnUNET_PredDose(varargin)
            options = OptionsMap(varargin{:});
            <EMAIL>(options);
            obj.SetDefaultCfg();
            AddProcess_nnUNET(obj);
        end

        function AddProcess_nnUNET(self)
            optype = 'nnUNET_PredDose';       
            fixedstr=[];
            templateparas='';
            names = {'ModelOptionFile', 'InputImage', 'OutputFile', 'RxDose', 'ROIMaskFile'};
            for k=1:numel(names)
                name=names{k};
                modeloptfile=self.getOption(name);
                if ~isempty(modeloptfile)
                    fixedstr.(name)=modeloptfile;
                else
                    fixedstr.(name)=['[' name ']'];
                end
            end
            AddProcess(self, optype, fixedstr, templateparas);
        end
    end

    methods (Static)
        
       
    end
end