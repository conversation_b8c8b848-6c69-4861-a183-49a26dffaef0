#-----------------------------------------------------------------------
#
# Example configuration file for the dcmqrscp and dcmqrti applications.
#
#-----------------------------------------------------------------------

#
# Global Configuration Parameters
#
NetworkTCPPort  = 104
MaxPDUSize      = 16384
MaxAssociations = 16

#
# Default settings for specific character set behavior:
# SpecificCharacterSet = fallback

#
# Uncomment to override response character set with UTF-8 and enable
# transliteration and discarding of unsupported characters:
# SpecificCharacterSet = "ISO_IR 192", override, discard, transliterate

#
# UserName      = <not used>
# GroupName     = <not used>

HostTable BEGIN
#
# The HostTable defines symbolic names for collections of network
# DICOM Application Entities.  A symbolic name can represent a single
# application entity or it can represent a group of application entities.
# Each DICOM application entity is defined by a triple consisting of
# Application Entity Title, host name and TCP/IP port number.
#
# Entry Format: SymbolicName = ( AETitle, HostName, Portnumber ), ...   |
#               SymbolicName = SymbolicName, ...
#
# NOTE: in the current implementation you cannot substitute an IP address
# for a hostname.
#
acme1           = (ACME1, acmehost1, 5678)
acme2           = (ACME2, acmehost2, 5678)
acmeCTcompany   = acme1, acme2
united1         = (UNITED1, unitedhost1, 104)
united2         = (UNITED2, unitedhost2, 104)
unitedMRcompany = united1, united2
#
HostTable END

VendorTable BEGIN
#
# The VendorTable is used by the dcmqrdb and dcmqrti applications.
# You can give a vendor name (r.h.s. entry below) to the dcmqrti
# program and it will talk to all hosts and AEs of the vendor.
# The dcmqrdb program can use the vendor table to restrict move destination
# to hosts belonging to a vendor.
# Also, the dcmqrti and dcmqrdb programs use the name defined on the left hand side
# as the vendor name to display above images.
#
# The format:
#       VendorName = SymbolicName
# The symbolic name should be defined in the HostTable.
#
"Acme CT Company"   = acmeCTcompany
"United MR Company" = unitedMRcompany
#
VendorTable END

AETable BEGIN
#
# Each row of the AETable defines an Application Entities (AE) Title known
# to the dcmqrdb application.  Each AE Title represents a separate
# image database located in the specified file system directory (storage area).
# Each AE Title has read/write, quota and peer access restrictions.
#
# Entry Format: AETitle  StorageArea  Access  Quota  Peers
# AccessFormat: R | RW | W
# Quota Format: ( maxStudies, maxBytesPerStudy )
# Peers Format: ( Hostname, AETitle, Portnumber ), ...  |
#               Entry in HostTable                      |
#               ANY
#
COMMON       /home/<USER>/db/COMMON       R  (200, 1024mb) ANY
ACME_STORE   /home/<USER>/db/ACME_STORE   RW (9, 1024mb)   acmeCTcompany
UNITED_STORE /home/<USER>/db/UNITED_STORE RW (9, 1024mb)   unitedMRcompany
#
AETable END
