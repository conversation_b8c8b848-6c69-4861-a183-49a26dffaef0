classdef TotalSeg < daemon.taskdef.TaskDef
    properties
        
    end

    methods
        function obj = TotalSeg(varargin)         
            <EMAIL>(varargin{:});
            obj.SetDefaultCfg;
        end
    end

    methods (Static)
        function CreateTaskDef(modeloptfile, tasksdeffile)
             %names = {'ModelOptionFile', 'InputImage', 'OutputFile'};
             modeloptfile = DosUtil.SimplifyPath(modeloptfile);
             [modelfolder, modelname, ext]= fileparts(modeloptfile);
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             modelopts = OptionsMap(modeloptfile);
             modality = modelopts.getOption('InputModality');

             preprocess=[];
             if strcmpi(modality, 'CT')
                preprocess = struct('OperationType', 'IntensityMapData', 'MapType', 'linear',...
                    'IntensityScale', 1, 'IntensityOffset', -1000);
             end

             taskdefname = ['temp_' modelname];
             obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             ModelOptionFile= modeloptfile;
             InputImage='[InputImage]';
             OutputFile='[OutputFile]';
             dependency = struct("filename", InputImage);
             RemoveTiny = struct('OperationType', 'RemoveROIs', 'OperationName', 'RemoveTiny');
             m=0;   Process=[];
             m=m+1; Process{m}=struct('OperationType', 'nnUNETInference',...
                 'ModelOptionFile', ModelOptionFile,...
                 'InputImage', InputImage, 'OutputFile', OutputFile,'PreProcess', preprocess, 'PostProcess', RemoveTiny); 
             obj.Generate(tasksdeffile, dependency, Process);

             
             modality  = modelopts.getOption('InputModality');
             taskdefname = [modality '_' modelname];
             obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             CustomSubFolder=modelname; 
             InputImage='../image.nii.gz';
             OutputFile='labelmask';
             dependency= struct("filename", InputImage, "taskfilename", "../DcmConverter/[Modality].[SeriesInstanceUID].tsk");
             m=0;   Process=[];
             m=m+1; Process{m}=struct('OperationType', 'nnUNETInference',...
                 'ModelOptionFile', ModelOptionFile,...
                 'InputImage', InputImage, 'OutputFile', OutputFile,'PreProcess', preprocess, 'PostProcess', RemoveTiny);
             obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end

        function Factory_totalseg(modeloptfolder,tasksdeffile)
            res = rdir([modeloptfolder 'Dataset*.opt']);
            fnames = {res(:).name};
            for k=1:numel(fnames)
                modeloptfile=fnames{k};
                daemon.taskdef.TotalSeg.CreateTaskDef(modeloptfile, tasksdeffile)
            end
        end

        function Segman(tasksdeffile, taskdefname, modality, submodels)
             for k=1:numel(submodels)
                 submodel = submodels{k}; 
                 InputImage = ['../' submodel '/labelmask.nii.gz'];
                 Dependency{k}= struct("filename", InputImage, "taskfilename", [modality '_' submodel '/' modality '.[SeriesInstanceUID].tsk']);
                 MergeOperation{k} =struct('SrcRoiMaskFile',  InputImage);
             end
             Process=[];
             CustomSubFolder=taskdefname;
        %          "OutputStructSet" :{
	    % 	"ID": "Segman_MR_Common", 
	    % 	"ROIMaskFileName": "roimask",
	    % 	"LabelMaskFileName": "labelmask",
	    % 	"LabelMaskWithImageContours": 1,
	    % 	"ContourSmoothWindow": 2
	    % },
	    % "DcmExport":
	    % {
	    % 	"OrigImageHeaderFile": "../image_dcm.json",
	    % 	"OutRSFileName": "RS_Segman.dcm",
	    % 	"OutRSSetName": "Segman_MR_Common"
	    % }
                
            OutputStructSet=struct(...
		        "ID", taskdefname, ...
		        "ROIMaskFileName", "roimask",...
		        "LabelMaskFileName", "labelmask",...
		        "LabelMaskWithImageContours", 1,...
		        "ContourSmoothWindow", 3);
            DcmExport=struct(...
	            "OrigImageHeaderFile", "../image_dcm.json",...
	    	    "OutRSFileName", "RS_Segman.dcm",...
	    	    "OutRSSetName", taskdefname);
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, Process,{'DBTableName', modality}, {'CustomSubFolder', CustomSubFolder}, ...
                {'OutputStructSet', OutputStructSet}, {'DcmExport', DcmExport}, {'MergeOperation', MergeOperation});
        end

        function Factory_SegmanMR(tasksdeffile)
            modality    = 'MR';
            taskdefname = 'SegmanMR_Common3mm';
            submodels   = {'Dataset852_TotalSegMRI_total_3mm_1088subj', 'Dataset598_mri_body_6mm_139subj'};
            daemon.taskdef.TotalSeg.Segman(tasksdeffile, taskdefname, modality, submodels);
            
            taskdefname = 'SegmanMR_Common';
            submodels   = {'Dataset850_TotalSegMRI_part1_organs_1088subj', 'Dataset851_TotalSegMRI_part2_muscles_1088subj', 'Dataset597_mri_body_139subj'};
            daemon.taskdef.TotalSeg.Segman(tasksdeffile, taskdefname, modality, submodels);

            taskdefname = 'SegmanMR_Spine';
            submodels   = {'Dataset850_TotalSegMRI_part1_organs_1088subj', 'Dataset756_mri_vertebrae_1076subj'};
            daemon.taskdef.TotalSeg.Segman(tasksdeffile, taskdefname, modality, submodels);
        end

        function MergeSeg(tasksdeffile, taskdefname, modality, submodels)
             for k=1:numel(submodels)
                 submodel = submodels{k}; 
                 InputImage = ['../' submodel '/labelmask.nii.gz'];
                 Dependency{k}= struct("filename", InputImage, "taskfilename", [modality '_' submodel '/' modality '.[SeriesInstanceUID].tsk']);
                 MergeOperation{k} =struct('SrcRoiMaskFile',  InputImage);
             end
  
            CustomSubFolder=taskdefname;
            Process = struct('OperationType', 'MergeExportROIMask');
            Process.MergeOperation=MergeOperation; 
            Process.OutputStructSet=struct(...
		        "ID", taskdefname, ...
		        "LabelMaskFileName", "labelmask");
            Process.OutMaskImageType='labelmask';
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TotalSeg({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});           
            obj.Generate(tasksdeffile, Dependency, Process,{'DBTableName', modality}, {'CustomSubFolder', CustomSubFolder});
        end
        
        function Factory_MergeSegCT(tasksdeffile)
            models = ai.seg.TotalSeg.CTModel;
            modality    = 'CT';
            taskdefname = 'CT_totalseg_3mm';
            submodels   = {models.Body6mm, models.Total3mm};
            %submodels   = {models.Body6mm};
            daemon.taskdef.TotalSeg.MergeSeg(tasksdeffile, taskdefname, modality, submodels);
            
            taskdefname = 'CT_totalseg';
            submodels   = {models.Body, models.Organs, models.VBs, models.Cardiac, models.Muscles, models.Ribs};
            daemon.taskdef.TotalSeg.MergeSeg(tasksdeffile, taskdefname, modality, submodels);      
        end


        % function Factory_SegmanCT(tasksdeffile)
        %     models = ai.seg.TotalSeg.CTModel;
        %     modality    = 'CT';
        %     taskdefname = 'SegmanCT_Common3mm';
        %     submodels   = {models.Total3mm, models.Body6mm};
        %     daemon.taskdef.TotalSeg.Segman(tasksdeffile, taskdefname, modality, submodels);
        % 
        %     taskdefname = 'SegmanCT_Common';
        %     submodels   = {models.Organs, models.VBs, models.Cardiac, models.Muscles, models.Ribs, models.Body};
        %     daemon.taskdef.TotalSeg.Segman(tasksdeffile, taskdefname, modality, submodels);      
        % end
    end
end