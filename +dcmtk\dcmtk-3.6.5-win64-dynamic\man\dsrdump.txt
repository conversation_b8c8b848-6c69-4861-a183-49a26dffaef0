dsrdump(1)                        OFFIS DCMTK                       dsrdump(1)



NAME
       dsrdump - Dump DICOM SR file and data set


SYNOPSIS
       dsrdump [options] dsrfile-in...

DESCRIPTION
       The  dsrdump utility dumps the contents of a DICOM Structured Reporting
       (SR) document (file format or raw data set) to stdout in textual  form.
       The output of the document content follows the format proposed in <PERSON>'s book 'DICOM Structured Reporting' (PixelMed Publishing, 2000).

       If dsrd<PERSON> reads a raw data set (DICOM data without a file format meta-
       header)  it  will attempt to guess the transfer syntax by examining the
       first few bytes of the file. It is not  always  possible  to  correctly
       guess  the  transfer syntax and it is better to convert a data set to a
       file format whenever possible (using the dcmconv utility). It  is  also
       possible  to  use the -f and -t[ieb] options to force dsrd<PERSON> to read a
       dataset with a particular transfer syntax.

PARAMETERS
       dsrfile-in  DICOM SR input filename to be dumped

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

   input options
       input file format:

         +f    --read-file
                 read file format or data set (default)

         +fo   --read-file-only
                 read file format only

         -f    --read-dataset
                 read data set without file meta information

       input transfer syntax:

         -t=   --read-xfer-auto
                 use TS recognition (default)

         -td   --read-xfer-detect
                 ignore TS specified in the file meta header

         -te   --read-xfer-little
                 read with explicit VR little endian TS

         -tb   --read-xfer-big
                 read with explicit VR big endian TS

         -ti   --read-xfer-implicit
                 read with implicit VR little endian TS

   processing options
       additional information:

         -Ip   --processing-details
                 show currently processed content item

       error handling:

         -Er   --unknown-relationship
                 accept unknown/missing relationship type

         -Ev   --invalid-item-value
                 accept invalid content item value
                 (e.g. violation of VR or VM definition)

         -Ec   --ignore-constraints
                 ignore relationship content constraints

         -Ee   --ignore-item-errors
                 do not abort on content item errors, just warn
                 (e.g. missing value type specific attributes)

         -Ei   --skip-invalid-items
                 skip invalid content items (including sub-tree)

         -Dv   --disable-vr-checker
                 disable check for VR-conformant string values

       specific character set:

         +U8   --convert-to-utf8
                 convert all element values that are affected
                 by Specific Character Set (0008,0005) to UTF-8

                 # requires support from an underlying character encoding library
                 # (see output of --version on which one is available)

   output options
       general printing:

         +Pf   --print-filename
                 print header with filename for each document

         -Ph   --no-document-header
                 do not print general document information

         +Pn   --number-nested-items
                 print position string in front of each line

         -Pn   --indent-nested-items
                 indent nested items by spaces (default)

       printing values:

         +Pl   --print-long-values
                 print long item values completely

         -Pl   --shorten-long-values
                 print long item values shortened (default)

         +Pu   --print-instance-uid
                 print SOP instance UID of referenced objects

         -Ps   --print-sopclass-short
                 print short SOP class name of referenced image objects,
                 e.g. "CT image" (default)

         +Ps   --print-sopclass-long
                 print long SOP class name of referenced objects

         +Psu  --print-sopclass-uid
                 print SOP class UID of referenced objects

         +Pc   --print-all-codes
                 print all codes (including concept name codes)

         +Pi   --print-invalid-codes
                 print invalid codes (for debugging purposes)

         -Pi   --no-invalid-codes
                 print text "invalid code" instead (default)

         +Pt   --print-template-id
                 print template identification information

       enhanced encoding mode:

         +Pe   --indicate-enhanced
                 indicate that enhanced mode is used for codes

         -Pe   --no-enhanced-mode
                 do not indicate enhanced mode (default)

       color:

         +C    --print-color
                 use ANSI escape codes for colored output

                 # not available on Windows systems

         -C    --no-color
                 do not use any ANSI escape codes (default)

                 # not available on Windows systems

NOTES
   DICOM Conformance
       The dsrdump utility supports the following SOP Classes:

       SpectaclePrescriptionReportStorage           1.2.840.10008.*******.1.78.6
       MacularGridThicknessAndVolumeReportStorage   1.2.840.10008.*******.1.79.1
       BasicTextSRStorage                           1.2.840.10008.*******.1.88.11
       EnhancedSRStorage                            1.2.840.10008.*******.1.88.22
       ComprehensiveSRStorage                       1.2.840.10008.*******.1.88.33
       Comprehensive3DSRStorage                     1.2.840.10008.*******.1.88.34
       ProcedureLogStorage                          1.2.840.10008.*******.1.88.40
       MammographyCADSRStorage                      1.2.840.10008.*******.1.88.50
       KeyObjectSelectionDocumentStorage            1.2.840.10008.*******.1.88.59
       ChestCADSRStorage                            1.2.840.10008.*******.1.88.65
       XRayRadiationDoseSRStorage                   1.2.840.10008.*******.1.88.67
       RadiopharmaceuticalRadiationDoseSRStorage    1.2.840.10008.*******.1.88.68
       ColonCADSRStorage                            1.2.840.10008.*******.1.88.69
       ImplantationPlanSRDocumentStorage            1.2.840.10008.*******.1.88.70
       AcquisitionContextSRStorage                  1.2.840.10008.*******.1.88.71
       SimplifiedAdultEchoSRStorage                 1.2.840.10008.*******.1.88.72
       PatientRadiationDoseSRStorage                1.2.840.10008.*******.1.88.73
       PlannedImagingAgentAdministrationSRStorage   1.2.840.10008.*******.1.88.74
       PerformedImagingAgentAdministrationSRStorage 1.2.840.10008.*******.1.88.75

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  dsrdump  utility  will  attempt  to  load  DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

SEE ALSO
       dcmconv(1)

COPYRIGHT
       Copyright (C) 2000-2019 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dsrdump(1)
