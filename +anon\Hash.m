classdef Hash < OptionsMap
    properties
        
    end
    
    methods
        function obj = Hash(varargin)
            obj = obj@OptionsMap(varargin{:}); 
        end
        
        function newuid = HashUID(self, uid)
            hash_uint8 = self.MD5(uid);
            uids = strsplit(uid, '.'); 
            len = cellfun(@(x)(numel(x)), uids); 
            [~, I]    = max(len); 
            uids{I} = self.byte2digit(hash_uint8);
            N = numel(uids);
            newuid = uids{1}; 
            for k=2:N
                newuid =[newuid '.' uids{k}];
            end
        end
        
        function newid = HashID(self, id)
            hash_uint8 = self.MD5(id);
            newid = self.byte2digit(hash_uint8);
        end
        
        function newid = HashName(self, id)
            hash_uint8 = self.MD5(id);
            newid = self.byte2upperletter(hash_uint8);
        end
        
        function newdatestr = HashDateStr(self, str,offset)
            if nargin<3
                offset = 90;
            end
            num0       = datenum(str, 'yyyymmdd'); 
            hash_uint8 = self.MD5(str);
            deltadate  = mod(str2double(self.byte2digit(hash_uint8)), offset)-offset/2;
            newdatestr = datestr(num0+ deltadate, 'yyyymmdd');
        end
    end
    
    methods (Static)
        function digits = str2digit(str)
            bytes  = anon.Hash.MD5(str);
            digits = char(mod(bytes, 10)+uint8('0'));
        end
        
        function digits = byte2digit(bytes)
            digits = char(mod(bytes, 10)+uint8('0'));
        end
        
        function digits = byte2upperletter(bytes)
            digits = char(mod(bytes, 26)+uint8('A'));
        end
        
        function digits = byte2lowerletter(bytes)
            digits = char(mod(bytes, 26)+uint8('a'));
        end
        
        function digits = byte2word(bytes)
            digits = char(mod(bytes, uint8('z')-uint8('0')+1)+uint8('0'));
        end
        
        function  [hash_uint8, hash_byte] = MD5(str)
            persistent hasher
            if isempty(hasher)
                hasher = System.Security.Cryptography.HashAlgorithm.Create('MD5');
            end
            hash_byte = hasher.ComputeHash( uint8(str) );  % System.Byte class
            hash_uint8 = uint8(hash_byte );
        end
        
        function  [hash_uint8, hash_byte] = SHA512(str)
            persistent hasher
            if isempty(hasher)
                hasher = System.Security.Cryptography.HashAlgorithm.Create('SHA512');
            end
            hash_byte = hasher.ComputeHash( uint8(str) );  % System.Byte class
            hash_uint8 = uint8(hash_byte );
        end
        
        function [uids] = DicomUIDs(str, num)
            if ~exist('num', 'var')
                num=1; 
            end
            basestr = '1.3.6.1.4.1.9590.100.1.2.';
            hashobj = anon.Hash();
            uids{1} = [basestr hashobj.HashID(str)]; 
            for k=2:num
                 uids{k} = [basestr hashobj.HashID(uids{k-1})];
            end
        end
        
        function [uid] = DicomUID(str)
            basestr = '1.3.6.1.4.1.9590.100.1.2.';
            hashobj = anon.Hash();
            uid = [basestr hashobj.HashID(str)]; 
        end
        
        %https://stackoverflow.com/questions/44687495/matlab-create-md5-checksum-for-variables
        %A more general solution than @OmG 's answer, that relies on a little bit of undocumented functionality:
        %The undocumented function getByteStreamFromArray returns the byte stream that would be written to disk if you were to call the save -v7 command on the variable. 
        %It works for any variable that is less than 2GB in size, including not only the built-in types (numeric, logical, struct, cell etc.) covered by @OmG 's CoreHash, but also built-in and user-defined classes as well.
        % Note that getByteStreamFromArray calls saveobj, so it will ignore Transient properties - this is almost certainly a good thing for hashing as well as saving.
        % PS In either solution, SHA1 is probably better than MD5.
        function str = Data2Str(in)
            % Get a bytestream from the input. Note that this calls saveobj.
            inbs = getByteStreamFromArray(in);

            % Create hash using Java Security Message Digest.
            md = java.security.MessageDigest.getInstance('SHA1');
            md.update(inbs);

            % Convert to uint8.
            d = typecast(md.digest, 'uint8');

            % Convert to a hex string.
            str = dec2hex(d)';
            str = lower(str(:)');
        end
         
        function uid = Data2DicomUID(data)
            str   = anon.Hash.Data2Str(data);
            [uid] = anon.Hash.DicomUID(str);
        end
    end
end

