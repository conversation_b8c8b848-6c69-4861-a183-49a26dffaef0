#
#  Copyright (C) 2009-2011, OFFIS e.V.
#  All rights reserved.  See COPYRIGHT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 Oldenburg, Germany
#
#
#  Module:  oflog
#
#  Author:  <PERSON><PERSON>
#
#  Purpose: oflog sample configuration file
#

# This is an example config file for oflog. It's here to give an insight into
# some of the possibilities that oflog provides together with log4cplus.
#
# As you see, all lines that start with a '#' are comments.
#
# At any place in this file, you can use variables. They are expanded into
# settings from this file, e.g. ${log4cplus.rootLogger} would expand to the
# current value of this logger (see a few lines below). In addition, the
# following variables are predefined by oflog:
#
#  ${appname}        The name of the running application
#  ${date}, ${time}  The date and time at which this file was read
#  ${pid}            The process ID of the application which read this file
#  ${hostname}       The hostname of the machine on which we are running
#
# If no such setting exists, environment variables are tried. On most systems
# e.g. ${USER} would expand to the user name of the currently logged in user.
#
# Loggers are organized into hierarchies. If a log message is sent to the logger
# named dcmtk.dcmdata.libi2d, by default it will also be sent to dcmtk.dcmdata,
# dcmtk and the special "root logger" which is the base of the hierarchy.
#
# So, if you want to to catch all log messages, just configure the root logger:
log4cplus.rootLogger = DEBUG, console, logfile

# This line configures the root logger to sent all log events of level DEBUG or
# higher to the appenders "console" and "logfile" (see below).
#
# These are the available log levels, in ascending order:
# TRACE, DEBUG, INFO, WARN, ERROR, FATAL
#
# Now let's configure the above-mentioned appender called "console". Appenders
# take log events and send them somewhere. Since we want to see what's going on,
# our console appender shows the message on the console (who would have
# thought). But since "debug" is a little verbose, we only display warnings and
# higher levels.
# You can use arbitrary names as the name for an appender.
log4cplus.appender.console = log4cplus::ConsoleAppender
log4cplus.appender.console.Threshold = INFO
log4cplus.appender.console.logToStderr = true
log4cplus.appender.console.ImmediateFlush = true

# As you can see, we are saying that console is of type "ConsoleAppender". Then
# we set some appender-specific properties. Each appender got different ones.

# Now let's configure the "logfile" appender which will write a logfile for us.
log4cplus.appender.logfile = log4cplus::FileAppender
log4cplus.appender.logfile.File = ${appname}.log
log4cplus.appender.logfile.Append = true
log4cplus.appender.logfile.ImmediateFlush = true

# One thing that all appenders need is a layout. It tells the appender how the
# log messages should be formated. The simple layout should be enough for
# starters, but the PatternLayout is way more flexible.
log4cplus.appender.console.layout = log4cplus::PatternLayout
log4cplus.appender.logfile.layout = log4cplus::PatternLayout

# The pattern layout can be told how the message should look like. Some of the
# available variables include:
# %n  A line ending
# %m  The actual log message
# %p  The log level of that message (TRACE, DEBUG, INFO, WARN, ERROR, FATAL)
# %P  The first character of the log level (T, D, I, W, E, F) - added to log4cplus
# %i  The process ID of the process generating the log entry
# %h  The hostname of the system generating the log entry
# %H  The full qualified domain name of the system generating the log entry
# %M  The function in which this log event was generated
# %F  The full filename of the source code file where this log event was generated
# %b  The basename of the source code file where this log event was generated
# %L  The line number where this log event was generated
# %l  This is equivalent to %F:%L
# %D{%m/%d/%y %H:%M:%S} The time when this log event was generated. %D is local
#     time, %d GMT. The available "time flags" are those from strftime() plus
#     %q and %Q (milliseconds and fractional milliseconds).
# A more complete list can be found here (if installed):
#     <htmldir>/classlog4cplus_1_1PatternLayout.html#_details
#
# The line below creates a PatternLayout which looks like a "standard"
# SimpleLayout, but which also includes the current time.
log4cplus.appender.console.layout.ConversionPattern = %D{%H:%M:%S} %5p: %m%n
log4cplus.appender.logfile.layout.ConversionPattern = %D{%Y-%m-%d %H:%M:%S.%q} %5p: %m%n

# Ok, now that we got the basics covered, here is the logger hierarchy used by
# dcmtk:
# dcmtk.dcmdata              Every module gets its own hierarchy
# dcmtk.dcmdata.libi2d       And some modules even use this ;)
# dcmtk.apps                 All apps are in a separate hierarchy
# dcmtk.apps.dcmdump         And each app gets its own logger in here
#
# Each and every logger can be configured separately.
#
# If you want to know exactly which logger names are available, use %c in your
# ConversionPattern (see above) which is replaced by the name of the logger.

# Now come some special ".progress" loggers which we set to "INFO" log level:
log4cplus.logger.dcmtk.apps.storescu.progress = INFO
log4cplus.logger.dcmtk.apps.storescp.progress = INFO
log4cplus.logger.dcmtk.apps.movescu.progress  = INFO
log4cplus.logger.dcmtk.dcmqrdb.progress       = INFO

# Also for the C-FIND and other DIMSE response messages, a special logger is
# used in order to control the level of output in more detail.
log4cplus.logger.dcmtk.dcmnet.responses = INFO

# No log messages are ever sent to these loggers. Instead, only their log level
# is checked. If it is "INFO" (not "DEBUG", not "WARN", exactly "INFO") then the
# mentioned application displays a dot for each transfered PDU on the console.
# That way the user gets visual feedback about the transfer and can be sure
# something is happening.

# -----------------------------------------------------------------------
# From here on there are just commented out examples of what is possible.
# -----------------------------------------------------------------------

# So those debug log messages generated by dcmdata annoy you? Let's get rid of
# them!
# dcmdata's logger is configured to inherit its log level from its parent, but
# to also send events to the dcmdata_logger appender.
#log4cplus.logger.dcmtk.dcmdata = INHERIT, dcmdata_logger

# And the log messages sent to this appender are just discarded.
#log4cplus.appender.dcmdata_logger = log4cplus::NullAppender

# Now there is a problem left. Remember that each logger passes its log messages
# on to its parent? So dcmtk.dcmdata gives them to the dcmtk logger which passes
# them on to the root logger and we get those messages on the console again.
# Luckily, we can override this behavior for specific loggers:
#log4cplus.additivity.dcmtk.dcmdata = false

# Those debug messages from dcmnet... Uhm, no, we don't want them on the
# console, but logging them to a file would be nice. Sounds like
# log4cplus::fileAppender. But we don't want the file to grow too huge, so let's
# automatically switch to a new file. There are two options for this:
#
# Either you switch after some specific file size:

#log4cplus.appender.dcmnet_logger = log4cplus::RollingFileAppender
#log4cplus.appender.dcmnet_logger.File = dcmnet.log
#log4cplus.appender.dcmnet_logger.MaxFileSize = 500KB
#log4cplus.appender.dcmnet_logger.MaxBackupIndex = 5

# This leaves the last five files as dcmnet.log.1 etc behind, they are rotated
# when we reach the MaxFileSize limit.

# Or, you tell it to rotate log files based on the time:
#log4cplus.appender.dcmnet_logger = log4cplus::DailyRollingFileAppender
#log4cplus.appender.dcmnet_logger.File = dcmnet.log
#log4cplus.appender.dcmnet_logger.Schedule = twice_daily
#log4cplus.appender.dcmnet_logger.MaxBackupIndex = 5

# Available values for "Schedule" are:
# monthly, weekly, daily, twice_daily, hourly, minutely
#
# The rotated files are saved as dcmnet.log.<time> where <time> depends on the
# selected schedule.

# Of course, we also have to tell dcmnet to use the above appender and to not
# log them to the console. And our appender needs a layout.
#log4cplus.logger.dcmtk.dcmnet = INHERIT, dcmnet_logger, dcmnet_cons_logger
#log4cplus.additivity.dcmtk.dcmnet = false
#log4cplus.appender.dcmnet_logger.layout = log4cplus::PatternLayout
#log4cplus.appender.dcmnet_logger.layout.ConversionPattern = %D{%H:%M:%S} %p - %m%n

# Oh and the errors and warnings from dcmnet are important, aren't they?
# We want them visible on the console.
#log4cplus.appender.dcmnet_cons_logger = log4cplus::ConsoleAppender

# So now we have to tell dcmnet_cons_logger to only log errors and warnings and
# ignore the rest.
#log4cplus.appender.dcmnet_cons_logger.filters.1 = log4cplus::spi::LogLevelRangeFilter
#log4cplus.appender.dcmnet_cons_logger.filters.1.logLevelMin = warn
#log4cplus.appender.dcmnet_cons_logger.filters.1.logLevelMax = fatal
#log4cplus.appender.dcmnet_cons_logger.filters.1.acceptOnMatch = true
#log4cplus.appender.dcmnet_cons_logger.filters.2 = log4cplus::spi::DenyAllFilter

# For more information about the available options, you can consult log4cplus'
# website at http://log4cplus.sourceforge.net/ .
