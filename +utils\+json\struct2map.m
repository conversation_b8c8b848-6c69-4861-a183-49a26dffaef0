function map =struct2map(str, matchnamepat)
    if isa(str, 'containers.Map')
        return;
    end
    if ~exist(matchnamepat, 'var')
        matchnamepat = '^x(\d+|_)';
    end
    map = containers.Map; 
    fns = fieldnames(str);
    for k=1:numel(fns)
        fn = fns{k};
        value = str.(fn);
        if ~isempty(regexp(fn, matchnamepat, 'ONCE'))
            fn(1)=[];
        end
        map(fn)=value; 
    end
end