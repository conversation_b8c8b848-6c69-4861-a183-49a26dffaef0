classdef TableFilter_RTStruct < xls.TableFilter
    properties
        
    end
    
    methods
        function obj = TableFilter_RTStruct(varargin)
            options = OptionsMap();
            options.setOption('fields.required',...
                {'ReferencedImageSeriesUID'});  
            <EMAIL>(options, varargin{:});
            fields = obj.getoptioni('fields.required');
            for k=1:numel(fields)
                filter = struct('FieldName', fields{k}, 'MatchMode', 'notempty', 'Inclusion', true);
                obj.AddFilter(filter);
            end
            datetimerange = obj.getoptioni_numeric('datetimerange');
            datetimemode  = obj.getoptioni('datetimerangemode', 'relative');
            if ~isempty(datetimerange)
                filter    = struct('FieldName', '', 'MatchMode', 'datetimerange',...
                    'FieldValue', datetimerange,  'Inclusion', true, 'datetimemode', datetimemode);
                obj.AddFilter(filter);
            end
        end

        function flag = ParseDateTimeRange(obj, filter, str)
            datetimerange = filter.FieldValue;
            mode = filter.datetimemode;
            if strcmpi(mode, 'relative')
                 datetimerange = datetimerange+now; 
            end
            val = datenum([str.SeriesDate '.' str.SeriesTime], 'yyyymmdd.HHMMSS');
            flag = (val>= datetimerange(1)) && (val<datetimerange(2));
        end
    end

    methods(Static)
        function T1 = UnitTest()
            T       = xls.readtable_str('D:\ARTDaemon\Ethos\DataBase\plandb\RTPlanDB.xlsx');
            filters = xls.TableFilter_RTPlan({'datetimerange', [-1 0]});
            T1      = filters.FilterTable(T);
            filters.SaveFilter_json('c:\temp\rtplanfilter.json');
        end
    end
end

