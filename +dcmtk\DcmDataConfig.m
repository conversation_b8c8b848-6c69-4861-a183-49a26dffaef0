classdef DcmDataConfig < utils.json.JsonConfig
    properties

    end
    
    methods
        function obj = DcmDataConfig(cfgfile, varargin)
           <EMAIL>(cfgfile, varargin{:});
           info = obj.getoptioni('dcmdata.MyInfo'); 
           if ~isempty(info)
               obj.SetConfig('MyInfo', info); 
           end
        end
    
        function parentcfg=UpdateParent(self, info, rootfolder, subfolder)
           if ~exist('subfolder', 'var') ||isempty(subfolder)
               subfolder  ='';
           end
           myinfo   = self.GetConfig('MyInfo');
           DcmLevel = upper(myinfo.DcmLevel);
           switch upper(DcmLevel)
               case {'CT', 'MR', 'RS', 'RP'}
                   parentfolder = DosUtil.mksubdir(rootfolder,[info.PatientID '/STUDY.' info.StudyInstanceUID '/' subfolder]);             
                   parentfolder = DosUtil.SimplifyPath(parentfolder); 
                   varnames   = {'StudyInstanceUID'	'StudyDate'	'StudyDescription'};
                   for k=1:numel(varnames)
                        varname = varnames{k}; 
                        parentinfo.(varname) = StructBase.getfieldx_default(info, varname, '');
                   end
                   parentinfo.ID = parentinfo.('StudyInstanceUID'); 
                   parentinfo.DcmLevel = 'STUDY'; 
               case {'STUDY'}
                   parentfolder = DosUtil.mksubdir(rootfolder,[info.PatientID '/' subfolder]);             
                   varnames   = {'PatientID'	'PatientName'	'PatientBirthDate'	'PatientSex'};
                   for k=1:numel(varnames)
                        varname = varnames{k}; 
                        parentinfo.(varname) = StructBase.getfieldx_default(info, varname, '');
                   end
                   parentinfo.ID = parentinfo.('PatientID'); 
                   parentinfo.DcmLevel = 'PATIENT'; 
               case {'PATIENT'}
                   parentfolder = DosUtil.mksubdir(rootfolder, subfolder);
                   parentinfo.ID = 'ROOT'; 
                   parentinfo.DcmLevel = 'ROOT'; 
           end
           myinfo.filename = DosUtil.relativepath(self.m_cfgfile, parentfolder,'/');
           parentcfgfile   = [parentfolder 'dcmdata.cfg']; 
           parentcfg       = dcmtk.DcmDataConfig(parentcfgfile, {'dcmdata.MyInfo',parentinfo});
           parentcfg.AddStruct2List(myinfo, [DcmLevel 'List']);
           parentcfg.writeJson; 
        end
        
        function res = DcmLevel(self)
            res = upper(self.GetConfig_default('MyInfo.DcmLevel', '')); 
        end
    end
    
    methods (Static)
        function UpdateParents(cfg, info, rootfolder, varargin)
            
            level = DcmLevel(cfg);
            if ~ismember(level, {'CT', 'MR', 'RS', 'RP', 'STUDY', 'PATIENT'})
                return; 
            else
                cfg = cfg.UpdateParent(info, rootfolder, varargin{:});
                dcmtk.DcmDataConfig.UpdateParents(cfg, info, rootfolder, varargin{:});
            end
        end
        
        function cfg = Factory(cfgfile, imginfo, rootfolder)
            if ~exist('rootfolder', 'var')
                rootfolder = '';
            end
            
            modality        = upper(imginfo.Modality); 
            switch modality
                case {'CT', 'MR', 'PT'}
                    myinfo.DcmLevel = modality;
                    uid = imginfo.SeriesInstanceUID;  
                    myinfo.SeriesDescription=StructBase.getfieldx(imginfo, 'SeriesDescription');

                case {'RTPLAN'}
                    myinfo.DcmLevel = 'RP';
                    uid = imginfo.SOPInstanceUID;  
                    myinfo.SeriesDescription=StructBase.getfieldx(imginfo, 'SeriesDescription');

                case {'RTSTRUCT'}
                    myinfo.DcmLevel = 'RS';
                    uid = imginfo.SOPInstanceUID;  
                    myinfo.SeriesDescription=StructBase.getfieldx(imginfo, 'SeriesDescription');
                    
                case {'STUDY'}
                    myinfo.DcmLevel = 'STUDY';
                    uid = imginfo.StudyInstanceUID;  
                    
                case {'PATIENT'}
                    myinfo.DcmLevel = 'PATIENT';
                    uid = imginfo.PatientID;  
            end
            
            myinfo.ID  = [myinfo.DcmLevel '.' uid]; 
            
            myinfo.UID = [uid];
            
            myinfo.Modality = modality;
            
            cfg = dcmtk.DcmDataConfig(cfgfile, {'dcmdata.MyInfo', myinfo}); 
            if ~isempty(rootfolder)
                dcmtk.DcmDataConfig.UpdateParents(cfg, imginfo, rootfolder);
            end
        end
    end
end

