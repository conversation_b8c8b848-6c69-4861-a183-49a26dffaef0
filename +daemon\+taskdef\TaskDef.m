classdef TaskDef <utils.json.TaskDef
    properties
       
    end

    methods
        function obj = TaskDef(varargin)
            <EMAIL>([], varargin{:});
        end

        function cfg = DefaultCfg(self, str)
            taskdefname = self.TaskDefName;
            TaskDoneIndicator=[taskdefname '.tsk'];
            cfg = struct("TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            if exist('str', 'var')&&~isempty(str)
                fns = fieldnames(str);
                for k=1:numel(fns)
                    name = fns{k};
                    cfg.(name)=str.(name);
                end
            end
        end

        function SetDefaultCfg(self, cfg)
            if ~exist('cfg', 'var')
                cfg=[];
            end
            taskdefname = self.TaskDefName;
            if startsWith(taskdefname, 'rtplan_', 'IgnoreCase',true)||startsWith(taskdefname, 'RP_', 'IgnoreCase',true)
                cfg.("DBTableName")="RTPLANDB";
            elseif startsWith(taskdefname, 'CT_', 'IgnoreCase',true)
                cfg.("DBTableName")="CT";
            elseif startsWith(taskdefname, 'MR_', 'IgnoreCase',true)
                cfg.("DBTableName")="MR";
            elseif startsWith(taskdefname, 'rtstruct_', 'IgnoreCase',true)||startsWith(taskdefname, 'RS_', 'IgnoreCase',true)
                cfg.("DBTableName")="RTSTRUCT";    
            elseif startsWith(taskdefname, 'temp_', 'IgnoreCase',true)
                cfg.("TaskOutputFolder")= "[TaskOutputFolder]";
            end
            cfg = self.DefaultCfg(cfg);
            self.SetDef_struct(cfg);
        end

        function Generate(obj, tasksdeffile, Dependency, Process, varargin)
            if ~isempty(Dependency)
                Dependency=StructBase.toCell(Dependency);
                for k=1:numel(Dependency)
                    obj.AddDependency(Dependency{k});
                end
            end
            if ~isempty(Process)
                Process=StructBase.toCell(Process);
                for k=1:numel(Process)
                    obj.AddConfig2List('Process', Process{k});
                end
            end

            options = OptionsMap(varargin{:});
            obj.SetDef_options(options);
            obj.writeJson;
            if ~isempty(tasksdeffile)
                tasksdeffile = DosUtil.CatenatePath(tasksdeffile,TaskDefFolder(obj));
                obj.AddToTasksDefFile( tasksdeffile);
            end
        end
    end

    methods (Static)
        function words = extractBracketWordsFromFile(filename)
            % Read file content
            fid = fopen(filename, 'r');
            if fid == -1
                error('Could not open file.');
            end
            textData = fread(fid, '*char')';
            fclose(fid);
        
            % Extract words inside square brackets
            matches = regexp(textData, '\[([^\]\n\t\r\f\v"]+)\]', 'tokens');
            
            % Convert from cell of cells to a simple cell array
            words = [matches{:}];
        end

        function [process, defaultsettings]=CreateTemplateArguments(argnames)
            process=[]; defaultsettings=[];
            %process = struct('OperationType', opname);
            if ischar(argnames)
                argnames =strsplit(argnames, '|');
            end

            N = numel(argnames);
            for k=1:N
                argname = argnames{k};
                process.(argname)=['[' argname ']'];
                defaultsettings.(argname)='';
            end
            %process = StructBase.mergeStruct(info, process);
            %infoT = struct2table(process);
            %infoT = table('Size', [0, N],'VariableTypes', repmat({'char'}, 1, N),  'VariableNames', argnames);
         end

         function TaskDefFile2TaskTemplate(tskdeffile, exportXlsx)
            fieldNames = unique(daemon.taskdef.TaskDef.extractBracketWordsFromFile(tskdeffile));
            info = cell2struct(cell(size(fieldNames)), fieldNames, 2);
            tskdef=utils.json.readJson(tskdeffile);
            DefaultSettings=StructBase.getfieldx(tskdef, 'DefaultSettings');
            if ~isempty(DefaultSettings)&&isstruct(DefaultSettings)
                fns = fieldnames(DefaultSettings);
                fns = intersect(fns, fieldNames);
                for m=1:numel(fns)
                    fn = fns{m};
                    info.(fn) = DefaultSettings.(fn);
                end
            end
            [taskdeffolder, taskdefname]=fileparts(tskdeffile);
            tempfolder = mksubdir([taskdeffolder '\'], 'temp_tsk');
            utils.json.writeJson(info, [tempfolder taskdefname '.tskinfo']);
            if exist('exportXlsx', 'var') && exportXlsx
                infoT = struct2table(info, 'AsArray', true);
                writetable(infoT, [tempfolder taskdefname '_tsk.xlsx']);
            end
         end

         function TaskDefFiles2TaskTemplate(tskdeffolder, varargin)
            res = DosUtil.rdir(tskdeffolder); 
            fnames = {res(:).name};
            for k=1:numel(fnames)
                try
                 daemon.taskdef.TaskDef.TaskDefFile2TaskTemplate(fnames{k}, varargin{:});
                catch 
                    disp(['error: ' fnames{k}])
                end
            end
        end

        function TaskDef_conditions(tasksdeffile, taskdefname, conditions, taskfiles, varargin)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            for m=1:numel(conditions)
                Process{m} = struct('Condition', conditions{m}, "taskfilename", taskfiles{m});
            end
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end

        function TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin)
            filters = StructBase.toCell(filters);
            taskfiles = StructBase.toCell(taskfiles);
            for m=1:numel(filters)
                condition  = struct("ConditionType",  'InfoFilter',  "InfoSeq", 1, "Filter", filters{m});
                Process{m} = struct('Condition', condition, "taskfilename", taskfiles{m});
            end
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end

        function Process=Process_addfilters(Process, filters, varargin)
            Process = StructBase.toCell(Process);
            filters = StructBase.toCell(filters);
            for m=1:numel(filters)
                condition  = struct("ConditionType",  'InfoFilter',  "InfoSeq", 1, "Filter", filters{m});
                Process{m}.Condition = condition;
            end
        end

        
        function TaskDef_filterGenders(tasksdeffile, taskdefname, varargin)
            genders = {'M', 'F'};
            for m=1:numel(genders)
                gender = genders{m};
                filters{m}   = struct("FieldName", 'PatientSex', 'MatchMode', 'regexp', "FieldValue", ['^' gender],  "Inclusion", true);           
                taskfiles{m} = [taskdefname '_' gender '/[Modality].[SeriesInstanceUID].tsk'];
            end
            daemon.taskdef.TaskDef.TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin{:});
        end

       
        

        function nnUNETSeg1(tasksdeffile,modality, modelfolder,  modelname, modeloptext)
            label_image_type='labelmask';
            taskdefname0= matlab.lang.makeValidName(modelname);
            %modelfolder = 'C:\ARTDaemon\distribute\nnUNETDatav2.ts-mr\';
            %modeloptext = '#nnUNetTrainerNoMirroring__nnUNetPlans__3d_fullres#fold_0';
            ModelOptionFile=DosUtil.SimplifyPath([modelfolder modelname modeloptext '.opt']);
            
            for astemp=[0 1]
                obj=daemon.taskdef.TaskDef.nnUNETInference(tasksdeffile, modality, modelname, taskdefname0, ModelOptionFile, label_image_type, astemp);
            end
        end

        function obj=nnUNETInference(tasksdeffile, modality, subtaskname, taskdefname0, modeloptfile, label_image_type, astemp, augmentprocess)
              %modality = 'MR';
              modeloptfile=DosUtil.SimplifyPath(modeloptfile) ;
              if ~astemp
                 CustomSubFolder=subtaskname; 
                 InputImage='../image.nii.gz';
                 OutputFile=label_image_type;
                 taskdefname = [modality '_' taskdefname0];
                 dependency= struct("filename", InputImage, "taskfilename", ['../DcmConverter/DCMCONVERT_' modality '/[Modality].[SeriesInstanceUID].tsk']);
                 if strcmpi(label_image_type, 'labelmask')
                      listname = 'StructSet';
                      associateinfo=struct('ID', subtaskname, ...
                     "LabelMaskFileName", [CustomSubFolder, '/'  OutputFile]);
                 else
                     listname = 'ImageSet';
                     associateinfo=struct('ID', subtaskname, "SeriesDescription", subtaskname, "Modality", subtaskname, ...,
                         'SeriesDate', '[SeriesDate]',...
                         "ImageFileName", [CustomSubFolder, '/'  OutputFile]);
                 end
                AssociateStruct2List = struct('OperationType','AssociateStruct',...
                    'AssociateFileName', '../dcmdata.cfg', 'ListName', listname, 'Info', associateinfo);
             else
                 %TaskOutputFolder='[TaskOutputFolder]'; 
                 InputImage='[InputImage]';
                 OutputFile='[OutputFile]';
                 taskdefname = ['temp_' taskdefname0];
                 CustomSubFolder=''; 
                 dependency= struct("filename", InputImage);
                 AssociateStruct2List=[];
              end

            taskdeffolder = [fileparts(tasksdeffile) '/'];
            m=0; Process =[];
            m=m+1; Process{m}   = struct('OperationType','nnUNETInference',...
                    'ModelOptionFile', modeloptfile, ...
                    'InputImage',InputImage, ...
                    'OutputFile', OutputFile, ...
                    'label_image_type', label_image_type);
            
            if exist('augmentprocess', 'var') &&~isempty(augmentprocess)
                Process = cat(2,  Process, StructBase.toCell(augmentprocess)); 
                m=numel(Process);
            end
            if ~isempty(AssociateStruct2List)
                m=m+1; Process{m}   = AssociateStruct2List;
            end
           obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           obj.SetDefaultCfg();
           obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end

        function str = SegmanStr(db, ID)
            str.OrigImageHeaderFile= '../image_dcm.json';
            str.DBTableName = db; 
            str.DcmExport=struct("OrigImageHeaderFile", '../image_dcm.json',...
                "OutRSFileName", 'RS_Segman.dcm',...
                "OutRSSetName", ID);

            str.OutputStructSet=struct(...
                "ID", ID, ...
                "ROIMaskFileName", "roimask", ...
                "LabelMaskFileName", "labelmask", ...
                "LabelMaskWithImageContours", 1, ...
                "ContourSmoothWindow", 3, ...
                "ContourMinNumPoints", 20);
        end

        function CreateSegmanDef(tasksdeffile,taskdefname, db, segmanID, dependency,MergeOperation, PostProcessing, varargin)
           taskdeffolder = [fileparts(tasksdeffile) '/'];
           obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           obj.SetDefaultCfg();
           str = daemon.taskdef.TaskDef.SegmanStr(db, segmanID);
           obj.Generate(tasksdeffile, dependency, [],  {'MergeOperation', MergeOperation}, {'CustomSubFolder', taskdefname}, ...
               {'PostProcessing', PostProcessing},OptionsMap.struct2options(str), varargin{:});
        end
    end
end