function DcmRTAdminDaemon(varargin)
    if isdeployed
        warning('off','all');
    end
    
    %%%%%%%%%%%%%%%%%%%%%%check license%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    daemon.utils.LicenseTool.ValidateArtDaemonLicense('rtadmin');
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    
    % force the computer to remain awake
    try
    dosutils.insomnia('on','verbose');
    catch
    end
    server = daemon.DcmRTAdminServer(varargin{:});
    server.RunServer; 
end
