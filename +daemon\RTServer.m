classdef RTServer <OptionsWithLog
    properties
        m_AutoTaskFilter; 
        
        m_ManualTaskFilter; 
    end
    
    methods
        function obj = RTServer(servername, varargin)
            obj = obj@OptionsWithLog(servername, varargin{:});
            obj.setOption('ServerName', servername); 
            %obj.setOption('AutoDefaultTask', 1); 
            obj.setUndefinedOption('log.file', [obj.LogFolder() servername '.log']); 
            
            filterfile = obj.getoptioni('autotaskfilter.file'); 
            if exist(filterfile, 'file')
                obj.m_AutoTaskFilter  = xls.TableFilter_RTPlan({'FilterFileName', filterfile});
            end
            
            filterfile = obj.getoptioni('manualtaskfilter.file'); 
            if exist(filterfile, 'file')
                obj.m_ManualTaskFilter= xls.TableFilter_RTPlan({'FilterFileName', filterfile});
            end
        end
        
%         function res = RootFolder(self)
%             res = self.getoptioni([ServerName(self) '_ROOT']);
%         end
        
        function folder = getOption_FullPath(self, str)
            folder = self.getOption(str);
            if ~isempty(folder) && ~DosUtil.isabsolutepath(folder)
                folder = DosUtil.toFullFile(folder, [],[pwd '\']);
                self.setOption(str, folder);
            end
        end
        
        function folder = RootFolder(self)
            str = [ServerName(self) '_ROOT'];
%             folder = self.getoptioni(str);
%             if ~DosUtil.isabsolutepath(folder)
%                 folder = [pwd '/' folder];
%                 folder = strrep(folder, '\', '/');
%                 self.setOption(str, folder);
%             end
            folder = getOption_FullPath(self, str);
        end
        
        function res = ServerName(self)
            res = self.getoptioni('ServerName');
        end
        
        function res = DcmDataFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER',   [self.RootFolder() 'DataBase\DicomData\']);
        end
        
        function res = PlanDBFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.plandb', [self.RootFolder() 'DataBase\PlanDB\']);
        end
        
        function res = TaskFolder(self)
            str = 'tasks.rootfolder'; 
            %res = self.getOption(str);
            res = getOption_FullPath(self, str);
            if isempty(res)
                res = DosUtil.mksubdir(RootFolder(self), 'tasks');
                self.setOption(str, res); 
            end
        end
        
        function res = LogFolder(self)
            str = 'Log.Folder'; 
            %res = self.getOption(str);
            res = getOption_FullPath(self, str);
            if isempty(res)
                res =  DosUtil.mksubdir(RootFolder(self), 'log');
                self.setOption(str, res); 
            end
        end
        
        function res = HistoryFolder(self)
            str = 'taskshistory.rootfolder'; 
            res = getOption_FullPath(self, str);
            if isempty(res)
                res = DosUtil.mksubdir(RootFolder(self), 'tasks.history');
                self.setOption(str, res); 
            end
        end
        
        function res = RegisteredTask(self)
            res = self.getoptioni('registered.task');
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
        
        function flag = AutoDefaultTask(self)
            flag = self.getoptioni_numeric('AutoDefaultTask', 1);
        end
        
        function res = TaskFileExts(self)
            res = self.getoptioni('TaskFileExts', {'*.tsk', '*.csv', '*.xlsx'}); 
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
        
        
        function ScheduleNewTasks(self)
            
        end
        
        function RunServer(self, servermode)
            if ~exist('servermode', 'var')
                servermode = 1;
            end
                       
            registeredtasks = self.RegisteredTask; 
            defaulttask = self.getoptioni('default.task', ''); 
            
            self.LogInfo(['*********************************Start ' self.ServerName '***********************']);
            
            while(1)
                ScheduleNewTasks(self);
                
                if AutoDefaultTask(self)
                    self.ProcessDefaultTask();
                end
                
                if ~isempty(registeredtasks)
                    fnames = self.ListTaskFiles();
                    for k=1:numel(fnames)
                        fname = fnames{k};
                        try
                            task = self.ParseTaskFile(fname, registeredtasks, defaulttask);
                            if ~isempty(task)
                                try
                                    %self.LogInfo(['process ' upper(task) ' in file ', fname]);    

                                    self.ProcessTaskFile(fname, task);

                                catch err
                                    self.LogErr(err);
                                end
                                self.LogTask(task, fname);
                            end
                        catch err
                            self.LogErr(err);
                        end
                    end
                end
                
                if ~servermode
                    break;
                end
                
                if (~isdeployed)
                    disp(['waitfor new task.....']); 
                end
                pause(3);
            end
        end
        
        function fnames = ListTaskFiles(self)
            taskfolder = [self.TaskFolder '**\'];
            exts = TaskFileExts(self);
            %patlistfolders= {[taskfolder, '*.tsk'], [taskfolder, '*.csv'], [taskfolder, '*.xlsx']};  
            patlistfolders = cellfun(@(x)([taskfolder x]), exts, 'uniformoutput', false);   
            fnames={};
            for m=1:numel(patlistfolders)
                res = DosUtil.rdir(patlistfolders{m});
                fnames =cat(2, fnames, {res(:).name}); 
            end
        end
        
        %to be implemented by subclass        
        function ProcessTaskFile(self, fname, task)
            
        end
        
        function res = CalcOutputRoot(self)
            res = DosUtil.mksubdir(RootFolder(self), 'RTCalc'); 
        end
        
        function res = CalcOutputFolder(self, patid, planid)
            res = DosUtil.mksubdir(CalcOutputRoot(self), [patid '\RP.' planid]); 
        end
        
        function res = CalcStatusFolder(self, patid, planid)
            res = CalcOutputFolder(self, patid, planid); 
        end
        
        function res = CalcStatusFile(self, patid, planid)
            res = [CalcStatusFolder(self, patid, planid) 'status.done']; 
        end
        
        function T = LoadActivePlanTable(self)
            folder = PlanDBFolder(self);
            res    = DosUtil.rdir([folder 'RTPlanDB_*.xlsx']); 
            fnames = {res(:).name};
            T=table;
            
            for k=1:numel(fnames)
                T1  = xls.readtable_str(fnames{k});
                
                T = cat(1, T, T1);
            end
            
            if ~isempty(self.m_AutoTaskFilter)
                [~, T] = FilterTable(self.m_AutoTaskFilter, T);
            end
            
            T = FilterActivePlanTable(self, T);
        end
        
        function T = FilterActivePlanTable(self, T0)
            T=table; 
            for k=1:size(T0, 1)
                patid    = T0.PatientID{k}; 
                planid   = T0.SOPInstanceUID{k}; 
                %patfolder = [DcmDataFolder(self) patid '\'];
                statusfile = CalcStatusFile(self, patid, planid);
                if ~exist(statusfile, 'file')
                    T = cat(1, T, T0(k, :)); 
                end
            end
        end
        
        
        function ProcessDefaultTask(self)
            T = LoadActivePlanTable(self);
            for k=1:size(T, 1)
                try
                    %self.ProcessPlan(T(k, :));
                    T1 = FilterActivePlanTable(self, T(k, :));
                    if ~isempty(T1)
                        self.ProcessPlan(T1);
                    end
                catch err
                    self.LogErr(err);
                end
            end
        end
        
        %to be implemented by subclass 
        function ProcessPlan(self, T)
            
        end
        
        function UpdateStatus(self, statusfolder, statusstr, varargin)
            self.LogInfo(statusstr); 
%             delete([statusfolder 'status.*']); 
            self.UpdateStatusFile(statusfolder, statusstr, varargin{:});
        end
        
        function LogTask(self, task,  fname)
            if ~exist(fname, 'file')
                return
            end
            
            logfolder  = DosUtil.mksubdir(HistoryFolder(self), task); 
%             str = char(datetime); str = strrep(str, ':', '.'); 
            [PATHSTR,NAME,EXT] = fileparts(fname);
            dstfile = [logfolder,NAME  EXT];
%             if exist(dstfile, 'file')
%                 %dstfile = [dstfile str];
%                 dstfile = [logfolder,NAME EXT];
%             end
            
            movefile(fname, dstfile, 'f'); 
            %numpatients = numel(res.mrns); 
            % self.LogInfo([task ' task file: ', fname ' moved to ' dstfile]);
        end

        function task = RetrieveTaskTypeFromTaskFile(self, fname)
            registeredtasks0 = self.RegisteredTask; 
            defaulttask = self.getoptioni('default.task', ''); 
            registeredtasks = registeredtasks0(:)';
            registeredtasks = lower(registeredtasks);
            [FILEPATH,NAME,EXT] = fileparts(fname);
            FILEPATH=strrep(FILEPATH, '\', '/');
            a = lower(regexpi(FILEPATH, '/', 'split'));
            %b = lower(regexpi(NAME, '\.', 'split'));
            b = lower(regexpi(NAME, '#', 'split'));
            task = defaulttask;
            for k=1:numel(registeredtasks)
                registeredtask = registeredtasks{k};
                if ismember(registeredtask, a) || ismember(registeredtask, b)
                    task = registeredtasks0{k}; return;
                end
            end
        end
    end
    
    methods (Static)
        function task = ParseTaskFile(fname, registeredtasks0, defaulttask)
            if ~exist('defaulttask', 'var')
                defaulttask = '';
            end
            
            registeredtasks = registeredtasks0(:)';
            registeredtasks = lower(registeredtasks);
            [FILEPATH,NAME,EXT] = fileparts(fname);
            a = lower(regexpi(FILEPATH, filesep, 'split'));
            b = lower(regexpi(NAME, '\.', 'split'));
            task = defaulttask;
            for k=1:numel(registeredtasks)
                registeredtask = registeredtasks{k};
                if ismember(registeredtask, a) || ismember(registeredtask, b)
                    task = registeredtasks0{k}; return;
                end
            end
        end
        
        function UpdateStatusFile(statusfolder, statusstr, msg)
            if ~exist('msg', 'var')
                fclose(fopen([statusfolder, 'status.' statusstr], 'w'));
            else
                fid = fopen([statusfolder,  'status.' statusstr], 'w');
                fprintf(fid, msg); 
                fclose(fid);
            end
        end
    end
end

