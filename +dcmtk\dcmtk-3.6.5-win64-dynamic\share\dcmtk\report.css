/* Copyright (C) 2000-2010, OFFIS e.V. 
   All rights reserved.  See COPYRIGHT file for details.
   Sample CSS for HTML output of "dsr2html"
*/

body {
	background-color: #dddddd;
	font-family: sans-serif;
}

h1, h2, h3, h4, h5, h6 {
	background-color: #000099;
	color: white;
	width: 100%;
}

h1 {
	padding: 4px;
	font-size: x-large;
	font-weight: bold;
}

h2 {
	padding: 3px;
	font-size: large;
}

h3, h4, h5, h6 {
	padding: 2px;
}

h4, h5, h6 {
	font-size: medium;
}

h2 a[name] {
	color: white;
	text-decoration: none;
}

div {
    background-color: white;
	text-align: justify;
}

div small {
	width: 100%;
	color: black;
	text-align: left;
}

td {
	background-color: #ccccff;
	font-family: sans-serif;
	padding: 4px;
}

a[href] {
	color: maroon;
}

a:hover {
	color: red;
}

a[name]:hover {
    text-decoration: underline;
}

div.footnote {
	background-color: #dddddd;
	color: gray;
}

div.footnote a, div.footnote small {
	color: gray;
}

div.footnote a:hover {
    color: black;
}

span.under {
    text-decoration: underline;
}

hr {
	color: silver;
	background: transparent;
	border: thin;
}
