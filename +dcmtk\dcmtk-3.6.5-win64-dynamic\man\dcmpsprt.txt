dcmpsprt(1)                       OFFIS DCMTK                      dcmpsprt(1)



NAME
       dcmpsprt  -  Read DICOM images and presentation states and render print
       job


SYNOPSIS
       dcmpsprt [options] dcmfile-in...

DESCRIPTION
       The dcmpsprt utility reads one or more DICOM  images  and  presentation
       states  and  creates  a print job. The print job consists of one Stored
       Print object and one or more Hardcopy Grayscale images and  is  written
       to  the database specified in the configuration file. The print job can
       optionally be spooled to the printer. In this case, dc<PERSON><PERSON><PERSON> calls  the
       dcmprscu application which performs communication with the printer.

PARAMETERS
       dcmfile-in  DICOM image file(s) to be printed

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   processing options
         +p   --pstate  [p]state file: string
                render the following image with presentation state p
                (this option can be specified multiple times)

         -c   --config  [f]ilename: string
                process using settings from configuration file f

         -p   --printer  [n]ame: string (default: 1st printer in cfg file)
                select printer with identifier n from cfg file

   spooling options
         -s   --spool
                spool print job to DICOM printer

              --nospool
                do not spool print job to DICOM printer (default)

   film orientation options
              --portrait
                set portrait orientation

              --landscape
                set landscape orientation

              --default-orientation
                use printer default (default)

   trim (border) options
              --trim
                set trim on

              --no-trim
                set trim off

              --default-trim
                use printer default (default)

   requested decimate/crop behavior options
              --request-decimate
                request decimate

              --request-crop
                request crop

              --request-fail
                request failure

              --default-request
                use printer default (default)

   print presentation LUT options
              --default-plut
                do not create presentation LUT (default)

              --identity
                set IDENTITY presentation LUT shape

              --plut  [l]ut identifier: string
                add LUT l to print job

              --inverse-plut
                render the inverse presentation LUT into the
                bitmap of the hardcopy grayscale image

              --illumination  [v]alue: integer (0..65535)
                set illumination to v (in cd/m^2)

              --reflection  [v]alue: integer (0..65535)
                set reflected ambient light to v (in cd/m^2)

   basic film session options (only with &ndash;spool)
              --copies  [v]alue: integer (1..100, default: 1)
                set number of copies to v

              --medium-type  [v]alue: string
                set medium type to v

              --destination  [v]alue: string
                set film destination to v

              --label  [v]alue: string
                set film session label to v

              --priority  [v]alue: string
                set print priority to v

              --owner  [v]alue: string
                set film session owner ID to v

   annotation options
              --no-annotation
                do not create annotation (default)

         -a   --annotation  [t]ext: string
                create annotation with text t

         +pd  --print-date
                prepend date/time to annotation (default)

         -pd  --print-no-date
                do not prepend date/time to annotation

         +pn  --print-name
                prepend printer name to annotation (default)

         -pn  --print-no-name
                do not prepend printer name to annotation

         +pl  --print-lighting
                prepend illumination to annotation (default)

         -pl  --print-no-lighting
                do not prepend illumination to annotation

   overlay options
         +O   --overlay  [f]ilename: string, [x] [y]: integer
                load overlay data from PBM file f and display at position (x,y)

         +Og  --ovl-graylevel  [v]alue: integer (0..4095)
                use overlay gray level v (default: 4095 = white)

   other print options
         -l   --layout  [c]olumns [r]ows: integer (default: 1 1)
                use 'STANDARD
              --filmsize  [v]alue: string
                set film size ID to v

              --magnification  [v]alue: string
                set magnification type to v

              --smoothing  [v]alue: string
                set smoothing type to v

              --configinfo  [v]alue: string
                set configuration information to v

              --resolution  [v]alue: string
                set requested resolution ID to v

              --border  [v]alue: string
                set border density to v

              --empty-image  [v]alue: string
                set empty image density to v

              --max-density  [v]alue: string
                set max density to v

              --min-density  [v]alue: string
                set min density to v

              --img-polarity  [v]alue: string
                set image box polarity to v (NORMAL or REVERSE)

              --img-request-size  [v]alue: string
                set requested image size to v (width in mm)

              --img-magnification  [v]alue: string
                set image box magnification type to v

              --img-smoothing  [v]alue: string
                set image box smoothing type to v

              --img-configinfo  [v]alue: string
                set image box configuration information to v

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmpsprt utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

FILES
       <etcdir>/dcmpstat.cfg,  <etcdir>/printers.cfg  -  sample  configuration
       files

SEE ALSO
       dcmprscu(1)

COPYRIGHT
       Copyright (C) 1999-2014 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmpsprt(1)
