dcmpsmk(1)                        OFFIS DCMTK                       dcmpsmk(1)



NAME
       dcmpsmk - Create DICOM grayscale softcopy presentation state


SYNOPSIS
       dcmpsmk [options] dcmfile-in dcmfile-out

DESCRIPTION
       The  dcmpsmk  utility  reads a DICOM image file and creates a grayscale
       softcopy presentation state object  according  to  Supplement  33.  The
       presentation  state object is written back to file. A number of command
       line options allow to specify how  certain  constructs  that  might  be
       present  in  the  image  file  should be referenced or activated in the
       presentation state. The newly created presentation state references the
       source  image  and contains values that should allow for a 'reasonable'
       display of the image when rendered under control  of  the  presentation
       state.

PARAMETERS
       dcmfile-in   DICOM image file(s) to be read

       dcmfile-out  DICOM presentation state file to be created

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input optons
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

   processing options
       VOI transform handling:

         +Vl  --voi-lut
                use first VOI LUT if present (default)

         +Vw  --voi-window
                use first window center/width if present

         -V   --voi-ignore
                ignore VOI LUT and window center/width

       curve handling:

         +c   --curve-activate
                activate curve data if present (default)

         -c   --curve-ignore
                ignore curve data

       overlay handling:

         +oc  --overlay-copy
                copy overlays if not embedded, activate otherwise (default)

         +oa  --overlay-activate
                activate overlays

         -o   --overlay-ignore
                ignore overlays

       shutter handling:

         +s   --shutter-activate
                use shutter if present in image (default)

         -s   --shutter-ignore
                ignore shutter

       presentation LUT shape handling:

         +p   --plut-activate
                use presentation LUT shape if present (default)

         -p   --plut-ignore
                ignore presentation LUT shape

       layering:

         +l1  --layer-single
                all curves and overlays are in one layer

         +l2  --layer-double
                one layer for curves, one for overlays (default)

         +ls  --layer-separate
                separate layers for each curve and overlay

       location of referenced image:

         -lx  --location-none
                image reference without location (default)

         -ln  --location-network  [a]etitle: string
                image located at application entity a

         -lm  --location-media  [f]ilesetID, fileset[UID]: string
                image located on storage medium

   output options
       output transfer syntax:

         +t=   --write-xfer-same
                 write with same TS as image file (default)

         +te   --write-xfer-little
                 write with explicit VR little endian TS

         +tb   --write-xfer-big
                 write with explicit VR big endian TS

         +ti   --write-xfer-implicit
                 write with implicit VR little endian TS

NOTES
       If  more  than one input file (dcmfile-in) is specified, the additional
       image files are only referenced from  the  created  presentation  state
       file, but no further (e.g. display-related) information is taken over.

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmpsmk utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

COPYRIGHT
       Copyright  (C)  1998-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcmpsmk(1)
