dcmcjpls(1)                       OFFIS DCMTK                      dcmcjpls(1)



NAME
       dcmcjpls - Encode DICOM file to JPEG-LS transfer syntax


SYNOPSIS
       dcmcjpls [options] dcmfile-in dcmfile-out

DESCRIPTION
       The  dcmcjpls  utility  reads an uncompressed DICOM image (dcmfile-in),
       performs a JPEG-LS compression (i. e.  conversion  to  an  encapsulated
       DICOM transfer syntax) and writes the converted image to an output file
       (dcmfile-out).

PARAMETERS
       dcmfile-in   DICOM input filename to be converted

       dcmfile-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

   JPEG-LS encoding options
       JPEG-LS process:

         +el  --encode-lossless
                encode JPEG-LS lossless only TS (default)

         # This options selects the JPEG-LS lossless only transfer syntax
         # and performs a lossless compression.

         +en  --encode-nearlossless
                encode JPEG-LS near-lossless TS (NEAR: 2)

         # This options selects the JPEG-LS lossy transfer syntax
         # and performs a near-lossless compression.

       JPEG-LS bit rate (near-lossless only):

         +md  --max-deviation  [d]eviation: integer (default: 2)
                defines maximum deviation for an encoded pixel

         # This option specifies the maximum deviation for a single pixel from
         # the original pixel value.

       lossless compression:

         +pr  --prefer-raw
                prefer raw encoder mode (default)

         # This option enables the raw encoder. The raw encoder encodes the
         # complete pixel cell as it was read from the source image without
         # performing any modifications.

         +pc  --prefer-cooked
                prefer cooked encoder mode

         # This option enables the cooked encoder. The cooked encoder moves
         # overlay data to separate tags (60xx,3000) and only encodes the
         # stored bits in each pixel.

       JPEG-LS compression:

         +t1  --threshold1  [t]hreshhold: integer
                set JPEG-LS encoding parameter threshold 1

         +t2  --threshold2  [t]hreshhold: integer
                set JPEG-LS encoding parameter threshold 2

         +t3  --threshold3  [t]hreshhold: integer
                set JPEG-LS encoding parameter threshold 3

         # By default, the values for T1, T2, T3 are computed based on
         # the number of bits per sample.

         +rs  --reset  [r]eset: integer (default: 64)
                set JPEG-LS encoding parameter reset

       JPEG-LS interleave:

         +il  --interleave-line
                force line-interleaved JPEG-LS images (default)

         # This flag forces line-interleaved mode for the resulting image.
         # In line-interleave mode each line from the source image is
         # compressed separately for each component and then the next line
         # is encoded.

         +is  --interleave-sample
                force sample-interleaved JPEG-LS images

         # This flag forces sample-interleaved mode for the resulting image.
         # In sample-interleave mode each pixel's components are encoded before
         # the next pixel is encoded.

         +in  --interleave-none
                force uninterleaved JPEG-LS images

         # This flag forces uninterleaved mode for the resulting image.
         # In this mode, each of the image's components are completely encoded
         # before the next component is handled.

         +iv  --interleave-default
                use the fastest possible interleave mode

         # This flag selects an interleave mode based on the source image's mode.
         # If possible, the image is not converted to a different interleave mode.

       JPEG-LS padding of odd-length bitstreams:

         +ps  --padding-standard
                pad with extended EOI marker (default)

         # Pad odd-length JPEG-LS bitstreams by writing an extended end of image
         # segment marker FF FF D9, as required by the DICOM standard.

         +pz  --padding-zero
                pad with zero byte (non-standard)

         # Pad odd-length JPEG-LS bitstreams by writing a zero byte after the
         # end of image segment marker, i.e. FF D9 00. This is not DICOM conformant
         # but required for interoperability with the HP LOCO reference implementation,
         # which does not support extended JPEG-LS bitstreams..fi

   encapsulated pixel data encoding options
       encapsulated pixel data fragmentation:

         +ff  --fragment-per-frame
                encode each frame as one fragment (default)

         # This option causes the creation of one compressed fragment for each
         # frame (recommended).

         +fs  --fragment-size  [s]ize: integer
                limit fragment size to s kbytes

         # This option limits the fragment size which may cause the creation of
         # multiple fragments per frame.

       basic offset table encoding:

         +ot  --offset-table-create
                create offset table (default)

         # This option causes the creation of a valid offset table for the
         # compressed JPEG fragments.

         -ot  --offset-table-empty
                leave offset table empty

         # This option causes the creation of an empty offset table
         # for the compressed JPEG fragments.

       SOP Class UID:

         +cd  --class-default
                keep SOP Class UID (default)

         # Keep the SOP Class UID of the source image.

         +cs  --class-sc
                convert to Secondary Capture Image (implies --uid-always)

         # Convert the image to Secondary Capture.  In addition to the SOP Class
         # UID, all attributes required for a valid secondary capture image are
         # added. A new SOP instance UID is always assigned.

       SOP Instance UID:

         +ud  --uid-default
                assign new UID if lossy compression (default)

         # Assigns a new SOP instance UID if the compression is lossy JPEG.

         +ua  --uid-always
                always assign new UID

         # Unconditionally assigns a new SOP instance UID.

         +un  --uid-never
                never assign new UID

         # Never assigns a new SOP instance UID.

   output options
       post-1993 value representations:

         +u   --enable-new-vr
                enable support for new VRs (UN/UT) (default)

         -u   --disable-new-vr
                disable support for new VRs, convert to OB

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

       data set trailing padding:

         -p=  --padding-retain
                do not change padding (default)

         -p   --padding-off
                no padding

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

NOTES
       The dcmcjpls utility compresses DICOM images of  all  SOP  classes.  It
       processes  all  Pixel  Data  (7fe0,0010)  elements in the dataset, i.e.
       compression is also performed on an icon image. However, dcmcjpls  does
       not attempt to ensure that the compressed image still complies with all
       restrictions of the object's IOD.

       The user is responsible for making sure that the compressed  images  he
       creates  are  compliant  with  the  DICOM standard. If in question, the
       dcmcjpls utility allows one to convert an image to secondary capture  -
       this SOP class does not pose restrictions as the ones mentioned above.

TRANSFER SYNTAXES
       dcmcjpls  supports  the following transfer syntaxes for input (dcmfile-
       in):

       LittleEndianImplicitTransferSyntax             1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax             1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax   1.2.840.10008.1.2.1.99 (*)
       BigEndianExplicitTransferSyntax                1.2.840.10008.1.2.2

       (*) if compiled with zlib support enabled

       dcmcjpls supports the following transfer syntaxes for output  (dcmfile-
       out):

       JPEGLSLosslessTransferSyntax                   1.2.840.10008.1.2.4.80
       JPEGLSLossyTransferSyntax                      1.2.840.10008.1.2.4.81

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmcjpls utility will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmdjpls(1)

COPYRIGHT
       Copyright  (C)  2009-2019  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmcjpls(1)
