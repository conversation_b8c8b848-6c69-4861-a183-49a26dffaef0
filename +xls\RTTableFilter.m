classdef RTTableFilter <xls.TableFilter
    properties
        
    end

    methods
        function obj = RTTableFilter(varargin)
           <EMAIL>(varargin{:});
           matchinfo = obj.getoptioni('matchinfo');
           if ~isempty(matchinfo)
               obj.ConvertMatchInfo(matchinfo);
           end
        end

        function ConvertMatchInfo(self, infos)
            for k=1:numel(self.m_Filter)
                self.m_Filter{k} = self.ConvertMatchFilter(self.m_Filter{k}, infos);
            end
        end
    end

    methods (Static)
        function filter = ConvertMatchFilter(filter, infos)
            if isfield(filter, 'FieldValue') && ischar(filter.FieldValue)
                 filter.FieldValue   = utils.json.TasksDef.WildCardRepStr(filter.FieldValue, infos); 
            end

            if strcmp(filter.FieldName, 'DateTimeDifference')
                try 
                    dateStr =  utils.json.TasksDef.WildCardRepStr(filter.Date, infos);
                    dateval = datenum([dateStr], 'yyyymmdd');
                catch 
                    dateval = now; 
                end
                
%                 try 
%                     timestr =  utils.json.TasksDef.WildCardRepStr(filter.Time, infos);
%                     timeval = datenum([timestr], 'HHMMSS');
%                 catch 
%                     timeval = 0; 
%                 end
%                 datetimeval = dateval+timeval; 
                datetimeval = dateval; 
                daterange = filter.FieldValue(:)' + datetimeval; 
                filter.datetimemode = 'Absolute';
                filter.MatchMode = 'DateTimeRange';
                filter.FieldValue=daterange; 
            end
        end
    end
end