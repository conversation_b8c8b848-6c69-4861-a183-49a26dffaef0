classdef HouseKeepProj <SAProject  
    properties
    end
    
    methods 
        function self = HouseKeepProj(varargin)
            self = self@SAProject(varargin{:}); 
            filepath = fileparts(mfilename('fullpath')); 
            addpath(fullfile(filepath,  '..')); 
            %addpath('C:\Source\matlab\dcmserver\src\');
            sourcePath = fullfile(filepath,  '..\..\');
            self.setOption('sourcePath', sourcePath); 
            addpath(sourcePath);
        end
        
        function res = BuildType(self)
            res = self.getoptioni('buildtype', 'standalone') ;
%               res = self.getoptioni('buildtype', 'console') ;
        end
        
%         function res = RecursivePath(self)
%             SourcePath = [self.SolutionRootPath '\Source\'];
%             res = {[SourcePath 'Common\']};
%             ResearchFolder='C:\Source\matlab\research-lu\';
% %             res = cat(2, res, [ResearchFolder 'Commissioner\']); 
%             res = cat(2, res, [ResearchFolder 'utils\']); 
%         end
       
        function res = RecursivePath(self)
           SourcePath = [self.SolutionRootPath];
           res = {[SourcePath 'repo-common\Common\']};
        end
        
%         function res = ProjectPath(self)
%             res = {'C:\Source\matlab\dcmserver\src\'}; 
%         end
        

        function res = Attachments(self)
             res  = self.Attachments@SAProject();
             res = cat(2, res, privateFolder(self)); 
             %binfolder = {'C:\Source\matlab\dcmserver\src\+dcmtk\dcmtk-3.6.5-win64-dynamic\bin\'};
             %binfolder = {[ProjectPath(self) '+dcmtk\dcmtk-3.6.5-win64-dynamic\bin\'], [ProjectPath(self) '+dosutils\insomnia_header.h']};
             sourcepath = self.getoptioni('sourcePath');
             binfolder = {[sourcepath '+dcmtk\dcmtk-3.6.5-win64-dynamic\bin\'], [sourcepath '+dosutils\insomnia_header.h']};
             res = cat(2, res, binfolder); 
        end
    end
    
    methods (Static)
        function name = AppName()
            name = 'DcmServerHouseKeep'; 
        end
        
        function res = mFiles
            res = {'DcmServerHouseKeep.m'}; 
        end
    end
 end

