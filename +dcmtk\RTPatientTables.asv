classdef RTPatientTables<OptionsMap 
    properties
        
    end
    
    methods
        function obj = RTPatientTables(varargin)
            options = OptionsMap();
            options.setOption('registered.modality', {'CT', 'MR', 'PT', 'US', 'REG', 'SEG', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'RTIMAGE', 'RTRECORD'});
            options.setOption('DCMSERVER_FOLDER', 'C:\DicomRTPatient\');
            obj@OptionsMap(options, varargin{:});
        end
        
        function res = RootFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER');
        end

        function res = RootFolder2(self)
            res = self.getoptioni('DCMSERVER_FOLDER2', '');
        end
        
        function res = PatientID(self)
            res = self.getoptioni_char('PatientID');
        end
        
        % function res = PatientFolder(self)
        %     res = self.getoptioni('PatientFolder', [RootFolder(self) PatientID(self) '\']);
        % end

        function res = PatientFolder(self)
            patid = PatientID(self);
            defaultfolder  = [RootFolder(self) patid '\']; 
            defaultfolder2 = [RootFolder2(self) patid '\']; 
            if ~exist([defaultfolder 'patient.' patid '.xlsx'], 'file') && exist([defaultfolder2 'patient.' patid '.xlsx'], 'file')
                defaultfolder=defaultfolder2;
            end
            res = self.getoptioni('PatientFolder',defaultfolder);
        end
        
        function res = TableFileName(self)
            patid = PatientID(self);
            res = self.getoptioni('TableFileName', [PatientFolder(self) 'patient.' patid '.xlsx']);  
        end
        
        function res = GetTable(self, modality)
            modality = upper(modality);
            res = self.getOption(['table.'  modality]);
            if isempty(res)
                switch upper(modality)
                    case {'CT', 'MR', 'PT', 'US'}
                        uniquekey = 'SeriesInstanceUID';
                    otherwise
                        uniquekey = 'SOPInstanceUID';
                end
                res = xls.RecordKeepTable({'table.filename',TableFileName(self)}, {'table.sheetname',modality}, {'table.uniquekey', uniquekey}); 
                res.LoadTable();
                self.setOption(['table.'  modality], res);
            end
        end
        
        function info = GetTableRow(self, modality, uid, varargin)
            T = GetTable(self, modality);
            info = T.GetTableRow(uid, varargin{:});
        end
        
        function T = GetRawTable(self, modality)
            T = table;
            res = GetTable(self, modality);
            if ~isempty(res)
                T = res.GetRawTable();
            end
        end
        
        function SetRawTable(self,T, modality)
            modality = upper(modality);
            res = xls.TableBase({'table.sheetname',modality});
            res.SetRawTable(T);
            self.setOption(['table.'  modality], res);
        end
        
        function SaveTables(self, modalities)
            if ~exist('modalities', 'var') || isempty(modalities)
                modalities = self.getoptioni('registered.modality');
            end
            if ischar(modalities)
                modalities = strsplit(modalities, '|');
            end
            for k=1:numel(modalities)
                SaveTable(self, modalities{k});
            end
        end
        
        function SaveTable(self, modality)
            T = self.getOption(['table.'  upper(modality)]);
            if ~isempty(T) && ~isEmpty(T)
                T.SaveTable();
            end
        end
        
        function RefreshTables(self, modalities)
            if ~exist('modalities', 'var') || isempty(modalities)
                modalities = self.getoptioni('registered.modality');
            end
            if ischar(modalities)
                modalities = strsplit(modalities, '|');
            end
            for k=1:numel(modalities)
                RefreshTable(self, modalities{k});
            end
        end
        
        function RefreshTable(self, modality)
            self.removeOption(['table.'  upper(modality)]); 
        end
        
        function HouseKeep(self, varargin)
            fname=TableFileName(self);
            if exist(fname, 'file')
                delete(fname);
            end
            RefreshTables(self);
            
            res       = DosUtil.rdir([PatientFolder(self) '*\*\*.dcm']);
            dcmfiles  = {res(:).name};
            instanceT = dcmtk.DcmDb.RetrieveInstanceRecordKeepTable(dcmfiles);
            rawT = instanceT.GetRawTable;
            options = OptionsMap(varargin{:}); 
            planDB = options.getoptioni('plandb'); 
            HouseKeepInstances(self, rawT, planDB, varargin{:});
            RefreshTables(self);
        end
        
        function HouseKeepInstances(self, T, planDB, varargin)
            options = OptionsMap(varargin{:});
            dbnotes = options.getoptioni('DBNotes', '');

            fullDB = []; 
            if ~exist('planDB', 'var')
                 planDB=[];
            else
                try
                    fullDB = planDB.GetFullDB();
                catch
                end
            end
             
            modalities = unique(T.Modality);
            modalities = upper(modalities);
            %rootfolder = RootFolder(self);
            parser     = ai.DcmParser({'dcmparser.rs.retrieveroinames', 0}, {'dcmparser.rp.retrievertdose', 0}, {'dcmparser.rp.retrieveimage', 0});
            
            for k=1:numel(modalities)
                modality = modalities{k};
                I = ismember(upper(T.Modality), modality);
                T1 = T(I, :);
                if ismember(modality, {'CT', 'MR', 'PT', 'US'})
                    seriesuids = T1.('SeriesInstanceUID');
                    uniques = unique(seriesuids);
                    M=[];
                    for m = 1:numel(uniques)
                        M(m) = find(ismember(seriesuids, uniques{m}), 1);
                    end
                    T1 = T1(M, :); 
                end
                modalityT = GetTable(self, modality);
                for n=1:size(T1, 1)
                    studyuid  = T1.('StudyInstanceUID'){n};
                    seriesuid = T1.('SeriesInstanceUID'){n};
                    sopuid    = T1.('SOPInstanceUID'){n};
                   
                    seriesfolder = [PatientFolder(self) studyuid filesep modality '.' seriesuid filesep];
                    imagefile = [seriesfolder sopuid '.dcm'];
                    switch upper(modality)
                        case {'CT', 'MR', 'PT', 'US'}
                            info = parser.ParseImageSeries(seriesfolder);
                            
                        case {'RTPLAN'}
                            info = parser.ParseRP(imagefile);
                        
                        case {'RTSTRUCT'}
                            info = parser.ParseRS(imagefile);
                        
                        case {'RTDOSE'}
                            info = parser.ParseRD(imagefile);    
                            
                        case {'REG'}
                            info = parser.ParseREG(imagefile);  
                        
                        case {'SEG'}
                            info = parser.ParseSEG(imagefile);   
                            
                        otherwise                           
                            info = parser.ParseImage(imagefile);
                    end

                    %info.Notes = tasknotes; 
                    if isstruct(dbnotes)
                        dbfns = fieldnames(dbnotes);
                        for mm=1:numel(dbfns)
                            dbfn=dbfns{mm};
                            info.(dbfn)=dbnotes.(dbfn);
                        end
                    end

                    if isa(fullDB, 'mydb.sqlite.DataBase')
                        try
%                         dbT = fullDB.GetDBTable(modality);
%                         if ~isempty(dbT)
% %                             if ~strcmpi(modality, 'RTPLAN')
%                                 dbT.InsertMatlabTable(info);
% %                             end
%                         end
                            
                            if ~strcmpi(modality, 'RTPLAN')
                                fullDB.UpdateDcmRecord(info);
                            end

                        catch
                        end
                    end
                    
                    modalityT.UpdateRow(info);
                end
                
                if ~strcmpi(modality, 'RTPLAN')
                    modalityT.SaveTable();
                end
            end
            
            res = intersect(upper(modalities), {'RTPLAN', 'RTDOSE', 'RTSTRUCT'});
            
            rp        = self.GetTable('RTPLAN');
            crosslink = ~isempty(res) && ~isempty(rp); 
            if crosslink
                CrossLinkRPTable(self);
                rp.SaveTable();
                modalities = setdiff(modalities, 'RTPLAN'); 
            end
            
%             for k=1:numel(modalities)
%                 modality = modalities{k};
%                 res = self.getoptioni(['table.'  modality]);
%                 if ~isempty(res)
%                     res.SaveTable();
%                 end
%             end
            
            if exist('planDB', 'var') && ~isempty(planDB)
                try
%                 planT = GetRawTable(self, 'RTPLAN');
%                 [~, IA, IB] = intersect(planT.('SOPInstanceUID'), T.('SOPInstanceUID')); 
%                 planT = planT(IA, :);
                planuids = GetAffectedPlanUIDs(self, T.('SOPInstanceUID'));
                if ~isempty(planuids)
                    planT    = GetTable(self, 'RTPLAN').GetSubRawTable('SOPInstanceUID', planuids);

                    %planT.Notes = arrayfun(@(x)(tasknotes), [1:size(planT, 1)]', 'uniformoutput', false);
                    %info.Notes = tasknotes; 
                    if isstruct(dbnotes)
                        dbfns = fieldnames(dbnotes);
                        for mm=1:numel(dbfns)
                            dbfn=dbfns{mm};
                            
                            planT.(dbfn) = arrayfun(@(x)(dbnotes.(dbfn)), [1:size(planT, 1)]', 'uniformoutput', false);
                        end
                    end

                    planDB.AddActiveRecords(planT);  
                    
                    try
%                         fullDB = planDB.GetFullDB();
%                         dbT    = fullDB.GetDBTable('RTPLANDB');
%                         if ~isempty(dbT)
%                              dbT.InsertMatlabTable(planT);
%                         end
                        fullDB.UpdateDcmRecords(planT);
                    catch
                    end
                end
                catch
                end
            end

            try
                 fullDB = planDB.GetFullDB();
                 patids = unique(T.PatientID);
                 for k=1:numel(patids)
                    fullDB.UpdatePatientCourseTable(patids{k});
                 end
            catch
            end
        end
        
        function planuids = GetAffectedPlanUIDs(self, uids)
            planuids = {};
            
            planT = GetRawTable(self, 'RTPLAN');
            if isempty(planT)
                return;
            end
            
            [planuids] = intersect(planT.('SOPInstanceUID'), uids);
            
            rdT        = GetTable(self, 'RTDOSE').GetSubRawTable('SOPInstanceUID', uids);
            if ~isempty(rdT)
                [planuids1]= rdT.('ReferencedRTPlanUID');
                planuids   = union(planuids, planuids1); 
            end
            
            rsT        = GetTable(self, 'RTSTRUCT').GetSubRawTable('SOPInstanceUID', uids);
            if ~isempty(rsT)
                rsuids     = rsT.('SOPInstanceUID');
                planrsuids = planT.('ReferenceStructureSetUID');
                [~, I]     = intersect(planrsuids, rsuids); 
                if ~isempty(I)
                    planuids2  = planT.('SOPInstanceUID')(I); 
                    %planuids   = cat(1, planuids, planuids2); 
                    planuids   = union(planuids, planuids2); 
                end
            end
        end
        
        
        function rp = CrossLinkRPTable(self)
            rp  = self.GetTable('RTPLAN');
            if isempty(rp) || rp.isEmpty()
                return;
            end
            
            LinkRD2RP(self, rp);
            LinkRS2RP(self, rp);
        end
        
        function LinkRD2RP(self, rp)
            rdT = self.GetRawTable('RTDOSE');
            if isempty(rdT)
                return;
            end
            
            rpuids = rdT.ReferencedRTPlanUID;
            rduids = rdT.SOPInstanceUID;
                       
            DoseSummationType=rdT.DoseSummationType;
            SOPInstanceUID = unique(rpuids);
            N=numel(SOPInstanceUID);
            ReferencedRTDoseUID=cell(N, 1); ReferencedRTBeamDoseUID=cell(N, 1); %ReferencedRTBeamDoseNumber=cell(N, 1);
            for k=1:N
                %I1  = find(ismember(rpuids, SOPInstanceUID{k}) & ismember(upper(DoseSummationType), 'PLAN'));
                I1  = find(ismember(rpuids, SOPInstanceUID{k}) & (ismember(upper(DoseSummationType), 'PLAN')|ismember(upper(DoseSummationType), 'FRACTION')));

                if ~isempty(I1)
                    ReferencedRTDoseUID{k, 1} = xls.TableBase.Content2Str(rduids(I1));
                else
                    ReferencedRTDoseUID{k, 1} = '';
                end
                I2  = find(ismember(rpuids, SOPInstanceUID{k}) & ismember(upper(DoseSummationType), 'BEAM'));
                if ~isempty(I2)
                    ReferencedRTBeamDoseUID{k, 1} = xls.TableBase.Content2Str(rduids(I2));
                else
                    ReferencedRTBeamDoseUID{k, 1} = '';
                end
            end
            
            T1  = table(SOPInstanceUID, ReferencedRTDoseUID, ReferencedRTBeamDoseUID); 
            rpT =  xls.TableBase.joinrep(rp.GetRawTable(), T1, 'SOPInstanceUID');
            rp.SetRawTable(rpT);
        end
        
        function LinkRS2RP(self, rp)
            rsT  = self.GetRawTable('RTSTRUCT');
            if isempty(rsT)
                return;
            end

            T1 = table(); 
            T1.ReferenceStructureSetUID=rsT.SOPInstanceUID;
            T1.ReferencedImageSeriesUID=rsT.ReferencedImageSeriesUID;
            T1.ReferencedImageModality =StructBase.getfieldx(rsT, 'ReferencedImageModality'); 
            T1.StructureSetLabel       =rsT.StructureSetLabel;
            rpT = rp.GetRawTable();
            rpT = xls.TableBase.joinrep(rpT, T1, 'ReferenceStructureSetUID');
            rp.SetRawTable(rpT);
        end
        
        function T = ExtendRTPlanTable(self, varargin)
             options = OptionsMap(varargin{:});            
             T=[];
             T0 = GetRawTable(self, 'RTPLAN');
             if isempty(T0)
                 return 
             end
             
             selectedstudyuid = options.getoptioni_char('SelectedStudyUID'); 
             if ~isempty(selectedstudyuid)
                I = ismember(T0.StudyInstanceUID, selectedstudyuid); 
                T0 = T0(I, :);
             end
             
             if isempty(T0)
                 return 
             end
             
             selectedplanuid = options.getoptioni_char('SelectedRTPlanUID'); 
             %for compatability, be removed later
             if isempty(selectedplanuid)
                 selectedplanuid = options.getoptioni_char('selectedplanuid'); 
             end
             if ~isempty(selectedplanuid)
                I = ismember(T0.SOPInstanceUID, selectedplanuid); 
                T0 = T0(I, :);
             end
             
             if isempty(T0)
                 return 
             end
             
             rdtable = GetRawTable(self, 'RTDOSE');
             patfolder = self.PatientFolder();
             N=size(T0, 1);
             for k=1:N
                 T1 = self.GetRPFiles(T0(k, :), patfolder,rdtable);
                 T = cat(1, T, T1);
             end
             
%              if ~isempty(selectedplanuid)
%                 SetRawTable(self,T, 'RTPLAN');
%              end
        end
        
        function T = ExtendRTStructTable(self, varargin)
             options = OptionsMap(varargin{:});            
             T = GetRawTable(self, 'RTSTRUCT');
             
             selectedstudyuid = options.getoptioni_char('SelectedStudyUID'); 
             if ~isempty(selectedstudyuid)
                I = ismember(T.StudyInstanceUID, selectedstudyuid); 
                T = T(I, :);
             end
             
             selectedrsuid = options.getoptioni_char('SelectedRTStructUID'); 
             if ~isempty(selectedrsuid)
                I = ismember(T.SOPInstanceUID, selectedrsuid); 
                T = T(I, :);
             end
             
             if isempty(T)
                 return
             end
             
             patfolder = self.PatientFolder();
             for k=1:size(T, 1)
                 studyuid = T.StudyInstanceUID{k};
                 seriesuid= T.SeriesInstanceUID{k};
                 studyfolder=  [patfolder studyuid '\']; 
                 rsuid   = T.SOPInstanceUID{k};
                 RSFile{k} = [studyfolder 'RTStruct.' seriesuid '\' rsuid '.dcm'];
                 imguid  = T.ReferencedImageSeriesUID{k};
                 a = DosUtil.rdir([patfolder '**\*.' imguid]); 
                 if ~isempty(a)
                     ImageFolder{k} = [a(1).name filesep]; 
                 else
                     ImageFolder{k} ='';
                 end
             end
             T.RSFile = RSFile; 
             T.ImageFolder = ImageFolder; 
        end
        
        function [imagefolder, modality] = GetDcmImageFolder(self, imageuid)
            imagefolder=[];
            modality=[]; 
            res = DosUtil.rdir([PatientFolder(self) '*\*.' imageuid]); 
            if isempty(res)
                return; 
            end
            
            imagefolder = res(1).name; 
%             res         = regexpi(imagefolder, 'MR|CT|PT', 'match');
%             modality    = res{1}; 
            res = regexpi(imagefolder, ['(MR|CT|PT)(?=.' imageuid ')'], 'match');
            if ~isempty(res)
                 modality    = res{1}; 
            end
        end
        
        function rpinfo = GetMatchScheduledPlanInfo_ethos(self, adpplanuid)
            rpinfo=[]; 
            T = self.GetRawTable('RTPlan');
            k = ismember(T.SOPInstanceUID, adpplanuid);
            rpinfo0 = table2struct(T(k, :)); 
            
            if isempty(regexpi(rpinfo0.RTPlanLabel, '/ADP'))
                return; 
            end

            I1 = cellfun(@(x)(~isempty(regexpi(x, '/SCH'))), T.RTPlanLabel);
            I2 = cellfun(@(x)(strcmp(x, rpinfo0.RTPlanDate)), T.RTPlanDate);
            T1 = T(I1&I2, :);
            
            hr0 = datenum([ '00000100.' rpinfo0.RTPlanTime], 'yyyymmdd.HHMMSS')*24;
            hrs = cellfun(@(x)(datenum([ '00000100.' x], 'yyyymmdd.HHMMSS')),  T1.RTPlanTime)*24;
            dhrs= hr0-hrs; 
            J   = find(dhrs>0);
            if isempty(J)
                return;
            end
            [val, k] = min(dhrs(J));
            m = J(k); 
            %T1 = T1(m, :);
            rpuid = T1.SOPInstanceUID{m};
            rpinfo = table2struct(self.ExtendRTPlanTable({'SelectedRTPlanUID', rpuid}));
        end

        function T1 = TrackADPPlans_ethos(obj)
            planT = obj.GetRawTable('RTPlan');
            %RTPlanLabel=planT.RTPlanLabel; 
            I= cellfun(@(x)(~isempty(regexpi(x, '/ADP', 'ONCE'))), planT.RTPlanLabel);
            adpplanT = planT(I, :);
            adpplanT = sortrows(adpplanT, {'RTPlanDate', 'RTPlanTime'}, 'descend');
            T1 = xls.TableBase.GroupTable(adpplanT, 'RTPlanDate', {'PatientID', 'SOPInstanceUID', 'RTPlanLabel'}, 'RTPlan');
            T1=renamevars(T1, {'SOPInstanceUID', 'RTPlanLabel'}, {'SOPInstanceUID_ADP', 'RTPlanLabel_ADP'});
            RTPlanUID_ADP = T1.('SOPInstanceUID_ADP');
            RTPlanUID_SCH=cellfun(@(x)(''), RTPlanUID_ADP, 'UniformOutput',false);
            RTPlanLabel_SCH=cellfun(@(x)(''), RTPlanUID_ADP, 'UniformOutput',false);
            for k=1:numel(RTPlanUID_ADP)
                try
                adpplanuid=RTPlanUID_ADP{k};
                rpinfo = GetMatchScheduledPlanInfo_ethos(obj, adpplanuid);
                RTPlanUID_SCH{k, :}=rpinfo.SOPInstanceUID;
                RTPlanLabel_SCH{k, :}=rpinfo.RTPlanLabel;
                catch
                end
            end
            T1.('SOPInstanceUID_SCH')=RTPlanUID_SCH;
            T1.('RTPlanLabel_SCH')=RTPlanLabel_SCH;
            T1 = sortrows(T1, {'RTPlanDate'}, 'ascend');
            T1 = movevars(T1, 'PatientID', 'before', 1);
        end

        function [rpinfo, focalobj] = GetMatchScheduledPlanInfo_focal(self, adpplanuid)
            rpinfo=[]; focalobj=[];
            T = self.GetRawTable('RTPlan');
            k = ismember(T.SOPInstanceUID, adpplanuid);
            rpinfo0 = table2struct(T(k, :)); 
            
            patid=rpinfo0.PatientID; 
            planname =rpinfo0.RTPlanName;
            focalroot = self.getOption('focal.rootfolder', 'c:/FocalData/');
            focalartfname = [focalroot '/1~' patid '/plan/' planname '/' patid '.art'];
            if ~exist(focalartfname, 'file')
                return
            end
            focalobj = focal.ARTPlanParser(focalartfname);
            refplanname = focalobj.RefPlanName();
            [flag, m]= ismember(refplanname, T.RTPlanName);
            if ~flag
                return
            end
            rpuid = T.SOPInstanceUID{m};
            rpinfo = table2struct(self.ExtendRTPlanTable({'SelectedRTPlanUID', rpuid}));
        end

        function ExportRTPlanData2DcmRTDB(obj, dbobj, planuids)
            planT = obj.GetRawTable('RTPlan');
            if isempty(planT)
                return
            end

            if exist("planuids", 'var') &&~isempty(planuids)
                I  =ismember()
            end

            ctT   = obj.GetRawTable('CT');
            doseT = obj.GetRawTable('RTDose');
            rsT   = obj.GetRawTable('RTStruct');
            % planT = sortrows(planT, {'RTPlanDate', 'RTPlanTime'}, 'ascend');
            % planT = planT(1, :);
            % planinfo = table2struct(planT);
            
            dbobj.InsertMatlabTable(planT, 'RTPLANDB');
            
            [I, loc] = ismember(ctT.SeriesInstanceUID, planinfo.ReferencedImageSeriesUID);
            dbobj.InsertMatlabTable(ctT(I, :), 'CT');
            
            [I, loc] = ismember(rsT.SOPInstanceUID,  planinfo.ReferenceStructureSetUID);
            rsinfo = table2struct(rsT(I, :));
            dbobj.InsertMatlabTable(rsT(I, :), 'RTSTRUCT');
            
            [I, loc] = ismember(doseT.SOPInstanceUID,  planinfo.ReferencedRTDoseUID);
            dbobj.InsertMatlabTable(doseT(I, :), 'RTDOSE');
            dbobj.UpdatePatientTables(patid);
        end
    end
    
    methods (Static)
         function T = GetRPFiles(T, patfolder, rdtable)
            %patfolder  =  PatientDataFolder(obj);
            T.RPFile={''};
            T.RSFile={''};
            T.PlanDoseFile={''};
            T.BeamDoseFile={''};
            T.ImageFolder={''};
            
            studyuid = T.StudyInstanceUID{1};
            seriesuid= T.SeriesInstanceUID{1};
            studyfolder=  [patfolder studyuid '\'];
            rpuid  = T.SOPInstanceUID{1};
            %res    = DosUtil.rdir([patfolder '**\RTPlan.*\' rpuid '.dcm']);
            T.RPFile= {[studyfolder 'RTPLAN.' seriesuid '\' rpuid '.dcm']};
            rsuid   = T.ReferenceStructureSetUID{1};
            res = DosUtil.rdir([patfolder '**\' rsuid '.dcm']);
            if ~isempty(res)
                T.RSFile= {res(1).name};
            end
            imguid   = T.ReferencedImageSeriesUID{1};
            imgfolder= [studyfolder 'CT.' imguid '\'];

            if exist(imgfolder, 'dir') 
                T.ImageFolder={imgfolder};
            else
                imgfolder=[studyfolder 'MR.' imguid '\'];
                if exist(imgfolder, 'dir') 
                    T.ImageFolder={imgfolder};
                end
            end
            
            if exist('rdtable', 'var')&&~isempty(rdtable)
                I = ismember(rdtable.ReferencedRTPlanUID, rpuid);
                rdtable =rdtable(I, :);
                I = find(ismember(upper(rdtable.DoseSummationType), 'PLAN'));
                if isempty(I)
                    I = find(ismember(upper(rdtable.DoseSummationType), 'FRACTION'));
                end
                if numel(I)==1
                    doseuid = rdtable.SOPInstanceUID{I(1)};
                    res = DosUtil.rdir([patfolder '**\' doseuid '.dcm']);
                    T.PlanDoseFile = {res(1).name};
                elseif numel(I)>1
                    disp('error: multiple plan ReferencedRTDoseUID exist'); 
                end
                I = find(ismember(upper(rdtable.DoseSummationType), 'BEAM'));
                BeamDoseFile='';
                for k=1:numel(I)
                    doseuid = rdtable.SOPInstanceUID{I(k)};
                    res = DosUtil.rdir([patfolder '**\' doseuid '.dcm']);
                    fname = res(1).name;
                    if k==1
                        BeamDoseFile=fname;
                    else
                        BeamDoseFile=[BeamDoseFile, '|' fname]; 
                    end
                end
                T.BeamDoseFile = {BeamDoseFile};
            else
                doseuids=strsplit(T.ReferencedRTDoseUID{1}, '|');
                if numel(doseuids)==1
                    try
                    res = DosUtil.rdir([patfolder '**\' doseuids{1} '.dcm']);
                    T.PlanDoseFile = {res(1).name};
                    catch
                        disp(['error: read PlanDoseFile ' doseuids{1}]); 
                    end
                elseif numel(doseuids)>1
                    disp('error: multiple ReferencedRTDoseUID exist'); 
                end
            end
         end
        
         function HouseKeepIncoming(rootfolder, T, planDB, varargin)
             if ~exist('planDB', 'var')
                 planDB=[];
             end
             
             MRNs   = T.PatientID; 
             uniqueMRNs = unique(MRNs); 
             for k  = 1:numel(uniqueMRNs)
                 id = uniqueMRNs{k};
                 I  = ismember(MRNs, id); 
                 T1 = T(I, :); 
                 pat= dcmtk.RTPatientTables({'DCMSERVER_FOLDER', rootfolder}, {'PatientID', id}); 
                 pat.HouseKeepInstances(T1, planDB, varargin{:});
             end
         end
         
         function [pat, xlsfile] = HouseKeepPatient(patfolder)
                xlsfile = [patfolder 'patient.xlsx'];
                pat     = dcmtk.RTPatientTables({'PatientFolder', patfolder}, {'TableFileName',  xlsfile});
                pat.HouseKeep;
         end
         
         function eval = EvalPlan(patxlsfile, varargin)
             eval = DicomRPViewer(patxlsfile, varargin{:}); 
         end
         
         function EvalPatient(patxlsfile, varargin)
            options   = OptionsMap(varargin{:}); 
            patfolder = [fileparts(patxlsfile) filesep]; 
            pat       = dcmtk.RTPatientTables({'PatientFolder', patfolder}, {'TableFileName',  patxlsfile});
            rpuids    = pat.GetRawTable('RTPLAN').SOPInstanceUID;
 
            savefig   = options.getoptioni('viewer.savefig', 0);
            figfolder = [];
            if savefig
                figfolder = DosUtil.mksubdir(patfolder, 'Figure');
            end
            
            figfile   = []; 
            for k=1:numel(rpuids)
                rpuid = rpuids{k};
                if ~isempty(figfolder)
                    figfile = [figfolder 'RP.' rpuid];
                end
                dcmtk.RTPatientTables.EvalPlan(patxlsfile,{'selectedplanuid', rpuid},...
                    {'viewer.FigureFile', figfile}, varargin{:});
            end
        end
    end
end

