#
#  Copyright (C) 1998-2019, OFFIS e.V.
#  All rights reserved.  See COPY<PERSON><PERSON>HT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 Oldenburg, Germany
#
#
#  Module:  dcmpstat
#
#  Author:  <PERSON> et al.
#
#  Purpose: Sample configuration file for DCMTK "dcmpstat" tools and the
#    DICOMscope application
#

# ============================================================================
# General settings for the viewer applications
[[GENERAL]]
# ============================================================================

# ----------------------------------------------------------------------------
# General application settings in this section.
[APPLICATION]
# ----------------------------------------------------------------------------

# Directory in which log files are stored.
# Default: application root directory, for print: same as spool directory.
LogDirectory = log

# Name of the file where the general log messages are stored.
# Default: no log file, i.e. do not write any log messages.
LogFile = general.log

# Filter for the general log messages:
#  ERROR = only error messages
#  WARN  = also warning messages (includes ERROR)
#  INFO  = also informational messages (includes WARN)
#  DEBUG = also debug messages (includes INFO)
# Default: no general log messages at all.
LogLevel = INFO

# Port on which the GUI application accepts notification messages from
# the network processes. Default: 0 (no notifications sent)
MessagePort = 11000

# Indicates whether client processes are allowed to keep their notification
# message port open during the lifetime of the process. Default: false
KeepMessagePortOpen = false

# ----------------------------------------------------------------------------
# Monitor calibration settings in this section.
[MONITOR]
# ----------------------------------------------------------------------------

# Settings for Clinton Medical monitor with SXGA 1152x864

# monitor description file for software based correction (GSDF etc.)
#Characteristics = monitor.lut

# screen resolution in pixels, width\height
Resolution = 1152\864

# size of the visible screen area in mm, width\height
Screensize = 400\300

# preview size in pixels, width\height
PreviewSize = 256\256

# Settings for Siemens Monitor with Dome Board
#
# screen resolution in pixels, width\height
#Resolution = 2048\2560
#
# size of the visible screen area in mm, width\height
#ScreenSize = 280\367
#
# preview size in pixels, width\height
#PreviewSize = 512\512

# ----------------------------------------------------------------------------
# General print settings in this section.
[PRINT]
# ----------------------------------------------------------------------------

# Path to the DICOM print spooler (Print Management SCU) application to be used
Spooler = dcmprscu

# Path to the DICOM print server (Print Management SCP) application to be used
Server = dcmprscp

# Directory in which spooled print jobs reside
Directory = spool

# Log complete DICOM protocol (--dump --verbose) in print spooler/server.
# Default: false
DetailedLog = true

# Log complete ACSE and DIMSE protocol in binary form (as DICOM file).
# This setting is independent from the DetailedLog setting and currently
# only implemented by the print server. Default: false
BinaryLog = false

# Time (in seconds) the print spooler should wait before polling (again)
# the spool directory. Default: use spooler default.
Sleep = 5

# Minimum resolution for a print bitmap (width\height in pixel). If a
# bitmap to be printed is smaller than this, it is scaled up by an
# appropriate integer factor before burning in graphical annotations.  This
# allows to have acceptable annotation visibility on low resolution
# bitmaps.
# Default: No lower limit for the print bitmap resolution.
MinPrintResolution = 1024\1024

# Maximum resolution for a print bitmap (width\height in pixel). If a
# bitmap to be printed is larger than this, it is scaled down by an
# appropriate integer factor before sent to the printer.  This allows to
# reduce the amount of pixel data to be transfered.
# Default: No upper limit for the print bitmap resolution.
MaxPrintResolution = 8192\8192

# Specifies the default value for the Illumination to be transmitted to the
# printer when using the Presentation LUT SOP Class. Default: 2000
DefaultIllumination = 2000

# Specifies the default value for the Reflected Ambient Light to be transmitted
# to the printer when using the Presentation LUT SOP Class. Default: 10
DefaultReflection = 10

# Delete print job files from spool directory after processing.
# If false, spool jobs are renamed instead. Default is false.
DeletePrintJobs = true

# Always delete terminate job files from spool directory (these special print job files
# are created to inform the spool processes that the application has been terminated).
# If false, terminate jobs are handled like normal print jobs (see DeletePrintJobs).
# Default is false.
AlwaysDeleteTerminateJobs = true

# ----------------------------------------------------------------------------
# Database settings in this section.
[DATABASE]
# ----------------------------------------------------------------------------

# directory in which DICOM images and index.dat reside
Directory = database

# Path to the dump tool used to display the contents of DICOM files located
# in the database
Dump = c:\program files\tcl\bin\wish83 dcmpsdmp.tcl

# Path to the check tool used to evaluate the contents of DICOM files located
# in the database
Check = c:\program files\tcl\bin\wish83 dcmpschk.tcl

# ----------------------------------------------------------------------------
# General Presentation LUT settings in this section.
[LUT]
# ----------------------------------------------------------------------------

# directory in which LUT files reside
Directory = lut

# ----------------------------------------------------------------------------
# General Structured Reporting (SR) settings in this section.
[REPORT]
# ----------------------------------------------------------------------------

# directory in which SR "template" files reside
Directory = reports

# ----------------------------------------------------------------------------
# This section contains the settings for network communication.
[NETWORK]
# ----------------------------------------------------------------------------

# Path to the DICOM sender (Store SCU) application to be used
Sender = dcmpssnd

# Path to the DICOM receiver (Store SCP) application to be used
Receiver = dcmpsrcv

# Application entity title sender processes use to identify themselves.
# Also used as default application entity title for the [[communication]]
# section.
aetitle = DCMPSTATE

# ----------------------------------------------------------------------------
# This section contains the settings for secure TLS (Transport Layer
# Security) communication.
[TLS]
# ----------------------------------------------------------------------------

# directory in which TLS related files (certificates, keys, random data,
# Diffie-Hellman parameters etc.) are located. The content of this directory
# should be kept confidential because it may help an attacker to break
# the security of the TLS transmission.
#
TLSDirectory = tls

# directory in which certificates of the trusted Certification Authorities
# are located. During negotiation of a secure TLS connection, the remote
# site's certificate may be verified, depending on the settings for the
# sender or receiver process in [[communication]].  Verification checks
# if the certificate has been issued by one of the CAs located in this
# directory. This means that the content of this directory defines a simple
# means of access control for TLS communication.
#
# File names of certificates in this directory must have
# names derived from a hash key of the certificate DN, see -hash
# option of openssl x509 command.
#
CACertificateDirectory = cacerts

# directory in which certificates and encrypted private keys of the known
# users are located. User certificates and keys are used for the
# verification of DICOM Structured Reports and for the creation of digital
# signatures.
#
UserKeyDirectory = usrcerts

# DICOMscope allows to have certificates, keys and Diffie-Hellman parameters
# either in PEM ("privacy enhanced mail") format or in DER ("distinguished
# encoding rules") format.  However, all files within one DICOMscope
# installation must use the same format, which is defined by this setting.
# True => PEM format, false => DER format. Default is PEM.
#
UsePEMFormat = true

# ----------------------------------------------------------------------------
# This section contains the settings for the query/retrieve server.
[QUERY_RETRIEVE]
# ----------------------------------------------------------------------------

# Specifies whether the configuration file for the query/retrieve server is
# created automatically from the data contained in this file each time the
# server is started. Default is true.
AutoCreateConfigFile = true

# Path to the DICOM query/retrieve server (Q/R SCP) to be used.
# The configuration filename is created from this entry by adding the file
# extension ".cfg".
Server = dcmqrscp

# IP Port number on which the server listens for new connections.
# On Unix platforms, the receiver must be started with setuid root
# if port numbers < 1024 (i.e. the standard DICOM port number 104)
# should be used.
Port = 10003

# Maximum PDU (protocol data unit) size to use when negotiating
# incoming connections. Must be between 4096 and 131072.
# Default is 16384.
MaxPDU = 32768

# Application entity title dcmqrscp will use to identify itself.
AETitle = DCMQRSCP

# Maximum number of associations the server can handle at the same time.
# This is only applicable on Unix platforms, since Windows does not support
# the fork() command which is used for this purpose.
MaxAssociations = 16

# Timeout (in seconds) for outgoing connection requests, must be larger
# than zero. Optional setting, default is Unlimited (wait for TCP/IP timeout).
# Timeout = 5

# ----------------------------------------------------------------------------
# This section contains the settings for the graphical user interface (GUI).
[GUI]
# ----------------------------------------------------------------------------

# If this setting is true, we assume a system with very high resolution
# (2 x 2.5K) and a graphics board that performs calibration according to the
# DICOM GSDF in hardware. Therefore, we display much larger icons and
# disable rendering of the GSDF in software. This setting requires that
# a static 256 grayscale color palette is used by the operating system
# to access the graphics board.
HighResolutionGraphics = false

#Starts the update thread for the study browser.
#Defaults is true
AutoUpdateStudyBrowser = true

#Sets the background color of the study manager as rgb color
#Default: If HighResolutionGraphics, the default is 0\0\0 otherwise 255\255\255
#BackgroundColor = 255\255\255

# Sets the size of the fonts used in all GUI components
# Default: If HighResolutionGraphics, the default is 30 otherwise 12
#FontSize = 30

# Sets the name of the fonts used in all GUI components
# Default value: SansSerif
#FontName = SansSerif

# Sets the size of the fonts used for textual annotations
# Default: If HighResolutionGraphics, the default is 48 otherwise 16
#FontSizeText = 16

# Sets the name of the fonts used for textual annotations
# Default value: SansSerif
#FontNameText = SansSerif

# Sets the size of the fonts used for the process log
# Default: If HighResolutionGraphics, the default is 30 otherwise 12
#FontSizeLog = 30

# Sets the name of the fonts used for the process log
# Default value: Monospaced
#FontNameLog = Monospaced

# Sets the placement of the main tab in the GUI.
# Valid values are: North, South, East, West. Default is North.
#TabPlacement = North

# Sets the placement of the image processing panel in the viewer.
# Valid values are: North, South, East, West, None.
# Default: If HighResolutionGraphics, the default is East otherwise South
#FunctionPanelPlacement = East

# Sets the placement of the paint function panel in the viewer.
# Valid values are: North, South, East, West, None, Toolbar, Integrated
# Default value: Integrated
#PaintPanelPlacement = Integrated

# Checks if the presentation state information panel should be visible
# Valid values are: true, false. Default value: false
PSPanelOn = true

# Displays a warning message if an unsigned instance (report, presentation
# state, image) referenced from the current report is opened
# Valid values are: true, false. Default value: true
WarnUnsignedObjectsInSR = true

# ============================================================================
# Storage and Print applications are configured in the following section.
[[COMMUNICATION]]
# ============================================================================

# ----------------------------------------------------------------------------
# The following text documents all settings defined for a local or remote
# storage or print application. Each application or communication target
# requires one section with a unique section title.
#
# [COMMUNICATION_PEER_1]
# ----------------------------------------------------------------------------
#
# The following entry defines which DICOM services are offered by the
# application described in this section. Four types are supported:
#
# STORAGE - a remote DICOM Storage Service Class SCP. Entries of this type are
#   shown as "send targets" in the DICOMscope browser GUI.  An entry of this
#   type is also required for each system that wants to download objects
#   from the DICOMscope database using Query/Retrieve.  The settings from
#   this section are used to resolve an application entity title into the
#   target hostname and IP port number.
#
# RECEIVER - a local DICOM Storage Service Class SCP which accepts incoming
#   images and stores them in the local database.
#
# PRINTER - a remote DICOM Print Management Service Class SCP.
#   For each entry of this type, a separate Print SCU ("spooler process")
#   is started together with DICOMscope.
#   Entries of this type are shown as printers in the DICOMscope
#   print preview GUI.
#
# LOCALPRINTER - a DICOM Print Management SCP that is running as part of
#   the DICOMscope software on the local system.  For each entry of this
#   type both a DICOM Print SCP and a Print SCU (spooler) are started
#   together with DICOMscope. This allows DICOMscope to print "to itself".
#
# Default value for this setting is: STORAGE.
#
# type = LOCALPRINTER
#
# ----------------------------------------------------------------------------
#
# Hostname: The IP number or domain name of the communication partner in
# conventional TCP/IP notation. For entries of type LOCALPRINTER this setting
# must have the value "localhost". This setting is mandatory, no default.
#
# hostname = localhost
#
# ----------------------------------------------------------------------------
#
# IP port number under which the application or remote peer receives DICOM
# associations.
#
# port = 104
#
# ----------------------------------------------------------------------------
#
# Human readable description of the communication partner. Used in the GUI to
# present the communication partner to the application user in selection boxes.
#
# description = Local DICOM Print SCP
#
# ----------------------------------------------------------------------------
#
# For outgoing connections (STORAGE, PRINTER) this setting contains the
# called application entity title of the communication partner.
# For incoming connections (RECEIVER, LOCALPRINTER) this setting contains
# the responding application entity used during association negotiation.
#
# aetitle = DICOM_PRINTER
#
# ----------------------------------------------------------------------------
#
# Only negotiate the default Implicit VR Little Endian transfer syntax for
# all abstract syntaxes. This setting is useful if we're communicating with
# very old DICOM software which claims to support Explicit VR communication
# but fails to do so...  Optional setting, default is: false.
#
# ImplicitOnly  = false
#
# ----------------------------------------------------------------------------
#
# Do not create data types (VRs) that were defined in post-1993 editions
# of the DICOM standard when converting implicit VR files to explicit VR
# network transmission. Maybe necessary for old DICOM Store SCPs.
# Optional setting, default is: false.
#
# DisableNewVRs = false
#
# ----------------------------------------------------------------------------
#
# Maximum PDU (protocol data unit) size to negotiate for incoming PDUs.
# Value must be between 4096 and 131072. Default is 16384.
#
# MaxPDU = 32768
#
# ----------------------------------------------------------------------------
#
# Timeout (in seconds) for outgoing connection requests, must be larger
# than zero. Optional setting, default is Unlimited (wait for TCP/IP timeout).
#
# Timeout = 5
#
# ----------------------------------------------------------------------------
#
# Use the Transport Layer Security (TLS) protocol for image transmission
# in accordance with the DICOM Security Enhancements One (Supplement 31).
# Optional setting, default is: false.
#
# UseTLS = false
#
# ----------------------------------------------------------------------------
#
# Filename of the X.509 certificate to be used for TLS authentication.
# The file should be located in the directory specified by
# GENERAL/TLS/TLSDIRECTORY.
#
# SCP usage: The TLS protocol requires that a TLS server
# must have a certificate. Therefore, this parameter is mandatory.
# If absent, the certificate file is loaded from "sitecert.pem" in the
# TLS directory.
#
# SCU usage: For a TLS client the certificate is optional.
# Therefore, this setting is optional for Store SCUs.  If absent, no
# TLS authentication is performed for the client. Warning: Anonymous TLS
# may be susceptible to man-in-the-middle attacks.
#
# Certificate = cert.pem
#
# ----------------------------------------------------------------------------
#
# Filename of the private key "matching" the certificate,
# to be used for TLS authentication. The file should be located in the
# directory specified by GENERAL/TLS/TLSDIRECTORY.
#
# SCP usage: The TLS protocol requires that a TLS server
# must have a certificate. Therefore, this parameter is mandatory.
# If absent, the certificate file is loaded from "sitekey.pem" in the
# TLS directory.
#
# SCU usage: For a TLS client the certificate is optional.
# Therefore, this setting is optional for Store SCUs.  If absent, no
# TLS authentication is performed for the client. Warning: Anonymous TLS
# may be susceptible to man-in-the-middle attacks.
#
# PrivateKey = pkey.pem
#
# ----------------------------------------------------------------------------
#
# Private keys may be stored in encrypted form (e.g. 3DES),
# protected with a password. Since the DICOMscope Store SCU/SCP processes
# do not have any interaction with the user, they cannot request a password
# from the command line.  Two options are available to the user:
# - the private key can be unencrypted. In this case this setting should
#   be absent from the config file. Users should carefully adjust file system
#   access rights to the private key file to make sure that the private key
#   is not compromised (e.g. copied by an unauthorized used).
# - the private key can be encrypted, with the password in clear text in this
#   configuration file.  In this case, the DICOMscope configuration file must
#   be protected from read access by unauthorized users, otherwise the private
#   key may be compromised as well.
# A real secure approach would be to keep the private key in a safe place,
# e.g. in a microprocessor card, but this is not yet supported.
#
# PrivateKeyPassword = Should_be_kept_secret
#
# ----------------------------------------------------------------------------
#
# Select the following security profile when using TLS connections.
# Known terms are:
#   BCP195:     BCP 195 TLS Profile (default)
#   BCP195-ND:  Non-downgrading BCP 195 TLS Profile
#   BCP195-EX:  Extended BCP 195 TLS Profile
#   AES:        AES TLS Secure Transport Connection Profile (retired)
#   BASIC:      Basic TLS Secure Transport Connection Profile (retired)
#   NULL:       Authenticated unencrypted communication (retired)
#
# TLSProfile = BCP195
#
# Note 1: The direct configuration of a list of ciphersuites using the
# "CipherSuites" configuration setting is not supported anymore;
# this setting will be ignored.
#
# Note 2: The BCP195 profile, which is the default, provides backwards
# compatibility with older implementations only supporting the AES or
# BASIC profile.
#
# Note 3: The BASIC profile requires 3DES to be enabled in the OpenSSL
# library that DCMTK is compiled with. Starting with OpenSSL 1.1.0, this
# has to be manually enabled at compile time.
#
# ----------------------------------------------------------------------------
#
# This setting defines the policy for handling the remote peer's TLS
# certificate. Known terms are:
#   REQUIRE: verify peer certificate, refuse transmission if absent
#   VERIFY:  verify peer certificate if present
#   IGNORE:  don't verify peer certificate
# Optional setting, default is: REQUIRE.
#
# PeerAuthentication = REQUIRE
#
# ----------------------------------------------------------------------------
#
# File containing a set of random-generated Diffie-Hellman parameters
# as required for DH[E]/DSS ciphersuites.
# File should be located in the directory specified by GENERAL/TLS/TLSDIRECTORY
# in the format specified by GENERAL/TLS/USEPEMFORMAT.
# Optional setting, default is: used built-in set of DH parameters.
#
# DiffieHellmanParameters = dhparams.pem
#
# ----------------------------------------------------------------------------
#
# File containing random data which is used to initialize the pseudo-random
# generator. After transmission, a modified file is written back.
# The random seed file should be different for each send/receive
# target - see DCMTK documentation on random data for details.
# The file should be located in the directory specified by
# GENERAL/TLS/TLSDIRECTORY.
# Optional setting, default is: no random data (which may cause TLS connection
# setup to fail).
#
# RandomSeed = random.dat
#
#
# ============================================================================
# The next two settings described below are only used with entries of type
# STORAGE and RECEIVER and have no meaning for PRINTER or LOCALPRINTER.
# ============================================================================
#
# Receive data in "bit preserving mode". In this mode, data is stored
# to file directly as read from network. Transfer syntax in file is
# identical to transfer syntax used for network transmission.
# If this mode is switched off (default), images are converted to explicit VR
# Little Endian format before storing in file, which makes it easier
# to burn images on CD-R in "General Purpose Image Exchange Profile" format.
# Optional entry, default is: false
#
# BitPreservingMode = false
#
# ----------------------------------------------------------------------------
#
# Silently ignore space padded SOP Class and SOP Instance UIDs in incoming
# images. Optional setting, default is: false.
#
# CorrectUIDPadding = false
#
#
# ============================================================================
# The remaining settings described below are only used with entries of type
# PRINTER and LOCALPRINTER and have no meaning for STORAGE or RECEIVER.
# ============================================================================
#
# Print SCU usage: assume that the Print SCP supports transmission of image
# pixel data with 12 bits/pixel in the Basic Grayscale Image Box SOP Class.
# Print SCP usage: accept image pixel data with 12 bits/pixel.
# Optional setting, default is: true. Not used for Storage type entries.
#
# Supports12Bit = true
#
# ----------------------------------------------------------------------------
#
# Print SCU usage: assume that the Print SCP supports the Presentation LUT
# SOP Class (Supplement 22) and attempt to negotiate it during association
# negotiation.
# Print SCP usage: activate support for the Presentation LUT SOP Class.
# Optional setting, default is: false. Not used for Storage type entries.
#
# SupportsPresentationLUT = true
#
# ----------------------------------------------------------------------------
#
# The 1999 edition of the DICOM standard contains an inconsistency in the
# definition of the Presentation LUT SOP class.  The attributes
# "Referenced Presentation LUT", "Illumination" and "Reflected Ambient Light"
# can either be part of the Basic Film Session or be part of the Basic Film
# Box. DICOM Correction Proposal CP 173 defines that these attributes
# have to be specified on Basic Film Box level.  However, not all existing
# Print SCPs supporting the Presentation LUT SOP Class are implemented
# in accordance with CP 173. Both Print SCU and Print SCP can, therefore,
# be configured to use either Film Session or Film Box (but never both).
#
# Print SCU usage: If flag is true, transmit attributes in the Basic Film
# session instead of the Basic Film Box.
# Print SCP usage: If flag is true, attributes are accepted and returned
# in Basic Film Session instead of Basic Film Box.
# Optional setting, default is: false (which means that behaviour will be
# consistent with CP 173.) Not used for Storage type entries.
#
# PresentationLUTinFilmSession = false
#
# ----------------------------------------------------------------------------
#
# Some Print SCPs which support Presentation LUTs require that the number
# of entries in a Presentation LUT matches the bit depth of the image pixel
# data (4096 entries for 12 bit pixel data, 256 entries for 8 bit pixel
# data).
#
# Print SCU usage: If flag is true, assume that Print SCP requires match
# between Presentation LUT and image data. If for a given print job this
# match cannot be guaranteed, perform rendering of the Presentation LUT in
# the Print SCU and use an IDENTITY Presentation LUT shape in the Print SCP.
# Print SCP usage: If flag is true, enforce a matching rule as described
# above. All Presentation LUT N-CREATE or Basic Grayscale Image Box N-SET
# operations that would violate the rule will be refused.
# Optional setting, default is: true. Not used for Storage type entries.
#
# PresentationLUTMatchRequired = true
#
# ----------------------------------------------------------------------------
#
# Print SCU usage: if the printer supports 12-bit image transmission and the
# film is to be printed with a Presentation LUT that could be rendered either
# in the print client or in the printer without loss of precision,
# prefer rendering in the printer.
# Print SCP usage: flag is ignored.
# Optional setting, default is: false. Not used for Storage type entries.
#
# PresentationLUTPreferSCPRendering = false
#
# ----------------------------------------------------------------------------
#
# Some Print SCPs do not support the optional "Requested Image Size"
# attribute in Basic Grayscale Image Box N-SET operations.
#
# Print SCU usage: If flag is false, assume that the printer does not support
# requested image size, never send this attribute.
# Print SCP usage: If flag is true, support requested image size. Otherwise
# refuse N-SET operations containing a requested image size attribute.
# Optional setting, default is: false. Not used for Storage type entries.
#
# SupportsImageSize = false
#
# ----------------------------------------------------------------------------
#
# Some Print SCPs do not support the optional "Requested Decimate/Crop Behaviour"
# attribute defined in Supplement 37 in Basic Grayscale Image Box N-SET
# operations.
#
# Print SCU usage: If flag is false, assume that the printer does not support
# requested decimate/crop behaviour, never send this attribute.
# Print SCP usage: If flag is true, support requested decimate/crop behaviour.
# Otherwise refuse N-SET operations containing this attribute.
# Optional setting, default is: false. Not used for Storage type entries.
#
# SupportsDecimateCrop = false
#
# ----------------------------------------------------------------------------
#
# Some Print SCPs do not support the optional "Trim" attribute
# in Basic Film Box N-CREATE or N-SET operations.
#
# Print SCU usage: If flag is false, assume that the printer does not support
# trim, never send this attribute.
# Print SCP usage: If flag is true, support trim.
# Otherwise refuse N-CREATE or N-SET operations containing this attribute.
# Optional setting, default is: false. Not used for Storage type entries.
#
# SupportsTrim = true
#
# ----------------------------------------------------------------------------
#
# Print SCU usage: this entry contains a list of all STANDARD\C,R column, row
# combinations supported as Image Display Format by the Print SCP
# for portrait orientation. Landscape image display formats are derived
# automatically by exchanging column and row for each format.
# Print SCP usage: this entry specifies all STANDARD\C,R image display formats
# accepted by the Print SCP for portrait orientation. Landscape image display
# formats are also derived automatically.
# This is a mandatory entry, no default.
#
# DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
#
# ----------------------------------------------------------------------------
#
# Film Size ID identifiers supported by the printer for the Basic Film Box.
# Multiple values can be specified, must be separated by '\' characters.
#
# Print SCU usage: Optional setting, default: don't specify film size ID,
# use printer default.
# Print SCP usage: The first specified value is used as the default. This
# is a mandatory setting for entries of type LOCALPRINTER.
#
# FilmSizeID = 8INX10IN\10INX12IN\10INX14IN\11INX14IN\14INX14IN\14INX17IN\24CMX24CM\24CMX30CM
#
# ----------------------------------------------------------------------------
#
# Medium Type identifiers supported by the printer for the Basic Film Session.
# Multiple values can be specified, must be separated by '\' characters.
#
# Print SCU usage: Optional setting, default: don't specify medium type,
# use printer default.
# Print SCP usage: The first specified value is used as the default. This
# is a mandatory entry.
#
# MediumType = PAPER\CLEAR FILM\BLUE FILM
#
# ----------------------------------------------------------------------------
#
# Requested Resolution ID identifiers supported by the printer
# for the Basic Film Box (optional attribute defined in Supplement 37).
# Multiple values can be specified, must be separated by '\' characters.
#
# Print SCU usage: Optional setting, default: don't specify resolution ID,
# use printer default.
# Print SCP usage: The first specified value is used as the default. This
# is an optional entry. If omitted, the Print SCP does not support the
# attribute and rejects N-CREATE or N-SET requests containing the attribute.
#
# ResolutionID = STANDARD\HIGH
#
# ----------------------------------------------------------------------------
#
# Magnification Type identifiers supported by the printer for the Basic
# Film Box or Basic Grayscale Image Box. Multiple values can be specified,
# must be separated by '\' characters.
#
# Print SCU usage: Optional setting, default: don't specify magnification type,
# use printer default.
# Print SCP usage: The first specified value is used as the default. This
# is a mandatory entry.
#
# MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
#
# ----------------------------------------------------------------------------
#
# Smoothing Type identifiers supported by the printer for the Basic Film
# Box or Image Box. Multiple values can be specified, must be separated by
# '\' characters.
#
# Print SCU usage: Optional setting, default: don't specify smoothing type,
# use printer default.
# Print SCP usage: The first specified value is used as the default. This
# is an optional entry. If omitted, the Print SCP does not support the
# attribute and rejects N-CREATE or N-SET requests containing the attribute.
#
# SmoothingType = NONE
#
# ----------------------------------------------------------------------------
#
# Border Density identifiers supported by the printer for the Basic Film
# Box. Multiple values can be specified, must be separated by '\'
# characters.
#
# Print SCU usage: Optional setting, default: don't specify border density,
# use printer default.
# Print SCP usage: If any of the identifiers is numeric, then all numbers
# are accepted. The first specified value is used as the default. This is
# an optional entry. If omitted, the Print SCP does not support the
# attribute and rejects N-CREATE or N-SET requests containing the
# attribtute.
#
# BorderDensity = BLACK\WHITE\150
#
# ----------------------------------------------------------------------------
#
# Empty Image Density identifiers supported by the printer for the Basic
# Film Box. Multiple values can be specified, must be separated by '\'
# characters.
#
# Print SCU usage: Optional setting, default: don't specify empty image
# density, use printer default.
# Print SCP usage: If any of the identifiers is numeric, then all numbers
# are accepted. The first specified value is used as the default. This is
# an optional entry. If omitted, the Print SCP does not support the
# attribute and rejects N-CREATE or N-SET requests containing the
# attribtute.
#
# EmptyImageDensity = BLACK\WHITE\150
#
# ----------------------------------------------------------------------------
#
# Print SCU usage: Max Density values supported by the printer for the
# Basic Film Box. Multiple values can be specified, must be separated by
# '\' characters. Optional setting, default: don't specify max density
# density, use printer default.
# Print SCP usage: only the first value is read and defines the default max
# density that is used when the Print SCU does not specify max density.
# This is a mandatory entry.
#
# MaxDensity = 320\310\300\290\280\270
#
# ----------------------------------------------------------------------------
#
# Print SCU usage: Min Density values supported by the printer for the
# Basic Film Box. Multiple values can be specified, must be separated by
# '\' characters. Optional setting, default: don't specify min density
# density, use printer default.
# Print SCP usage: only the first value is read and defines the default min
# density that is used when the Print SCU does not specify min density.
# This is an optional entry. If omitted, the Print SCP does not support the
# attribute and rejects N-CREATE or N-SET requests containing the
# attribtute.
#
# MinDensity = 20\25\30\35\40\45\50
#
# ----------------------------------------------------------------------------
#
# The DICOMscope application provides limited support for the Basic
# Annotation Box SOP Class. When printing on printers supporting
# Annotation, a single annotation can be specified for each print job. This
# setting defines the annotation display format ID and annotation position
# that are used when creating the annotation.
#
# Print SCU usage: Two values must be specified: First the Annotation
# Position, then the Annotation Display Format ID, separated by '\'. This
# is an optional setting, default is not to use Basic Annotation Box.
# Print SCP usage: The Print SCP component does not support the Basic
# Annotation Box SOP Class. This setting should be omitted for all entries
# of type LOCALPRINTER.
#
# Annotation = 1\ANNOTATION
#
# ----------------------------------------------------------------------------
#
# Some printers use the Film Session Label as a replacement for annotations.
#
# Print SCU usage: If flag is true, any annotation defined in the user
# interface is replicated in the Film Session Label attribute of the Basic
# Film Session. This is an optional setting, default is: false.
# Print SCP usage: Ignored. This setting should be omitted for all entries
# of type LOCALPRINTER.
#
# SessionLabelAnnotation = false
#
# ----------------------------------------------------------------------------
#
# Configuration Information that can be sent to the printer for the Basic
# Film Box or Image Box. Only a single value per entry can be specified
# because values may contain backslash characters. (VR=ST). Keywords are
# "Configuration_1", "Configuration_2", etc., without leading zeroes.
#
# Print SCU usage: These are optional settings, default is not to use
# configuration information.
# Print SCP usage: These are optional settings. If omitted, the Print
# SCP does not support the attribute and rejects N-CREATE or N-SET requests
# containing the attribute. The Print SCP default for Configuration
# Information is always an empty string.
#
# Configuration_1 = Configuration Value 1
# Configuration_2 = Configuration Value 2
#
# ----------------------------------------------------------------------------
#
# Film Destination identifiers supported by the Print SCP for the Basic
# Film Session. Multiple values can be specified, must be separated by '\'
# characters.
#
# Print SCU usage: ignored.
# Print SCP usage: The first specified value is used as the default. This
# is a mandatory entry.
#
# FilmDestination = MAGAZINE\PROCESSOR\BIN_1\BIN_2
#
# ----------------------------------------------------------------------------
#
# The Affected SOP Class UID attribute is optional in DIMSE N-CREATE-RSP
# messages but some clients rely on its presence. This setting can be used
# to test client behaviour.
#
# Print SCU usage: ignored.
# Print SCP usage: Defines whether the Print SCP should include Affected SOP
# Class UID in DIMSE N-CREATE-RSP messages. This is an optional setting,
# default is false.
#
# OmitSOPClassUIDFromCreateResponse = false
#
# ----------------------------------------------------------------------------


# ----------------------------------------------------------------------------
#
# We define three send targets:
# One is a standard Store SCP which accepts conventional
# DICOM associations on port 10004, the others accept TLS secured DICOM
# associations on port 10007, with different ciphersuites.
#
# ----------------------------------------------------------------------------

# ----------------------------------------------------------------------------
[STORE_1]
# ----------------------------------------------------------------------------
Type = STORAGE
Aetitle = STORESCP
Description = Hostname, unsecure transmission
Hostname = Hostname
MaxPDU = 32768
Port = 10004
ImplicitOnly  = false
DisableNewVRs = false

# ----------------------------------------------------------------------------
[STORE_2]
# ----------------------------------------------------------------------------
Type = STORAGE
Aetitle = STORESCP
Description = Hostname, TLS transmission, no encryption
Hostname = Hostname
MaxPDU = 32768
Port = 10007
ImplicitOnly  = false
DisableNewVRs = false
UseTLS = true
TLSProfile = NULL
RandomSeed = store2.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem

# ----------------------------------------------------------------------------
[STORE_3]
# ----------------------------------------------------------------------------
Type = STORAGE
Aetitle = STORESCP
Description = Hostname, TLS transmission, with encryption
Hostname = Hostname
MaxPDU = 32768
Port = 10007
ImplicitOnly  = false
DisableNewVRs = false
UseTLS = true
TLSProfile = BCP195
RandomSeed = store3.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem

# ----------------------------------------------------------------------------
#
# We support two types of receivers:
# One is a standard Store SCP which accepts conventional
# DICOM associations on port 10004, the other one accepts TLS secured DICOM
# associations on port 10007.
#
# ----------------------------------------------------------------------------

# ----------------------------------------------------------------------------
[RECEIVE_1]
# ----------------------------------------------------------------------------
Type = RECEIVER
Aetitle = STORESCP1
Description = Standard receiver at port 10004
Hostname = localhost
MaxPDU = 32768
Port = 10004
ImplicitOnly  = false
DisableNewVRs = false
BitPreservingMode = false

# ----------------------------------------------------------------------------
[RECEIVE_2]
# We use the BCP 195 TLS profile. We require that the client authenticates
# with a certificate issued by a Certification Authority that we trust.
# ----------------------------------------------------------------------------
Type = RECEIVER
Aetitle = STORESCP2
Description = Secure TLS receiver at port 10007
Hostname = localhost
MaxPDU = 32768
Port = 10007
ImplicitOnly  = false
DisableNewVRs = false
BitPreservingMode = false
UseTLS = true
TLSProfile = BCP195
RandomSeed = receiver.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem

# ----------------------------------------------------------------------------
# Print SCP that supports most options of the DICOM Print protocol and
# the IHE technical framework requirements for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - layouts: 1x1, 1x2, 2x2, 2x3, 3x3, 3x4, 3x5, 4x4, 4x5
#   - supports typical defined terms for empty image density, film
#     destination, film size ID, magnification type, medium type, resolution
#     ID, requested decimate/crop behaviour, image size and trim.
# ----------------------------------------------------------------------------

[IHEFULL]
Aetitle = IHEFULL
Description = IHE Full Print SCP
Hostname = localhost
Port = 10005
Type = LOCALPRINTER

BorderDensity = 150\20\BLACK\WHITE
DisableNewVRs = false
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
EmptyImageDensity = 20\BLACK\WHITE
FilmDestination = MAGAZINE\PROCESSOR\BIN_1\BIN_2
FilmSizeID = 8INX10IN\10INX12IN\10INX14IN\11INX14IN\14INX14IN\14INX17IN\24CMX24CM\24CMX30CM
ImplicitOnly  = false
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 32768
MediumType = PAPER\CLEAR FILM\BLUE FILM
MinDensity = 20
OmitSOPClassUIDFromCreateResponse = false
PresentationLUTMatchRequired = false
PresentationLUTinFilmSession = false
ResolutionID = STANDARD\HIGH
SmoothingType = NONE
Supports12Bit = true
SupportsDecimateCrop = true
SupportsImageSize = true
SupportsPresentationLUT = true
SupportsTrim = true

# ----------------------------------------------------------------------------
# Print SCP that only supports the absolute minimum that is required by
# the IHE technical framework for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - supports STANDARD\1,1 layout only
# ----------------------------------------------------------------------------

[IHERESTRICTED]
Aetitle = IHERESTRICTED
Description = IHE Restricted Print SCP
Hostname = localhost
Port = 10006
Type = LOCALPRINTER

DisableNewVRs = true
DisplayFormat=1,1
FilmDestination = STOREDPRINT
FilmSizeID = 8INX10IN\11INX14IN\14INX17IN
ImplicitOnly = true
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 16384
MediumType = STOREDPRINT
OmitSOPClassUIDFromCreateResponse = true
PresentationLUTMatchRequired = true
PresentationLUTinFilmSession = false
Supports12Bit = true
SupportsPresentationLUT = true

# ----------------------------------------------------------------------------
# Print SCP that supports most options of the DICOM Print protocol and
# the IHE technical framework requirements for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - layouts: 1x1, 1x2, 2x2, 2x3, 3x3, 3x4, 3x5, 4x4, 4x5
#   - supports typical defined terms for empty image density, film
#     destination, film size ID, magnification type, medium type, resolution
#     ID, requested decimate/crop behaviour, image size and trim.
# This Print SCP communicates over TLS and requires peer authentication.
# ----------------------------------------------------------------------------

[IHEFULL_TLS]
Aetitle = IHEFULL_TLS
Description = IHE Full Print SCP
Hostname = localhost
# in the IHE Year 2 MESA release, this printer listened on port 10005
Port = 10012
Type = LOCALPRINTER

BorderDensity = 150\20\BLACK\WHITE
DisableNewVRs = false
DisplayFormat=1,1\1,2\2,2\2,3\3,3\3,4\3,5\4,4\4,5
EmptyImageDensity = 20\BLACK\WHITE
FilmDestination = MAGAZINE\PROCESSOR\BIN_1\BIN_2
FilmSizeID = 8INX10IN\10INX12IN\10INX14IN\11INX14IN\14INX14IN\14INX17IN\24CMX24CM\24CMX30CM
ImplicitOnly  = false
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 32768
MediumType = PAPER\CLEAR FILM\BLUE FILM
MinDensity = 20
OmitSOPClassUIDFromCreateResponse = false
PresentationLUTMatchRequired = false
PresentationLUTinFilmSession = false
ResolutionID = STANDARD\HIGH
SmoothingType = NONE
Supports12Bit = true
SupportsDecimateCrop = true
SupportsImageSize = true
SupportsPresentationLUT = true
SupportsTrim = true

# TLS settings
UseTLS = true
TLSProfile = BCP195
RandomSeed = iheprt1.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem

# ----------------------------------------------------------------------------
# Print SCP that only supports the absolute minimum that is required by
# the IHE technical framework for Print Server actors:
#   - supports Presentation LUT and 12-bit image transmission
#   - supports STANDARD\1,1 layout only
# This Print SCP communicates over TLS and requires peer authentication.
# ----------------------------------------------------------------------------

[IHERESTRICT_TLS]
Aetitle = IHERESTRICT_TLS
Description = IHE Restricted Print SCP
Hostname = localhost
Port = 10013
Type = LOCALPRINTER

DisableNewVRs = true
DisplayFormat=1,1
FilmDestination = STOREDPRINT
FilmSizeID = 8INX10IN\11INX14IN\14INX17IN
ImplicitOnly = true
MagnificationType = REPLICATE\BILINEAR\CUBIC\NONE
MaxDensity = 320
MaxPDU = 16384
MediumType = STOREDPRINT
OmitSOPClassUIDFromCreateResponse = true
PresentationLUTMatchRequired = true
PresentationLUTinFilmSession = false
Supports12Bit = true
SupportsPresentationLUT = true

# TLS settings
UseTLS = true
TLSProfile = BCP195
RandomSeed = iheprt2.rnd
PeerAuthentication = REQUIRE
Certificate = sitecert.pem
PrivateKey = sitekey.pem

# ============================================================================
# Logins and Certificates for Digital Signature purposes
# are defined in the following section.
[[USERS]]
# ============================================================================

# ----------------------------------------------------------------------------
# The following text documents all settings defined for a user.
# Each user requires one section with a unique section title.
#
# [USER_1]
# ----------------------------------------------------------------------------
#
# Login of user, must be unique. Required entry.
#
# Login = name
#
# ----------------------------------------------------------------------------
#
# Name of user in human readable form, must be unique (e.g. might be
# presented in combo box GUI).
# Required entry.
#
# Name = name
#
# ----------------------------------------------------------------------------
#
# Name of user in DICOM Person Name (PN) format. Used when creating a
# Verifying Observer Sequence in a DICOM SR document.
# Required entry.
#
# DicomName = name
#
# ----------------------------------------------------------------------------
#
# Name of Organization for which this user is acting, in DICOM Long String (LO)
# format (i.e. max 64 characters). Used when creating a
# Verifying Observer Sequence in a DICOM SR document.
# Required entry.
#
# Organization = name
#
# ----------------------------------------------------------------------------
#
# Code for identifying the user, with the entries required for the DICOM
# Code Sequence Macro (DICOM Part 3, Table 8.8-1).  This is an optional entry
# that is used when creating a Verifying Observer Sequence in a DICOM SR
# document. If present, it must consist of four parts separated by backslash
# characters:
# - coding scheme designator, DICOM SH format (max. 16 characters)
# - coding scheme version (may be empty), DICOM SH format
# - code value, DICOM SH format
# - code meaning, DICOM LO format (max. 64 characters)
#
# Code = Coding_Scheme_Designator\Coding_Scheme_Version\Code_Value\Code_Meaning
#
# ----------------------------------------------------------------------------
#
# File containing the user's X.509 certificate for digital signature purposes.
#
# The certificate file must be located in directory defined in
# GENERAL/TLS/USERKEYDIRECTORY. The file format is defined in
# GENERAL/TLS/USEPEMFORMAT. This is a mandatory entry.
#
# Certificate = filename
#
# ----------------------------------------------------------------------------
#
# File containing the user's private key.  The key is used during creation
# of digital signatures.  The password with which the key is protected
# (when stored in PEM format) is also used to authenticate a user when
# a DICOM Structured Report is to be verified by a "Verifying Observer".
#
# The key file must be located in directory defined in
# GENERAL/TLS/USERKEYDIRECTORY. The file format is defined in
# GENERAL/TLS/USEPEMFORMAT. It is strongly recommended to keep the files
# in PEM format which (unlike DER) allows 3DES encryption of private keys!
# This is a mandatory entry.
#
# PrivateKey = filename
#
# ----------------------------------------------------------------------------

[USER_1]
Login = default
Name = Default User
DicomName = User^Default^^^
Organization = OFFIS e.V.
Code = 99_OFFIS_DCMTK\\USR.000001\DICOMscope 3.6.0 Default User
Certificate = usercert.pem
PrivateKey = userkey.pem

# ============================================================================
# Print Presentation LUT files are defined in the following section.
[[LUT]]
# ============================================================================

# ----------------------------------------------------------------------------
# Assign a unique name to each LUT and put this into the section name.
[LINEAR]
# ----------------------------------------------------------------------------

# Description of the LUT. Used in the GUI to present the selectable LUTs
# to the application user.
description = Linear LUT (256 Entries)

# Filename of the LUT file, should reside in LUT directory (see above).
filename = linear256us.lut

[LIGHTEN]
description = Lighten Image (256 Entries)
filename = lighten256us.lut

[DARKEN]
description = Darken Image (256 Entries)
filename = darken256us.lut

[MIDTONE]
description = Enhance Midtone (256 Entries)
filename = midtone256us.lut

[PHILIPS]
description = Philips Standard (256 Entries)
filename = philips256us.lut

[LINEAR12]
description = Linear LUT (4096 Entries)
filename = linear4096us.lut

[LIGHTEN12]
description = Lighten Image (4096 Entries)
filename = lighten4096us.lut

[DARKEN12]
description = Darken Image (4096 Entries)
filename = darken4096us.lut

[MIDTONE12]
description = Enhance Midtone (4096 Entries)
filename = midtone4096us.lut

[PHILIPS12]
description = Philips Standard (4096 Entries)
filename = philips4096us.lut

# ============================================================================
# VOI Window Level/Width Preset Definitions
[[VOI]]
# ============================================================================

# ----------------------------------------------------------------------------
# Assign a unique name to each preset and put this into the section name.
[CT_ABDOMEN]
# ----------------------------------------------------------------------------

# Description of the preset. Used in the GUI to present the selectable VOIs
description = Abdomen

# Modality to which this preset applies. Must be one of the DICOM defined
# terms for element (0008,0060) Modality, see PS3.3 C.*******.1
modality = CT

# Window center
center = 45

# Window width
width = 250

[CT_BONE]
description = Bone
modality = CT
center = 500
width = 4000

[CT_CRANE]
description = Crane
modality = CT
center = 35
width = 100

[CT_LUNG]
description = Lung
modality = CT
center = -500
width = 2000

[CT_MEDIASTINUM]
description = Mediastinum
modality = CT
center = 45
width = 440

[CT_PELVIS]
description = Pelvis
modality = CT
center = 45
width = 300

# ============================================================================
# Structured Reporting (SR) "templates" are defined in the following section.
[[REPORT]]
# ============================================================================

# ----------------------------------------------------------------------------
# Assign a unique name to each "template" and put this into the section name.
[KEY_IMAGE]
# ----------------------------------------------------------------------------

# Description of the report. Used in the GUI to present the selectable SR
# "template" to the application user.
description = Key Image Note (IHE Y2)

# Filename of the SR "template" file, should reside in REPORT directory
# (see above).
filename = reportki.dcm

[SIMPLE_IMAGE]
description = Simple Image Report (IHE Y2)
filename = reportsi.dcm
