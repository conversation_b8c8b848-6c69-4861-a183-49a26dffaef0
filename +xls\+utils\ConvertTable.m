function T = ConvertTable(T,convertfun, varnames)
    varnames0 = T.Properties.VariableNames;
    if ~exist('varnames', 'var')
        varnames =  varnames0;
    end

    if ischar(varnames)
        varnames = strsplit(varnames, '|');
    end

    if ischar(convertfun)
%         switch lower(convertfun)
%             case {'numeric', 'str2num'}
%                 convertfun = @str2num;
%             case {'str2vec1', 'str2double'}
%                 convertfun = @str2vec1; 
%             case {'str2vec3'}
%                 convertfun = @str2vec3; 
%             case {'str2vec6'}
%                 convertfun = @str2vec6;
%             case {'numvec1'}
%                 convertfun = @numvec1;
%         end
        convertfun = str2func(convertfun);
    end

    varnames = intersect(varnames, varnames0); 
    
    for k=1:numel(varnames)
        name = varnames{k};
        try
              var = T.(name);
              var = cell2mat(cellfun(@(x)convertfun(x), var, 'UniformOutput', false));
              T.(name) = var;
        catch
        end
    end
end


function x = numvec1(x)
    if isempty(x) 
        x = NaN; 
    end
end

function x = numvec2(x)
    if isempty(x) 
        x = [NaN NaN]; 
    end
end

function x = numvec3(x)
    if isempty(x) 
        x = [NaN NaN NaN]; 
    end
end

function x = numvec6(x)
    if isempty(x) 
        x = [NaN NaN NaN NaN NaN NaN]; 
    end
end

function res = str2vec1(x)
    if isempty(x)
        res = [NaN];
    else
        res = str2num(x);
    end
end

function res = str2vec2(x)
    if isempty(x)
        res = [NaN NaN];
    else
        res = str2num(x);
    end
end

function res = str2vec3(x)
    if isempty(x)
        res = [NaN NaN NaN];
    else
        res = str2num(x);
    end
end

function res = str2vec6(x)
    if isempty(x)
        res = [NaN NaN NaN NaN NaN NaN];
    else
        res = str2num(x);
    end
end