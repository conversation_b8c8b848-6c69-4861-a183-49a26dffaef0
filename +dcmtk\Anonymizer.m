classdef Anonymizer <OptionsWithLog
    %UNTITLED Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
        
    end
    
    methods
        function obj = Anonymizer(varargin)
            options = OptionsMap();
            options.setOption('anon.FieldsToKeep', {'PatientAge','PatientSex','StudyDescription'});
            obj = obj@OptionsWithLog('DicomAnon', varargin{:});
        end
        
        function uidnames = UIDFiledNames(self)
            uidnames = self.getoptioni('anon.UIDFiledNames');
            if isempty(uidnames)
                uidnames0 = {'StudyInstanceUID',  'SeriesInstanceUID', 'SOPInstanceUID', 'FrameOfReferenceUID'};
                uidnames1 = cellfun(@(x)(['Referenced' x]), uidnames0, 'uniformoutput', false); 
                uidnames  = cat(2, uidnames0, uidnames1);
                self.setOption('anon.UIDFiledNames', uidnames); 
            end
        end
        
        function str = UIDRegExpStr(self)
            str = self.getoptioni('anon.UIDRegExpStr');
            if isempty(str)
                uidnames = UIDFiledNames(self);
                str = ['(^' uidnames{1} '$)'];
                for k=2:numel(uidnames)
                    str = [str '|' '(^' uidnames{k} '$)'];
                end
            end
        end
        
%         function names = GetDateFiledNames(self, fn)
%             res = regexp(fn, 'Date$', 'match');
%             I = cellfun(@(x)(~isempty(x)), res);
%             names = fn(I);
%         end
%         
%         function names = GetTimeFiledNames(self, fn)
%             res = regexp(fn, 'Time$', 'match');
%             I = cellfun(@(x)(~isempty(x)), res);
%             names = fn(I);
%         end
        
%         function names = GetUIDFiledNames(self, dcminfo, names, prefix)
%             if ~exist('names','var')
%                 names = {};
%             end
%             if ~exist('prefix','var')
%                 prefix = '';
%             end
%             
%             fn     = fieldnames(dcminfo); 
%             names0 = self.ParseUIDFieldName(fn);
%             names1 = cellfun(@(x)([prefix x]), names0, 'uniformoutput', false);
%             names  = cat(1, names, names1); 
%             for k  = 1:numel(fn)
%                 name = fn{k};
%                 info = dcminfo.(name); 
%                 if isstruct(info)
%                     prefix1 = [ prefix name '.'];
%                     names   = GetUIDFiledNames(self, info, names, prefix1);
%                 end
%             end
%         end
        
%         function names = ParseUIDFieldName(self, fn)
%             str = UIDRegExpStr(self);
%             res = regexp(fn, str, 'match');
%             I   = cellfun(@(x)(~isempty(x)), res);
%             names = fn(I);
%         end
        
        function info = ProcessStruct(self, info, varargin)
            fn = fieldnames(info);
            for k=1:numel(fn)
                name = fn{k}; 
                val  = info.(name);
                if isstruct(val)
                    info.(name) = self.ProcessStruct(val, varargin{:});
                else
                    info.(name) = self.ProcessField(name, val);
                end
            end
        end
        
        function res = ProcessField(self, name, val)
             str = UIDRegExpStr(self);
             matchuid = regexp(name, str, 'ONCE');
             %matchdate= regexp(name, 'Date$', 'ONCE');
             res = val;
             if ~isempty(matchuid)
                 res = self.HashUID(val);
             elseif ~isempty(regexp(name, 'Date$', 'ONCE'))
                 res(5:8) = '0101';
             elseif ~isempty(regexp(name, 'Time$', 'ONCE'))
                res = '000000';
             end
        end
    end
    
    methods (Static)
        function newuid = HashUID(uid)
            persistent hasher
            if isempty(hasher)
                hasher = System.Security.Cryptography.HashAlgorithm.Create('MD5');
                %hasher = System.Security.Cryptography.HashAlgorithm.Create('SHA512');
                %hasher = System.Security.Cryptography.HashAlgorithm.Create('SHA1');
            end
            
            %str = uid;
            %nums= cellfun(@(x)(str2num(['uint64(',x,')'])), strsplit(uid, '.'));
            hash_byte = hasher.ComputeHash( uint8(uid) );  % System.Byte class
            hash_uint8 = uint8(hash_byte );
            uids = strsplit(uid, '.'); N = numel(uids); 
            for k=1:4
                uids{N-k+1} = strrep(num2str(hash_uint8(4*(k-1)+[1:4])), ' ', '');
            end
            newuid = uids{1}; 
            for k=2:N
                newuid =[newuid '.' uids{k}];
            end
        end
    end
end

function  hash_uint8 = Hash_sha512(str)
    persistent hasher
    if isempty(hasher)
        hasher = System.Security.Cryptography.HashAlgorithm.Create('SHA512');
    end
    hash_byte = hasher.ComputeHash( uint8(str) );  % System.Byte class
    hash_uint8 = uint8(hash_byte );
end