classdef DCMTKWrapper <OptionsWithLog
    properties
  
    end
    
    methods
        function obj = DCMTKWrapper(varargin)
            obj = obj@OptionsWithLog('DcmServer', varargin{:});
        end

        function res = RootFolder(self)
            res = self.getoptioni('DCMSERVER_ROOT');
        end
        
        function res = DcmDataFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER');
        end

        function res = DefaultRetrieveLevel(self)
            res = self.getoptioni('defaultretrievelevel', 'image');
        end
        
        function counts = retrieveImages(obj, uids, varargin)
            counts = 0; 
            try
                for k=1:numel(uids)
                    uid=uids{k};
                    status = obj.retrieveImage(uid); 
                    if ~isempty(status)
                        counts = counts+1; 
                    end
                end
                
%                 if counts>0
%                     self.StoreImages(); 
%                 end
            catch err
            end
        end
        
%         function res = StoreImages(self)
%             res = true; 
%         end
        
        function [status, result] = retrieveImage(obj, sopinstanceuid)
             if isstruct(sopinstanceuid)
                info = sopinstanceuid;
                sopinstanceuid=StructBase.getfieldx(info, 'SOPInstanceUID');
                if ai.DcmParser.ExistDcmFile(DcmDataFolder(obj), info)
                    return;
                end
             end
             [status, result] = obj.move('QueryRetrieveLevel=IMAGE', ['SOPInstanceUID=' sopinstanceuid]); 
        end
        
        function [status, result] = retrieveSeries(obj,  seriesuid, studyuid, patid)
            status=[];  result=[];
            if isstruct(seriesuid)
                info = seriesuid;
                seriesuid=StructBase.getfieldx(info, 'SeriesInstanceUID');
                studyuid =StructBase.getfieldx(info, 'StudyInstanceUID');
                patid    =StructBase.getfieldx(info, 'PatientID');
                isVol = ismember(StructBase.getfieldx_default(info, 'Modality', ''),  {'CT', 'MR', 'PT'});
                if isVol && ai.DcmParser.ExistDcmFolder_series(DcmDataFolder(obj), info, {'CT', 'MR', 'PT'})
                    return;
                end
            end

%             [status, result]  = obj.move(['-k 0008,000D=' studyuid],  ['-k 00020,000E=' seriesuid]);
            level = obj.DefaultRetrieveLevel();
            if strcmpi(level, 'series')
                [status, result]  = obj.move('QueryRetrieveLevel=SERIES', ['StudyInstanceUID=' studyuid],  ['SeriesInstanceUID=' seriesuid]);  
            elseif strcmpi(level, 'image')
                imgs = obj.queryImage(seriesuid);
                imagecounts = obj.retrieveImages(imgs); 
                %[status, result] = obj.move('QueryRetrieveLevel=IMAGE', ['SOPInstanceUID=' uid]); 
            end
        end
        
        function [uids] = retrieveStudy(obj,  studyuid, modality, varargin)
            [uids, Rsps] = querySeries(obj, studyuid, modality, varargin{:});
            for k=1:numel(uids)
                seriesuid=uids{k};
                [status, result] = retrieveSeries(obj,  seriesuid, studyuid);
            end
        end

        function [status, result] = openDcmServer(obj)
            status =[]; result =[]; 
            dcmserver_folder = obj.DCMSERVER_FOLDER;
            if isempty(dcmserver_folder)
                return;
            end
            if 1
                processname = 'AppDcmServer.exe';
                killprocess(processname); 

                cmd = [obj.DCMTK_Bin_Folder processname ' ' dcmserver_folder];
                DosUtils.system_vbs(cmd, [dcmserver_folder '..\storedcm.vbs']);
            else
%                 start(timer('TimerFcn',{@AppDcmServer,dcmserver_folder},'StartDelay',2));
            end
        end
        
        function openRecvPort(obj)
            processname = ['storescp_' obj.DCMTK_Client_AETitle '.exe'];
            
            exefile = [obj.DCMTK_Bin_Folder processname];
            if ~exist(exefile, 'file')
                copyfile([obj.DCMTK_Bin_Folder 'storescp.exe'], exefile); 
            end
            
            killprocess(processname); 
            
            scpFolder = obj.DCMTK_Client_SCPSTORE_FOLDER;
            cmd = [obj.DCMTK_Bin_Folder processname ' -aet ' obj.DCMTK_Client_AETitle  ' ' num2str(obj.DCMTK_Client_Port)    ' -od ' scpFolder ];
            DosUtils.system_vbs(cmd, [scpFolder '..\storescp.vbs']);
        end
        
        function [status, result] =  dcmsend(obj, dcmfile, options)
            if nargin <3
                options = ''; 
            end
            %cmd = [obj.DCMTK_Bin_Folder 'dcmsend.exe ' options obj.SCUStr  ' "' dcmfile '"'  ];
            dcmfile=strrep(dcmfile, '/', filesep);
            cmd = [obj.DCMTK_Bin_Folder 'storescu.exe ' options obj.SCUStr  ' "' dcmfile '"'  ];
            [status, result] = DosUtils.systemEx(cmd);
            
        end
        
        function [status, result] =  dcmsend_sd(obj, dcmfolder)
            [status, result] = obj.dcmsend(dcmfolder, '-v +sd +r -nh');
            if status~=0
                obj.LogWarning(['dcmsend (status ' num2str(status) ')--' dcmfolder]);
            else
                obj.LogInfo(['dcmsend --' dcmfolder]);
            end
        end
        
        function [status, result] =  movescu(obj, options)
            cmd = [obj.DCMTK_Bin_Folder 'movescu.exe ' obj.SCUStr ' -aem ' obj.DCMTK_Client_AETitle ' -S '  options  ];
            [status, result] = DosUtils.systemEx(cmd);
        end
        
        function [status, result] =  echoscu(obj, options)
            cmd = [obj.DCMTK_Bin_Folder 'echoscu.exe ' obj.SCUStr ' ' options];
            [status, result] = DosUtils.systemEx(cmd);
        end
        
        function [status, result] = move(obj, varargin)
            queryStr = parseQueryOptions0(obj, varargin{:});
            [status, result] =  movescu(obj, queryStr);
        end
        
        function [studyRsps, result] = query(obj, level, varargin)
            queryStr = parseQueryOptions(obj, ['QueryRetrieveLevel=' level], varargin{:});
%             [studyRsps, result] = queryscu(obj, queryStr);
            
            cmd = [obj.DCMTK_Bin_Folder 'findscu.exe ' obj.SCUStr() ' -S '  queryStr];
            [status, result] = DosUtils.systemEx(cmd);    
            studyRsps = obj.parseQueryOutput(level, result);
            
        end
        
%         function [studyRsps, result] = queryscu(obj, queryStr)
%             cmd = [obj.DCMTK_Bin_Folder 'findscu.exe ' obj.SCUStr() ' -S '  queryStr];
%             [status, result] = DosUtils.systemEx(cmd);    
%             studyRsps = obj.parseQueryOutput(result);
%         end
        
        function [uids, Rsps] = queryStudy(obj, patID, varargin)
            uids =[]; Rsps=[]; 
            try
            [Rsps] = query(obj, 'STUDY', {'PatientID' patID}, varargin{:});
            uids   = {Rsps(:).StudyInstanceUID};
            catch err
            end
        end
        
         function [uids, Rsps] = queryStudy_daterange(obj, daterange, varargin)
            uids =[]; Rsps=[]; 
            try
            [Rsps] = query(obj, 'STUDY', {'StudyDate' daterange}, varargin{:});
            uids   = {Rsps(:).StudyInstanceUID};
            catch err
            end
        end
        
        function [uids, Rsps] = querySeries(obj, studyID, modality, varargin)
            if ~exist('modality', 'var')
                modality =[]; 
            end
            
            uids =[]; Rsps=[]; 
            try
            if ~isempty(modality)
                [Rsps] = query(obj, 'SERIES', ['StudyInstanceUID=' studyID],['Modality=' modality],  varargin{:});
            else
                [Rsps] = query(obj, 'SERIES', ['StudyInstanceUID=' studyID], varargin{:});
            end
            
            if ~isempty(Rsps)
                uids   = {Rsps(:).SeriesInstanceUID};
            end
            
            catch err
                obj.LogErr(err);
            end
        end
        
        function Rsps = queryPatient(obj, patID, level, varargin)
            [Rsps] = query(obj,  level, {'PatientID' patID}, varargin{:});
        end
        
        function [uids, Rsps] = queryImage(obj, seriesID, varargin)
            uids =[]; Rsps=[]; 
            
            try
            %[Rsps] = query(obj, '8,52=IMAGE', ['20,e=' num2str(seriesID)],['8,23=' contentDate],  varargin{:});
            [Rsps] = query(obj, 'IMAGE', ['SeriesInstanceUID=' seriesID], varargin{:});
            if ~isempty(Rsps)
            uids   = {Rsps(:).SOPInstanceUID};
            end
            catch err
                obj.LogErr(err);
            end
        end
        
        function [uids, Rsps]= query_image(obj, patID, modality, contentDate, varargin) 
           if ~exist('contentDate', 'var')
                contentDate=[];
           end
           
           % ----- STUDY LEVEL -----
           studyUIDs = obj.queryStudy(patID); 
           uids =[]; Rsps=[]; 
           % ----- SERIES LEVEL -----
           for k = 1:numel(studyUIDs)
                studyID = studyUIDs{k};
                [uids1, Rsps1]= query_image_study(obj, studyID, modality, contentDate, varargin{:}); 
                uids = cat(2, uids, uids1); 
                Rsps = cat(2, Rsps, Rsps1); 
            end
        end
        
        function [uids, Rsps]= query_image_study(obj, studyID, modality, contentDate, varargin) 
            [seriesUIDs, seriesRsps] = obj.querySeries(studyID, modality);  
            uids = []; Rsps=[]; 
            datetags = {'SeriesDate', 'StudyDate'};
            if exist('contentDate', 'var') && ~isempty(contentDate)
                contentDate = obj.convertDateRange(contentDate);
                I=[];
                for ks = 1:numel(seriesRsps)
                    for m=1:numel(datetags)
                        date0 = StructBase.getfieldx(seriesRsps(ks), datetags{m});
                        if ~isempty(date0)
                            continue;
                        end
                    end
                    imgdate = datenum(date0, 'yyyymmdd');
                    if imgdate>=contentDate(1)&&imgdate<=contentDate(2)
                       I=[I, ks]; 
                    end
                end
                seriesUIDs=seriesUIDs(I);                 seriesRsps=seriesRsps(I);
            end

            % ----- IMAGE LEVEL -----
            for ks = 1:numel(seriesUIDs)
                seriesID = seriesUIDs{ks}; 
                [~, imageRsps] = obj.queryImage(seriesID, varargin{:});
                if isempty(imageRsps)
                    continue;
                end
                Rsps = cat(2, Rsps, imageRsps); 
            end
            
            if ~isempty(Rsps)
                uids   = {Rsps(:).SOPInstanceUID};
            end
        end
        
        function res = DCMSERVER_FOLDER(self)
            res = self.getoptioni('DCMSERVER_FOLDER',  []); 
        end
        
         %%
        function T = QueryPatientSeries(dcm, mrns, modality)
            if ischar(mrns)
                mrns = {mrns};
            end
            if ~exist('modality', 'var')
                modality='';
            end
            
            studyfns = {'PatientName', 'StudyDate', 'StudyTime', 'StudyID' 'StudyDescription'}; 
            Rsps=[]; T=[];
            for k=1:numel(mrns)
                mrn = mrns{k}; 
                [studyuids, studyRsps] = dcm.queryStudy(mrn);
                
                for m=1:numel(studyuids)
                    try
                    studyRsp = studyRsps(m); 
                    studyuid = studyuids{m};
                    [seriesuids, Rsps1]=dcm.querySeries(studyuid, modality);
                    try
                    for n=1:numel(Rsps1)
                        Rsps1(n) = StructBase.copyStructFields(Rsps1(n), studyRsp, studyfns); 
                    end
                    catch
                    end
                    Rsps = cat(2, Rsps, Rsps1); 
                    catch err
                        disp(['error: ' mrn ' ' studyuid]); 
                    end
                end
            end
            
            if ~isempty(Rsps)
                T = struct2table(Rsps,'AsArray', true); 
            end
%             T = sortrows(T, {'PatientID',  'StudyDate'}, {'ascend', 'ascend'}); 
        end
    end
    
    methods (Access =private)
       
        
        function res = DCMTK_Client_AETitle(self) 
            res = self.getoptioni('DCMTK_Client_AETitle'); 
        end
        
        function res = DCMTK_Client_Port(self)   
           res = self.getoptioni('DCMTK_Client_Port'); 
        end
        
        function res = DCMTK_Server_AETitle(self)
            res = self.getoptioni('DCMTK_Server_AETitle', 'RTPACS_RES');
        end
        
        function res = DCMTK_Server_Port(self)
            res = self.getoptioni('DCMTK_Server_Port', '4242');
        end

        function res = DCMTK_Server_IP(self) 
            res = self.getoptioni('DCMTK_Server_IP', '**************');
        end
               
        function res = DCMTK_Bin_Folder(self)
            %res = self.getoptioni('DCMTK_Bin_Folder', [fileparts(mfilename('fullpath')) filesep 'bin' filesep]); 
            res = self.getoptioni('DCMTK_Bin_Folder', [fileparts(mfilename('fullpath')) filesep 'dcmtk-3.6.5-win64-dynamic\bin' filesep]); 
        end
         
        function res = DCMTK_Client_SCPSTORE_FOLDER (self)
           serverfolder = DCMSERVER_FOLDER(self); 
           if ~isempty(serverfolder)
               res = DosUtil.mksubdir(serverfolder, 'incoming'); 
           else
               res = self.getoptioni('DCMTK_Client_SCPSTORE_FOLDER', DosUtil.mksubdir('c:\dicom\', 'SCPSTORE_FOLDER')); 
           end
        end
        
        function res = SCUQueryStr(obj)
            res = cat(2, SCUStr(obj), CommonQueryStr(obj)); 
        end
        
        function res = SCUStr(obj)
            res = [' ' obj.DCMTK_Server_IP ' ' num2str(obj.DCMTK_Server_Port) ' -aet ' obj.DCMTK_Client_AETitle ' -aec ' obj.DCMTK_Server_AETitle ' ']; 
        end
        
        function res = CommonQueryStr(obj, options)
            level    = options.getoptioni('QueryRetrieveLevel'); 
            tagNames = obj.TagNames(level);
            res = []; 
            for k=1:numel(tagNames)
                tagName = tagNames{k};
                if ~options.isOption(tagName)
                    res = strcat(res, [' -k ' tagName]);   
                end
            end
%             res = cellfun(@(x)(strcat(res, [' -k ' x])), tagNames, 'UniformOutput', false); 
        end
        
        function str = parseQueryOptions0(obj, varargin)
            options=OptionsMap(varargin{:});
            
            fn = options.getOptionNames();
            str =[]; 
            for k=1:numel(fn)
                tagName = fn{k}; 
                tagVal  = options.getOption(tagName); 
                str0    = obj.getQueryStr(tagName, tagVal);
                str     = cat(2, str, str0); 
            end
        end
        
         function str = parseQueryOptions(obj, varargin)
            options=OptionsMap(varargin{:});
            
            str = parseQueryOptions0(obj, varargin{:});
            
            str2 = obj.CommonQueryStr(options);
            
            str = cat(2, str, str2); 
         end  
         
%          function tagNames =  TagNames(self, level)
%             %tagNames = self.getoptioni('tagnames', self.DefaultTagNames());
%             
%          end
         
         function tagNames =  TagNames(obj, level)
           switch upper(level)
                case {'STUDY'}
                    tagNames =  obj.StudyTagNames();
                case {'SERIES'}
                    tagNames =  obj.SeriesTagNames();
                otherwise
                    tagNames =  obj.DefaultTagNames();
           end
        end
         
                    % return cell array of struct
        function dcmRsps = parseQueryOutput(self,level,  out, splitStr)
            if ~exist('splitStr', 'var')
                splitStr = '---------------------------';
            end
            
            if isempty(out)
                dcmRsps = {};
            else
                strRsps = regexp(out, splitStr, 'split');
%                 strRsps = regexp(out, '\n', 'split');
                strRsps = strRsps(2:end); % first one is empty
                tags = self.TagNames(level); 
                dcmRsps = cellfun(@(str)grepTagsFromResponse(str, tags), strRsps, 'UniformOutput', false);
                %dcmRsps = arrayfun(@(str)grepTagsFromResponse(str), strRsps, 'UniformOutput', false);
            end
            dcmRsps = cell2mat(dcmRsps); 
        end
    end
    
    
    methods (Static)
           function [status, result] = closeRecvPort
                [status, result] = system('taskkill /F /IM storescp.exe '); 
           end
            
           function str = dicomtag(tagName)
                [g, e] = dicomlookup(tagName);
                g = dec2hex(g, 4);
                e = dec2hex(e, 4);
                str = [g ',' e]; 
           end
           
           function str = getQueryStr(tagName, tagVal)
               tag = dcmtk.DCMTKWrapper.dicomtag(tagName);
               if ~isempty(tagVal)
                   str = [' -k ' tag '="' num2str(tagVal) '"'];  
               else
                   str = [' -k ' tag ];  
               end
           end
           
           function contentDate = convertDateRange(contentDate)
                if ischar(contentDate)&&~isempty(contentDate)
%                     k=strfind(contentDate, '-'); %for date range
%                     if ~isempty(k) 
%                         startDate = datenum(contentDate(1:(k-1)),   'yyyymmdd');
%                         endDate   = datenum(contentDate((k+1):end), 'yyyymmdd'); 
%                         contentDateNum=startDate:endDate;
%                         N = numel(contentDateNum);
%                         contentDate = cell(1, N); 
%                         for n=1:N
%                             contentDate{n} = datestr(contentDateNum(n), 'yyyymmdd'); 
%                         end
%                     else
%                         contentDate={contentDate};
%                     end
                    res = strsplit(contentDate, '-');
                    if numel(res)==1
                        startdate = datenum(res{1}, 'yyyymmdd'); 
                        enddate   = startdate;
                    elseif isempty(res{1})
                        startdate = 0; 
                        enddate   = datenum(res{2}, 'yyyymmdd');
                    elseif isempty(res{2})
                        startdate = datenum(res{1}, 'yyyymmdd');
                        enddate   = now;
                    else
                        startdate = datenum(res{1}, 'yyyymmdd');
                        enddate   = datenum(res{2}, 'yyyymmdd');
                    end
                    contentDate=[startdate, enddate];
               end
           end
           

        function tagNames =  DefaultTagNames()
            tagNames = {'PatientID' 'PatientName' 'StudyDate' 'StudyTime'  'StudyInstanceUID' 'StudyID' ...
                'Modality' 'SeriesDate' 'SeriesTime' 'SeriesInstanceUID' 'SeriesNumber' 'StudyDescription' 'SeriesDescription'...
                'ImageType' 'SOPInstanceUID' 'ContentDate' 'ContentTime' 'ReferencedSOPClassUID' 'ReferencedSOPInstanceUID' 'InstanceNumber' 'ReferencedBeamNumber'};
        end
      
        function tagNames =  SeriesTagNames()
            tagNames = {'PatientID' 'PatientName' 'StudyDate' 'StudyTime'  'StudyInstanceUID' 'StudyID' ...
                'Modality' 'SeriesInstanceUID' 'SeriesNumber' 'StudyDescription' 'SeriesDescription'...
                'SeriesDate' 'SeriesTime'};
        end 
        
        function tagNames =  StudyTagNames()
            tagNames = {'PatientID' 'PatientName' 'StudyDate' 'StudyTime'  'StudyInstanceUID' 'StudyID' 'StudyDescription'};
         end  
        
        function [ret, out] = systemEx(cmd)
            tname = tempname;
            ret = system(sprintf('%s > %s', cmd, tname));
            out = fileread(tname);
            delete(tname);
        end
    end
end

    
% get multiple tags from one response
% used for parsing
function dcm = grepTagsFromResponse(str, tagNames)
%     tagNames = dcmtk.DCMTKWrapper.DefaultTagNames();
    dcm = struct();
    for k = 1:numel(tagNames)
        tagName = tagNames{k};
        dcm.(tagName) = []; 
        [g, e] = dicomlookup(tagName);
        g = dec2hex(g, 4);
        e = dec2hex(e, 4);
%             ret = regexpi(str, ['\(' g ',' e '\)[^\n]*\[([^\n]*)\][^\n]*' tagName], 'dotall', 'tokens');
        ret = regexpi(str, ['\(' g ',' e '\)[^\n]*\[([^\n]*)\][^\n]*' ], 'dotall', 'tokens');
        try
        if numel(ret) == 1
            %dcm.(tagName) = ret{1}{1};
            %dcm.(tagName) = strtrim(ret{1}{1});
            dcm.(tagName) = deblank(ret{1}{1});
        elseif numel(ret) > 1
            error; % should have at most one match for one response
        end
        catch err
            disp(ret)
        end
    end
end

function [status, result] = killprocess(processname) 
    status =[]; result =[]; 
    list = DosUtils.tasklist; 
    processes = {list(:).name};
    if ismember(processname, processes)
        [status, result] = system(['taskkill /F /IM ' processname]); 
    end
end