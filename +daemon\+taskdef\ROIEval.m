classdef ROIEval <daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = ROIEval(varargin)
            options = OptionsMap(varargin{:});
            <EMAIL>(options);
            obj.SetDefaultCfg();
        end

         function cfg = DefaultCfg(self, str)
            taskdefname = self.TaskDefName;
            TaskDoneIndicator=[taskdefname '[TaskExt].tsk'];
            cfg = struct("TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            if exist('str', 'var')&&~isempty(str)
                fns = fieldnames(str);
                for k=1:numel(fns)
                    name = fns{k};
                    cfg.(name)=str.(name);
                end
            end
        end
    end

    methods (Static)
        function names = TemplateParameters()
                names = {'TaskExt', 'ROIMaskFile', 'RefNomenclature', 'RefPlaceHoldROIs', 'EvalPropNames', 'EvalROINames', 'OutXlsFileName', 'OutXlsSheetName'};
        end

        function str = TemplateStruct(str0, names)
            if ~exist('names', 'var')||isempty(names)
                names = daemon.taskdef.ROIEval.TemplateParameters;
            end
            for k=1:numel(names)
                name = names{k};
                str.(name)=['[' name ']'];
            end
            if ~isempty(str0)
                fns = fieldnames(str0);
                for m=1:numel(fns)
                    fn = fns{m};
                    str.(fn)=str0.(fn);
                end
            end
        end

        function str = DefaultSettings(str0, names)
            if ~exist('names', 'var')||isempty(names)
                names = daemon.taskdef.ROIEval.TemplateParameters;
            end
            for k=1:numel(names)
                name = names{k};
                str.(name)='';
            end
            if ~isempty(str0)
                fns = fieldnames(str0);
                for m=1:numel(fns)
                    fn = fns{m};
                    str.(fn)=str0.(fn);
                end
            end
        end
  
        function [process,DefaultSettings,Dependency]  =ConstructTempStruct(processtype, depenency,  tempstr, defaultstr, varargin)
            if ~exist('tempstr', 'var')
                tempstr    = [];
            end
            if ~exist('defaultstr', 'var')
                defaultstr = [];
            end
            %taskdeffolder = [fileparts(tasksdeffile) '/'];
            process = daemon.taskdef.ROIEval.TemplateStruct(tempstr, varargin{:});
            process.('OperationType')=processtype; 
            %process.('EvalType')=evaltype;
            DefaultSettings=daemon.taskdef.ROIEval.DefaultSettings(defaultstr, varargin{:});
            
            if ~isempty(depenency)&&ischar(depenency)
                depenency=strsplit(depenency, '|');
            end

            for k=1:numel(depenency)
                Dependency{k} = struct('filename', process.(depenency{k}));
            end
        end

        function [process,DefaultSettings,Dependency]  =ConstructROIEvalStruct(evaltype, varargin)
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructTempStruct('ROIEval', varargin{:});
            process.('EvalType')=evaltype;
        end

        function [process,DefaultSettings,Dependency]  =ConstructROISimilarityStruct(varargin)
            tempstr  = struct('TestROIMaskFile', '[TestROIMaskFile]', 'TestNomenclature', '[TestNomenclature]',  'TestPlaceHoldROIs', '[TestPlaceHoldROIs]', ...
                'EvalPropNames', '[EvalPropNames]');
            depenency= ['ROIMaskFile|TestROIMaskFile'];
            defaultstr = struct('TestNomenclature', '',  'TestPlaceHoldROIs', '', 'EvalPropNames', 'RefVolume|TestVolume|DSC|HD95|ASD');
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructROIEvalStruct('ROISimilarity', depenency,  tempstr, defaultstr);
            process.('EvalType')='ROISimilarity';
        end

        function ROIEvalTemp(tasksdeffile,taskdefname, evaltype, depenency,  tempstr, defaultstr)
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructROIEvalStruct(evaltype, depenency,  tempstr, defaultstr);
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj            = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings});
        end
        
        function ROIEvalImage(tasksdeffile,taskdefname, evaltype, depenency,  tempstr, defaultstr)
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructROIEvalStruct(evaltype, depenency,  tempstr, defaultstr);
            process.('OutXlsFileName')='../ROIStat.xlsx';
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj            = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings}, {'CustomSubFolder','[CustomSubFolder]'});
        end

        function ROISimilarityTemp(tasksdeffile,taskdefname)
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructROISimilarityStruct();
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj           = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings});
        end

        function RSEvalSeg(tasksdeffile,taskdefname, segname, placeholdrois)
            if ~exist("segname", 'var') ||isempty(segname)
               segname = '[EvalSegName]';
            end

            refroimaskfile='./roimask.nii.gz'; 
            testroimaskfile=['../[ReferencedImageModality].[ReferencedImageSeriesUID]/' segname '/roimask.nii.gz'];
            [process,DefaultSettings]  =daemon.taskdef.ROIEval.ConstructROISimilarityStruct();
            Dependency{1} = struct("filename", refroimaskfile, 'taskfilename', '../DcmConverter/DCMCONVERT_RS/RS.[SOPInstanceUID].tsk');
            Dependency{2} = struct("filename", testroimaskfile);
            process.('ROIMaskFile')    =  refroimaskfile;
            process.('TestROIMaskFile')=  testroimaskfile;
            
            process.('OutXlsFileName') = ['./SegEval.xlsx'];
            process.('OutXlsSheetName')   = segname;
            if exist('placeholdrois', 'var') &&~isempty(placeholdrois)
                process.('RefPlaceHoldROIs') = placeholdrois;
            end

            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj           = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            %CustomSubFolder = [taskdefname '.' segname]; 
            TaskDoneIndicator=[taskdefname '.' segname '.tsk']; 
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings}, {'TaskDoneIndicator',TaskDoneIndicator}, {'TaskConfigOutput', TaskDoneIndicator});
        end

        function ROIMask2StructSet(tasksdeffile,taskdefname, tempstr, varargin)
            %tempstr = [];
            tempparas  = {'TaskExt','RefROIMaskImage', 'ID', 'RSFileName', 'LabelMaskFileName', 'ROIMaskFileName', 'LabelMaskWithImageContours', 'ContourSmoothWindow', 'roimask2rs', 'RefPlaceHoldROIs'};
            defaultstr = struct('ID','', 'RSFileName','', 'LabelMaskFileName', '',...
                'ROIMaskFileName', '', 'LabelMaskWithImageContours',1, 'ContourSmoothWindow', 3, 'roimask2rs', 1,  'RefPlaceHoldROIs', '');
            dependency = 'RefROIMaskImage';
            [process,DefaultSettings,Dependency]  =daemon.taskdef.ROIEval.ConstructTempStruct('ROIMask2StructSet',dependency,  tempstr, defaultstr, tempparas);
            %ROIMask2StructSet(taskstr, statusfolder, info);
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj   = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings}, varargin{:});
        end

        function MergeExportROIMask(tasksdeffile,taskdefname, MergeOperation, tempstr, varargin)
            tempparas  = {'TaskExt','OutMaskImageType', 'OrigImageHeaderFile', 'PlaceHoldOutROINames', 'OutLabelMaskFileName', 'OutROIMaskFileName', 'PreProcessing', 'PostProcessing'};
            defaultstr = struct('OutMaskImageType','roimask', 'OrigImageHeaderFile', '','PlaceHoldOutROINames', '',  'OutLabelMaskFileName','', 'OutROIMaskFileName', '', 'PreProcessing','', 'PostProcessing', '');
            dependency = '';
            OutputStructSet=struct("LabelMaskFileName", '[OutLabelMaskFileName]', "ROIMaskFileName", '[OutROIMaskFileName]');
            [process,DefaultSettings]  =daemon.taskdef.ROIEval.ConstructTempStruct('MergeExportROIMask',dependency,  tempstr, defaultstr, tempparas);
            
            SrcRoiMaskFiles=cellfun(@(x)(x.SrcRoiMaskFile), MergeOperation, 'UniformOutput',false);
            Dependency     =cellfun(@(x)struct('filename', x), SrcRoiMaskFiles, 'UniformOutput',false);
            process.MergeOperation=MergeOperation;
            
            process.OutputStructSet=OutputStructSet; 
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj   = daemon.taskdef.ROIEval({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, process, {'DefaultSettings', DefaultSettings}, varargin{:});
        end

    end
end