classdef RtPlan < dcm4che2.RtDicomBase
    properties

    end
    
    properties (Constant = true)
          Tags_Modality  = {'PhysiciansOfRecord' 'RTPlanLabel' 'RTPlanName' 'RTPlanDescription' 'RTPlanDate' 'RTPlanTime' 'FrameOfReferenceUID' 'ApprovalStatus'};
          Tags_ModalityXX={{'ReferenceStructureSetUID' 'ReferencedStructureSetSequence[1]/ReferencedSOPInstanceUID'}, {'TreatmentMachineName', 'BeamSequence[1]/TreatmentMachineName'}};
    end
    
    methods
        function self = RtPlan(varargin)
            self = <EMAIL>(varargin{:}); 
        end
        
        function rp = Clone(self)
            rp   = dcm4che2.RtPlan();
            % opts = OptionsMap(self); 
            % rp.setOptions(opts); 
            if ~isempty(self.jInfo)
                rp.jInfo = dcm4che2.clone(self.jInfo);
            end
        end
        
        function res = StructureSetUID(self)
            res = self.GetTagxx('ReferencedStructureSetSequence[1]/ReferencedSOPInstanceUID'); 
        end
        
        function value = NumberOfFractionsPlanned(self)
           value = double(self.GetTagxx('FractionGroupSequence[1]/NumberOfFractionsPlanned'));
%            value = obj.getfield(obj, 'NumberOfFractionsPlanned');
        end
        
        function res = PatientSetupPosition(self)
            res = self.GetTagxx('PatientSetupSequence[1].PatientPosition');
        end
        
%         function value = NumberOfBeams(self)
%             value = self.GetTagxx('FractionGroupSequence[1]/NumberOfBeams');
%         end
        
%         function res = GetBeam(self, num)
%             beaminfo = self.GetTagxx(['BeamSequence[' num2str(num) ']']);
%             res =  dcm4che2.RtBeam(beaminfo);
%         end
        
        %in units of cm, to be compatible with rtdose.RTPlanInfo
        function pos = getIsocenterPosition(self, beamnum)
            if ~exist('beamnum', 'var')
                beamnum = 1;
            end
            beams = GetTreatmentBeams(self);
            pos = beams{beamnum}.Isocenter; 
            pos = pos(:)/10; 
        end
        
        %in units of cm, to be compatible with rtdose.RTPlanInfo
        function isocenters = getIsocenterPositions(self)
            beams = GetTreatmentBeams(self);
            K = numel(beams);
            isocenters=NaN(K, 3);
            for k=1:K
                pos = beams{k}.Isocenter/10; 
                isocenters(k, :) = pos(:)'; 
            end
        end

        function [pos, range] = getIsocenterPositionRange(self)
            isocenters = getIsocenterPositions(self);
            minpos = min(isocenters, [], 1);
            maxpos = max(isocenters, [], 1);
            pos    = (minpos+maxpos)/2;
            range = maxpos-minpos; 
        end

        function num = NumTreatmentBeams(self)
            num = numel(GetTreatmentBeams(self));
        end
        
        function SetMachineConstraintsCfg(self, cfg)
             cfg   = StructBase.toCell(cfg); 
             beams = GetTreatmentBeams(self);
             for k=1:numel(beams)
                 name = beams{k}.TreatmentMachineName();
                 for m=1:numel(cfg)
                     MachineName = StructBase.getfieldx_default(cfg, 'MachineName', '');
                     
                 end
             end
        end
        
        function beams = GetTreatmentBeams(self)
            beams = self.getoptioni('dcm4che2.RtBeams');
            if isempty(beams)
                beamseq = self.GetTagx('BeamSequence');
                for k=1:numel(beamseq)
                    beams{k} = dcm4che2.RtBeam(beamseq{k});
                    beams{k}.setOption('RtPlan', self);
                end
                BeamNumbers = cellfun(@(x)(x.BeamNumber), beams); 
                
                seq = self.GetTagxx('FractionGroupSequence[1]/ReferencedBeamSequence');
                for k=1:numel(seq)
                    ReferencedBeamNumber = self.getTagx(seq{k}, 'ReferencedBeamNumber');
                    [~, loc] = ismember(ReferencedBeamNumber,BeamNumbers);
                    beams{loc}.setOption('ReferencedBeam', seq{k}); 
                    %beams{ReferencedBeamNumber}.setOption('ReferencedBeam', seq{k});
                end
                
                %%%08/08/2022, filter out Setup beams%%%%%%%%%
                I = cellfun(@(x)(x.isTreatmentBeam), beams);
                beams = beams(I);
                %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
                
                self.setOption('dcm4che2.RtBeams', beams);
            end
        end
        
        function res = TotalCP(self)
            beams    = GetTreatmentBeams(self);
            res = 0; 
            for k=1:numel(beams)
                res  = res + beams{k}.NumberOfControlPoints;
            end
        end

        function res = TotalMU(self)
            beams    = GetTreatmentBeams(self);
            res = 0; 
            for k=1:numel(beams)
                res  = res + beams{k}.BeamMeterset;
            end
        end

        function T = GetBeamInfoTable(self, varargin)
            beams  = GetTreatmentBeams(self); 
            T      = table;
            for m  = 1:numel(beams)
                T1 = beams{m}.getBeamPropertyTable(varargin{:});
                T  = cat(1, T, T1);
            end
        end
        
        function info = ParseInfo(self)
              tags = cat(2, self.Tags_Common,    self.Tags_Modality, self.Tags_ModalityXX);
              info = self.GetDcmInfo(self.jInfo, tags);
        end

        function name = TreatmentMachineName(obj)
            info = ParseInfo(obj);
            name = StructBase.getfieldx_default(info, 'TreatmentMachineName'); 
        end

        function name = ManufacturerModelName(obj)
              name = obj.GetTagxx('ManufacturerModelName'); 
        end

%         function res = TreatmentMachineName2ModelMap(obj)
%             str = 'TreatmentMachineName2ModelMap';
%             if obj.isOption(str)
%                 res = obj.getOption(str);
%             else
%                 res = OptionsMap; 
%                 res.setOption('truebeam', 'vitalbeam|truebeam|21');
%                 res.setOption('versa', 'versa|synergy|agility');
%                 res.setOption('ethos', 'ethos|halcyon');
%                 res.setOption('unity', 'unity');
%                 res.setOption('gk', 'icon|perfexion');
%                 res.setOption('gp', 'gammapod');
%                 obj.setOption(str, res);
%             end
%         end
% 
%         function res = TreatmentMachineModel(obj)
%             res = []; 
%             str = 'TreatmentMachineModel';
%             if obj.isOption(str)
%                 res = obj.getOption(str);
%             else
%                 info = ParseInfo(obj);
%                 name = StructBase.getfieldx_default(info, 'TreatmentMachineName'); 
%                 map = TreatmentMachineName2ModelMap(obj);
%                 models = map.getOptionNames; 
%                 for k=1:numel(models)
%                     model = models{k};
%                     val = map.getOption(model); 
%                     if ~isempty(regexpi(name,val))
%                         res = model; return; 
%                     end
%                 end
%             end
%         end

        function res  = TreatmentBeamModel(obj)
            res = []; 
            str = 'TreatmentBeamModel';
            if obj.isOption(str)
               res = obj.getOption(str);
            else
               beamtypes=TreatmentBeamMachineEnergy(obj);
               res = beamtypes{1};
            end
        end

        function res = TreatmentBeamModels(obj)
            res = TreatmentBeamMachineEnergy(obj);
        end

        function res = TreatmentBeamMachineEnergy(obj)
               beams = GetTreatmentBeams(obj);
               res = cellfun(@(x)(x.MachineEnergy), beams, 'UniformOutput',false);
        end

        function info = GetPlanInfo(self, propnames)
            if ischar(propnames)
                propnames = strsplit(propnames, '|');
            end
             beams = GetTreatmentBeams(self);
            for k=1:numel(propnames)
                propname=propnames{k};
                switch propname
                    case 'TreatmentBeamModel'
                        info.(propname) = TreatmentBeamModel(obj);
                    case {'NumberOfTreatmentBeams', 'TotalBeam'}
                        info.(propname) = numel(beams);
                    case {'TotalMeterset', 'TotalMU'}
                        info.(propname) = TotalMU(self);
                    case {'NumberOfControlPoints', 'TotalCP'}
                        info.(propname) = TotalCP(self);
                    otherwise
                        info.(propname) = self.GetTagxx(propname);
                end
            end
        end

        function info = GetPrescriptionInfo(self)
            info.NumberOfFractions=double(self.GetTagxx('FractionGroupSequence[1]/NumberOfFractionsPlanned'));
            info.RxSite=self.GetTagxx('DoseReferenceSequence[1]/DoseReferenceDescription');
            info.RxDose=self.GetTagxx('DoseReferenceSequence[1]/TargetPrescriptionDose');
        end
%         function  
%               {'BeamEnergy', 'BeamSequence[1]/ControlPointSequence[1]/NominalBeamEnergy'}, {'DoseRateSet', 'BeamSequence[1]/ControlPointSequence[1]/DoseRateSet'}}
%         end
    end
end
