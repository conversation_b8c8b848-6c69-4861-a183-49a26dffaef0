classdef MccHelper
    % MccHelper Helper class to call mcc.
    %
    % This class is created to assist in automated building since deploytool is
    % not programmable and uses absolute pathes.
    % Key features:
    % 1. Use relative path
    % 2. Include toolbox programmatically
    % 3. Flexible output name compared to mcc alphanumeric and _ only
    %
    % Notes:
    % Opted for class instead of function + inputParser because usage syntax is
    % more concise.  E.g.
    % mh = MccHelper;
    % mh.mFiles    = {'main.m'};
    % mh.toolboxes = {'images'};
    % versus.
    % MccHelper('mFiles', {'main.m'}, 'toolboxes', {'images'});
    %
    % Xiaohu Mo

    properties
        mFiles          % cellstr of Matlab files
        buildType       % (-m/-e) console or standalone
        outputDir       % (-d)
        outputFile      % (-o) file name without extension and path
        attachments     % (-a) cellstr of files to be attached/included
        toolboxes       % (-p) cellstr of toolboxes to be included, e.g. images. Default to exclude all.
    end
    methods
        function self = MccHelper()
            self.mFiles = cell(0);
            self.buildType = 'standalone';
            self.outputDir = '';
            self.outputFile = '';
            self.attachments = cell(0);
            self.toolboxes = cell(0);
        end
        function self = set.mFiles(self, inp)
            if ischar(inp)
                self.mFiles = {inp};
            elseif iscellstr(inp)
                self.mFiles = inp;
            else
                error('MccHelper:InvalidInput', 'Input should be char or cellstr');
            end
        end
        function self = set.buildType(self, inp)
            if ischar(inp)
                self.buildType = inp;
            else
                error('MccHelper:InvalidInput', 'char');
            end
        end
        function self = set.outputDir(self, inp)
            if ischar(inp)
                self.outputDir = inp;
            end
        end
        function self = set.outputFile(self, inp)
            if ischar(inp)
                self.outputFile = inp;
            end
        end
        function self = set.attachments(self, inp)
            if iscellstr(inp)
                self.attachments = inp;
            end
        end
        function self = set.toolboxes(self, inp)
            if iscellstr(inp)
                self.toolboxes = inp;
            end
        end
        function build(self)
            if isempty(self.mFiles)
                error('mccHelper:InvalidInput', 'Must specify at least one Matlab file');
            end
            arglist = [self.mFiles {'-N'}];
            switch lower(self.buildType)
                case 'standalone'
                    arglist = [arglist {'-e'}];
                case 'console'
                    arglist = [arglist {'-m'}];
            end
            if ~isempty(self.outputDir)
                arglist = [arglist {'-d' self.outputDir}];
            end
            if ~isempty(self.outputFile)
                % Use a random name first then move.
                % This is a workaround due to mcc limitation.
                % arglist = [arglist {'-o' self.outputFile}];
                tempOutputFile = regexp(tempname, '[a-z0-9_]*$', 'once', 'match');
                arglist = [arglist {'-o' tempOutputFile}];
            end
            for k = 1:numel(self.attachments)
                arglist = [arglist {'-a' self.attachments{k}}];
            end
            for k = 1:numel(self.toolboxes)
                arglist = [arglist {'-p' toolboxdir(self.toolboxes{k})}];
            end

            mcc(arglist{:});
            if ~isempty(self.outputFile)
                if ispc
                    outputSuffix = '.exe';
                else
                    outputSuffix = '';
                end
                movefile(fullfile(self.outputDir, [tempOutputFile outputSuffix]),...
                    fullfile(self.outputDir, [self.outputFile outputSuffix]));
            end
        end
    end
end
