function T = readtable_str(fname,  varargin)
        %T = readtable(fname);
        T = table;
        
        if ~exist(fname, 'file')
            return;
        end
        
        try
            T = readtable(fname, varargin{:});
        catch
            return;
        end
        
        varnames = T.Properties.VariableNames;

        for k=1:numel(varnames)
            varname = varnames{k};
            varval = T.(varname); 
            if isnumeric(varval)
                varval = arrayfun(@(x)(num2str(x)), varval, 'uniformoutput', false); 
            end
            if ischar(varval)
                varval = {varval};
            end
            T.(varname) = varval;
        end
end

