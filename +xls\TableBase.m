classdef TableBase <OptionsMap    
    properties (Access = protected)
        m_Table;
    end
    
    methods
        function self  = TableBase(varargin)
            self = self@OptionsMap(varargin{:}); 
            self.m_Table = table; 
        end
        
        function CloneTable(self, src)
            if isa(src, 'xls.TableBase')
                self.m_Table = src.GetRawTable();
            end
        end
        
        function ClearTable(self)
            if ~istable(self.m_Table)
                self.m_Table = table;
            else
                self.m_Table(:, :)=[];
            end
        end
        
        function res = NumRows(self)
            res = size(self.m_Table, 1); 
        end
        
        function res= TableUniqueKey(obj)
            res     = obj.getoptioni('table.uniquekey');
        end
        
        function SortRows(obj, varargin)
            obj.m_Table = sortrows(obj.m_Table, varargin{:}); 
        end
        
        function UpdateRows(obj, rowT)
            if isstruct(rowT)
                 rowT = struct2table(rowT,'AsArray', true); 
            end
            
            key     = TableUniqueKey(obj);
            nameA   = obj.GetVariableNames();
            nameB   = rowT.Properties.VariableNames;
            [~, cA, cB] = intersect(nameA, nameB);
            
            varA = obj.m_Table.(key); 
            varB = obj.m_Table.(key); 
            [~, rA, rB] = intersect(varA, varB);
            obj.m_Table{rA, cA} = rowT{rB, cB};  
        end
        
        function SetEmptyVariables(self, varnames, vartypes)
            self.m_Table = self.CreateEmptyTable(varnames, vartypes);
        end
        

        function SetVariableNames(self, names)
            if ~isempty(self.m_Table)
                self.m_Table = self.m_Table(:, names);
            else
                %self.m_Table= table('VariableNames', names);
                self.m_Table = array2table(cell(0,numel(names)), 'VariableNames', names);
            end
        end
        
        function res = GetVariableNames(self)
            try
                res = self.m_Table.Properties.VariableNames;
            catch
                res ='';
            end
        end
        
        function res = isEmpty(self)
            res = isempty(self.m_Table);
        end
        
        function res = GetRawTable(self)
            res = self.m_Table; 
        end
        
         function T = GetMatchRows(self, varargin)
            T = table; 
            N = size(self.m_Table, 1); 
            if N<=0
                return; 
            end
            if isstruct(varargin{1})
                options  = OptionsMap;
                options.fromStruct(varargin{1});
            else
                options  = OptionsMap(varargin{:});
            end
            varnames = GetVariableNames(self);
            keynames = options.getOptionNames;
            flags    = ones(N, 1, 'like', true); 
            for k=1:numel(keynames)
                keyname = keynames{k}; 
                if ~ismember(keyname, varnames)
                    continue;
                end
                keyvalue = options.getOption(keyname); 
                [flags1, loc] = ismember( self.m_Table.(keyname), keyvalue);
                flags = flags & flags1; 
            end
            T = self.m_Table(flags, :); 
        end
        
        function T = GetSubRows(self, keyname, keyvalues)
            try
                [flag, loc] = ismember(keyvalues, self.m_Table.(keyname));
                rows = loc(flag); 
                T = self.m_Table(rows, :); 
            catch
                T = table; 
            end
        end
        
        function T = GetSubRawTable(self, keyname, keyvalues, varnames)
            T = GetSubRows(self, keyname, keyvalues);
            
            if ~isempty(T) && exist('varnames', 'var') && ~isempty(varnames)
                [C, cols, IB] = intersect(T.Properties.VariableNames, varnames);
                 T = T(:, cols); 
            end
        end
        
        function SetRawTable(self, T)
            self.m_Table = T; 
        end
        
        
        function res = SheetName(self)
            res = self.getoptioni('table.sheetname'); 
        end
        
        function flag = LoadTable(self, fname, varargin)
            %self.m_Table = readtable(fname, 'sheet', SheetName(self));
            %self.m_Table = self.table_cellstr(fname, [], 'sheet', SheetName(self));
            flag = false; 
            if ~exist('fname', 'var') || isempty(fname)
                fname = self.TableFileName();
            end
            options = OptionsMap(varargin{:});
            PreserveVariableNames=options.getOption('PreserveVariableNames', false);

            if ~exist(fname, 'file')
                return
            end
            
            sheetname = SheetName(self);
            
            if ~isempty(sheetname)
                names = sheetnames(fname);
                if ~ismember(upper(sheetname), upper(names))
                    return
                end
                self.m_Table = xls.readtable_str(fname, 'sheet', SheetName(self), 'PreserveVariableNames', PreserveVariableNames);
            else
                self.m_Table = xls.readtable_str(fname, 'PreserveVariableNames', PreserveVariableNames);
            end

            
            numericvarnames = self.getoptioni('numericvarnames');
            if strcmp(numericvarnames, 'ALL_NUMERIC')
                numericvarnames=self.m_Table.Properties.VariableNames; 
            end

            if ischar(numericvarnames)
                numericvarnames=strsplit(numericvarnames, '|');
            end
            numericvarnames1 = options.getoptioni('numericvarnames');
            if ischar(numericvarnames1)
                numericvarnames1=strsplit(numericvarnames1, '|');
            end
            
            numericvarnames=cat(2, numericvarnames(:)', numericvarnames1(:)');
            if ~isempty(numericvarnames)
                numericvarnames=intersect(numericvarnames, GetVariableNames(self));
                Convert2Numeric(self,numericvarnames);
                self.setOption('numericvarnames', numericvarnames);
            end
            
            IgnoreVarNames = options.getoptioni('IgnoreVarNames');
            if ~isempty(IgnoreVarNames)
                if ischar(IgnoreVarNames)
                    IgnoreVarNames=strsplit(IgnoreVarNames, '|');
                end
                IgnoreVarNames=intersect(VariableNames(self), IgnoreVarNames);
                if ~isempty(IgnoreVarNames)
                    self.m_Table(:, IgnoreVarNames)=[];
                end
            end
            flag = true; 
        end
        
        % function Convert2Numeric(self,numericvarnames, convertfun)
        %     if ischar(numericvarnames)
        %         numericvarnames = strsplit(numericvarnames, '|');
        %     end
        % 
        %     if ~exist('convertfun', 'var')
        %         convertfun = @str2num; 
        %     end
        % 
        %     for k=1:numel(numericvarnames)
        %         name = numericvarnames{k};
        %         try
        %           var = self.m_Table.(name);
        %           var = cell2mat(cellfun(@(x)convertfun(x), var, 'UniformOutput', false));
        %           self.m_Table.(name) = var;
        %         catch
        %         end
        %     end
        % end
        
        function Convert2Numeric(self,varargin)
            % if ischar(numericvarnames)
            %     numericvarnames = strsplit(numericvarnames, '|');
            % end
            % 
            % if ~exist('convertfun', 'var')
            %     convertfun = @str2num; 
            % end
            % 
            % for k=1:numel(numericvarnames)
            %     name = numericvarnames{k};
            %     try
            %       var = self.m_Table.(name);
            %       var = cell2mat(cellfun(@(x)convertfun(x), var, 'UniformOutput', false));
            %       self.m_Table.(name) = var;
            %     catch
            %     end
            % end
            self.m_Table = self.Convert2NumericVariables(self.m_Table, varargin{:});
        end

        function SaveTable(self, fname, varargin)
            if ~exist('fname', 'var') || isempty(fname)
                fname = self.TableFileName();
            end
            sheet = SheetName(self); 
            if ~isempty(sheet)
                self.WriteTable(self.m_Table, fname, 'sheet', SheetName(self), varargin{:});
            else
                self.WriteTable(self.m_Table, fname,  varargin{:});
            end
        end
        
        function res= TableFileName(obj)
            res     = obj.getoptioni('table.filename');
        end
        
        function T = CalcRowStat(self,  varargin)
            T = self.RowStat(self.m_Table,  varargin{:});
        end
        
        
        function names = VariableNames(self)
            names = self.m_Table.Properties.VariableNames;
        end
        
        
    end
    
    methods (Static)
        function T1 = AddSummaryRow(T, sumvarnames, sumops)
            varnames = T.Properties.VariableNames; 
            sumvarnames=intersect(sumvarnames, varnames, 'stable');
            if ~exist('sumops', 'var')
                sumops = cellfun(@(x)('sum'), sumvarnames, 'uniformoutput', false);
            end
            N = size(T, 1);
            Tnew = T(N, :);
            for k=1:numel(sumvarnames)
                sumop = sumops{k};
                varname = sumvarnames{k}; 
                switch sumop
                    case 'sum'
                        Tnew{1,varname} = sum(T.(varname), 1);
                    case 'mean'
                        Tnew{1,varname} = mean(T.(varname), 1);
                    case 'min'
                        Tnew{1,varname} = min(T.(varname), [], 1);
                    case 'max'
                        Tnew{1,varname} = max(T.(varname), [], 1);
                    case 'first'
                        Tnew{1,varname} = T{1, varname};
                    case 'last'
                        Tnew{1,varname} = T{N, varname};
                end
            end
            nanvars = setdiff(varnames, sumvarnames);
            for k=1:numel(nanvars)
                varname = nanvars{k};
                if iscell(Tnew{1,varname})
                    Tnew{1,varname}={''};
                elseif isnumeric(Tnew{1,varname})
                    Tnew{1,varname}=NaN(size(Tnew{1,varname}));
                end
            end
            T1 = [T; Tnew];
        end
        
        function T = table_cellstr(T, varnames, varargin)
            %T = readtable(fname);
            if ischar(T)
                T = readtable(T, varargin{:});
            end
            
            if ~exist('varnames', 'var') || isempty(varnames)
                varnames = T.Properties.VariableNames;
            end
            
            for k=1:numel(varnames)
                varname = varnames{k};
                varval = T.(varname); 
                if isnumeric(varval)
                    varval = arrayfun(@(x)(num2str(x)), varval, 'uniformoutput', false); 
                end
                if ischar(varval)
                    varval = {varval};
                end
                T.(varname) = varval;
            end
        end
        
        function [rawT, T] = ReadTable(fname, varargin)
            T=xls.TableBase({'table.filename', fname}, varargin{:});
            T.LoadTable([], varargin{:});
            rawT=T.GetRawTable();
        end
        
        function WriteTable(T0, fname, varargin)
             if isempty(T0)
                 return; 
             end
             varnames = T0.Properties.VariableNames;
             for k=1:numel(varnames)
                 varname = varnames{k};
                 val = T0.(varname);
                 if isnumeric(val)||islogical(val) 
                     if size(val, 2)>1
                        val = num2str(val);
                     elseif size(val, 2)==0
                        val = NaN([size(val, 1), 1]); 
                     end
                 elseif iscell(val)
                     for m=1:numel(val)
                         val{m} = xls.TableBase.Content2Str(val{m});
                     end
                 end
                 
                 T0.(varname) = val; 
             end
             %writetable(T0, fname,  varargin{:}); 
             try
                 [flag, loc]=ismember('sheet', varargin);
                 if flag && mod(loc, 2)==1 && loc+1<=numel(varargin)
                     varargin{loc+1}=xls.TableBase.convertToExcelSheetName(varargin{loc+1});
                 end
             catch
             end
             if verLessThan('Matlab','9.8')
                 writetable(T0, fname, varargin{:}); 
             else
                 writetable(T0, fname, "WriteMode", "overwritesheet", varargin{:}); 
             end
        end
         
         function val = Content2Str(val)
            if isempty(val)
                val =''; return
            end
            if isnumeric(val)||islogical(val)
                 if size(val, 2)==1
                     val=val';
                 end
                 val = num2str(val); 
            elseif iscell(val) && ~iscell(val{1})
                str = [];
                M = numel(val);
                for m=1:M
                    str = cat(2, str, num2str(val{m}));
                    if m<M
                        str = cat(2, str,'|');
                    end
                end
                val = str;
            elseif iscell(val)
                str = [];
                M = numel(val);
                for m=1:M
                    str = cat(2, str, xls.TableBase.Content2Str(val{m}));
                    if m<M
                        str = cat(2, str,';');
                    end
                end
                val = str;
            end
         end
        
         %to replace Content2Str
        function val = Content2StrX(val, catchar)
            try
            if isempty(val)
                val =''; return
            end
            if ~exist('catchar', 'var')
                catchar = '|'; 
            end
            if isnumeric(val)||islogical(val)
                 val = num2str(val); 
            elseif iscell(val) && ~iscell(val{1})
                str = [];
                M = numel(val);
                for m=1:M
                    str = cat(2, str, num2str(val{m}));
                    if m<M
                        str = cat(2, str,catchar);
                    end
                end
                val = str;
            elseif iscell(val)
                str = [];
                M = numel(val);
                for m=1:M
                    str = cat(2, str, xls.TableBase.Content2StrX(val{m}), catchar);
                    if m<M
                        str = cat(2, str,';');
                    end
                end
                val = str;
            end
            catch
                val ='';
            end
        end
        
         function T = RowStat(RawTable, statmodes, varnames)
           T = table;
           if ~exist('varnames', 'var') || isempty(varnames)
                varnames = RawTable.Properties.VariableNames;
           end
           
           N = numel(varnames);
           if ischar(statmodes)
               statmodes = arrayfun(@(x)(statmodes), 1:N, 'uniformoutput', false);
           end
           
           for k=1:N
               res = NaN;
               varname = varnames{k};
               try
                   val = RawTable.(varname);
                   if isnumeric(val)
                       switch(statmodes{k})
                           case 'mean'
                               res = mean(val, 1);
                           case 'sum'
                               res = sum(val, 1);
                           case 'std'
                               res = std(val, 1);
                           case 'min'
                               res = min(val, 1);
                           case 'max'
                               res = max(val, 1);
                           case 'median'
                               res = median(val, 1);
                       end
                   end
               catch
               end
               T.(varname) = res; 
           end
         end
        
         
         function [I, T] = FilterVariable(T0, VariableName, VariableValue)
            sites = T0.(VariableName); 
            if iscell(VariableValue)
                VariableValue = xls.TableBase.regexp_wordwrap(VariableValue);
            end
            a= regexpi(sites', VariableValue);
            I = cellfun(@(x)(~isempty(x)), a); %2396 , number of H&N patients
            if nargout>1
                T = T0(I, :); 
            end
        end
         
        function T = ReplaceVarible(T0, varname, options)           
            optnames = options.getOptionNames;
            T = T0; 
            varval = T0.(varname); 
            for k=1:numel(optnames)
                name = optnames{k}; 
                val  = options.getoptioni(name); 
                I    = xls.TableBase.FilterVariable(T0, varname, val);
                varval(I)= {name}; 
            end
            T.(varname) = varval; 
        end  
        
         function res = regexp_wordwrap(names)
            left = '(\s|\-|_|^)'; 
            right = '(\s|\-|_|$)';
            if ~iscell(names)
                names = {names}; 
            end
            k=1;
            res = ['(' left  names{k} right ')'];
            for k=2:numel(names)
                res = cat(2, res, '|',  ['(' left  names{k} right ')']);  
            end
         end
         
         function [T, uids] = joinrep(T0, T1, key)
%              varnames = intersect(T0.Properties.VariableNames, T1.Properties.VariableNames);
%              if ~ismember(key, varnames)
%                  T=T0; return;
%              end
%              varnames = setdiff(varnames,key);
%              for k=1:numel(varnames)
%                  T0.(varnames{k})=[];
%              end
%              T = join(T0, T1, 'keys', key); 
            T = T0; 
            [varnames, C0, C1] = intersect(T0.Properties.VariableNames, T1.Properties.VariableNames);
            if ~ismember(key, varnames)
                return;
            end
%             [uids, R0, R1]     = intersect(T0.(key), T1.(key));
%             if ~isempty(uids)
%                 T(R0, C0) = T1(R1, C1);
%             end
            [I0, R1]     = ismember(T0.(key), T1.(key));
            uids = T0.(key)(I0); 
            for k=1:numel(I0)
                if I0(k)
                    T{k, C0} = T1{R1(k), C1};
                end
            end
         end
         
         %join two table T0 and T1 with common keys
         function T = IntersectJoin(T0, T1, key, commonusefirst)
            if ~exist('commonusefirst', 'var')
               commonusefirst=1;
            end
            [commvars, C0, C1] = intersect(T0.Properties.VariableNames, T1.Properties.VariableNames);
            if ~ismember(key, commvars)
                return;
            end
%             [uids, R0, R1]     = intersect(T0.(key), T1.(key));
%             if ~isempty(uids)
%                 T(R0, C0) = T1(R1, C1);
%             end
            [I0, R1] = ismember(T0.(key), T1.(key)); R1 = R1(I0); 
            T0 = T0(I0, :); T1=T1(R1, :);
            if commonusefirst
                T1=removevars(T1, commvars); 
                T =[T0 T1];
            else
                for k=1:numel(commvars)
                    varname = commvars{k};
                    T0.(varname)=T1.(varname); 
                end
                T1=removevars(T1, commvars); 
                T =[T0 T1];
            end
         end
         
         function rawT = CreateEmptyTable(varnames, vartypes)
              N = numel(varnames); 
              if ischar(vartypes)
                  vartypes = arrayfun(@(x)(vartypes),1:N, 'uniformoutput', false);  
              end
              rawT  = table('Size',[0,N],... 
                    'VariableNames', varnames,...
                    'VariableTypes', vartypes);
         end

         function str = catCell2Str(val, seperator)
               if ~exist('seperator', 'var')
                   seperator = '|';
               end
               
               if ~iscell(val)
                   str = num2str(val); return; 
               end

                str = [];
                M = numel(val);
                for m=1:M
                    str = cat(2, str, num2str(val{m}));
                    if m<M
                        str = cat(2, str,seperator);
                    end
                end
         end

         function T = Convert2NumericVariables(T, numericvarnames, convertfun)
            if ischar(numericvarnames)
                numericvarnames = strsplit(numericvarnames, '|');
            end
            numericvarnames = intersect(numericvarnames, T.Properties.VariableNames); 
            if ~exist('convertfun', 'var')
                convertfun = @str2num; 
            end

            for k=1:numel(numericvarnames)
                name = numericvarnames{k};
                try
                  var = T.(name);
                  var = cell2mat(cellfun(@(x)convertfun(x), var, 'UniformOutput', false));
                  T.(name) = var;
                catch
                end
            end
         end

         function sheetName = convertToExcelSheetName(inputStr)
            % Maximum allowed length for Excel sheet names
            maxLen = 31;
            
            % Illegal characters in Excel sheet names
            illegalChars = '/\?*[]:';
        
            % Replace illegal characters with an underscore
            for i = 1:length(illegalChars)
                inputStr = strrep(inputStr, illegalChars(i), '_');
            end
            
            % Check the length of the input string
            if length(inputStr) > maxLen
                % If too long, take the first character, replace the middle with "~", and end with the last character
                % The resulting length will be maxLen characters
                sheetName = [inputStr(1), '~', inputStr(end-(maxLen-3):end)];
            else
                % If within length limits, use the original (sanitized) string
                sheetName = inputStr;
            end
         end

         function T = GroupTable(rawT, groupvar, copyvarnames, subname, varargin)
             if ~exist("subname", 'var')
                 subname ='';
             end
             options      = OptionsMap(varargin{:}); 
             T = varfun(@(x)(x(1)), rawT, 'GroupingVariables', groupvar, ...
                'InputVariables', copyvarnames);
             for k=1:numel(copyvarnames)
                varname = copyvarnames{k};
                T.Properties.VariableNames{['Fun_' varname]} = varname;
             end
             %subname = options.getoptioni('subname', 'Group');
             T.Properties.VariableNames{'GroupCount'} = [subname 'Count'];
             T=movevars(T, [subname 'Count'], 'after', size(T, 2));
         end

         function [T,I]=FilterTable(T, filter)
             myfilter = xls.TableFilter.Factory(filter);
             [I, T]=myfilter.FilterTable(T);
         end
    end
end
