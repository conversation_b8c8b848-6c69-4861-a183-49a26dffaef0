classdef DBTable_Course < mydb.sqlite.DBTable
    properties
        
    end

    methods
        function obj = DBTable_Course(varargin)    
            <EMAIL>(options, {'dbt.PrimaryKey', 'CourseUID'});
        end
    end

    methods (Static)
        function varnames = TableVaribleNames
             varnames = {'CourseUID', 'PatientID' 'CourseNumber', 'FirstStudyDate', 'LastStudyDate', 'StudyDates'};
             varnames = cat(2, varnames, mydb.sqlite.DBTable.DBTableTimeStampVarNames);
        end
    end
end