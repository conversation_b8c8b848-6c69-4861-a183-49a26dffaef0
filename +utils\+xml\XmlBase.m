classdef XmlBase <OptionsMap
    properties
        m_Xml; 
        m_Data; 
    end
    
    methods
        function obj = XmlBase(varargin)
            obj@OptionsMap(varargin{:}); 
            %obj.ReadXml; 
        end
        
        function ReadXml(self, fname)
            if ~exist('fname', 'var') 
                fname = self.getOption('XmlFileName');                
            end

            if ~exist(fname, 'file')
                return
            end

            self.setOption('XmlFileName', fname);
            self.m_Xml = xml2struct(fname); 
            xmlDataName = self.getOption('XmlDataName', ''); 
            if ~isempty(xmlDataName)
                self.m_Data = simplifystructsfromxml(StructBase.getfieldx(self.m_Xml, xmlDataName));
            end
        end

        function ReadJson(self, fname)
            if ~exist('fname', 'var') 
                fname = self.getOption('JsonFileName');                
            end

            if ~exist(fname, 'file')
                return
            end
            
            self.setOption('JsonFileName', fname);

            self.m_Data =  utils.json.readJson(fname);
        end
        
        function WriteJson(self, fname)
            utils.json.writeJson(self.m_Data, fname);
        end
        
        function folder = getOption_FullPath(self, str)
            folder = self.getOption(str);
            if ~isempty(folder) && ~DosUtil.isabsolutepath(folder)
                folder = DosUtil.toFullFile(folder, [],[pwd '\']);
                self.setOption(str, folder);
            end
        end

        % function SetCKPlanID(self, planid)
        %     self.m_Data.('CKPlanID')=planid; 
        % end
        % 
        % function res = CKPlanID(self)
        %     res = StructBase.getfieldx_default(self.m_Data,'CKPlanID', '');
        % end
        % 
        % function SetPatientID(self, patid)
        %     self.m_Data.('PatientID')=patid; 
        % end
        % 
        % function res = PatientID(self)
        %     res = StructBase.getfieldx_default(self.m_Data,'PatientID', '');
        % end
        % 
        % function res = PatientName(self)
        %     res = StructBase.getfieldx_default(self.m_Data,'PatientName', '');
        % end
        % 
        % function res = PlanName(self)
        %     res = StructBase.getfieldx_default(self.m_Data,'PlanName', '');
        % end
    end
    
    methods(Static)
        function coor = ParseCoor(str)
            coor = [str2double(str.X) str2double(str.Y) str2double(str.Z)];
        end

        function A = ParseTransformMatrix4x4(str)
            A = [...
                    str2double(str.T00) str2double(str.T01) str2double(str.T02) str2double(str.T03);
                    str2double(str.T10) str2double(str.T11) str2double(str.T12) str2double(str.T13);
                    str2double(str.T20) str2double(str.T21) str2double(str.T22) str2double(str.T23);
                    str2double(str.T30) str2double(str.T31) str2double(str.T32) str2double(str.T33)
                ];
        end

        function T = Cell2Table(celldata, varargin)
             if ~iscell(celldata)
                 T = ck.xml.XmlBase.Struct2Table(celldata, varargin{:});
                 return;
             end
             celldata = celldata(:); 
             options  = OptionsMap(varargin{:});
             varNames = options.getOption('TableVariableNames', fieldnames(celldata{1}));
             varTypes = options.getOption('TableVariableTypes');
             K = numel(varNames);
             if isempty(varTypes)
                varTypes = arrayfun(@(x)('double'), 1:K, 'UniformOutput',false);
             end
             cellvarnames = options.getOption('Variables.Cell');
             if ~isempty(cellvarnames)
                 if ischar(cellvarnames)
                     cellvarnames=strsplit(cellvarnames, '|');
                 end
                 I = ismember(varNames, cellvarnames);
                 varTypes(I) = arrayfun(@(x)('cell'), 1:sum(I),'UniformOutput',false);
             end

            M = numel(celldata);
            T = table('Size', [M, K], 'VariableTypes', varTypes, 'VariableNames', varNames);
            %T = table('Size', [M, K], 'VariableNames', varNames);
            for k=1:K
                varName = varNames{k}; 
                varType = varTypes{k}; 
                convertToDouble = @(s) ifelse(isfield(s, varName), str2doubleSafe(s.(varName)), NaN);
                convertToCell   = @(s) ifelse(isfield(s, varName), s.(varName), '');
                if strcmpi(varType, 'double')
                    T.(varName) = cellfun(convertToDouble, celldata);
                elseif strcmpi(varType, 'cell')
                    T.(varName) = cellfun(convertToCell, celldata, 'UniformOutput',false);
                end
            end
        end

        function T = Struct2Table(celldata, varargin)
             celldata = celldata(:); 
             options  = OptionsMap(varargin{:});
             varNames = options.getOption('TableVariableNames', fieldnames(celldata(1)));
             varTypes = options.getOption('TableVariableTypes');
             K = numel(varNames);
             if isempty(varTypes)
                varTypes = arrayfun(@(x)('double'), 1:K, 'UniformOutput',false);
             end
             cellvarnames = options.getOption('Variables.Cell');
             if ~isempty(cellvarnames)
                 if ischar(cellvarnames)
                     cellvarnames=strsplit(cellvarnames, '|');
                 end
                 I = ismember(varNames, cellvarnames);
                 varTypes(I) = arrayfun(@(x)('cell'), 1:sum(I),'UniformOutput',false);
             end

            M = numel(celldata);
            T = table('Size', [M, K], 'VariableTypes', varTypes, 'VariableNames', varNames);
            %T = table('Size', [M, K], 'VariableNames', varNames);
            for k=1:K
                varName = varNames{k}; 
                varType = varTypes{k}; 
                convertToDouble = @(s) str2doubleSafe(s.(varName));
                convertToCell   = @(s) s.(varName);
                if strcmpi(varType, 'double')
                    T.(varName) = arrayfun(convertToDouble, celldata);
                elseif strcmpi(varType, 'cell')
                    T.(varName) = arrayfun(convertToCell, celldata, 'UniformOutput',false);
                end
            end
        end

        function structdata=Struct2Numeric(structdata)
            fns = fieldnames(structdata);
            for k=1:numel(fns)

            end
        end
    end
end

% Helper functions
function val = str2doubleSafe(x)
    % Try to convert to double, return NaN if it fails
    if isnumeric(x)
        val = x;
    elseif ischar(x)
        val = str2double(x);
        if isnan(val)
            val = NaN; % If conversion fails, return NaN
        end
    else
        val = NaN; % If it's neither numeric nor char, return NaN
    end
end

function val = str2double3Safe(x)
    % Try to convert to double, return NaN if it fails
    if isnumeric(x)
        val = x;
    elseif ischar(x)
        val = str2num(x);
    else
        val = [NaN NaN NaN]; % If it's neither numeric nor char, return NaN
    end
end

function result = ifelse(cond, trueVal, falseVal)
    % Return trueVal if cond is true, otherwise return falseVal
    if cond
        result = trueVal;
    else
        result = falseVal;
    end
end