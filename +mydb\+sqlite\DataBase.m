classdef DataBase <mydb.sqlite.DBFile
    properties
        m_conn=-1; 
    end
    
    methods
        function obj = DataBase(dbfile, varargin)
            <EMAIL>(dbfile, varargin{:});
            obj.setOption('dbfilename', dbfile);
            
            if ~isempty(dbfile)
                obj.OpenDB();
            end
        end
        
        function dbT = GetDBTable(self, tablename)
            dbT = self.getoptioni(['DBTable.' tablename]); 
            if isempty(dbT) && ExistTable(self, tablename)
                dbT = mydb.sqlite.DBTable({'sqlite.DBConnection',self.m_conn},...
                    {'table_name', tablename});
                self.setOption(['DBTable.' tablename], dbT); 
            end
        end
        
        function T = QueryDBTable(self, tablename, varargin)
            T =[];
            dbT = GetDBTable(self, tablename); 
            if ~isempty(dbT)
                T = dbT.Query(varargin{:});
            end
        end
        
        function flag = ExistTable(self, tablename)
            T = ListTables(self);
            if ~isempty(T)
                flag = ismember(upper(tablename), upper(T)); 
            else
                flag = false;
            end
        end
        
        function T = ListTables(self)
            cmd = 'SELECT  name FROM  sqlite_master WHERE type="table" AND name NOT LIKE "sqlite_%"'; 
            res = self.sqlQuery(cmd);
            T   = [];
            if ~isempty(res)
                T   = {res(:).name};
            end
        end
        
        function InsertMatlabTable(self, T, tablename, varargin)
            dbT = self.GetDBTable(tablename);            
            dbT.InsertMatlabTable(T);
        end
        
        function res = DBFileName(self)
            res = self.getoptioni('dbfilename');
        end
        
        function OpenDB(self)
            self.CloseDB(); 
            conn = self.getoptioni_numeric('DBConnection', 1);
            self.m_conn = mksqlite(conn, 'open', DBFileName(self));
            self.LogInfo(['open connection to db file--' DBFileName(self)]);
        end
        
        function CloseDB(self)
            if self.m_conn>0
                mksqlite(self.m_conn, 'close');
                self.LogInfo(['close connection to db file--' DBFileName(self)]);
            end
            
            self.m_conn = -1; 
        end
        
        function res = sqlQuery(self, sqlcmd)
            res = mksqlite(self.m_conn, sqlcmd);
        end
        
        function res   = DeleteTableRows(self, tablename, conditionstr)
            dbT = GetDBTable(self, tablename);
            res = [];
            if ~isempty(dbT)
                res = dbT.DeleteRows(conditionstr);
            end
        end

        function Export2Xls(self, task)
            tbnames = StructBase.getfieldx_default(task, 'ExportTableNames', self.ListTables);
            for k=1:numel(tbnames)
                tbname = tbnames{k};
                dbT = GetDBTable(self, tbname);
                if ~isempty(dbT)
                    dbT.Export2Xls(task);
                end
            end
        end
    end
    
    methods(Static)
        function CloseAll()
            mksqlite(0, 'close');
        end
        
        function T = ReadDBTable(dbfile, tablename)
            db = mydb.sqlite.DataBase(dbfile);
            T = db.QueryDBTable(tablename); 
            db.CloseDB();
        end
    end
end

