classdef SqlDataBase <OptionsWithLog
    properties
        m_conn=[]; 
    end
    
    methods
        function obj = SqlDataBase(cfg, varargin)
            obj@OptionsWithLog('SqlDataBase', varargin{:});
            if nargin>0 
                if ischar(cfg)
                    cfg = utils.json.readJson(cfg); 
                end
                obj.setOption('database.cfg', cfg);
                if ~isempty(cfg)
                    obj.OpenDB();
                end
           end     
        end
        
        function dbT = GetDBTable(self, tablename)
            dbT = self.getoptioni(['DBTable.' tablename]); 
            if isempty(dbT) && ExistTable(self, tablename)
                dbT = mydb.sqlite.DBTable({'sqlite.DBConnection',self.m_conn},...
                    {'table_name', tablename});
                self.setOption(['DBTable.' tablename], dbT); 
            end
        end
        
        function T = QueryDBTable(self, tablename, varargin)
            T =[];
            dbT = GetDBTable(self, tablename); 
            if ~isempty(dbT)
                T = dbT.Query(varargin{:});
            end
        end
        
        function flag = ExistTable(self, tablename)
            T = ListTables(self);
            if ~isempty(T)
                flag = ismember(upper(tablename), upper(T)); 
            else
                flag = false;
            end
        end
        
        function T = ListTables(self)
            cmd = 'SELECT  name FROM  sqlite_master WHERE type="table" AND name NOT LIKE "sqlite_%"'; 
            res = self.sqlQuery(cmd);
            T   = [];
            if ~isempty(res)
                T   = {res(:).name};
            end
        end
        
        function InsertMatlabTable(self, T, tablename, varargin)
            dbT = self.GetDBTable(tablename);            
            dbT.InsertMatlabTable(T);
        end
        
        function res = DBFileName(self)
            res = self.getoptioni('dbfilename');
        end
        
        function OpenDB(self)
            cfg = self.getOption('database.cfg');
%             self.CloseDB(); 
%             conn = self.getoptioni_numeric('DBConnection', 1);
%             self.m_conn = mksqlite(conn, 'open', DBFileName(self));
%             self.LogInfo(['open connection to db file--' DBFileName(self)]);
            if ~isempty(cfg)
                self.m_conn = self.MakeConnetion(cfg); 
            end
        end
        
        function CloseDB(self)
            if ~isempty(self.m_conn)
               close(self.m_conn); 
            end
            
            self.m_conn = []; 
        end
        
        function res = sqlQuery(self, sqlcmd)
            res = mksqlite(self.m_conn, sqlcmd);
        end
        
        function res   = DeleteTableRows(self, tablename, conditionstr)
            dbT = GetDBTable(self, tablename);
            res = [];
            if ~isempty(dbT)
                res = dbT.DeleteRows(conditionstr);
            end
        end
    end
    
    methods(Static)
        function T = ReadDBTable(dbfile, tablename)
            db = mydb.sqlite.DataBase(dbfile);
            T = db.QueryDBTable(tablename); 
            db.CloseDB();
        end
        
        function conn = MakeConnetion(cfg)
            port = StructBase.getfieldx(cfg, 'port');
            if ~isempty(port)
                jdbcString = sprintf('**************************************', cfg.server, cfg.port, cfg.databasename);
            else
                jdbcString = sprintf('***********************************', cfg.server,  cfg.databasename);
            end
            % Load the JDBC driver
            %javaaddpath('sqljdbc42.jar'); % Path to the Microsoft SQL Server JDBC driver JAR file
            fname = mfilename('fullpath');
            [folder] = fileparts(fname); 
            %JDBCDriverLocation = "C:\Drivers\sqljdbc_9.2\enu\mssql-jdbc-9.2.1.jre8.jar"; 
            
            % Create a database connection
            switch lower(cfg.dbtype)
                case {'mssql', 'microsoft-sql'}
                    JDBCDriverLocation = [folder '\resource\sqljdbc_9.2\enu\mssql-jdbc-9.2.1.jre8.jar'];
                    javaaddpath(JDBCDriverLocation); 
                    driverstr = 'com.microsoft.sqlserver.jdbc.SQLServerDriver';
                case 'mysql'
                    JDBCDriverLocation = [folder '\resource\mysql-connector-j-8.3.0.jar'];
                    javaaddpath(JDBCDriverLocation); 
                    %driverstr = 'com.mysql.jdbc.Driver';
                    driverstr ='com.mysql.cj.jdbc.Driver';
            end
            conn = database(cfg.databasename, cfg.username, cfg.password, driverstr, jdbcString);
        end
    end
end

