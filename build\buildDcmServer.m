function buildDcmServer()
% Build script for adaptive daemon

% Important: RELATIVE path from this function to solution root
solutionRootPath = [fileparts(fullfile(mfilename('fullpath'))) filesep '..' filesep];
% Update and get repository info
hginfo = writeHgInfo(fullfile(solutionRootPath, 'hgversion.txt'));

cd(solutionRootPath)
addpath(genpath(fullfile(solutionRootPath)));

mh = atdutils.MccHelper();

mh.buildType = 'standalone';
mh.mFiles = {'AppDcmServer.m'};
mh.outputDir = fullfile(solutionRootPath, '..', 'bin');
mh.outputFile = sprintf('AppDcmServer.%s-%s.%s-r%s-%s', hginfo('branch'), hginfo('latesttag'), hginfo('latesttagdistance'), hginfo('rev'), hginfo('shortnode'));
mh.attachments = {...
    'hgversion.txt'...
    'dcm4che-core-2.0.26.jar'...
    'log4j-1.2.16.jar'...
    'slf4j-api-1.6.1.jar'...
    'slf4j-log4j12-1.6.1.jar'};
mh.toolboxes = {'images'};

mh.build();
