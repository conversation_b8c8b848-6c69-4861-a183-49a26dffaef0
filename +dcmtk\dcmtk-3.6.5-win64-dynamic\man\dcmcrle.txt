dcmcrle(1)                        OFFIS DCMTK                       dcmcrle(1)



NAME
       dcmcrle - Encode DICOM file to RLE transfer syntax


SYNOPSIS
       dcmcrle [options] dcmfile-in dcmfile-out

DESCRIPTION
       The  dcmcrle  utility  reads  an uncompressed DICOM image (dcmfile-in),
       performs RLE compression (i.e.  conversion  to  an  encapsulated  DICOM
       transfer  syntax)  and  writes  the  converted  image to an output file
       (dcmfile-out).

PARAMETERS
       dcmfile-in   DICOM input filename to be converted

       dcmfile-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

       input transfer syntax:

         -t=  --read-xfer-auto
                use TS recognition (default)

         -td  --read-xfer-detect
                ignore TS specified in the file meta header

         -te  --read-xfer-little
                read with explicit VR little endian TS

         -tb  --read-xfer-big
                read with explicit VR big endian TS

         -ti  --read-xfer-implicit
                read with implicit VR little endian TS

   encapsulated pixel data encoding options
       pixel data fragmentation:

         +ff  --fragment-per-frame
                encode each frame as one fragment (default)

         +fs  --fragment-size  [s]ize: integer
                limit fragment size to s kbytes (non-standard)

       basic offset table encoding:

         +ot  --offset-table-create
                create offset table (default)

         -ot  --offset-table-empty
                leave offset table empty

       SOP Class UID:

         +cd  --class-default
                keep SOP Class UID (default)

         +cs  --class-sc
                convert to Secondary Capture Image (implies --uid-always)

       SOP Instance UID:
         +un  --uid-never
                never assign new UID (default)

         +ua  --uid-always
                always assign new UID

   output options
       post-1993 value representations:

         +u   --enable-new-vr
                enable support for new VRs (UN/UT) (default)

         -u   --disable-new-vr
                disable support for new VRs, convert to OB

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

       data set trailing padding:

         -p=  --padding-retain
                do not change padding (default)

         -p   --padding-off
                no padding

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

NOTES
       The dcmcrle utility compresses DICOM images  of  all  SOP  classes.  It
       processes  all  Pixel  Data  (7fe0,0010)  elements in the dataset, i.e.
       compression is also performed on an icon image.

       Please note that the DICOM standard does  not  allow  for  storing  the
       pixel  data  with multiple fragments per frame (when RLE compression is
       used). So limiting the fragment size with  option  --fragment-size  (or
       +fs) may result in a non-standard compliant DICOM image.

TRANSFER SYNTAXES
       dcmcrle  supports  the  following transfer syntaxes for input (dcmfile-
       in):

       LittleEndianImplicitTransferSyntax             1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax             1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax   1.2.840.10008.******** (*)
       BigEndianExplicitTransferSyntax                1.2.840.10008.1.2.2

       (*) if compiled with zlib support enabled

       dcmcrle supports the following transfer syntaxes for  output  (dcmfile-
       out):

       RLELosslessTransferSyntax                      1.2.840.10008.1.2.5

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmcrle utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmdrle(1)

COPYRIGHT
       Copyright  (C)  2002-2018  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcmcrle(1)
