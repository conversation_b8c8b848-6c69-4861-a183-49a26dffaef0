movescu(1)                        OFFIS DCMTK                       movescu(1)



NAME
       movescu - DICOM retrieve (C-MOVE) SCU


SYNOPSIS
       movescu [options] peer port [dcmfile-in...]

DESCRIPTION
       The  movescu  application implements both an SCU for the Query/Retrieve
       Service Class and  an  SCP  for  the  Storage  Service  Class.  movescu
       supports  retrieve  functionality  using  the  C-MOVE message. It sends
       query keys to an SCP and awaits responses. It will accept  associations
       for  the  purpose  of  receiving  images sent as a result of the C-MOVE
       request. The application can be used to test SCPs of the Query/Retrieve
       Service  Class.  The  movescu  application can initiate the transfer of
       images to a third party or can retrieve images to itself. Note that the
       use  of  the  term  'move' is a misnomer. The C-MOVE operation actually
       performs an image copy (no images will be deleted from the SCP).

PARAMETERS
       peer        hostname of DICOM peer

       port        tcp/ip port number of peer

       dcmfile-in  DICOM query file(s)

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

   network options
       override matching keys:

         -k    --key  [k]ey: gggg,eeee="str" or dictionary name="str"
                 override matching key

       query information model:

         -P    --patient
                 use patient root information model (default)

         -S    --study
                 use study root information model

         -O    --psonly
                 use patient/study only information model

       application entity titles:

         -aet  --aetitle  [a]etitle: string
                 set my calling AE title (default: MOVESCU)

         -aec  --call  [a]etitle: string
                 set called AE title of peer (default: ANY-SCP)

         -aem  --move  [a]etitle: string
                 set move destination AE title (default: MOVESCU)

       preferred network transfer syntaxes (incoming associations):

         +x=   --prefer-uncompr
                 prefer explicit VR local byte order (default)

         +xe   --prefer-little
                 prefer explicit VR little endian TS

         +xb   --prefer-big
                 prefer explicit VR big endian TS

         +xs   --prefer-lossless
                 prefer default JPEG lossless TS

         +xy   --prefer-jpeg8
                 prefer default JPEG lossy TS for 8 bit data

         +xx   --prefer-jpeg12
                 prefer default JPEG lossy TS for 12 bit data

         +xv   --prefer-j2k-lossless
                 prefer JPEG 2000 lossless TS

         +xw   --prefer-j2k-lossy
                 prefer JPEG 2000 lossy TS

         +xt   --prefer-jls-lossless
                 prefer JPEG-LS lossless TS

         +xu   --prefer-jls-lossy
                 prefer JPEG-LS lossy TS

         +xm   --prefer-mpeg2
                 prefer MPEG2 Main Profile @ Main Level TS

         +xh   --prefer-mpeg2-high
                 prefer MPEG2 Main Profile @ High Level TS

         +xn   --prefer-mpeg4
                 prefer MPEG4 AVC/H.264 High Profile / Level 4.1 TS

         +xl   --prefer-mpeg4-bd
                 prefer MPEG4 AVC/H.264 BD-compatible HP / Level 4.1 TS

         +x2   --prefer-mpeg4-2-2d
                 prefer MPEG4 AVC/H.264 HP / Level 4.2 TS for 2D Videos

         +x3   --prefer-mpeg4-2-3d
                 prefer MPEG4 AVC/H.264 HP / Level 4.2 TS for 3D Videos

         +xo   --prefer-mpeg4-2-st
                 prefer MPEG4 AVC/H.264 Stereo HP / Level 4.2 TS

         +x4   --prefer-hevc
                 prefer HEVC H.265 Main Profile / Level 5.1 TS

         +x5   --prefer-hevc10
                 prefer HEVC H.265 Main 10 Profile / Level 5.1 TS

         +xr   --prefer-rle
                 prefer RLE lossless TS

         +xd   --prefer-deflated
                 prefer deflated explicit VR little endian TS

         +xi   --implicit
                 accept implicit VR little endian TS only

         +xa   --accept-all
                 accept all supported transfer syntaxes

       proposed transmission transfer syntaxes (outgoing associations):

         -x=   --propose-uncompr
                 propose all uncompressed TS, explicit VR
                 with local byte ordering first (default)

         -xe   --propose-little
                 propose all uncompressed TS, explicit VR little endian first

         -xb   --propose-big
                 propose all uncompressed TS, explicit VR big endian first

         -xd   --propose-deflated
                 propose deflated explicit VR little endian TS
                 and all uncompressed transfer syntaxes

         -xi   --propose-implicit
                 propose implicit VR little endian TS only

       network host access control (tcp wrapper):

         -ac   --access-full
                 accept connections from any host (default)

         +ac   --access-control
                 enforce host access control rules

       port for incoming network associations:

               --no-port
                 no port for incoming associations (default)

         +P    --port  [n]umber: integer
                 port number for incoming associations

       handling of illegal datasets following 'pending' move responses:

         -pi   --pending-ignore
                 assume no dataset present (default)

         -pr   --pending-read
                 read and ignore dataset

       other network options:

         -to   --timeout  [s]econds: integer (default: unlimited)
                 timeout for connection requests

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes (default: 16384)

         -dhl  --disable-host-lookup
                 disable hostname lookup

               --repeat  [n]umber: integer
                 repeat n times

               --abort
                 abort association instead of releasing it

               --ignore
                 ignore store data, receive but do not store

               --cancel  [n]umber: integer
                 cancel after n responses (default: never)

         -up   --uid-padding
                 silently correct space-padded UIDs

   output options
       general:

         -od   --output-directory  [d]irectory: string (default: ".")
                 write received objects to existing directory d

       bit preserving mode:

         -B    --normal
                 allow implicit format conversions (default)

         +B    --bit-preserving
                 write data exactly as read

       output file format:

         +F    --write-file
                 write file format (default)

         -F    --write-dataset
                 write data set without file meta information

       output transfer syntax
       (not with --bit-preserving or compressed transmission):

         +t=   --write-xfer-same
                 write with same TS as input (default)

         +te   --write-xfer-little
                 write with explicit VR little endian TS

         +tb   --write-xfer-big
                 write with explicit VR big endian TS

         +ti   --write-xfer-implicit
                 write with implicit VR little endian TS

         +td   --write-xfer-deflated
                 write with deflated explicit VR little endian TS

       post-1993 value representations (not with --bit-preserving):

         +u    --enable-new-vr
                 enable support for new VRs (UN/UT) (default)

         -u    --disable-new-vr
                 disable support for new VRs, convert to OB

       group length encoding (not with --bit-preserving):

         +g=   --group-length-recalc
                 recalculate group lengths if present (default)

         +g    --group-length-create
                 always write with group length elements

         -g    --group-length-remove
                 always write without group length elements

       length encoding in sequences and items (not with --bit-preserving):

         +e    --length-explicit
                 write with explicit lengths (default)

         -e    --length-undefined
                 write with undefined lengths

       data set trailing padding
       (not with --write-dataset or --bit-preserving):

         -p    --padding-off
                 no padding (default)

         +p    --padding-create  [f]ile-pad [i]tem-pad: integer
                 align file on multiple of f bytes and items on
                 multiple of i bytes

       deflate compression level
       (only with --propose-deflated or --write-xfer-deflated/same):

         +cl   --compression-level  [l]evel: integer (default: 6)
                 0=uncompressed, 1=fastest, 9=best compression

NOTES
       Each file supplied on the command line will be sent to the SCP as  part
       of  a  C-MOVE  request.  The  query file must be a valid DICOM data set
       containing the dataset part of a  C-MOVE-RQ  message.  The  query  file
       could, for instance, be created with the dump2dcm utility from a script
       like the following example:

       # request all images for the patient with ID=PAT001
       (0008,0052) CS [PATIENT]     # QueryRetrieveLevel
       (0010,0020) LO [PAT001]      # PatientID

       Individual attributes of each file sent can be modified or supplemented
       using the -k (or --key) option. For example the command:

       movescu -k 0010,0020=PAT002 caesar 5678 patqry.dcm

       will,  when  sent  to  the  SCP  caesar  at TCP/IP port 5678, cause any
       PatientID attribute in patqry.dcm to have the value 'PAT002'.  If  such
       an  attribute  is  present  it  will  be replaced, if absent it will be
       inserted. The -k option can be present more than once. The  value  part
       (after  the  '=')  may  be absent causing the attribute to be sent with
       zero length. It is not possible to replace or insert attributes  within
       sequences using the -k option.

       If  no  file  is  specified  on  the  command  line,  the query must be
       specified completely with one or more -k  options.  If  multiple  query
       files  are  provided, movescu will send multiple C-MOVE requests to the
       SCP.

       For all uses of movescu, the SCP must be configured to 'know' about the
       AE title where the images are to be transmitted. This is required since
       the C-MOVE request only contains the AE Title of the target and the SCP
       must  be  able  to  convert  this AE Title into TCP/IP address and port
       number  in  order  to  actually  send  the  images  over   a   separate
       association.

       The  C-MOVE  operation  of  the  Query/Retrieve  Service  Class is able
       retrieve images (when option --port is used) or to initiate a  copy  of
       images to a third party.

   Known Problems
       movescu  is  intended  to  be used as a testing tool for DICOM software
       developers. The query keys file must be created by hand based upon  the
       contents of the SCP.

       The  movescu application makes no attempt to prevent incorrect queries.
       In particular, the query keys of a C-MOVE request should  only  contain
       the  QueryRetrieveLevel  attribute  and  one  or  more of the so-called
       'unique key attributes' (PatientID, StudyInstanceUID, SeriesInstanceUID
       and SOPInstanceUID).

       In case the storage client does not end the connection while movescu is
       playing the role of the storage server (i. e. the --port is  used)  but
       the   client   instead  expects  movescu  to  close  down  the  storage
       connection, movescu will wait infinitely. In  that  case  movescu  will
       neither close the association that was used to issue the C-MOVE request
       to the C-MOVE server.

   DICOM Conformance
   SCU Conformance
       The movescu application supports the following SOP Classes as an SCU:

       MOVEPatientRootQueryRetrieveInformationModel         1.2.840.10008.*******.2.1.2
       MOVEStudyRootQueryRetrieveInformationModel           1.2.840.10008.*******.2.2.2
       MOVEPatientStudyOnlyQueryRetrieveInformationModel    1.2.840.10008.*******.2.3.2

       The movescu application will propose presentation contexts for  one  of
       the  abovementioned supported SOP Classes depending on the command line
       options (-P, -S, or -O). It will also  propose  the  corresponding  SOP
       Class  from the following list, although it is not really used (this is
       a relict of the RSNA'93 demonstration):

       FINDPatientRootQueryRetrieveInformationModel         1.2.840.10008.*******.2.1.1
       FINDStudyRootQueryRetrieveInformationModel           1.2.840.10008.*******.2.2.1
       FINDPatientStudyOnlyQueryRetrieveInformationModel    1.2.840.10008.*******.2.3.1

       For  outgoing  associations,  the  following  transfer   syntaxes   are
       supported:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax         1.2.840.10008.1.2.1.99 (*)
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2

       (*) if compiled with zlib support enabled (see --version output)

       Which  transfer  syntaxes  are  actually proposed in what order, can be
       specified with the --propose options.

   SCP Conformance
       The movescu application supports the following SOP Classes as an SCP:

       VerificationSOPClass                                 1.2.840.10008.1.1

       RETIRED_StoredPrintStorage                           1.2.840.10008.5.1.1.27
       RETIRED_HardcopyGrayscaleImageStorage                1.2.840.10008.5.1.1.29
       RETIRED_HardcopyColorImageStorage                    1.2.840.10008.5.1.1.30
       ComputedRadiographyImageStorage                      1.2.840.10008.*******.1.1
       DigitalXRayImageStorageForPresentation               1.2.840.10008.*******.1.1.1
       DigitalXRayImageStorageForProcessing                 1.2.840.10008.*******.1.1.1.1
       DigitalMammographyXRayImageStorageForPresentation    1.2.840.10008.*******.1.1.2
       DigitalMammographyXRayImageStorageForProcessing      1.2.840.10008.*******.1.1.2.1
       DigitalIntraOralXRayImageStorageForPresentation      1.2.840.10008.*******.1.1.3
       DigitalIntraOralXRayImageStorageForProcessing        1.2.840.10008.*******.1.1.3.1
       CTImageStorage                                       1.2.840.10008.*******.1.2
       EnhancedCTImageStorage                               1.2.840.10008.*******.1.2.1
       LegacyConvertedEnhancedCTImageStorage                1.2.840.10008.*******.1.2.2
       RETIRED_UltrasoundMultiframeImageStorage             1.2.840.10008.*******.1.3
       UltrasoundMultiframeImageStorage                     1.2.840.10008.*******.1.3.1
       MRImageStorage                                       1.2.840.10008.*******.1.4
       EnhancedMRImageStorage                               1.2.840.10008.*******.1.4.1
       MRSpectroscopyStorage                                1.2.840.10008.*******.1.4.2
       EnhancedMRColorImageStorage                          1.2.840.10008.*******.1.4.3
       LegacyConvertedEnhancedMRImageStorage                1.2.840.10008.*******.1.4.4
       RETIRED_NuclearMedicineImageStorage                  1.2.840.10008.*******.1.5
       RETIRED_UltrasoundImageStorage                       1.2.840.10008.*******.1.6
       UltrasoundImageStorage                               1.2.840.10008.*******.1.6.1
       EnhancedUSVolumeStorage                              1.2.840.10008.*******.1.6.2
       SecondaryCaptureImageStorage                         1.2.840.10008.*******.1.7
       MultiframeSingleBitSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.1
       MultiframeGrayscaleByteSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.2
       MultiframeGrayscaleWordSecondaryCaptureImageStorage  1.2.840.10008.*******.1.7.3
       MultiframeTrueColorSecondaryCaptureImageStorage      1.2.840.10008.*******.1.7.4
       RETIRED_StandaloneOverlayStorage                     1.2.840.10008.*******.1.8
       RETIRED_StandaloneCurveStorage                       1.2.840.10008.*******.1.9
       DRAFT_WaveformStorage                                1.2.840.10008.*******.1.9.1
       TwelveLeadECGWaveformStorage                         1.2.840.10008.*******.1.9.1.1
       GeneralECGWaveformStorage                            1.2.840.10008.*******.1.9.1.2
       AmbulatoryECGWaveformStorage                         1.2.840.10008.*******.1.9.1.3
       HemodynamicWaveformStorage                           1.2.840.10008.*******.1.9.2.1
       CardiacElectrophysiologyWaveformStorage              1.2.840.10008.*******.1.9.3.1
       BasicVoiceAudioWaveformStorage                       1.2.840.10008.*******.1.9.4.1
       GeneralAudioWaveformStorage                          1.2.840.10008.*******.1.9.4.2
       ArterialPulseWaveformStorage                         1.2.840.10008.*******.1.9.5.1
       RespiratoryWaveformStorage                           1.2.840.10008.*******.1.9.6.1
       RETIRED_StandaloneModalityLUTStorage                 1.2.840.10008.*******.1.10
       RETIRED_StandaloneVOILUTStorage                      1.2.840.10008.*******.1.11
       GrayscaleSoftcopyPresentationStateStorage            1.2.840.10008.*******.1.11.1
       ColorSoftcopyPresentationStateStorage                1.2.840.10008.*******.1.11.2
       PseudoColorSoftcopyPresentationStateStorage          1.2.840.10008.*******.1.11.3
       BlendingSoftcopyPresentationStateStorage             1.2.840.10008.*******.1.11.4
       XAXRFGrayscaleSoftcopyPresentationStateStorage       1.2.840.10008.*******.1.11.5
       GrayscalePlanarMPRVolumetricPresentationStateStorage 1.2.840.10008.*******.1.11.6
       CompositingPlanarMPRVolumetricPresent.StateStorage   1.2.840.10008.*******.1.11.7
       AdvancedBlendingPresentationStateStorage             1.2.840.10008.*******.1.11.8
       VolumeRenderingVolumetricPresentationStateStorage    1.2.840.10008.*******.1.11.9
       SegmentedVolumeRenderingVolumetricPres.StateStorage  1.2.840.10008.*******.1.11.10
       MultipleVolumeRenderingVolumetricPres.StateStorage   1.2.840.10008.*******.1.11.11
       XRayAngiographicImageStorage                         1.2.840.10008.*******.1.12.1
       EnhancedXAImageStorage                               1.2.840.10008.*******.********
       XRayRadiofluoroscopicImageStorage                    1.2.840.10008.*******.1.12.2
       EnhancedXRFImageStorage                              1.2.840.10008.*******.********
       RETIRED_XRayAngiographicBiPlaneImageStorage          1.2.840.10008.*******.1.12.3
       XRay3DAngiographicImageStorage                       1.2.840.10008.*******.********
       XRay3DCraniofacialImageStorage                       1.2.840.10008.*******.********
       BreastTomosynthesisImageStorage                      1.2.840.10008.*******.********
       BreastProjectionXRayImageStorageForPresentation      1.2.840.10008.*******.********
       BreastProjectionXRayImageStorageForProcessing        1.2.840.10008.*******.********
       IntravascularOpt.Coh.Tom.ImageStorageForPresentation 1.2.840.10008.*******.1.14.1
       IntravascularOpt.Coh.Tom.ImageStorageForProcessing   1.2.840.10008.*******.1.14.2
       NuclearMedicineImageStorage                          1.2.840.10008.*******.1.20
       ParametricMapStorage                                 1.2.840.10008.*******.1.30
       RawDataStorage                                       1.2.840.10008.*******.1.66
       SpatialRegistrationStorage                           1.2.840.10008.*******.1.66.1
       SpatialFiducialsStorage                              1.2.840.10008.*******.1.66.2
       DeformableSpatialRegistrationStorage                 1.2.840.10008.*******.1.66.3
       SegmentationStorage                                  1.2.840.10008.*******.1.66.4
       SurfaceSegmentationStorage                           1.2.840.10008.*******.1.66.5
       TractographyResultsStorage                           1.2.840.10008.*******.1.66.6
       RealWorldValueMappingStorage                         1.2.840.10008.*******.1.67
       SurfaceScanMeshStorage                               1.2.840.10008.*******.1.68.1
       SurfaceScanPointCloudStorage                         1.2.840.10008.*******.1.68.2
       RETIRED_VLImageStorage                               1.2.840.10008.*******.1.77.1
       VLEndoscopicImageStorage                             1.2.840.10008.*******.1.77.1.1
       VideoEndoscopicImageStorage                          1.2.840.10008.*******.1.77.1.1.1
       VLMicroscopicImageStorage                            1.2.840.10008.*******.1.77.1.2
       VideoMicroscopicImageStorage                         1.2.840.10008.*******.1.77.1.2.1
       VLSlideCoordinatesMicroscopicImageStorage            1.2.840.10008.*******.********
       VLPhotographicImageStorage                           1.2.840.10008.*******.********
       VideoPhotographicImageStorage                        1.2.840.10008.*******.********.1
       OphthalmicPhotography8BitImageStorage                1.2.840.10008.*******.********.1
       OphthalmicPhotography16BitImageStorage               1.2.840.10008.*******.********.2
       StereometricRelationshipStorage                      1.2.840.10008.*******.********.3
       OphthalmicTomographyImageStorage                     1.2.840.10008.*******.********.4
       WideFieldOphthalmicPhoto.Stereogr.Proj.ImageStorage  1.2.840.10008.*******.********.5
       WideFieldOphthalmicPhotogr.3DCoordinatesImageStorage 1.2.840.10008.*******.********.6
       OphthalmicOpticalCoherenceTomogr.EnFaceImageStorage  1.2.840.10008.*******.********.7
       OphthalmicOpticalCoh.Tomogr.BscanVolumeAnalysisStor. 1.2.840.10008.*******.********.8
       VLWholeSlideMicroscopyImageStorage                   1.2.840.10008.*******.********
       RETIRED_VLMultiframeImageStorage                     1.2.840.10008.*******.1.77.2
       LensometryMeasurementsStorage                        1.2.840.10008.*******.1.78.1
       AutorefractionMeasurementsStorage                    1.2.840.10008.*******.1.78.2
       KeratometryMeasurementsStorage                       1.2.840.10008.*******.1.78.3
       SubjectiveRefractionMeasurementsStorage              1.2.840.10008.*******.1.78.4
       VisualAcuityMeasurementsStorage                      1.2.840.10008.*******.1.78.5
       SpectaclePrescriptionReportStorage                   1.2.840.10008.*******.1.78.6
       OphthalmicAxialMeasurementsStorage                   1.2.840.10008.*******.1.78.7
       IntraocularLensCalculationsStorage                   1.2.840.10008.*******.1.78.8
       MacularGridThicknessAndVolumeReportStorage           1.2.840.10008.*******.1.79.1
       OphthalmicVisualFieldStaticPerimetryMeasurementsSt.  1.2.840.10008.*******.1.80.1
       OphthalmicThicknessMapStorage                        1.2.840.10008.*******.1.81.1
       CornealTopographyMapStorage                          1.2.840.10008.*******.1.82.1
       DRAFT_SRTextStorage                                  1.2.840.10008.*******.1.88.1
       DRAFT_SRAudioStorage                                 1.2.840.10008.*******.1.88.2
       DRAFT_SRDetailStorage                                1.2.840.10008.*******.1.88.3
       DRAFT_SRComprehensiveStorage                         1.2.840.10008.*******.1.88.4
       BasicTextSRStorage                                   1.2.840.10008.*******.1.88.11
       EnhancedSRStorage                                    1.2.840.10008.*******.1.88.22
       ComprehensiveSRStorage                               1.2.840.10008.*******.1.88.33
       Comprehensive3DSRStorage                             1.2.840.10008.*******.1.88.34
       ExtensibleSRStorage                                  1.2.840.10008.*******.1.88.35
       ProcedureLogStorage                                  1.2.840.10008.*******.1.88.40
       MammographyCADSRStorage                              1.2.840.10008.*******.1.88.50
       KeyObjectSelectionDocumentStorage                    1.2.840.10008.*******.1.88.59
       ChestCADSRStorage                                    1.2.840.10008.*******.1.88.65
       XRayRadiationDoseSRStorage                           1.2.840.10008.*******.1.88.67
       RadiopharmaceuticalRadiationDoseSRStorage            1.2.840.10008.*******.1.88.68
       ColonCADSRStorage                                    1.2.840.10008.*******.1.88.69
       ImplantationPlanSRDocumentStorage                    1.2.840.10008.*******.1.88.70
       AcquisitionContextSRStorage                          1.2.840.10008.*******.1.88.71
       SimplifiedAdultEchoSRStorage                         1.2.840.10008.*******.1.88.72
       PatientRadiationDoseSRStorage                        1.2.840.10008.*******.1.88.73
       PlannedImagingAgentAdministrationSRStorage           1.2.840.10008.*******.1.88.74
       PerformedImagingAgentAdministrationSRStorage         1.2.840.10008.*******.1.88.75
       ContentAssessmentResultsStorage                      1.2.840.10008.*******.1.90.1
       EncapsulatedPDFStorage                               1.2.840.10008.*******.1.104.1
       EncapsulatedCDAStorage                               1.2.840.10008.*******.1.104.2
       EncapsulatedSTLStorage                               1.2.840.10008.*******.1.104.3
       PositronEmissionTomographyImageStorage               1.2.840.10008.*******.1.128
       LegacyConvertedEnhancedPETImageStorage               1.2.840.10008.*******.1.128.1
       RETIRED_StandalonePETCurveStorage                    1.2.840.10008.*******.1.129
       EnhancedPETImageStorage                              1.2.840.10008.*******.1.130
       BasicStructuredDisplayStorage                        1.2.840.10008.*******.1.131
       CTPerformedProcedureProtocolStorage                  1.2.840.10008.*******.1.200.2
       RTImageStorage                                       1.2.840.10008.*******.1.481.1
       RTDoseStorage                                        1.2.840.10008.*******.1.481.2
       RTStructureSetStorage                                1.2.840.10008.*******.1.481.3
       RTBeamsTreatmentRecordStorage                        1.2.840.10008.*******.1.481.4
       RTPlanStorage                                        1.2.840.10008.*******.1.481.5
       RTBrachyTreatmentRecordStorage                       1.2.840.10008.*******.1.481.6
       RTTreatmentSummaryRecordStorage                      1.2.840.10008.*******.1.481.7
       RTIonPlanStorage                                     1.2.840.10008.*******.1.481.8
       RTIonBeamsTreatmentRecordStorage                     1.2.840.10008.*******.1.481.9
       RTPhysicianIntentStorage                             1.2.840.10008.*******.1.481.10
       RTSegmentAnnotationStorage                           1.2.840.10008.*******.1.481.11
       RTRadiationSetStorage                                1.2.840.10008.*******.1.481.12
       CArmPhotonElectronRadiationStorage                   1.2.840.10008.*******.1.481.13
       DICOS_CTImageStorage                                 1.2.840.10008.*******.1.501.1
       DICOS_DigitalXRayImageStorageForPresentation         1.2.840.10008.*******.1.501.2.1
       DICOS_DigitalXRayImageStorageForProcessing           1.2.840.10008.*******.1.501.2.2
       DICOS_ThreatDetectionReportStorage                   1.2.840.10008.*******.1.501.3
       DICOS_2DAITStorage                                   1.2.840.10008.*******.1.501.4
       DICOS_3DAITStorage                                   1.2.840.10008.*******.1.501.5
       DICOS_QuadrupoleResonanceStorage                     1.2.840.10008.*******.1.501.6
       DICONDE_EddyCurrentImageStorage                      1.2.840.10008.*******.1.601.1
       DICONDE_EddyCurrentMultiframeImageStorage            1.2.840.10008.*******.1.601.2
       DRAFT_RTBeamsDeliveryInstructionStorage              1.2.840.100********.34.1
       RTBeamsDeliveryInstructionStorage                    1.2.840.100********.34.7
       RTBrachyApplicationSetupDeliveryInstructionStorage   1.2.840.100********.34.10

       The movescu application will usually accept presentation  contexts  for
       all  of  the  abovementioned  supported  SOP  Classes  using any of the
       following transfer syntaxes:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2

       When acting as an SCP, the movescu  application  will  prefer  transfer
       syntaxes having an explicit encoding over the default implicit transfer
       syntax. If movescu is running on big-endian  hardware  it  will  prefer
       BigEndianExplicit  to  LittleEndianExplicit  transfer  syntax (and vice
       versa). This behavior can be changed with  the  --prefer  options  (see
       above).  Using  option  --accept-all  any  of  the  following  transfer
       syntaxes is supported:

       LittleEndianImplicitTransferSyntax                   1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                   1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax         1.2.840.10008.1.2.1.99 (*)
       BigEndianExplicitTransferSyntax                      1.2.840.10008.1.2.2
       JPEGProcess1TransferSyntax                           1.2.840.10008.1.2.4.50
       JPEGProcess2_4TransferSyntax                         1.2.840.10008.1.2.4.51
       JPEGProcess14SV1TransferSyntax                       1.2.840.10008.1.2.4.70
       JPEGLSLosslessTransferSyntax                         1.2.840.10008.1.2.4.80
       JPEGLSLossyTransferSyntax                            1.2.840.10008.1.2.4.81
       JPEG2000LosslessOnlyTransferSyntax                   1.2.840.10008.1.2.4.90
       JPEG2000TransferSyntax                               1.2.840.10008.1.2.4.91
       MPEG2MainProfileAtMainLevelTransferSyntax            1.2.840.10008.1.2.4.100
       MPEG2MainProfileAtHighLevelTransferSyntax            1.2.840.10008.1.2.4.101
       MPEG4HighProfileLevel4_1TransferSyntax               1.2.840.10008.1.2.4.102
       MPEG4BDcompatibleHighProfileLevel4_1TransferSyntax   1.2.840.10008.1.2.4.103
       MPEG4HighProfileLevel4_2_For2DVideoTransferSyntax    1.2.840.10008.1.2.4.104
       MPEG4HighProfileLevel4_2_For3DVideoTransferSyntax    1.2.840.10008.1.2.4.105
       MPEG4StereoHighProfileLevel4_2TransferSyntax         1.2.840.10008.*********
       HEVCMainProfileLevel5_1TransferSyntax                1.2.840.10008.*********
       HEVCMain10ProfileLevel5_1TransferSyntax              1.2.840.10008.*********
       RLELosslessTransferSyntax                            1.2.840.10008.1.2.5

       (*) if compiled with zlib support enabled (see --version output)

       The movescu application does not support extended negotiation.

   Access Control
       When compiled on Unix platforms with TCP  wrapper  support,  host-based
       access  control  can  be enabled with the --access-control command line
       option. In this case the access control rules defined in  the  system's
       host  access  control  tables  for  movescu  are  enforced. The default
       locations of the host access control tables  are  /etc/hosts.allow  and
       /etc/hosts.deny. Further details are described in hosts_access(5).

EXAMPLES
       movescu --patient --port 9876 --move TEST_AE --call ARCHIVE caesar 104 q.dcm

       sends  the  attributes contained in the DICOM file 'q.dcm' as part of a
       C-MOVE request to application entity ARCHIVE on the host caesar at port
       104 using the Patient Root query model. movescu itself uses the default
       AE title MOVESCU. The C-MOVE request will specify that images should be
       transferred  to  the  destination  AE  title  TEST_AE  (via  the --move
       option).  How  the  SCP  interprets  this  request   depends   on   its
       configuration.   movescu   will   listen  on  part  9876  for  incoming
       associations in order to receive the requested images from the SCP.

       Images can be copied to a third party by using a different  destination
       AE  title (e.g. --move ANOTHER_AE). Of course, the SCP must 'know' this
       AE title and the destination must be acting as an SCP  of  the  Storage
       Service Class (the storescp application can be used for this purpose).

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

EXIT CODES
       The movescu utility uses the following  exit  codes  when  terminating.
       This  enables  the  user  to  check  for the reason why the application
       terminated.

   general
       EXITCODE_NO_ERROR                         0
       EXITCODE_COMMANDLINE_SYNTAX_ERROR         1
       EXITCODE_INSUFFICIENT_PRIVILEGES          2
       EXITCODE_SETUID_FAILED                    3

   output file errors
       EXITCODE_CANNOT_WRITE_OUTPUT_FILE        40
       EXITCODE_INVALID_OUTPUT_DIRECTORY        45

   network errors
       EXITCODE_CANNOT_INITIALIZE_NETWORK       60
       EXITCODE_CANNOT_NEGOTIATE_ASSOCIATION    61
       EXITCODE_CANNOT_CREATE_ASSOC_PARAMETERS  65
       EXITCODE_NO_PRESENTATION_CONTEXT         66
       EXITCODE_CANNOT_CLOSE_ASSOCIATION        67
       EXITCODE_CMOVE_WARNING                   68
       EXITCODE_CMOVE_ERROR                     69

ENVIRONMENT
       The movescu utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       findscu(1), storescp(1), dump2dcm(1)

COPYRIGHT
       Copyright  (C)  1994-2019  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     movescu(1)
