=============================================================================
CERTIFICATION AUTHORITY (CA) CERTIFICATE MANAGEMENT IN DCMTK
=============================================================================

All tools in DCMTK that support TLS encrypted network connections
need to have a list of trusted certificates (typically CA certificates)
that are used in the verification of the peer certificate when
establishing the TLS connection.

There are two ways of managing this list of trusted certificates:
file-based and directory-based. The command line tools in DCMTK
offer the following command line options for this purpose:

  certification authority:
    +cf   --add-cert-file        [c]ertificate filename: string
                                 add certificate file to list of certificates
    +cd   --add-cert-dir         [c]ertificate directory: string
                                 add certificates in d to list of certificates

When using DCMTK at library level, the methods corresponding to these
command line options are DcmTransportLayer::addTrustedCertificateFile()
and DcmTransportLayer::addTrustedCertificateDir().


FILE BASED MANAGEMENT OF TRUSTED CERTIFICATES

The file-based option loads a file and adds all the certificates or CRLs
present in that file into the pool of trusted certificates. File format
is ASCII text containing concatenated PEM certificates and CRLs.


DIRECTORY BASED MANAGEMENT OF TRUSTED CERTIFICATES

The directory-based option specifies a directory containing certificates
and CRLs. These certificates and CRLs are loaded on demand, and cached
in memory once they are loaded.

The directory should contain one certificate or CRL per file in PEM
format, with a file name of the form hash.N for a certificate, or
hash.rN for a CRL. The hash is computed from the subject name (for
certificates) or the issuer name (for CRLs). The hash value can be
obtained by calling the openssl command line tool:

  openssl x509 -hash -noout -in <infile.pem>

where <infile.pem> should be replaced by the filename of the certificate
or CRL.

The .N or .rN suffix is a sequence number that starts at zero, and is
incremented consecutively for each certificate or CRL with the same hash
value. Gaps in the sequence numbers are not supported, it is assumed
that there are no more objects with the same hash beyond the first
missing number in the sequence.

Sequence numbers make it possible for the directory to contain multiple
certificates with same subject name hash value. For example, it is
possible to have in the store several certificates with same subject or
several CRLs with same issuer (and, for example, different validity
period).

Note that the hash algorithm used for subject name hashing changed in
OpenSSL 1.0.0, and all certificate stores have to be rehashed when
moving from OpenSSL 0.9.x to 1.0.0.

Note: This file contains text adapted from the OpenSSL API
documentation, which is available under the OpenSSL license.
The OpenSSL license is reproduced in DCMTK's COPYRIGHT file.
