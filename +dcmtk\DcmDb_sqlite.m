classdef DcmDb_sqlite < dcmtk.DcmDb
% DcmDb DICOM file repository with basic indexing
%
% Purpose:
% DICOM file repository for direct and ease of access.
%
% Features (responsibilities):
% 1. (Auto) store incoming files.
% 2. Validate DICOM file integrity.
% 3. Index tags to database.
% 4. Data consistency between files and indices.
% 5. Clients read only.

    properties (Constant = true)
    end
    
    properties (Access = private)
%         rootPath        % root directory of storage
        conn
        log
    end

    methods
        function self = DcmDb_sqlite(rootPath, varargin)
            self = <EMAIL>(rootPath, varargin{:}); 

            if ~isempty(self.rootPath)
                self.log = org.apache.log4j.Logger.getLogger('DcmDb');
                self.InitDB();
            end
        end
        
        
        function store(self, inpFiles, deleteOnSuccess)
            % Every input file is indexed upon input.
            % Invalid or non-conforming files are skipped.
            % Vectorized insert is necessary for performance.
            if ischar(inpFiles)
                inpFiles = {inpFiles};
            elseif iscellstr(inpFiles)
                % pass
            else
                error('MiniPacs:store:InvalidInput', 'Unknown input type');
            end

            if numel(inpFiles) > 0
                self.log.info(sprintf('Importing %d files', numel(inpFiles)));
            end
            insertInfos = cell(numel(inpFiles), 1);
            for k = 1:numel(inpFiles)
                try
                    inpFile = inpFiles{k};
                    % extract info
                    inpInfo = self.file2info(inpFile);
                    if isempty(inpInfo)
                        self.log.debug(sprintf('Skipped broken image "%s"', inpFile));
                        if deleteOnSuccess; delete(inpFile); end
                        continue;
                    end
                         
                   [fileexist, destFile, destDirectory] =  self.ExistImage(inpInfo);
                   if fileexist
                        if deleteOnSuccess; delete(inpFile); end
                        continue;
                   end
                    
                    % file copy
                    if exist(destDirectory, 'dir') ~= 7 && ~isempty(destDirectory)
                        mkdir(destDirectory);
                    end
                    
                    if ~strcmpi(inpFile, destFile)
                        if deleteOnSuccess
                            movefile(inpFile, destFile, 'f');
                        else
                            copyfile(inpFile, destFile, 'f');
                        end
                    end
                    
                    % save infos to be insert into index
                    insertInfos{k} = inpInfo;
                catch err
                    self.log.warn(sprintf('Skipped importing "%s" due to error', inpFile));
                    self.log.debug(sprintf('%s', oneLineErrorReport(err)));
                end
            end
            % index
            insertInfos(cellfun(@isempty, insertInfos)) = [];
            if ~isempty(insertInfos)
                mksqlite(self.conn, 'BEGIN');
                for k = 1:numel(insertInfos)
                    inpInfo = insertInfos{k};
                    mksqlite(self.conn, struct2sqlInsert(inpInfo, 'image', self.tags.common));
                    mksqlite(self.conn, struct2sqlInsert(inpInfo, inpInfo.Modality, [{'SOPInstanceUID'} self.tags.(inpInfo.Modality)]));
                end
                mksqlite(self.conn, 'COMMIT');
            end

        end
        % function cleanDirectory(self)
        % function ret = query(self, modality, varargin)
%         
%         function res = ExistImage(self, uid)
%           	res = ~isempty(mksqlite(self.conn, sprintf('SELECT SOPInstanceUID FROM image WHERE SOPInstanceUID="%s"', uid)));
%         end
%         
        
        function RefreshDB(self)
            self.CloseDB(); 
            dbfile = DBFileName(self); 
            dstfile = [dbfile '.' datestr(datetime, 'yyyy-mm-dd.HH-MM-SS')];
            movefile(dbfile, dstfile); 
            
            self.InstoreRoot(self.rootPath);
        end
        
        function InstoreRoot(self, folder0, varargin)           
            self.InitDB(); 
            res = DosUtil.rdir(folder0); 
            res(~[res(:).isdir]) =[]; 
            folders = {res(:).name};
            for k=1:numel(folders)
                folder = [folders{k} filesep];
                self.ParsePatient(folder, varargin{:});
            end
        end
        
        
        function ParsePatient(self, folder, deleteOnSuccess)
            if nargin <3
                deleteOnSuccess = 1; 
            end
            res = DosUtil.rdir([folder '**\*.dcm']); 
            inpFiles = {res(:).name};
            ParseFiles(self, inpFiles, deleteOnSuccess);
            if deleteOnSuccess
                removeEmptyDirs(folder);
            end
        end
        
        function ParseFiles(self, inpFiles, deleteOnSuccess)
            mksqlite(self.conn, 'BEGIN');
            for k = 1:numel(inpFiles)
                inpFile = inpFiles{k};
                self.ParseFile(inpFile, deleteOnSuccess)
            end
            mksqlite(self.conn, 'COMMIT');
        end
        
        function ParseFile(self, inpFile, deleteOnSuccess)
            inpInfo = self.file2info(inpFile);
            if self.ExistImage(inpInfo.SOPInstanceUID)
                if deleteOnSuccess; delete(inpFile); end
                return; 
            end
            
            [destDirectory, destFile] = self.info2path(inpInfo);
            if exist(destDirectory, 'dir') ~= 7 && ~isempty(destDirectory)
                mkdir(destDirectory);
            end
           
            if ~strcmpi(inpFile, destFile)
                if deleteOnSuccess
                    movefile(inpFile, destFile, 'f');
                else
                    copyfile(inpFile, destFile, 'f');
                end
            end
            try
            mksqlite(self.conn, struct2sqlInsert(inpInfo, 'image', self.tags.common));
            mksqlite(self.conn, struct2sqlInsert(inpInfo, inpInfo.Modality, [{'SOPInstanceUID'} self.ModalityTags(inpInfo.Modality)]));
            catch err
                disp('unrecognized token'); 
            end
                
        end
    end
    
    methods (Access = private)
        function res = DBFileName(self)
             res = fullfile(self.rootPath, 'dcmdb1.sqlite');
        end
        
        function OpenDB(self)
            self.CloseDB(); 
            self.conn = mksqlite(0, 'open', DBFileName(self));
        end
        
        function CloseDB(self)
            if ~isempty(self.conn)
                mksqlite(self.conn, 'close');
            end
            self.conn = []; 
        end
        
        function InitDB(self)
            % Open database, create if not exist
            OpenDB(self);

            userVersion = getfield(mksqlite(self.conn, 'PRAGMA user_version'), 'user_version');
            if userVersion == 0
                mksqlite(self.conn, sprintf('PRAGMA user_version = %d', self.version));
            elseif userVersion < self.version
                error('DcmDb:UserVersionTooLow', 'user_version %d < %d', userVersion, self.version);
            elseif userVersion > self.version
                error('DcmDb:UserVersionTooHigh', 'user_version %d > %d', userVersion, self.version);
            end

            mksqlite(self.conn, strjoin({...
                'CREATE  TABLE  IF NOT EXISTS "main"."image" ('...
                '    "SOPInstanceUID"    TEXT PRIMARY KEY  NOT NULL  UNIQUE,'...
                '    "PatientID"         TEXT,'...
                '    "PatientName"       TEXT,'...
                '    "PatientBirthDate"  TEXT,'...
                '    "PatientSex"        TEXT,'...
                '    "PatientWeight"     TEXT,'...
                '    "PatientSize"       TEXT,'...
                '    "StudyInstanceUID"  TEXT NOT NULL,'...
                '    "StudyDate"         TEXT,'...
                '    "StudyTime"         TEXT,'...
                '    "StudyDescription"  TEXT,'...
                '    "Modality"          TEXT NOT NULL,'...
                '    "SeriesInstanceUID" TEXT NOT NULL,'...
                '    "SeriesDate"        TEXT,'...
                '    "SeriesTime"        TEXT,'...
                '    "SeriesDescription" TEXT)'}));
            mksqlite(self.conn, 'CREATE  VIEW IF NOT EXISTS "main"."vfilemap" AS  SELECT SOPInstanceUID,PatientID || "/" || StudyInstanceUID || "/" || Modality || "." || SeriesInstanceUID || "/" || SOPInstanceUID || ".dcm" AS FilePath FROM image');
           

             modalities = fieldnames(self.tags); 
             modalities(1)=[]; 
             for k=1:numel(modalities)
                 modality = modalities{k}; 
                 self.CreateTable(modality);
                 self.CreateView(modality);
             end

        end
        
        function delete(self)
            mksqlite(self.conn, 'close');
        end
    end
    
    methods (Access = public)
        
    end
    
    methods % public API
        function res = sqlQuery(self, sqlcmd)
            res = mksqlite(self.conn, sqlcmd);
        end
        
        function ret = listFilesForPatientId(self, patientId)
            images = self.sqlQuery(sprintf('SELECT * FROM images WHERE PatientID="%s"', patientId));
            ret = arrayfun(@(x)struct('SOPInstanceUID', x.SOPInstanceUID, 'FilePath', fullfile(self.rootPath,patientId,x.StudyInstanceUID,[x.Modality '.' x.SeriesInstanceUID],[x.SOPInstanceUID '.dcm'])), images);
        end
        
        function checkIntegrity(self, level)
            % level 1: check file existance only
            switch level
                case 1
                    relFilePaths = mksqlite(self.conn, 'SELECT * FROM vfilemap');
                    for k = 1:numel(relFilePaths)
                        if exist(fullfile(self.rootPath, relFilePaths(k).FilePath), 'file') ~= 2
                            self.log.warn(sprintf('File "%s" does not exist', relFilePaths(k)));
                        end
                    end
            end
        end
        
        
        
        function CreateTable(self, modality)
            str = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" ("SOPInstanceUID" TEXT PRIMARY KEY  NOT NULL  UNIQUE', modality); 
            names = self.ModalityTags(modality);
            
            for k=1:numel(names)
                name = names{k}; 
                if strcmpi(name, 'FrameOfReferenceUID')
                    TEXT = 'TEXT NOT NULL';
                else
                    TEXT = 'TEXT'; 
                end
                str1 = [', "' name '" ' TEXT ]; 
                str = cat(2, str, str1); 
            end
            str = [str ')']; 
            mksqlite(self.conn, str); 
        end
        
        function CreateView(self, modality)
            str = sprintf('CREATE  VIEW  IF NOT EXISTS "main"."v%s" AS SELECT * FROM image AS im INNER JOIN %s AS md ON im.SOPInstanceUID=md.SOPInstanceUID INNER JOIN vfilemap AS vf ON md.SOPInstanceUID=vf.SOPInstanceUID', modality, modality); 
            mksqlite(self.conn, str);
        end
        
        
    end
end

function sql = struct2sqlInsert(s, tableName, fieldnames)
    sql = sprintf('INSERT OR REPLACE INTO "main"."%s" (%s) VALUES (%s)',...
        tableName,...
        strjoin(cellfun(@(x)sprintf('"%s"',x), fieldnames, 'UniformOutput', false), ','),...
        strjoin(cellfun(@(x)sprintf('"%s"',s.(x)), fieldnames, 'UniformOutput', false), ','));
end

function removeEmptyDirs(rootDir)
    dirlist = DosUtil.rdir(rootDir);
    % recursively clean subdirectories
    if any(cell2mat({dirlist.isdir}))
        arrayfun(@(x)removeEmptyDirs(x.name), dirlist(cell2mat({dirlist.isdir})));
    end
    % rmdir self
    if isempty(DosUtil.rdir(rootDir))
        rmdir(rootDir);
    end
end

function info = getOptionalTag(dcm, tagPath, info)
    if dcm4che2.hasTag(dcm, tagPath)
        info.(tagPath) = dcm4che2.getTag(dcm, tagPath);
    else
        info.(tagPath) = [];
    end
end

function info = getOptionalTagxx(dcm, tagName, tagPath, info)
   info.(tagName) = dcm4che2.getTagxx(dcm, tagPath);
end