function val = tag(dcm, action, tagPath, val)
% Get/set etc. on tag or tagPath of dcm4che2 DicomObject
%
% Usage:
%   val = tag(dcm, 'get', tagPath), get val from tagPath
%   tf = tag(dcm, 'has', tagPath), has tag at tagPath
%   tag(dcm, 'set', tagPath, val), put val to tagPath
%   tag(dcm, 'remove', tagPath), remove tagPath
% Input:
%   dcm, org.dcm4che2.data.BasicDicomObject
%   tagPath, scalar or vector of org.dcm4che2.data.Tag, or tag path string, e.g.
%     [org.dcm4che2.data.Tag.BeamSequence 1 org.dcm4che2.data.Tag.BeamType], 
%     or 'BeamSequence[1]/BeamType'. Note the array base is 1 (although in 
%     java is base 0). Also interesting/unfortunate to note the string notation
%     is actually faster than forming integer path in Matlab.
%   val, tag value to put (set)
% Output value type see conversion table below, and throws an error if get tag
%   not exist.
%
% Remarks:
%   This function has to be written in MATLAB as Java's polymorphism does not 
%   allow returning different types based on tag path.
%   Effort has been put to get the best performance, such as VR code lookup,
%   data type conversion, etc.
%
% Map of DICOM VR and Matlab data types
% VR      Description         Matlab Type
% ----------------------------------------------------------------
% N/A     Sequence item       object
% AT      Attribute Tag       Not supported yet  
% DS      Decimal String      double
% OB      Other Byte          int32 for PixelData,  int8 otherwise
% OW      Other Word          int32 for PixelData,  int8 otherwise
% UN      Unknown
% OF      Other Float         single
% SQ      Sequence of Items   cell array of objects
% IS      Integer String      int32
% SS      Signed Short        int16
% SL      Signed Long         int32
% US      Unsigned Short      uint16
% UL      Unsigned Long       uint32
% PN      Person Name         char (or cellstr if VM > 1, also applies below)
% LT      Long Text           char
% AE      Application Entity  char
% AS      Age String          char
% CS      Code String         char
% DA      Date                char
% DT      Date/Time           char
% LO      Long String         char
% SH      Short String        char
% ST      Short Text          char
% TM      Time                char
% UI      Unique Identifier   char
% UT      Unlimited Text      char
% FD      Float Double (8B)   double
% FL      Float Single (4B)   single
%
% See also: dcm4che2.getTag, dcm4che2.setTag, dcm4che2.hasTag
%
% Author: Xiaohu Mo, 2012/11/16

persistent VRCODE VRSTR VRINT
if isempty(VRCODE)
    vrObjs = struct(org.dcm4che2.data.VR.UI);
    VRCODE = struct();
    for fn = fieldnames(vrObjs)'
        vrName = fn{1};
        vrCode = code(vrObjs.(fn{1}));
        VRCODE.(vrName) = vrCode;
    end
    VRSTR = {VRCODE.AE VRCODE.AS VRCODE.CS VRCODE.LO VRCODE.LT VRCODE.PN VRCODE.SH VRCODE.ST VRCODE.UI VRCODE.UT   VRCODE.DA VRCODE.TM VRCODE.DT};
    VRINT = {VRCODE.IS VRCODE.SL VRCODE.UL VRCODE.SS VRCODE.US};
end

%% Convert string tagPath to integer if necessary
if ischar(tagPath)
    charTagPath = tagPath;
    tagPath = strrep(tagPath, '.', '/');
    tagPath = org.dcm4che2.data.Tag.toTagPath(tagPath);
else
    % Deprecated. Now whenever tagPath is int, it should already be base 0
%     tagPath = int32(tagPath);
%     % Subtract 1 due to different notation in dcm4che2
%     tagPath(2:2:end) = tagPath(2:2:end) - 1;
end

%% Special case where tagPath is a sequence item (numel(tagPath) is even)
if ~bitand(length(tagPath),1)
    switch action
        case 'remove'
            sq = get(dcm, tagPath(1:end-1));
            removeDicomObject(sq, tagPath(end));
        case 'has'
            val = getNestedDicomObject(dcm, tagPath);
            if isempty(val)
                val = false;
            else
                val = true;
            end
        case 'get'
            val = getNestedDicomObject(dcm, tagPath);
            if isempty(val)
                error('dcm4che2:getTag:TagNotFound', 'Cannot find tag "%s[%d]"', char(nameOf(dcm, tagPath(end-1))), tagPath(end));
            end
        case 'set'
            % Why putNestedDicomObject doesn't support this?
            for k = 1:2:numel(tagPath)-1
                sq = get(dcm, tagPath(1:k));
                if isempty(sq)
                    putSequence(dcm, tagPath(1:k));
                    sq = get(dcm, tagPath(1:k));
                end
                while countItems(sq) <= tagPath(k+1)
                    addDicomObject(sq, org.dcm4che2.data.BasicDicomObject);
                end
            end
            sq = get(dcm, tagPath(1:end-1));
            setDicomObject(sq, tagPath(end), val);
    end
%% Otherwise it's a normal tag
else
    if strcmpi(action, 'remove')
        remove(dcm, tagPath);
    elseif strcmpi(action, 'has')
        % Note: this is correct as get returns element object if exist
        % using contains method slightly faster
        if (numel(tagPath)==1 && ~contains(dcm, tagPath)) || (numel(tagPath)>1 && isempty(get(dcm,tagPath)))
            val = false;
        else
            val = true;
        end
    else % otherwise either get or set
        % Note: same code as in has() for better performance
        % error if get item not exist
        if strcmpi(action, 'get')
            if (numel(tagPath)==1 && ~contains(dcm, tagPath)) || (numel(tagPath)>1 && isempty(get(dcm,tagPath)))
                error('dcm4che2:getTag:TagNotFound', 'Cannot find tag "%s"', char(nameOf(dcm, tagPath(end))));
            end
        end

        vr = vrOf(dcm, tagPath(end));
        vrCode = code(vr);
        switch vrCode
            case VRSTR
                if strcmpi(action, 'get')
                    val = cell(getStrings(dcm, tagPath));
                    % Note: vm() does not support tagPath
                    if numel(val) <= 1
                        val = char(val);
                    end
                else
                    if ~iscell(val)
                        putString(dcm, tagPath, vr, val);
                    else
                        putStrings(dcm, tagPath, vr, val);
                    end
                end
            case VRCODE.AT
                if strcmpi(action, 'get')
                    tag = getInt(dcm, tagPath);
                    val = [round(tag/65536) mod(tag,65536)];
                    % val = [bitand(805568524,hex2dec('FFFF0000'))/hex2dec('10000') bitand(805568524,hex2dec('FFFF'))];
                else
                    putInt(dcm, tagPath, vr, 65536*val(1)+val(2));
                end
            case {VRCODE.DS VRCODE.FD}
                if strcmpi(action, 'get')
                    val = getDoubles(dcm, tagPath);
                else
                    putDoubles(dcm, tagPath, vr, val);
                end
            case {VRCODE.FL VRCODE.OF}
                if strcmpi(action, 'get')
                    val = getFloats(dcm, tagPath);
                else
                    putFloats(dcm, tagPath, vr, val);
                end
            case VRINT
                if strcmpi(action, 'get')
                    val = getInts(dcm, tagPath);
                else
                    putInts(dcm, tagPath, vr, int32(val));
                end
            case VRCODE.SQ
                if strcmpi(action, 'get')
                    sq = get(dcm, tagPath);
                    val =  arrayfun(@(k)getDicomObject(sq, k), 0:countItems(sq)-1, 'UniformOutput', false);
                else
                    if iscell(val)
                        for k = 1:numel(val)
                            % Note subtract 1 due to java base 0
                            dcm4che2.setTag(dcm, [tagPath k-1], val{k});
                        end
                    elseif isa(val, 'org.dcm4che2.data.SequenceDicomElement')
                        put(dcm, tagPath, vr, val);
                    else
                        error('dcm4che2:setTag:InvalidData', 'Invalid input data setting tag %s', char(nameOf(dcm, tagPath(end))));
                    end
                end
            case {VRCODE.OB VRCODE.OW}
                if strcmpi(action, 'get')
                    if tagPath(end) == org.dcm4che2.data.Tag.PixelData
                        samplesPerPixel = getInt(dcm, org.dcm4che2.data.Tag.SamplesPerPixel);
                        if samplesPerPixel ~= 1
                            error('dcm4che2:getTag:UnsupportedData', 'Data not supported: Samples Per Pixel = %d', samplesPerPixel);
                        end
                        photometricInterpretation = getString(dcm, org.dcm4che2.data.Tag.PhotometricInterpretation);
                        if ~strcmpi(photometricInterpretation,'MONOCHROME2')
                            error('dcm4che2:getTag:UnsupportedData', 'Data not supported: Photometric Interpretation = %s', photometricInterpretation);
                        end
                        pixelRepresentation = getInt(dcm, org.dcm4che2.data.Tag.PixelRepresentation);
                        if isempty(pixelRepresentation)
                            error('dcm4che2:getTag:UnsupportedData', 'Data not supported: Pixel Representation not exist');
                        end
                        bitsAllocated = getInt(dcm, org.dcm4che2.data.Tag.BitsAllocated);
                        if isempty(bitsAllocated)
                            error('dcm4che2:getTag:UnsupportedData', 'Data not supported: Bits Allocated not exist');
                        elseif ~any(bitsAllocated == [8 16 32])
                            error('dcm4che2:getTag:UnsupportedData', 'Data not supported: Bits Allocated = %d', bitsAllocated);
                        end
                        if pixelRepresentation == 0
                            val = typecast(getShorts(dcm, tagPath), 'uint16');
                        else
                            val = getShorts(dcm, tagPath);
                        end
                        if bitsAllocated == 32
                            val = reshape(int32(val), 2, []);
                            val = val(1,:) + val(2,:) * 2^16;
                        end
                        rows = getInt(dcm, org.dcm4che2.data.Tag.Rows);
                        columns = getInt(dcm, org.dcm4che2.data.Tag.Columns);
                        numberOfFrames = getInt(dcm, org.dcm4che2.data.Tag.NumberOfFrames);
                        if numberOfFrames > 0
                            val = reshape(val, columns, rows, numberOfFrames);
                        elseif rows > 0 && columns > 0
                            val = reshape(val, columns, rows);
                        end
                    else
                        val = getBytes(dcm, tagPath);
                    end
                else
                    if tagPath(end) == org.dcm4che2.data.Tag.PixelData
                        putShorts(dcm, tagPath, vr, val(:));
                    else
                        putBytes(dcm, tagPath, vr, val(:));
                    end
                end
            otherwise
                error('dcm4che2:getTag:UnsupportedData', 'Data not supported: VR = %s', char(toString(vr)));
        end
    end
end

% convert cellstr to java string array
function jStrs = cellstr2javaStringArray(cstr)
jStrs = javaArray('java.lang.String', numel(cstr));
for k = 1:numel(cstr)
    jStrs(k) = java.lang.String(cstr{k});
end

% convert Matlab datenum to java.util.Date
function jd = mdate2jdate(md)
[Y, M, D, H, MN, S] = datevec(md);
jd = java.util.Date(Y-1900, M-1, D, H, MN, S);

% convert java.util.Date to Matlab datenum
function md = jdate2mdate(jds)
md = zeros(length(jds), 1);
for k = 1:numel(md)
    jd = jds(k);
    md(k) = datenum(getYear(jd)+1900, getMonth(jd)+1, getDate(jd), getHours(jd), getMinutes(jd), getSeconds(jd));
end

