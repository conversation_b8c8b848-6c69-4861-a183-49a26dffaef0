classdef TaskTableFilter <xls.TableFilter
    %UNTITLED5 Summary of this class goes here
    %   Detailed explanation goes here
    
    properties
        m_Task;
    end
    
    methods
        function obj = TaskTableFilter(varargin)
           <EMAIL>(varargin{:});
           fname = obj.getoptioni('TaskFilterFileName'); 
           if exist(fname, 'file')
                 LoadTaskTableFilter(obj, fname);
           end
        end
        
        function LoadTaskTableFilter(obj, fname)
            text        = fileread(fname);
            str = jsondecode(text);
            obj.m_Task   = StructBase.getfieldx(str, 'task');
            obj.m_Filter = StructBase.getfieldx(str, 'tablefilter');
            if ischar(obj.m_Filter)
                LoadFilter_json(obj, obj.m_Filter);
            end
        end
    end
end

