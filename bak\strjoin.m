function str = strjoin(C, delimiter)
% STRJOIN Join strings in cell array into single string
%
% This strjoin() has the same syntax as in MATLAB 2013a to allow for smooth upgrade.
%
% Usage:
% str = strjoin(C) constructs the string, str, by linking each string in the cell array, C, with a single space.
% str = strjoin(C,delimiter) constructs the string, str, by linking each string of C with the elements in delimiter.
%
% Author: <PERSON><PERSON>

if nargin < 2
    delimiter = ' ';
end

if ~iscellstr(C) || ~ischar(delimiter)
    error('strjoin:InvalidInput', 'Unknown input format');
end

C(1:end-1) = cellfun(@(x)horzcat(x, delimiter), C(1:end-1), 'UniformOutput', false);
str = horzcat(C{:});

