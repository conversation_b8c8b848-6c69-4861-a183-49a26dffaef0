dcmdrle(1)                        OFFIS DCMTK                       dcmdrle(1)



NAME
       dcmdrle - Decode RLE-compressed DICOM file


SYNOPSIS
       dcmdrle [options] dcmfile-in dcmfile-out

DESCRIPTION
       The  dcmdrle  utility  reads a RLE-compressed DICOM image (dcmfile-in),
       decompresses the image data (i.e. conversion to a native DICOM transfer
       syntax) and writes the converted image to an output file (dcmfile-out).

PARAMETERS
       dcmfile-in   DICOM input filename to be converted

       dcmfile-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   input options
       input file format:

         +f   --read-file
                read file format or data set (default)

         +fo  --read-file-only
                read file format only

         -f   --read-dataset
                read data set without file meta information

         # This option allows one to decompress RLE compressed DICOM objects that have
         # been stored as dataset without meta-header. Such a thing should not exist
         # since the transfer syntax cannot be reliably determined without meta-header,
         # but unfortunately it does.

   processing options
       SOP Instance UID:

         +ud  --uid-default
                keep same SOP Instance UID (default)

         +ua  --uid-always
                always assign new UID

       RLE byte segment order:

         +bd  --byte-order-default
                most significant byte first (default)

         +br  --byte-order-reverse
                least significant byte first

         # This option allows one to decompress RLE compressed DICOM files in which
         # the order of byte segments is encoded in incorrect order. This only affects
         # images with more than one byte per sample.

   output options
       output file format:

         +F   --write-file
                write file format (default)

         -F   --write-dataset
                write data set without file meta information

       output transfer syntax:

         +te  --write-xfer-little
                write with explicit VR little endian (default)

         +tb  --write-xfer-big
                write with explicit VR big endian TS

         +ti  --write-xfer-implicit
                write with implicit VR little endian TS

       post-1993 value representations:

         +u   --enable-new-vr
                enable support for new VRs (UN/UT) (default)

         -u   --disable-new-vr
                disable support for new VRs, convert to OB

       group length encoding:

         +g=  --group-length-recalc
                recalculate group lengths if present (default)

         +g   --group-length-create
                always write with group length elements

         -g   --group-length-remove
                always write without group length elements

       length encoding in sequences and items:

         +e   --length-explicit
                write with explicit lengths (default)

         -e   --length-undefined
                write with undefined lengths

       data set trailing padding (not with --write-dataset):

         -p=  --padding-retain
                do not change padding (default if not --write-dataset)

         -p   --padding-off
                no padding (implicit if --write-dataset)

         +p   --padding-create  [f]ile-pad [i]tem-pad: integer
                align file on multiple of f bytes
                and items on multiple of i bytes

TRANSFER SYNTAXES
       dcmdrle  supports  the  following transfer syntaxes for input (dcmfile-
       in):

       LittleEndianImplicitTransferSyntax             1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax             1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax   1.2.840.10008.******** (*)
       BigEndianExplicitTransferSyntax                1.2.840.10008.1.2.2
       RLELosslessTransferSyntax                      1.2.840.10008.1.2.5

       (*) if compiled with zlib support enabled

       dcmdrle supports the following transfer syntaxes for  output  (dcmfile-
       out):

       LittleEndianImplicitTransferSyntax             1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax             1.2.840.10008.1.2.1
       BigEndianExplicitTransferSyntax                1.2.840.10008.1.2.2

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The dcmdrle utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

SEE ALSO
       dcmcrle(1)

COPYRIGHT
       Copyright  (C)  2002-2014  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany



Version 3.6.5                   Mon Oct 28 2019                     dcmdrle(1)
