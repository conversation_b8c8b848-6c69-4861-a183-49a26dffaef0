function hginfo = writeHgInfo(outputFile)
% WRITEHGINFO Write mercurial repository info to outputFile
%
% Usage:
%   writeHgInfo(outputFile)
%
% Currently listed info are:
% branch, tags, latesttag, latesttagdistance, rev, shortnode
%
% Author: <PERSON><PERSON>

if nargin < 1
    outputFile = 'hgversion.ini';
end

hginfo = getHgInfo();
fid = fopen(outputFile, 'w');
if fid > 0
    try
        for k = keys(hginfo)
            fprintf(fid, '%s=%s\n', k{1}, hginfo(k{1}));
        end
        fclose(fid);
    catch exc
        fclose(fid);
        error('writeRepoInfo:FailedWriteFile', 'Failed writing file "%s"', outputFile);
    end
else
    error('writeRepoInfo:FailedOpenFile', 'Failed to open file "%s"', outputFile);
end

function ret = getHgInfo()
ret = containers.Map('KeyType', 'char', 'ValueType', 'char');
for k = {'branch' 'tags' 'latesttag' 'latesttagdistance' 'rev'}
    [~, ret(k{1})] = system(sprintf('hg log -l 1 -r . --template "{%s}"', k{1}));
end
[~, ret('shortnode')] = system(sprintf('hg log -l 1 -r . --template "{%s}"', 'short(node)'));
% [~, ret('isodatesec')] = system(sprintf('hg log -l 1 -r . --template "{%s}"', 'isodatesec(date)'));
