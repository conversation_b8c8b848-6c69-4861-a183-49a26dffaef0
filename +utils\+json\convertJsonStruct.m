function cfg = convertJsonStruct(cfg, varargin)
    options = OptionsMap(varargin{:});
    fns = options.getoptioni('containers.Map.FieldNames');
    if ~isempty(fns)
        if ischar(fns)
            fns = strsplit(fns, '|');
            for k=1:numel(fns)
                fn = fns{k};
                value = StructBase.getfieldx(cfg, fn);
                if isstruct(value)
                    value = utils.json.struct2map(value);
                    cfg = StructBase.setfieldx(cfg, fn, value);
                end
            end
        end
    end

    recoverFieldName = options.getoptioni_numeric('struct2mapX', 0);
    if recoverFieldName
        cfg = utils.json.struct2mapX(cfg, '^x(\d+|_)');
    end
end