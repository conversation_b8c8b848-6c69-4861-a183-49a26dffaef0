findscu(1)                        OFFIS DCMTK                       findscu(1)



NAME
       findscu - DICOM query (C-FIND) SCU


SYNOPSIS
       findscu [options] peer port [dcmfile-in...]

DESCRIPTION
       The  findscu  application  implements  an  SCU  for  the Query/Retrieve
       Service Class and the Basic Worklist Management Service Class.  findscu
       only  supports  query  functionality using the C-FIND message. It sends
       query keys to an SCP and awaits responses. The application can be  used
       to  test  SCPs  of  the  Query/Retrieve  and  Basic Worklist Management
       Service Classes.

PARAMETERS
       peer        hostname of DICOM peer

       port        tcp/ip port number of peer

       dcmfile-in  DICOM query file(s)

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

   network options
       override matching keys:

         -k    --key  [k]ey: gggg,eeee="str", path or dictionary name="str"
                 override matching key

       query information model:

         -W    --worklist
                 use modality worklist information model (default)

         -P    --patient
                 use patient root information model

         -S    --study
                 use study root information model

         -O    --psonly
                 use patient/study only information model

       application entity titles:

         -aet  --aetitle  [a]etitle: string
                 set my calling AE title (default: FINDSCU)

         -aec  --call  [a]etitle: string
                 set called AE title of peer (default: ANY-SCP)

       post-1993 value representations:

         +u    --enable-new-vr
                 enable support for new VRs (UN/UT) (default)

         -u    --disable-new-vr
                 disable support for new VRs, convert to OB

       proposed transmission transfer syntaxes:

         -x=   --propose-uncompr
                 propose all uncompressed TS, explicit VR
                 with local byte ordering first (default)

         -xe   --propose-little
                 propose all uncompressed TS, explicit VR
                 little endian first

         -xb   --propose-big
                 propose all uncompressed TS, explicit VR
                 big endian first

         -xd   --propose-deflated
                 propose deflated explicit VR little endian TS
                 and all uncompressed transfer syntaxes

         -xi   --propose-implicit
                 propose implicit VR little endian TS only

       deflate compression level (only with --propose-deflated):

         +cl   --compression-level  [l]evel: integer (default: 6)
                 0=uncompressed, 1=fastest, 9=best compression

       other network options:

         -to   --timeout  [s]econds: integer (default: unlimited)
                 timeout for connection requests

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes (default: 16384)

               --repeat  [n]umber: integer
                 repeat n times

               --abort
                 abort association instead of releasing it

               --cancel  [n]umber: integer
                 cancel after n responses (default: never)

   transport layer security (TLS) options
       transport protocol stack:

         -tls  --disable-tls
                 use normal TCP/IP connection (default)

         +tls  --enable-tls  [p]rivate key file, [c]ertificate file: string
                 use authenticated secure TLS connection

         +tla  --anonymous-tls
                 use secure TLS connection without certificate

       private key password (only with --enable-tls):

         +ps   --std-passwd
                 prompt user to type password on stdin (default)

         +pw   --use-passwd  [p]assword: string
                 use specified password

         -pw   --null-passwd
                 use empty string as password

       key and certificate file format:

         -pem  --pem-keys
                 read keys and certificates as PEM file (default)

         -der  --der-keys
                 read keys and certificates as DER file

       certification authority:

         +cf   --add-cert-file  [c]ertificate filename: string
                 add certificate file to list of certificates

         +cd   --add-cert-dir  [c]ertificate directory: string
                 add certificates in d to list of certificates

       security profile:

         +px   --profile-bcp195
                 BCP 195 TLS Profile (default)

         +py   --profile-bcp195-nd
                 Non-downgrading BCP 195 TLS Profile

         +pz   --profile-bcp195-ex
                 Extended BCP 195 TLS Profile

         +pb   --profile-basic
                 Basic TLS Secure Transport Connection Profile (retired)

         +pa   --profile-aes
                 AES TLS Secure Transport Connection Profile (retired)

         +pn   --profile-null
                 Authenticated unencrypted communication
                 (retired, was used in IHE ATNA)

       ciphersuite:

         +cc   --list-ciphers
                 show list of supported TLS ciphersuites and exit

         +cs   --cipher  [c]iphersuite name: string
                 add ciphersuite to list of negotiated suites

       pseudo random generator:

         +rs   --seed  [f]ilename: string
                 seed random generator with contents of f

         +ws   --write-seed
                 write back modified seed (only with --seed)

         +wf   --write-seed-file  [f]ilename: string (only with --seed)
                 write modified seed to file f

       peer authentication:

         -rc   --require-peer-cert
                 verify peer certificate, fail if absent (default)

         -ic   --ignore-peer-cert
                 don't verify peer certificate

   output options
       general:

         -od   --output-directory  [d]irectory: string (default: ".")
                 write output files to existing directory d

       automatic data correction:

         +dc   --enable-correction
                 enable automatic data correction

         -dc   --disable-correction
                 disable automatic data correction (default)

       C-FIND responses:

         +sr   --show-responses
                 always output responses to the logger

         -sr   --hide-responses
                 do not output responses to the logger

         -X    --extract
                 extract responses to DICOM file (rsp0001.dcm...)

         -Xx   --extract-xml
                 extract responses to XML file (rsp0001.xml...)

         -Xs   --extract-xml-single  [f]ilename: string
                 extract all responses to given XML file f

NOTES
       Each file supplied on the command line will be sent to the SCP as  part
       of  a  C-FIND  request.  The  query file must be a valid DICOM data set
       containing the dataset part of a  C-FIND-RQ  message.  The  query  file
       could, for instance, be created with the dump2dcm utility from a script
       like the following example:

       # query patient names and IDs
       (0008,0052) CS [PATIENT]     # QueryRetrieveLevel
       (0010,0010) PN []            # PatientName
       (0010,0020) LO []            # PatientID

       Individual attributes of each file sent can be modified or supplemented
       using the -k option. For example the command:

       findscu -P -k "(0010,0010)=HEWETT*" caesar 5678 patqry.dcm

       will,  when  sent  to  the  SCP  caesar  at TCP/IP port 5678, cause any
       PatientName attribute in patqry.dcm to have  the  value  'HEWETT*'.  If
       such  an attribute is present it will be replaced, if absent it will be
       inserted. The -k option can be present more than once. The  value  part
       (after  the  '=')  may  be absent causing the attribute to be sent with
       zero length.

       In earlier versions of findscu, the tag  keys  were  specified  without
       braces  around  group  and element number, e. g. '0010,0010' instead of
       '(0010,0010)'. It is recommended switching to the new syntax;  however,
       the old syntax is still working.

       Also -k accepts dictionary names instead of element tags for specifying
       DICOM elements. For example, the findscu call  above  then  reads  like
       this:

       findscu -P -k PatientName="HEWETT*" caesar 5678 patqry.dcm

       It  is  also possible to specify sequences, items and nested attributes
       using the -k option. In these cases, a special 'path' notation  has  to
       be used, e. g.

       findscu -W -k "(0040,0100)[0].Modality=CT" caesar 5678

       This  call  queries  a  worklist  server at host caesar for any planned
       procedures for CT modalities by specifying tag  (0040,0100)  'Scheduled
       Procedure  Step Sequence' and an attribute 'Modality' in the first item
       of this sequence with value 'CT'. Details on this path notation can  be
       found in the documentation of dcmodify.

       If  no  file  is  specified  on  the  command  line,  the query must be
       specified completely with one or more -k  options.  If  multiple  query
       files  are  provided, findscu will send multiple C-FIND requests to the
       SCP.

       Each set of response identifiers received will be output to the  logger
       unless  option  --hide-responses,  any of the below --extract variants,
       --quiet or an appropriate logger configuration is used. In such  cases,
       the output to the logger can be enforced with option --show-responses.

       In  addition, the response datasets can also be extracted as individual
       DICOM files  (using  option  --extract)  or  XML  files  (using  option
       --extract-xml).  The  output  format  of the latter is described by the
       file dcm2xml.dtd (starting with top-level element 'data-set'). For  XML
       files,  the  Specific  Character  Set  is  mapped  automatically  to an
       appropriate XML encoding. If this is not possible, e.g. in case of  ISO
       2022  character  sets,  non-ASCII  characters  and  those below #32 are
       stored as '&#nnn;' where 'nnn' refers to the  numeric  character  code.
       Please note that this might lead to invalid character entity references
       (such as '&#27;' for ESC) and will cause most XML parsers to reject the
       document.

       Alternatively, all response datasets of an association can be extracted
       to a single XML file using option --extract-xml-single.  The  top-level
       element  of the XML document is 'responses' (with a 'type' attribute of
       'C-FIND'). The individual datasets are stored as  described  above.  If
       support  for  character  set  conversion  is enabled, UTF-8 encoding is
       used, i.e. all datasets are  converted  to  UTF-8  encoding  (which  is
       strongly recommended in order to avoid issues with non-ASCII characters
       when different character sets are used).

   DICOM Conformance
       The findscu application supports the following SOP Classes as an SCU:

       FINDPatientRootQueryRetrieveInformationModel       1.2.840.10008.5.1.4.1.2.1.1
       FINDStudyRootQueryRetrieveInformationModel         1.2.840.10008.5.1.4.1.2.2.1
       FINDPatientStudyOnlyQueryRetrieveInformationModel  1.2.840.10008.5.1.4.1.2.3.1
       FINDModalityWorklistInformationModel               1.2.840.10008.5.1.4.31

       The findscu application will propose presentation contexts for  one  of
       the  abovementioned  supported  SOP  Classes  depending on command line
       options (-P, -S, -O or -W). Basically, the following transfer  syntaxes
       are supported:

       LittleEndianImplicitTransferSyntax                 1.2.840.10008.1.2
       LittleEndianExplicitTransferSyntax                 1.2.840.10008.1.2.1
       DeflatedExplicitVRLittleEndianTransferSyntax       1.2.840.10008.1.2.1.99 (*)
       BigEndianExplicitTransferSyntax                    1.2.840.10008.1.2.2

       (*) if compiled with zlib support enabled (see --version output)

       Which  transfer  syntaxes  are  actually proposed in what order, can be
       specified with the --propose options.

       The findscu application does not support extended negotiation.

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  findscu  utility  will  attempt  to  load  DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

FILES
       <datadir>/dcm2xml.dtd - Document Type Definition (DTD) file

SEE ALSO
       movescu(1), dump2dcm(1), dcmodify(1)

COPYRIGHT
       Copyright (C) 1994-2019 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     findscu(1)
