#
#  Copyright (C) 2003-2019, OFFIS e.V.
#  All rights reserved.  See COPYRIGHT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 Oldenburg, Germany
#
#  Module:  dcmnet
#
#  Author:  <PERSON>, <PERSON><PERSON>
#
#  Purpose: Sample configuration file for storescp
#

# ============================================================================
[[TransferSyntaxes]]
# ============================================================================

[Uncompressed]
TransferSyntax1  = LocalEndianExplicit
TransferSyntax2  = OppositeEndianExplicit
TransferSyntax3  = LittleEndianImplicit

[UncompressedOrZlib]
TransferSyntax1  = DeflatedLittleEndianExplicit
TransferSyntax2  = LocalEndianExplicit
TransferSyntax3  = OppositeEndianExplicit
TransferSyntax4  = LittleEndianImplicit

[AnyTransferSyntax]
TransferSyntax1  = JPEG2000
TransferSyntax2  = JPEG2000LosslessOnly
TransferSyntax3  = JPEGExtended:Process2+4
TransferSyntax4  = JPEGBaseline
TransferSyntax5  = JPEGLossless:Non-hierarchical-1stOrderPrediction
TransferSyntax6  = JPEGLSLossy
TransferSyntax7  = JPEGLSLossless
TransferSyntax8  = RLELossless
TransferSyntax9  = MPEG2MainProfile@MainLevel
TransferSyntax10 = MPEG2MainProfile@HighLevel
TransferSyntax11 = MPEG4HighProfile/Level4.1
TransferSyntax12 = MPEG4BDcompatibleHighProfile/Level4.1
TransferSyntax13 = MPEG4HighProfile/Level4.2For2DVideo
TransferSyntax14 = MPEG4HighProfile/Level4.2For3DVideo
TransferSyntax15 = MPEG4StereoHighProfile/Level4.2
TransferSyntax16 = HEVCMainProfile/Level5.1
TransferSyntax17 = HEVCMain10Profile/Level5.1
TransferSyntax18 = DeflatedLittleEndianExplicit
TransferSyntax19 = LocalEndianExplicit
TransferSyntax20 = OppositeEndianExplicit
TransferSyntax21 = LittleEndianImplicit

# ============================================================================
[[PresentationContexts]]
# ============================================================================

[GenericStorageSCP]
#
# Don't forget to support the Verification SOP Class.
#
PresentationContext1   = VerificationSOPClass\Uncompressed
#
# Accept image SOP classes with virtually any transfer syntax we know.
# Accept non-image SOP classes uncompressed or with zlib compression only.
#
PresentationContext2   = BreastTomosynthesisImageStorage\AnyTransferSyntax
PresentationContext3   = ComputedRadiographyImageStorage\AnyTransferSyntax
PresentationContext4   = CornealTopographyMapStorage\AnyTransferSyntax
PresentationContext5   = CTImageStorage\AnyTransferSyntax
PresentationContext6   = DigitalIntraOralXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext7   = DigitalIntraOralXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext8   = DigitalMammographyXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext9   = DigitalMammographyXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext10  = DigitalXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext11  = DigitalXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext12  = EnhancedCTImageStorage\AnyTransferSyntax
PresentationContext13  = EnhancedMRColorImageStorage\AnyTransferSyntax
PresentationContext14  = EnhancedMRImageStorage\AnyTransferSyntax
PresentationContext15  = EnhancedPETImageStorage\AnyTransferSyntax
PresentationContext16  = EnhancedUSVolumeStorage\AnyTransferSyntax
PresentationContext17  = EnhancedXAImageStorage\AnyTransferSyntax
PresentationContext18  = EnhancedXRFImageStorage\AnyTransferSyntax
PresentationContext19  = IntravascularOpticalCoherenceTomographyImageStorageForPresentation\AnyTransferSyntax
PresentationContext20  = IntravascularOpticalCoherenceTomographyImageStorageForProcessing\AnyTransferSyntax
PresentationContext21  = MRImageStorage\AnyTransferSyntax
PresentationContext22  = MultiframeGrayscaleByteSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext23  = MultiframeGrayscaleWordSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext24  = MultiframeSingleBitSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext25  = MultiframeTrueColorSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext26  = NuclearMedicineImageStorage\AnyTransferSyntax
PresentationContext27  = OphthalmicPhotography16BitImageStorage\AnyTransferSyntax
PresentationContext28  = OphthalmicPhotography8BitImageStorage\AnyTransferSyntax
PresentationContext29  = OphthalmicThicknessMapStorage\AnyTransferSyntax
PresentationContext30  = OphthalmicTomographyImageStorage\AnyTransferSyntax
PresentationContext31  = PositronEmissionTomographyImageStorage\AnyTransferSyntax
PresentationContext32  = RTImageStorage\AnyTransferSyntax
PresentationContext33  = SecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext34  = UltrasoundImageStorage\AnyTransferSyntax
PresentationContext35  = UltrasoundMultiframeImageStorage\AnyTransferSyntax
PresentationContext36  = VideoEndoscopicImageStorage\AnyTransferSyntax
PresentationContext37  = VideoMicroscopicImageStorage\AnyTransferSyntax
PresentationContext38  = VideoPhotographicImageStorage\AnyTransferSyntax
PresentationContext39  = VLEndoscopicImageStorage\AnyTransferSyntax
PresentationContext40  = VLMicroscopicImageStorage\AnyTransferSyntax
PresentationContext41  = VLPhotographicImageStorage\AnyTransferSyntax
PresentationContext42  = VLSlideCoordinatesMicroscopicImageStorage\AnyTransferSyntax
PresentationContext43  = VLWholeSlideMicroscopyImageStorage\AnyTransferSyntax
PresentationContext44  = XRay3DAngiographicImageStorage\AnyTransferSyntax
PresentationContext45  = XRay3DCraniofacialImageStorage\AnyTransferSyntax
PresentationContext46  = XRayAngiographicImageStorage\AnyTransferSyntax
PresentationContext47  = XRayRadiofluoroscopicImageStorage\AnyTransferSyntax
# retired
PresentationContext48  = RETIRED_HardcopyColorImageStorage\AnyTransferSyntax
PresentationContext49  = RETIRED_HardcopyGrayscaleImageStorage\AnyTransferSyntax
PresentationContext50  = RETIRED_NuclearMedicineImageStorage\AnyTransferSyntax
PresentationContext51  = RETIRED_UltrasoundImageStorage\AnyTransferSyntax
PresentationContext52  = RETIRED_UltrasoundMultiframeImageStorage\AnyTransferSyntax
PresentationContext53  = RETIRED_VLImageStorage\AnyTransferSyntax
PresentationContext54  = RETIRED_VLMultiframeImageStorage\AnyTransferSyntax
PresentationContext55  = RETIRED_XRayAngiographicBiPlaneImageStorage\AnyTransferSyntax
#
# the following presentation contexts are for non-image SOP classes
#
PresentationContext56  = AmbulatoryECGWaveformStorage\UncompressedOrZlib
PresentationContext57  = ArterialPulseWaveformStorage\UncompressedOrZlib
PresentationContext58  = AutorefractionMeasurementsStorage\UncompressedOrZlib
PresentationContext59  = BasicStructuredDisplayStorage\UncompressedOrZlib
PresentationContext60  = BasicTextSRStorage\UncompressedOrZlib
PresentationContext61  = BasicVoiceAudioWaveformStorage\UncompressedOrZlib
PresentationContext62  = BlendingSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext63  = CardiacElectrophysiologyWaveformStorage\UncompressedOrZlib
PresentationContext64  = ChestCADSRStorage\UncompressedOrZlib
PresentationContext65  = ColonCADSRStorage\UncompressedOrZlib
PresentationContext66  = ColorSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext67  = Comprehensive3DSRStorage\UncompressedOrZlib
PresentationContext68  = ComprehensiveSRStorage\UncompressedOrZlib
PresentationContext69  = DeformableSpatialRegistrationStorage\UncompressedOrZlib
PresentationContext70  = EncapsulatedCDAStorage\UncompressedOrZlib
PresentationContext71  = EncapsulatedPDFStorage\UncompressedOrZlib
PresentationContext72  = EnhancedSRStorage\UncompressedOrZlib
PresentationContext73  = GeneralAudioWaveformStorage\UncompressedOrZlib
PresentationContext74  = GeneralECGWaveformStorage\UncompressedOrZlib
PresentationContext75  = GenericImplantTemplateStorage\UncompressedOrZlib
PresentationContext76  = GrayscaleSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext77  = HemodynamicWaveformStorage\UncompressedOrZlib
PresentationContext78  = ImplantAssemblyTemplateStorage\UncompressedOrZlib
PresentationContext79  = ImplantationPlanSRDocumentStorage\UncompressedOrZlib
PresentationContext80  = ImplantTemplateGroupStorage\UncompressedOrZlib
PresentationContext81  = IntraocularLensCalculationsStorage\UncompressedOrZlib
PresentationContext82  = KeratometryMeasurementsStorage\UncompressedOrZlib
PresentationContext83  = KeyObjectSelectionDocumentStorage\UncompressedOrZlib
PresentationContext84  = LensometryMeasurementsStorage\UncompressedOrZlib
PresentationContext85  = MacularGridThicknessAndVolumeReportStorage\UncompressedOrZlib
PresentationContext86  = MammographyCADSRStorage\UncompressedOrZlib
PresentationContext87  = MRSpectroscopyStorage\UncompressedOrZlib
PresentationContext88  = OphthalmicAxialMeasurementsStorage\UncompressedOrZlib
PresentationContext89  = OphthalmicVisualFieldStaticPerimetryMeasurementsStorage\UncompressedOrZlib
PresentationContext90  = ProcedureLogStorage\UncompressedOrZlib
PresentationContext91  = PseudoColorSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext92  = RawDataStorage\UncompressedOrZlib
PresentationContext93  = RealWorldValueMappingStorage\UncompressedOrZlib
PresentationContext94  = RespiratoryWaveformStorage\UncompressedOrZlib
PresentationContext95  = RTBeamsDeliveryInstructionStorage\UncompressedOrZlib
PresentationContext96  = RTBeamsTreatmentRecordStorage\UncompressedOrZlib
PresentationContext97  = RTBrachyTreatmentRecordStorage\UncompressedOrZlib
PresentationContext98  = RTDoseStorage\UncompressedOrZlib
PresentationContext99  = RTIonBeamsTreatmentRecordStorage\UncompressedOrZlib
PresentationContext100 = RTIonPlanStorage\UncompressedOrZlib
PresentationContext101 = RTPlanStorage\UncompressedOrZlib
PresentationContext102 = RTStructureSetStorage\UncompressedOrZlib
PresentationContext103 = RTTreatmentSummaryRecordStorage\UncompressedOrZlib
PresentationContext104 = SegmentationStorage\UncompressedOrZlib
PresentationContext105 = SpatialFiducialsStorage\UncompressedOrZlib
PresentationContext106 = SpatialRegistrationStorage\UncompressedOrZlib
PresentationContext107 = SpectaclePrescriptionReportStorage\UncompressedOrZlib
PresentationContext108 = StereometricRelationshipStorage\UncompressedOrZlib
PresentationContext109 = SubjectiveRefractionMeasurementsStorage\UncompressedOrZlib
PresentationContext110 = SurfaceScanMeshStorage\UncompressedOrZlib
PresentationContext111 = SurfaceScanPointCloudStorage\UncompressedOrZlib
PresentationContext112 = SurfaceSegmentationStorage\UncompressedOrZlib
PresentationContext113 = TwelveLeadECGWaveformStorage\UncompressedOrZlib
PresentationContext114 = VisualAcuityMeasurementsStorage\UncompressedOrZlib
PresentationContext115 = XAXRFGrayscaleSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext116 = XRayRadiationDoseSRStorage\UncompressedOrZlib
# retired
PresentationContext117 = RETIRED_StandaloneCurveStorage\UncompressedOrZlib
PresentationContext118 = RETIRED_StandaloneModalityLUTStorage\UncompressedOrZlib
PresentationContext119 = RETIRED_StandaloneOverlayStorage\UncompressedOrZlib
PresentationContext120 = RETIRED_StandalonePETCurveStorage\UncompressedOrZlib
PresentationContext121 = RETIRED_StandaloneVOILUTStorage\UncompressedOrZlib
PresentationContext122 = RETIRED_StoredPrintStorage\UncompressedOrZlib
# draft
PresentationContext123 = DRAFT_RTBeamsDeliveryInstructionStorage\UncompressedOrZlib
PresentationContext124 = DRAFT_SRAudioStorage\UncompressedOrZlib
PresentationContext125 = DRAFT_SRComprehensiveStorage\UncompressedOrZlib
PresentationContext126 = DRAFT_SRDetailStorage\UncompressedOrZlib
PresentationContext127 = DRAFT_SRTextStorage\UncompressedOrZlib
PresentationContext128 = DRAFT_WaveformStorage\UncompressedOrZlib
#
# the following SOP classes are missing in the above list:
#
# - AcquisitionContextSRStorage
# - AdvancedBlendingPresentationStateStorage
# - BreastProjectionXRayImageStorageForPresentation
# - BreastProjectionXRayImageStorageForProcessing
# - CArmPhotonElectronRadiationStorage
# - ColorPaletteStorage
# - CompositingPlanarMPRVolumetricPresentationStateStorage
# - ContentAssessmentResultsStorage
# - CTDefinedProcedureProtocolStorage
# - CTPerformedProcedureProtocolStorage
# - EncapsulatedSTLStorage
# - ExtensibleSRStorage
# - GrayscalePlanarMPRVolumetricPresentationStateStorage
# - HangingProtocolStorage
# - LegacyConvertedEnhancedCTImageStorage
# - LegacyConvertedEnhancedMRImageStorage
# - LegacyConvertedEnhancedPETImageStorage
# - MultipleVolumeRenderingVolumetricPresentationStateStorage
# - OphthalmicOpticalCoherenceTomographyBscanVolumeAnalysisStorage
# - OphthalmicOpticalCoherenceTomographyEnFaceImageStorage
# - ParametricMapStorage
# - PatientRadiationDoseSRStorage
# - PerformedImagingAgentAdministrationSRStorage
# - PlannedImagingAgentAdministrationSRStorage
# - ProtocolApprovalStorage
# - RadiopharmaceuticalRadiationDoseSRStorage
# - RTBrachyApplicationSetupDeliveryInstructionStorage
# - RTPhysicianIntentStorage
# - RTRadiationSetStorage
# - RTSegmentAnnotationStorage
# - SegmentedVolumeRenderingVolumetricPresentationStateStorage
# - SimplifiedAdultEchoSRStorage
# - TractographyResultsStorage
# - VolumeRenderingVolumetricPresentationStateStorage
# - WideFieldOphthalmicPhotographyStereographicProjectionImageStorage
# - WideFieldOphthalmicPhotography3DCoordinatesImageStorage
#
# - DICOS_2DAITStorage
# - DICOS_3DAITStorage
# - DICOS_CTImageStorage
# - DICOS_DigitalXRayImageStorageForPresentation
# - DICOS_DigitalXRayImageStorageForProcessing
# - DICOS_QuadrupoleResonanceStorage
# - DICOS_ThreatDetectionReportStorage
#
# - DICONDE_EddyCurrentImageStorage
# - DICONDE_EddyCurrentMultiframeImageStorage

# ----------------------------------------------------------------------------

[AllDICOMStorageSCP]
#
# Same as "GenericStorageSCP" but limited to non-retired and non-draft SOP Classes.
# This allows for accepting (almost) all DICOM Storage SOP Classes that are currently
# defined in the standard (an exception is made for some very new DICOM objects because
# of the limitation of 128 Presentation Contexts for SCPs, see DCMTK Feature #540).
#
PresentationContext1   = VerificationSOPClass\Uncompressed
#
# DICOM images
#
PresentationContext2   = BreastProjectionXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext3   = BreastProjectionXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext4   = BreastTomosynthesisImageStorage\AnyTransferSyntax
PresentationContext5   = ComputedRadiographyImageStorage\AnyTransferSyntax
PresentationContext6   = CornealTopographyMapStorage\AnyTransferSyntax
PresentationContext7   = CTImageStorage\AnyTransferSyntax
PresentationContext8   = DigitalIntraOralXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext9   = DigitalIntraOralXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext10  = DigitalMammographyXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext11  = DigitalMammographyXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext12  = DigitalXRayImageStorageForPresentation\AnyTransferSyntax
PresentationContext13  = DigitalXRayImageStorageForProcessing\AnyTransferSyntax
PresentationContext14  = EnhancedCTImageStorage\AnyTransferSyntax
PresentationContext15  = EnhancedMRColorImageStorage\AnyTransferSyntax
PresentationContext16  = EnhancedMRImageStorage\AnyTransferSyntax
PresentationContext17  = EnhancedPETImageStorage\AnyTransferSyntax
PresentationContext18  = EnhancedUSVolumeStorage\AnyTransferSyntax
PresentationContext19  = EnhancedXAImageStorage\AnyTransferSyntax
PresentationContext20  = EnhancedXRFImageStorage\AnyTransferSyntax
PresentationContext21  = IntravascularOpticalCoherenceTomographyImageStorageForPresentation\AnyTransferSyntax
PresentationContext22  = IntravascularOpticalCoherenceTomographyImageStorageForProcessing\AnyTransferSyntax
PresentationContext23  = LegacyConvertedEnhancedCTImageStorage\AnyTransferSyntax
PresentationContext24  = LegacyConvertedEnhancedMRImageStorage\AnyTransferSyntax
PresentationContext25  = LegacyConvertedEnhancedPETImageStorage\AnyTransferSyntax
PresentationContext26  = MRImageStorage\AnyTransferSyntax
PresentationContext27  = MultiframeGrayscaleByteSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext28  = MultiframeGrayscaleWordSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext29  = MultiframeSingleBitSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext30  = MultiframeTrueColorSecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext31  = NuclearMedicineImageStorage\AnyTransferSyntax
PresentationContext32  = OphthalmicPhotography16BitImageStorage\AnyTransferSyntax
PresentationContext33  = OphthalmicPhotography8BitImageStorage\AnyTransferSyntax
PresentationContext34  = OphthalmicThicknessMapStorage\AnyTransferSyntax
PresentationContext35  = OphthalmicTomographyImageStorage\AnyTransferSyntax
PresentationContext36  = ParametricMapStorage\AnyTransferSyntax
PresentationContext37  = PositronEmissionTomographyImageStorage\AnyTransferSyntax
PresentationContext38  = RTImageStorage\AnyTransferSyntax
PresentationContext39  = SecondaryCaptureImageStorage\AnyTransferSyntax
PresentationContext40  = UltrasoundImageStorage\AnyTransferSyntax
PresentationContext41  = UltrasoundMultiframeImageStorage\AnyTransferSyntax
PresentationContext42  = VideoEndoscopicImageStorage\AnyTransferSyntax
PresentationContext43  = VideoMicroscopicImageStorage\AnyTransferSyntax
PresentationContext44  = VideoPhotographicImageStorage\AnyTransferSyntax
PresentationContext45  = VLEndoscopicImageStorage\AnyTransferSyntax
PresentationContext46  = VLMicroscopicImageStorage\AnyTransferSyntax
PresentationContext47  = VLPhotographicImageStorage\AnyTransferSyntax
PresentationContext48  = VLSlideCoordinatesMicroscopicImageStorage\AnyTransferSyntax
PresentationContext49  = VLWholeSlideMicroscopyImageStorage\AnyTransferSyntax
PresentationContext50  = WideFieldOphthalmicPhotographyStereographicProjectionImageStorage\AnyTransferSyntax
PresentationContext51  = WideFieldOphthalmicPhotography3DCoordinatesImageStorage\AnyTransferSyntax
PresentationContext52  = XRay3DAngiographicImageStorage\AnyTransferSyntax
PresentationContext53  = XRay3DCraniofacialImageStorage\AnyTransferSyntax
PresentationContext54  = XRayAngiographicImageStorage\AnyTransferSyntax
PresentationContext55  = XRayRadiofluoroscopicImageStorage\AnyTransferSyntax
#
# all other DICOM objects
#
PresentationContext56  = AcquisitionContextSRStorage\UncompressedOrZlib
PresentationContext57  = AmbulatoryECGWaveformStorage\UncompressedOrZlib
PresentationContext58  = ArterialPulseWaveformStorage\UncompressedOrZlib
PresentationContext59  = AutorefractionMeasurementsStorage\UncompressedOrZlib
PresentationContext60  = BasicStructuredDisplayStorage\UncompressedOrZlib
PresentationContext61  = BasicTextSRStorage\UncompressedOrZlib
PresentationContext62  = BasicVoiceAudioWaveformStorage\UncompressedOrZlib
PresentationContext63  = BlendingSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext64  = CardiacElectrophysiologyWaveformStorage\UncompressedOrZlib
PresentationContext65  = ChestCADSRStorage\UncompressedOrZlib
PresentationContext66  = ColonCADSRStorage\UncompressedOrZlib
PresentationContext67  = ColorSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext68  = CompositingPlanarMPRVolumetricPresentationStateStorage\UncompressedOrZlib
PresentationContext69  = Comprehensive3DSRStorage\UncompressedOrZlib
PresentationContext70  = ComprehensiveSRStorage\UncompressedOrZlib
PresentationContext71  = ContentAssessmentResultsStorage\UncompressedOrZlib
PresentationContext72  = CTDefinedProcedureProtocolStorage\UncompressedOrZlib
PresentationContext73  = CTPerformedProcedureProtocolStorage\UncompressedOrZlib
PresentationContext74  = DeformableSpatialRegistrationStorage\UncompressedOrZlib
PresentationContext75  = EncapsulatedCDAStorage\UncompressedOrZlib
PresentationContext76  = EncapsulatedPDFStorage\UncompressedOrZlib
PresentationContext77  = EnhancedSRStorage\UncompressedOrZlib
PresentationContext78  = ExtensibleSRStorage\UncompressedOrZlib
PresentationContext79  = GeneralAudioWaveformStorage\UncompressedOrZlib
PresentationContext80  = GeneralECGWaveformStorage\UncompressedOrZlib
PresentationContext81  = GenericImplantTemplateStorage\UncompressedOrZlib
PresentationContext82  = GrayscalePlanarMPRVolumetricPresentationStateStorage\UncompressedOrZlib
PresentationContext83  = GrayscaleSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext84  = HangingProtocolStorage\UncompressedOrZlib
PresentationContext85  = HemodynamicWaveformStorage\UncompressedOrZlib
PresentationContext86  = ImplantAssemblyTemplateStorage\UncompressedOrZlib
PresentationContext87  = ImplantationPlanSRDocumentStorage\UncompressedOrZlib
PresentationContext88  = ImplantTemplateGroupStorage\UncompressedOrZlib
PresentationContext89  = IntraocularLensCalculationsStorage\UncompressedOrZlib
PresentationContext90  = KeratometryMeasurementsStorage\UncompressedOrZlib
PresentationContext91  = KeyObjectSelectionDocumentStorage\UncompressedOrZlib
PresentationContext92  = LensometryMeasurementsStorage\UncompressedOrZlib
PresentationContext93  = MacularGridThicknessAndVolumeReportStorage\UncompressedOrZlib
PresentationContext94  = MammographyCADSRStorage\UncompressedOrZlib
PresentationContext95  = MRSpectroscopyStorage\UncompressedOrZlib
PresentationContext96  = OphthalmicAxialMeasurementsStorage\UncompressedOrZlib
PresentationContext97  = OphthalmicVisualFieldStaticPerimetryMeasurementsStorage\UncompressedOrZlib
PresentationContext98  = ProcedureLogStorage\UncompressedOrZlib
PresentationContext99  = PseudoColorSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext100 = RadiopharmaceuticalRadiationDoseSRStorage\UncompressedOrZlib
PresentationContext101 = RawDataStorage\UncompressedOrZlib
PresentationContext102 = RealWorldValueMappingStorage\UncompressedOrZlib
PresentationContext103 = RespiratoryWaveformStorage\UncompressedOrZlib
PresentationContext104 = RTBeamsDeliveryInstructionStorage\UncompressedOrZlib
PresentationContext105 = RTBeamsTreatmentRecordStorage\UncompressedOrZlib
PresentationContext106 = RTBrachyApplicationSetupDeliveryInstructionStorage\UncompressedOrZlib
PresentationContext107 = RTBrachyTreatmentRecordStorage\UncompressedOrZlib
PresentationContext108 = RTDoseStorage\UncompressedOrZlib
PresentationContext109 = RTIonBeamsTreatmentRecordStorage\UncompressedOrZlib
PresentationContext110 = RTIonPlanStorage\UncompressedOrZlib
PresentationContext111 = RTPlanStorage\UncompressedOrZlib
PresentationContext112 = RTStructureSetStorage\UncompressedOrZlib
PresentationContext113 = RTTreatmentSummaryRecordStorage\UncompressedOrZlib
PresentationContext114 = SegmentationStorage\UncompressedOrZlib
PresentationContext115 = SimplifiedAdultEchoSRStorage\UncompressedOrZlib
PresentationContext116 = SpatialFiducialsStorage\UncompressedOrZlib
PresentationContext117 = SpatialRegistrationStorage\UncompressedOrZlib
PresentationContext118 = SpectaclePrescriptionReportStorage\UncompressedOrZlib
PresentationContext119 = StereometricRelationshipStorage\UncompressedOrZlib
PresentationContext120 = SubjectiveRefractionMeasurementsStorage\UncompressedOrZlib
PresentationContext121 = SurfaceScanMeshStorage\UncompressedOrZlib
PresentationContext122 = SurfaceScanPointCloudStorage\UncompressedOrZlib
PresentationContext123 = SurfaceSegmentationStorage\UncompressedOrZlib
PresentationContext124 = TractographyResultsStorage\UncompressedOrZlib
PresentationContext125 = TwelveLeadECGWaveformStorage\UncompressedOrZlib
PresentationContext126 = VisualAcuityMeasurementsStorage\UncompressedOrZlib
PresentationContext127 = XAXRFGrayscaleSoftcopyPresentationStateStorage\UncompressedOrZlib
PresentationContext128 = XRayRadiationDoseSRStorage\UncompressedOrZlib
#
# the following SOP classes are missing in the above list:
#
# - AdvancedBlendingPresentationStateStorage
# - CArmPhotonElectronRadiationStorage
# - ColorPaletteStorage
# - EncapsulatedSTLStorage
# - MultipleVolumeRenderingVolumetricPresentationStateStorage
# - OphthalmicOpticalCoherenceTomographyBscanVolumeAnalysisStorage
# - OphthalmicOpticalCoherenceTomographyEnFaceImageStorage
# - PatientRadiationDoseSRStorage
# - PerformedImagingAgentAdministrationSRStorage
# - PlannedImagingAgentAdministrationSRStorage
# - ProtocolApprovalStorage
# - RTPhysicianIntentStorage
# - RTRadiationSetStorage
# - RTSegmentAnnotationStorage
# - SegmentedVolumeRenderingVolumetricPresentationStateStorage
# - VolumeRenderingVolumetricPresentationStateStorage
#
# - RETIRED_HardcopyColorImageStorage
# - RETIRED_HardcopyGrayscaleImageStorage
# - RETIRED_NuclearMedicineImageStorage
# - RETIRED_UltrasoundImageStorage
# - RETIRED_UltrasoundMultiframeImageStorage
# - RETIRED_VLImageStorage
# - RETIRED_VLMultiframeImageStorage
# - RETIRED_XRayAngiographicBiPlaneImageStorage
#
# - RETIRED_StandaloneCurveStorage
# - RETIRED_StandaloneModalityLUTStorage
# - RETIRED_StandaloneOverlayStorage
# - RETIRED_StandalonePETCurveStorage
# - RETIRED_StandaloneVOILUTStorage
# - RETIRED_StoredPrintStorage
#
# - DRAFT_RTBeamsDeliveryInstructionStorage
# - DRAFT_SRAudioStorage
# - DRAFT_SRComprehensiveStorage
# - DRAFT_SRDetailStorage
# - DRAFT_SRTextStorage
# - DRAFT_WaveformStorage
#
# - DICOS_2DAITStorage
# - DICOS_3DAITStorage
# - DICOS_CTImageStorage
# - DICOS_DigitalXRayImageStorageForPresentation
# - DICOS_DigitalXRayImageStorageForProcessing
# - DICOS_QuadrupoleResonanceStorage
# - DICOS_ThreatDetectionReportStorage
#
# - DICONDE_EddyCurrentImageStorage
# - DICONDE_EddyCurrentMultiframeImageStorage

# ============================================================================
[[Profiles]]
# ============================================================================

[Default]
PresentationContexts = GenericStorageSCP

[AllDICOM]
PresentationContexts = AllDICOMStorageSCP
