function data = readpkl(pklFilePath)
    % Make sure to replace 'your_file.pkl' with the actual path to your .pkl file
    %pklFilePath = 'your_file.pkl';
    
    % Load the Python pickle module
    %import py.pickle as pickle
    import pickle.* 
    % Open the pickle file in binary mode
    %fileID = fopen(pklFilePath, 'rb');
    fileID =py.open(pklFilePath,'rb');
    % Read the content of the pickle file
    data = py.pickle.load(fileID);
    
    % Close the file
    %fclose(fileID);
    py.close(fileID);
    
    % Now you can work with the loaded data in the 'data' variable
    disp(data);
end