dcmrecv(1)                        OFFIS DCMTK                       dcmrecv(1)



NAME
       dcmrecv - Simple DICOM storage SCP (receiver)


SYNOPSIS
       dcmrecv [options] port

DESCRIPTION
       The  dcmrecv  application implements a Service Class Provider (SCP) for
       the Storage Service Class.  In  contrast  to  the  well-known  storescp
       utility,  dcmrecv  has  less options and might, therefore, be easier to
       use - this also explains the term  'simple'  in  the  title.  The  main
       purpose  of  this  application  is  to  receive  a whole bunch of DICOM
       datasets from a Storage Service Class User (SCU) and store  them  to  a
       configurable directory and file structure.

PARAMETERS
       port  tcp/ip port number to listen on

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

         +v    --verbose-pc
                 show presentation contexts in verbose mode

   network options
       association negotiation profile from configuration file:

         -xf   --config-file  [f]ilename, [p]rofile: string
                 use profile p from configuration file f

       application entity title:

         -aet  --aetitle  [a]etitle: string
                 set my AE title (default: DCMRECV)

         -uca  --use-called-aetitle
                 always respond with called AE title

       other network options:

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes (default: 16384)

         -dhl  --disable-host-lookup  disable hostname lookup

   output options
       general:

         -od   --output-directory  [d]irectory: string (default: ".")
                 write received objects to existing directory d

       subdirectory generation:

         -s    --no-subdir
                 do not generate any subdirectories (default)

         +ssd  --series-date-subdir
                 generate subdirectories from series date

       filename generation:

         +fd   --default-filenames
                 generate filename from instance UID (default)

         +fu   --unique-filenames
                 generate unique filename based on new UID

         +fsu  --short-unique-names
                 generate short pseudo-random unique filename

         +fst  --system-time-names
                 generate filename from current system time

         -fe   --filename-extension  [e]xtension: string (default: none)
                 append e to all generated filenames

       storage mode:

         -B    --normal
                 allow implicit format conversions (default)

         +B    --bit-preserving
                 write dataset exactly as received

               --ignore
                 ignore dataset, receive but do not store it

NOTES
   Typical Usage
       A typical use case of dcmrecv is to receive SOP instances that are sent
       from a storage SCU and save them as DICOM files. The following  command
       does exactly this:

       dcmrecv --verbose <port> --config-file storescp.cfg default

       If  you  prefer  some  automatically  created  subdirectory  structure,
       shorter file names and the extension '.dcm' for all  DICOM  files,  use
       the following command:

       dcmrecv -v -xf storescp.cfg default <port> --series-date-subdir
                                                  --short-unique-names
                                                  --filename-extension .dcm

       In case of very large SOP instances or if the dataset should be written
       exactly as received (e.g. for debugging purposes), the 'bit  preserving
       mode' could be used:

       dcmrecv -v -xf storescp.cfg default <port> --bit-preserving

       The  received  datasets  are always stored as DICOM files with the same
       Transfer Syntax as used for the network transmission.

   DICOM Conformance
       Basically, the dcmrecv application supports all Storage SOP Classes  as
       an  SCP,  including  private  ones.  This  requires,  however,  that  a
       corresponding  association  negotiation  profile  is  loaded   from   a
       configuration file. The format and semantics of this configuration file
       are documented in asconfig.txt.

       By default, that means if no association negotiation profile is loaded,
       dcmrecv  only  supports  the  Verification  SOP  Class  as an SCP (with
       default transfer syntax, i.e. Implicit VR Litte Endian).

       In the future,  there  might  be  additional  options  that  allow  for
       specifying   the   list   of   supported  Presentation  Contexts  (i.e.
       combination of SOP Class and Transfer Syntaxes) directly, i.e.  without
       loading a configuration file.

   Subdirectory Generation
       The  option  --series-date-subdir  allows for generating subdirectories
       (below the specified output directory) based on the value of  the  data
       element  Series  Date  (0008,0021)  from the received DICOM dataset. If
       this value could be retrieved from  the  dataset  and  is  valid  (i.e.
       consists of a valid DICOM date field), the subdirectory structure is as
       follows:

       <output-directory>/data/<year>/<month>/<day>/<filename>

       If the Series Date (0008,0021) cannot be retrieved or is  invalid,  the
       current system date is used for the following subdirectory structure:

       <output-directory>/undef/<year><month><day>/<filename>

       In  both cases, <year> consists of 4 decimal digits and <month> as well
       as <day> of 2 decimal digits.

   Filename Generation
       By default, the filenames for storing the received DICOM  datasets  are
       generated according to the following scheme:

       <short-modality-prefix>.<sop-instance-uid><filename-extension>

       If  the  same  SOP  instance  is  received  twice, a warning message is
       reported and the existing file is overwritten.

       The option --unique-filenames  makes  sure  that  each  received  DICOM
       dataset  is  stored  as  a  separate file, i.e. no files should ever be
       overwritten. This is done by using a newly  created  unique  identifier
       (UID) for each generated filename (and the infix '.X' in order to avoid
       conflicts with real SOP Instance UID values).  The  naming  scheme  for
       this option is as follows:

       <short-modality-prefix>.X.<unique-identifier><filename-extension>

       When  option  --short-unique-names is used, the filenames are generated
       by some pseudo-random name generator, which also makes sure that  there
       are no conflicts (i.e. existing files are not overwritten). This is the
       naming scheme:

       <short-modality-prefix>_<pseudo-random-name><filename-extension>

       With  <pseudo-random-name>  consisting  of  16  digits  in  hexadecimal
       notation.

       Finally,  option  --system-time-names  allows  for generating filenames
       based on the current system time:

       <date><time>.<short-modality-prefix><filename-extension>

       With  <date>  consisting  of   '<year><month><day>'   and   <time>   of
       '<hour><minute><second>.<micro-second>'.  Please  note that this scheme
       could result in naming conflicts if the resolution of the  system  time
       is not sufficiently high (i.e. does not support microseconds).

   Limitations
       Please  note  that option --bit-preserving cannot be used together with
       option  --series-date-subdir  since  the  received  dataset  is  stored
       directly  to  file  and  the  value  of the Series Date (0008,0021) is,
       therefore, not available before the file has been created.

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

EXIT CODES
       The  dcmrecv  utility  uses  the following exit codes when terminating.
       This enables the user to check  for  the  reason  why  the  application
       terminated.

   general
       EXITCODE_NO_ERROR                         0
       EXITCODE_COMMANDLINE_SYNTAX_ERROR         1

   input file errors
       EXITCODE_CANNOT_READ_INPUT_FILE          20 (*)

   output file errors
       EXITCODE_CANNOT_WRITE_OUTPUT_FILE        40 (*)
       EXITCODE_INVALID_OUTPUT_DIRECTORY        45

   network errors
       EXITCODE_CANNOT_INITIALIZE_NETWORK       60 (*)
       EXITCODE_CANNOT_START_SCP_AND_LISTEN     64
       EXITCODE_INVALID_ASSOCIATION_CONFIG      66

       (*)  Actually,  these codes are currently not used by dcmrecv but serve
       as a placeholder for the corresponding group of exit codes.

ENVIRONMENT
       The dcmrecv utility  will  attempt  to  load  DICOM  data  dictionaries
       specified  in the DCMDICTPATH environment variable. By default, i.e. if
       the  DCMDICTPATH  environment   variable   is   not   set,   the   file
       <datadir>/dicom.dic  will be loaded unless the dictionary is built into
       the application (default for Windows).

       The  default  behavior  should  be  preferred   and   the   DCMDICTPATH
       environment  variable  only used when alternative data dictionaries are
       required. The DCMDICTPATH environment variable has the same  format  as
       the  Unix  shell PATH variable in that a colon (':') separates entries.
       On Windows systems, a semicolon (';') is used as a separator. The  data
       dictionary  code  will  attempt  to  load  each  file  specified in the
       DCMDICTPATH environment variable. It is an error if no data  dictionary
       can be loaded.

FILES
       <docdir>/asconfig.txt - configuration file documentation
       <etcdir>/storescp.cfg - example association negotiation profile

SEE ALSO
       dcmsend(1), storescu(1), storescp(1)

COPYRIGHT
       Copyright  (C)  2013-2017  by OFFIS e.V., Escherweg 2, 26121 Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     dcmrecv(1)
