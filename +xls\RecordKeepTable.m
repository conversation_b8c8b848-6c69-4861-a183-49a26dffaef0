classdef RecordKeepTable <xls.TableBase   
    properties (Access = private)

    end
    
    methods
        function obj = RecordKeepTable(varargin)
            <EMAIL>(varargin{:}); 
            if exist(TableFileName(obj), 'file')
                try
                    obj.LoadTable(TableFileName(obj),  'VariableNamingRule', 'preserve'); 
                    if ~isempty(obj.m_Table)
                        key= TableUniqueKey(obj);
                        vals = obj.m_Table{:, key}; 
                        if isnumeric(vals)
                            obj.m_Table.(key) = arrayfun(@(x)(num2str(x)), vals, 'uniformoutput', false); 
                        end
                    end
                catch err
                    disp(err.message);
                end
            else
                obj.m_Table = table; 
            end
        end

        function res = RecordUpdateVaribles(self)
            res = self.getoptioni('RecordUpdateVaribles');
        end
        
        function info = GetRow_struct(obj, rowname)
            info = []; 
            if isempty(obj.m_Table)
                return;
            end
            key  = TableUniqueKey(obj);
            strs = obj.m_Table.(key); 
            %I = find(ismember(strs, data.(key))); 
            [flag, loc] = ismember(rowname, strs);
            if flag
                info = table2struct(obj.m_Table(loc, :)); 
            end
        end
        
  
        
        function [flag, m] = IsUptoDate(obj, data)
            flag = 0; m=[]; 
            key  = TableUniqueKey(obj);
            if isempty(obj.m_Table)
%                 obj.m_Table = table; 
                return
            end
            
            strs = obj.m_Table.(key); 
            %I = find(ismember(strs, data.(key))); 
            [flag, loc] = ismember(data.(key), strs);
            m = loc(flag); 
%             if isempty(I)
%                 return
%             end
            
            %flag = 1; 
            
%             m = I(1); 
%             fns = fieldnames(data); 
%             flag = 1; 
%             for k=1:numel(fns)
%                 name = fns{k}; 
%                 if ~isnumeric(data.(name)) || strcmp(name, key) 
%                     continue; 
%                 end
%                 try
%                 flag = flag && obj.m_Table{m, name} >= data.(name); 
%                 catch
%                 end
%             end
        end
        
        function [count, T1] = UpdateFromTable(obj, T, varargin)
            M   = size(T, 1); 
            flag = zeros(M, 1, 'like', true); 
            for m=1:M
                flag(m) = obj.UpdateRow(T(m, :), varargin{:});
                %res  = res + flag; 
            end
            count = sum(flag); 
            if nargout>=2
                T1 = T(flag, :);
            end
        end
        
        function res = UpdateRow(obj, data, overwrite)
            if ~exist('overwrite', 'var')
                overwrite = 1; 
            end
            
            [flag, m] = IsUptoDate(obj, data);
            
            if flag && ~overwrite
                res = 0; 
                return;
            end
            
            if isempty(m)
                m= size(obj.m_Table, 1)+1; 
            end
            fns = fieldnames(data); 
            
            varnames = obj.GetVariableNames();
            if ~isempty(varnames)
                fns = intersect(fns, varnames);
            end
            
            for k=1:numel(fns)
                name = fns{k}; 
                try
                    val = data.(name); 
                    if ischar(val)
                        obj.m_Table{m, name} = {val};
                    else
                        obj.m_Table{m, name} = val;
                    end
                catch
                end
            end
            res = 1; 
        end
        
        function info = GetTableRow(obj, uid, tostruct)
            info = [];
            if isempty(obj.m_Table)
                return;
            end
            if ~exist('tostruct', 'var')
                tostruct = 1; 
            end
             
            key  = TableUniqueKey(obj);           
            strs = obj.m_Table.(key); 
            [flag, loc] = ismember(uid, strs);
            loc(loc<=0)=[];
            
            info = obj.m_Table(loc, :); 
            if tostruct
                info = table2struct(info); 
            end
        end
    end
end

