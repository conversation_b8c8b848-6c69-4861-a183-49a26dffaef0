classdef ArtDaemonLicProj <SAProject  
    properties
    end
    
    methods 
        function self = ArtDaemonLicProj(varargin)
            self@SAProject(varargin{:}); 
            filepath = fileparts(mfilename('fullpath')); 
            addpath(fullfile(filepath,  '..')); 
        end
        
        function res = BuildType(self)
             res = 'console'; 
%            res = self.getoptioni('buildtype', 'standalone') ;
        end
        
        function res = privateFolder(self)
            res = {}; 
        end
        
        function res = RecursivePath(self)
           SourcePath = [self.SolutionRootPath];
           %res = {[SourcePath 'repo-common\Common\base\'], [SourcePath 'repo-common\Common\model\'], [SourcePath 'repo-common\Common\RT\']};
           res = {[SourcePath 'repo-common\Common\']};
        end
        
        function res = ProjectPath(self)
            SourcePath = [self.SolutionRootPath];
            dcmpath   = [SourcePath 'dcmserver\src\'];
            res = {dcmpath}; 
        end
    end
    
    methods (Static)
        function name = AppName()
            name = 'ArtDaemonLic'; 
        end
        
        function res = mFiles
            res = {'ArtDaemonLic.m'}; 
        end
        
        function res = Toolboxes
            res = {'matlab',  'textanalytics'};
        end  
        
    end
 end

