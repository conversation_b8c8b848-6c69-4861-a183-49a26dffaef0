classdef DcmConverter < daemon.taskdef.TaskDef
    properties
        
    end

    methods
        function obj = DcmConverter(varargin)         
            <EMAIL>(varargin{:});
            obj.SetDefaultCfg;
        end
    end

    methods (Static)
        function SynCT(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            taskdefname  = ['MR_SynCT2Dcm'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder='SynCTm_Unity2mm';
            synctfile =['image.nii.gz'];
            obj = daemon.taskdef.DcmConverter({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=struct(...
	     	    "filename",     synctfile,...
	     	    "taskfilename", "../MetsSeg/SynCTm_Unity2mm/MR.[SeriesInstanceUID].tsk");
            tempfname= ['../image_dcm.json'];
            copytags = cat(2, dicom.utils.VhImage2Dcm.Tags_Patient(), dicom.utils.VhImage2Dcm.Tags_Study());
            copytags = xls.TableBase.Content2Str(copytags);
            dcminfos{1} = struct('FileName', tempfname, 'CopyTagName', copytags);
            dcminfos{2} = struct('SeriesDescription', 'MR2SynCT', 'Manufacturer', 'MR2SynCT');
            Process = struct('OperationType','VolImage2Dcm','VolImage',synctfile, 'Modality', 'CT', 'OutputFolder','dcm/');
            %Process = str; 
            Process.DcmInfo=dcminfos; 
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});

            taskdefname  = ['MR_SynCT2TPS'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder='SynCTm_Unity2mm';
            
            obj = daemon.taskdef.DcmConverter({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=struct(...
	     	    "filename",     ['MR_SynCT2Dcm.tsk'],...
	     	    "taskfilename", 'MR_SynCT2Dcm/MR.[SeriesInstanceUID].tsk');
            Process =struct('OperationType','ExportDcm', ... 
                'InputDcm', ['dcm'],...
                'DcmExportPort', 'TPS');
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'IsCustomTask', 1});
        end
    end
end