dcmmklut(1)                       OFFIS DCMTK                      dcmmklut(1)



NAME
       dcmmklut - Create DICOM look-up tables


SYNOPSIS
       dcmmklut [options] dcmimg-out

DESCRIPTION
       The  dcmmklut utility allows one to create a variety of DICOM Modality,
       Presentation and VOI look-up tables  (LUT)  and  is  intended  for  the
       creation  of test images. The utility is able to read an existing DICOM
       image file, create a look  up  table  according  to  the  command  line
       options,  and write the new object back to file. It is also possible to
       create a new object containing the specified  LUT  without  reading  an
       existing  DICOM  image  file.  This  is  e.g.  useful  to  simply store
       different look-up tables in a DICOM-like structure. As  a  default  the
       output file is encoded with the same transfer syntax used for the input
       file, but the transfer syntax can also be specified as a  command  line
       option.

       The  LUT  data  can be derived from the shape of a gamma curve (default
       for the gamma factor is 1) or imported from a file (currently  the  MAP
       format   from   Ulead's  PhotoImpact  and  a  simple  text  format  are
       supported). The input and output width of the LUT can also be specified
       in  the  range  allowed by the DICOM standard. The interpolation of the
       input range is done by a polynomial curve fitting algorithm.

       In addition to the DICOM output file the LUT data can also be  exported
       to a tabbed text file which allows the easy visualization of the curves
       with a common spread sheet application (e.g. Microsoft Excel).

PARAMETERS
       dcmimg-out  DICOM output filename

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   LUT creation options
       LUT type:

         +Tm  --modality
                create as Modality LUT

         +Tp  --presentation
                create as Presentation LUT

         +Tv  --voi
                create as VOI LUT (default)

       LUT placement:

         +Pa  --add
                add to existing transform
                (default for and only with --voi)

         +Pr  --replace
                replace existing transform
                (default for --modality and --presentation)

       LUT content:

         +Cg  --gamma  [g]amma: float
                use gamma value (default: 1.0)

         +Cm  --map-file  [f]ilename: string
                read input data from MAP file

         +Ct  --text-file  [f]ilename: string
                read input data from text file

       LUT options:

         +Og  --inverse-gsdf
                apply inverse GSDF (print presentation LUT in OD)

              --min-density  [v]alue: integer (0..65535, default: 20)
                set min density to v (in hundreds of OD)

              --max-density  [v]alue: integer (0..65535, default: 300)
                set max density to v (in hundreds of OD)

         +Oi  --illumination  [v]alue: integer (0..65535, default: 2000)
                set illumination to v (in cd/m^2)

         +Or  --reflection  [v]alue: integer (0..65535, default: 10)
                set reflected ambient light to v (in cd/m^2)

       LUT structure:

         -b   --bits  [n]umber: integer
                create LUT with n bit values (8..16, default: 16)

         -e   --entries  [n]umber: integer
                create LUT with n entries (1..65536, default: 256)

         -f   --first-mapped  [n]umber: integer
                first input value mapped (-31768..65535, default: 0)

         -r   --random  [n]umber: unsigned integer
                perform n randomly selected permutations on the LUT

         -rs  --random-seed  [n]umber: unsigned integer
                initialize the random-number generator with n
                (default: 0, for reproducible results)

         -o   --order  [n]umber: integer
                use polynomial curve fitting algorithm with order n
                (0..99, default: 5)

         -E   --explanation  [n]ame: string
                LUT explanation (default: automatically created)

       LUT data alignment:

         -a   --byte-align
                create byte-aligned LUT
                (default for and only with 8 bit values)

         +a   --word-align
                create word-aligned LUT
                (default for 9-16 bit values)

       LUT data VR:

         +Dw  --data-ow
                write LUT Data as OW (default)

         +Du  --data-us
                write LUT Data as US

         +Ds  --data-ss
                write LUT Data as SS (minimal support)

   file options
         +Fi  --dicom-input  [f]ilename: string
                read dataset from DICOM file f

         +Fo  --text-output  [f]ilename: string
                write LUT data to tabbed text file f

NOTES
       Please check the DICOM standard for further restrictions on the look-up
       table structure. Especially the number of bits per table entry might be
       restricted in particular IODs.

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  dcmmklut  utility  will  attempt  to  load DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

FILES
       <datadir>/philips.lut - sample LUT in text format

COPYRIGHT
       Copyright (C) 1998-2014 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmmklut(1)
