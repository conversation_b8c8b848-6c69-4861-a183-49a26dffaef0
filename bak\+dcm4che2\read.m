function [ dcm ] = read( filename )
%DICOMREAD Summary of this function goes here
%   Detailed explanation goes here

dcm = org.dcm4che2.data.BasicDicomObject;
try
    fs = java.io.FileInputStream(filename);
    bis = java.io.BufferedInputStream(fs);
    din = org.dcm4che2.io.DicomInputStream(bis);
    % din = org.dcm4che2.io.DicomInputStream(java.io.BufferedInputStream(java.io.FileInputStream(filename)));
    din.readDicomObject(dcm, -1);
    din.close();
catch exc
    if exist('din', 'var')
        din.close();
    elseif exist('bis', 'var')
        bis.close();
    elseif exist('fs', 'var')
        fs.close();
    end
    rethrow(exc);
end
