
Release 3.6.0 (Public Release - 2011-01-06)

- Updated contents for the final release version of DCMTK 3.6.0.
  Affects: ANNOUNCE.360
           CMakeLists.txt
           INSTALL
           VERSION
           config/configure
           config/configure.in
           doxygen/manpages/man1/*

**** Changes from 2011.01.05 (s<PERSON><PERSON><PERSON>er)

- Remove all windows line endings that were recently introduced.
  Affects: ofstd/tests/tstthred.cc

**** Changes from 2011.01.05 (onken)

- Updated thread test in order to reflect missing OFSemaphore class
  on Mac OS X.
  Affects: ofstd/tests/tstthred.cc

**** Changes from 2011.01.05 (riesmeier)

- Updated information on tested FreeBSD systems.
  Affects: INSTALL

**** Changes from 2011.01.04 (onken)

- Disabled class OFSemaphore under Mac OS X since semaphores must be named
  in Mac OS X to work properly (currently, they are not). Threads are now
  again enabled by default in Mac OS X with CMake.
  Affects: CMakeLists.txt
           INSTALL
           ofstd/include/dcmtk/ofstd/ofthread.h
           ofstd/libsrc/ofthread.cc

**** Changes from 2011.01.03 (r<PERSON><PERSON><PERSON>)

- Updated copyright date to the current year.
  Affects: COPYRIGHT

- Updated details on tested systems based on feedback from external testers.
  Affects: INSTALL

- Commented out unused variable in order to avoid warning message (gcc 2.95.3).
  Affects: dcmjpls/libcharls/header.cc

**** Changes from 2010.12.23 (riesmeier)

- Updated contents for DCMTK 3.6.0-RC3 (third release candidate).
  Affects: CMakeLists.txt
           VERSION
           config/configure
           config/configure.in
           doxygen/manpages/man1/*

- Added FreeBSD 6.4 and 8.1 (x86) with gcc to the list of tested systems.
  Affects: INSTALL

**** Changes from 2010.12.22 (riesmeier)

- Added list of people who have contributed to this new release of the DCMTK.
  Affects: ANNOUNCE.360

- Fixed some typos and little inconsistencies. Updated homepage of libxml.
  Affects: INSTALL

- Added support for OpenBSD 4.x: Do not specify any additional C/C++ flags.
  Affects: config/configure
           config/configure.in

- OpenBSD does not like sigfillset() to be called with :: namespace prefix.
  Affects: oflog/libsrc/threads.cc

- Added OpenBSD 4.4 with gcc 3.3.5 (propolice) to the list of tested systems.
  Affects: INSTALL

**** Changes from 2010.12.21 (onken)

- Fixed wrong response assignment in DcmSCU's C-STORE code. Thanks to
  forum user "takeos" for the hint and fix.
  Affects: dcmnet/libsrc/scu.cc

**** Changes from 2010.12.20 (schlachter)

- Fixed on FreeBSD by removing flags that hide strlcpy() and strlcat().
  Affects: CMakeLists.txt

- Correctly detect netinet/tcp.h and other headers on FreeBSD.
  Affects: CMake/GenerateDCMTKConfigure.cmake

**** Changes from 2010.12.20 (riesmeier)

- Fixed possible NULL pointer dereferencing when checking the meta-header
  version (and no data dictionary is loaded).
  Affects: dcmdata/libsrc/dcfilefo.cc

- Fixed wrong console application description (apparently copied from another
  tool) and output the resource identifier in debug mode to the logger.
  Affects: dcmdata/tests/tstpread.cc

- Added explicit type casts in order to keep gcc 2.95.3 quiet.
  Affects: ofstd/tests/toffile.cc
           dcmdata/libsrc/dcmetinf.cc

- Added short description on environment variables CC and CFLAGS.
  Affects: INSTALL

- Updated information on IRIX system that was used for cross-platform testing.
  Affects: INSTALL

**** Changes from 2010.12.17 (riesmeier)

- Include another system header (stdio.h) needed for IRIX 6.3 with gcc 2.95.3.
  Affects: dcmwlm/wwwapps/Makefile.dep
           dcmwlm/wwwapps/preplock.cc

- Check whether "feof" and "ferror" are defined as macros (e.g. on IRIX 6.3).
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Updated hyperlink to SGI's Notes on building Open Source software on IRIX.
  Affects: INSTALL

**** Changes from 2010.12.16 (riesmeier)

- Fixed initialization of OFString variable (reported by gcc 2.95.3).
  Thanks to Andreas Barth <<EMAIL>> for the hint.
  Affects: dcmtls/libsrc/tlsscu.cc

- Added another Linux system to the list of tested Unix-like systems.
  Affects: INSTALL

- Slightly revised contents of the HISTORY file.
  Affects: HISTORY

- Added explicit type cast in order to keep VisualStudio 2005 quiet.
  Affects: dcmdata/tests/i2dbmp.cc

**** Changes from 2010.12.15 (riesmeier)

- Updated contents for DCMTK 3.6.0-RC2 (second release candidate).
  Affects: CMakeLists.txt
           VERSION
           config/configure
           config/configure.in
           doxygen/manpages/man1/*

- Updated list of tested Unix-like systems (some are still marked with a '?').
  Affects: INSTALL

- Added missing prefix "ios::" to I/O manipulator "fixed" (reported on HP-UX).
  Affects: dcmpstat/apps/dcmmklut.cc

**** Changes from 2010.12.15 (schlachter)

- Fixed a compile time error on HP-UX in oflog.
  Affects: oflog/libsrc/unixsock.cc

- Made OFFile compile successfully on HP-UX.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Fixed a problem with a missing prototype for vsnprintf on HP-UX.
  Affects: config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           dcmdata/apps/xml2dcm.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmsr/libsrc/dsrxmld.cc

**** Changes from 2010.12.13 (schlachter)

- Fixed the explicit LFS test which produced wrong results on FreeBSD.
  Affects: config/aclocal.m4
           config/configure

- Fix toffile by moving some casts to the correct position.
  Affects: ofstd/tests/toffile.cc

**** Changes from 2010.12.13 (riesmeier)

- Removed FreeBSD flags introduced a couple of months ago since they caused
  more problems than they solved.
  Thanks to Alexander Haderer <<EMAIL>> for the hint.
  Affects: config/configure
           config/configure.in

- Consistently use "large file support" for LFS instead of "long file support".
  Affects: config/aclocal.m4
           config/include/dcmtk/config/cfunix.h.in

- Added LFS configure options and other minor changes.
  Affects: INSTALL

**** Changes from 2010.12.10 (riesmeier)

- Slightly revised wording after further proof-reading.
  Affects: ANNOUNCE.360

**** Changes from 2010.12.09 (riesmeier)

- Updated content of anouncement file to be used for the official release.
  However, this is only a first version that will certainly be updated again.
  Affects: ANNOUNCE.360

- Fixed minor inconsistencies (mainly in comments).
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Consistently use "large file support" for LFS instead of "long file support".
  Affects: config/configure
           config/configure.in

- Fixed typo in checking message.
  Affects: config/aclocal.m4
           config/configure
           config/configure.in

- Removed outdated note that wlmscpfs does not support multi-processing on
  Windows systems.
  Affects: INSTALL

**** Changes from 2010.12.08 (schlachter)

- Explicitely check for libtiff 3.7.0 or newer.
  Affects: dcmimage/libsrc/dipitiff.cc

- Document libtiff 3.7.4 requirement on windows.
  Affects: INSTALL

**** Changes from 2010.12.08 (riesmeier)

- Added note that libtiff releases prior to version 3.7.0 will not work.
  Affects: INSTALL

- Fixed issue when linking "oflog" on Cygwin with CMake build system.
  Affects: oflog/libsrc/CMakeLists.txt

- Disable currently unused wide character file I/O functions in order to avoid
  problems with old compilers (e.g. gcc 2.95.3).
  Affects: config/docs/macros.txt
           ofstd/include/dcmtk/ofstd/offile.h

**** Changes from 2010.12.07 (riesmeier)

- Updated list of tested Windows systems and some other related sections.
  Affects: INSTALL

**** Changes from 2010.12.06 (schlachter)

- Fixed crash in oflog on Mac OS X 10.4.1 with gcc 4.0.1 due to different order
  of execution of global destructors.
  Thanks to Bernd Kuemmerlen <<EMAIL>> for the report.
  Affects: oflog/libsrc/oflog.cc

- Made the docs for the NDC clearer on how to avoid memleaks.
  Affects: oflog/include/dcmtk/oflog/ndc.h

- Fixed a correct warning from Intel Compiler 11.1 for shifting a Uint8 by 8.
  Affects: dcmdata/libi2d/i2dbmps.cc

- Fixed the i2dbmp test case for windows by opening files in binary mode.
  Affects: dcmdata/tests/i2dbmp.cc

- Removed BufferSize option from FileAppender due to problems with gcc 2.95.3.
  Affects: oflog/include/dcmtk/oflog/fileap.h
           oflog/libsrc/fileap.cc

- Don't explicitely try to catch std::exception.
  Affects: oflog/libsrc/appender.cc
           oflog/libsrc/config.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/threads.cc

**** Changes from 2010.12.06 (riesmeier)

- Fixed issue when calling "make install-html" without "make html".
  Thanks to Juergen Salk <<EMAIL>> for pointing this out.
  Affects: doxygen/Makefile.in

- Added explicit default constructor to keep Intel Compiler 11.1 quiet.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dipxrept.h

- Moved option "-maxdepth" to beginning of find command to avoid warnings.
  Affects: dcmwlm/data/Makefile.in

- Fixed issue with large file support for current Cygwin systems (1.7.7-1).
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Restructured calculation of seek position in order to avoid warning messages.
  Affects: ofstd/tests/toffile.cc

- Fixed little inconsistency regarding the libsndfile configure options.
  Thanks to Juergen Salk <<EMAIL>> for the report and fix.
  Affects: config/configure
           config/configure.in

**** Changes from 2010.12.03 (riesmeier)

- Fixed regular expression for the detection of old Mac OS X versions (Darwin
  8 and 9). Thanks to Bernd Kuemmerlen <<EMAIL>> for the
  report and suggested fix.
  Affects: config/configure
           config/configure.in

**** Changes from 2010.12.02 (riesmeier)

- Added linker flags to a different CMake variable in order to avoid
  auto-importing warnings on MinGW systems (previously, flags were ignored).
  Affects: CMakeLists.txt

- Updated latest tested version number of LIBPNG.
  Affects: INSTALL

**** Changes from 2010.12.01 (schlachter)

- Fixed build problem with MSC6 when zlib support is enabled.
  Affects: dcmdata/libsrc/dcistrmz.cc

**** Changes from 2010.12.01 (riesmeier)

- Updated contents for DCMTK 3.6.0-RC1 (first release candidate).
  Affects: CMakeLists.txt
           VERSION
           config/configure
           config/configure.in

- Updated contents for upcoming DCMTK 3.6.0 release.
  Added:   ANNOUNCE.360
           CHANGES.360
  Removed: ANNOUNCE.355
           CHANGES.355
  Affects: INSTALL

- Added OFFIS copyright header (beginning with the year 1994).
  Affects: dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/dcompat.h
           dcmnet/include/dcmtk/dcmnet/dicom.h
           dcmnet/include/dcmtk/dcmnet/dimse.h
           dcmnet/include/dcmtk/dcmnet/diutil.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/dcompat.cc
           dcmnet/libsrc/dimcancl.cc
           dcmnet/libsrc/dimcmd.cc
           dcmnet/libsrc/dimcmd.h
           dcmnet/libsrc/dimecho.cc
           dcmnet/libsrc/dimfind.cc
           dcmnet/libsrc/dimmove.cc
           dcmnet/libsrc/dimse.cc
           dcmnet/libsrc/dimstore.cc
           dcmnet/libsrc/diutil.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulextra.cc
           dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulfsm.h
           dcmnet/libsrc/dulparse.cc
           dcmnet/libsrc/dulpres.cc
           dcmnet/libsrc/dulpriv.h
           dcmnet/libsrc/dulstruc.h

- Fixed wrong installation directory for HTML documentation and Manpages.
  Affects: doxygen/CMakeLists.txt

- Added linker flags to avoid auto-importing warnings on MinGW systems.
  Affects: CMakeLists.txt

- Removed extra token after #endif directive (reported on a MinGW system).
  Affects: oflog/libsrc/winsock.cc

- Fixed syntax error introduced with yesterday's commit (flags for Mac OS X).
  Affects: CMakeLists.txt

- Fixed wrong name of dcmqrti tool (was "ti" in previous DCMTK versions).
  Affects: dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.11.30 (onken)

- Set extra flags for compiling without problems on Mac OS X.
  Affects: CMakeLists.txt

**** Changes from 2010.11.30 (riesmeier)

- Fixed incorrect variable name to be replaced in configuration file by "sed".
  Affects: doxygen/Makefile.in

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Updated latest tested CMake version to 2.8.3.
  Affects: CMakeLists.txt

- Made sure that CMake installs the correct documentation files. Also added
  created files to the "make clean" target (only works partly for CMake 2.4).
  Finally, "make DOXYGEN" now works again (as it did previously).
  Affects: doxygen/CMakeLists.txt

- Fixed minor typos.
  Affects: CMake/3rdparty.cmake

**** Changes from 2010.11.29 (riesmeier)

- Fixed issue with CMake versions prior to 2.8 (or prior to 2.6?).
  Affects: doxygen/CMakeLists.txt

**** Changes from 2010.11.29 (schlachter)

- Made CMake build the doxygen documentation in the binary dir and added new
  targets "html" and "man" that only build parts of the available docs.
  Affects: doxygen/CMakeLists.txt
           doxygen/Makefile.in
           doxygen/htmldocs.cfg
           doxygen/manpages.cfg

- Fixed some new issues with doxygen and CMake.
  Affects: doxygen/CMakeLists.txt
           doxygen/htmldocs.cfg
           doxygen/manpages.cfg

- Fixed stack overflow in libi2d with some JPEG data streams.
  Thanks to Jargo Koster <<EMAIL>> for the report.
  Affects: dcmdata/libi2d/i2djpgs.cc

- Correctly determine the remote address if only a socket fd is given.
  Thanks to Alexander Haderer <<EMAIL>> for the patch.
  Affects: dcmnet/libsrc/dul.cc

**** Changes from 2010.11.26 (riesmeier)

- Added definition of "DEBUG" to debug mode (since CMake does not do this
  automatically). Also did some further clean-up of the main CMake file.
  Affects: CMakeLists.txt

**** Changes from 2010.11.25 (riesmeier)

- Removed redundant include directories and installation commands. Also changed
  the way version dependent documentation files are installed.
  Affects: CMakeLists.txt

- Changed the way version dependent documentation files are installed.
  Affects: Makefile
           config/rootconf

- Minor changes, e.g. fixed inconsistent whitespaces and newlines.
  Affects: CMake/3rdparty.cmake
           dcmdata/apps/CMakeLists.txt

**** Changes from 2010.11.24 (riesmeier)

- Output a warning message when decompressing a JPEG compressed image with
  YCbCr color model and BitsStored < BitsAllocated because this is currently
  not handled properly (usually this only occurs for "true lossless" mode).
  Affects: dcmjpeg/libsrc/djcodecd.cc

**** Changes from 2010.11.24 (schlachter)

- Fix CMake's strerror_r test. It was backwards.
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/dcmtkTestCharPStrerror.cc

- Hardcode the windows result of some socket-related tests.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Reintroduce some MSC6 specific defines that were lost.
  Affects: CMake/osconfig.h.in

- Add a previously missing include directive.
  Affects: oflog/include/dcmtk/oflog/helpers/syncpwin.h

- Fix config/changext so that dcml2pnm compiles on windows.
  Affects: config/changext

**** Changes from 2010.11.22 (schlachter)

- Add checks for cstdio, cstdarg, ENAMETOOLONG and strerror_r's return value.
  Added:   CMake/dcmtkTestCharPStrerror.cc
           CMake/dcmtkTestNameTooLong.cc
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h.in

- Add checks for strerror_r, std::vsnprintf and variable length arrays to CMake.
  Added:   CMake/dcmtkTestVariableLengthArrays.cc
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h.in

- Use a macro instead of repeating the filename of the test file.
  Affects: ofstd/tests/toffile.cc

- Add definition for delete (std::nothrow) and check for std::ios::nocreate.
  Added:   CMake/dcmtkTestStdIosNocreate.cc
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h.in

- Check whether accept(), getsockopt() and select() accept int* arguments.
  Added:   CMake/dcmtkTestSelectIntP.cc
           CMake/dcmtkTestSocketIntP.cc
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h.in

- Made the new CMake checks work on Windows.
  Affects: CMake/dcmtkTestSelectIntP.cc
           CMake/dcmtkTestSocketIntP.cc

**** Changes from 2010.11.22 (riesmeier)

- Updated builtin dictionary (because of recently added private tags).
  Affects: dcmdata/libsrc/dcdictzz.cc

- Added missing private tag from Philips to second spelling variant of the
  private creator element (also see last commit).
  Affects: dcmdata/data/private.dic

**** Changes from 2010.11.19 (eichelberg)

- Added several new private tags from Philips documented in a recent
  conformance statement.
  Thanks to Per Inge Mathisen <<EMAIL>> for the contribution.
  Affects: dcmdata/data/private.dic

**** Changes from 2010.11.17 (eichelberg)

- Fixed issue with data dictionary causing private tags with group number
  range and flexible element number not to be found in the dictionary. Thanks
  to Peter Klotz <<EMAIL>> for the report and patch.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdicent.h

**** Changes from 2010.11.17 (schlachter)

- Removed some uses of "%s" with sscanf().
  Affects: dcmnet/apps/movescu.cc
           dcmwlm/tests/wltest.cc

**** Changes from 2010.11.15 (schlachter)

- Fixed some errors in doxygen comments.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h

- Removed a useless and potentially dangerous use of sscanf().
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2010.11.12 (riesmeier)

- Output ANSI escape codes at the beginnig of each line in order to make sure
  that always the correct color is used in case of truncated multi-line output.
  Affects: dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcmetinf.cc

**** Changes from 2010.11.10 (schlachter)

- Made DcmHashDictIterator::stepUp correctly stop at the end of the dict.
  Affects: dcmdata/libsrc/dchashdi.cc

- Corrected DcmHashDictIterator::operator!=. Previously it ignored hindex.
  Affects: dcmdata/include/dcmtk/dcmdata/dchashdi.h

**** Changes from 2010.11.09 (riesmeier)

- Added check whether all type 1 attributes are really present and have a
  non-empty value; this check was missing for the following record types:
  HANGING PROTOCOL, PALETTE, IMPLANT, IMPLANT GROUP, IMPLANT ASSY.
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2010.11.08 (schlachter)

- Fixed even more gcc warnings caused by additional compiler flags.
  Affects: dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcistrmf.cc
           dcmdata/libsrc/dcpath.cc
           oflog/include/dcmtk/oflog/logger.h
           oflog/include/dcmtk/oflog/helpers/strhelp.h
           ofstd/include/dcmtk/ofstd/ofmap.h

- Made dcmjpls successfully read images with spurious padding bytes.
  Affects: dcmjpls/libcharls/decodstr.h

- Made oflog use ofstd's cast macros.
  Affects: oflog/include/dcmtk/oflog/helpers/strhelp.h
           oflog/include/dcmtk/oflog/helpers/syncpwin.h
           oflog/include/dcmtk/oflog/helpers/threadcf.h
           oflog/include/dcmtk/oflog/spi/factory.h
           oflog/libsrc/lloguser.cc
           oflog/libsrc/loglevel.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/ntelogap.cc
           oflog/libsrc/patlay.cc
           oflog/libsrc/sockbuff.cc
           oflog/libsrc/socket.cc
           oflog/libsrc/socketap.cc
           oflog/libsrc/threads.cc
           oflog/libsrc/timehelp.cc
           oflog/libsrc/unixsock.cc
           oflog/libsrc/winconap.cc
           oflog/libsrc/winsock.cc

**** Changes from 2010.11.08 (riesmeier)

- Fixed inconsistency in the list of attributes for the implant group directory
  record (this has been corrected with the "FT2" version of Supplement 131).
  Affects: dcmdata/libsrc/dcddirif.cc

- Replaced non-ASCII characters by their ASCII equivalents.
  Affects: dcmtls/docs/ciphers.txt

**** Changes from 2010.11.05 (riesmeier)

- Added new SOP Class UIDs from Supplement 131 and 134 (Implant Templates).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcuid.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmqrdb/docs/dcmqrscp.man

- Added support for new Implantation Plan SR Document Storage SOP Class.
  Added:   dcmsr/include/dcmtk/dcmsr/dsrimpcc.h
           dcmsr/libsrc/dsrimpcc.cc
  Affects: dcmdata/libsrc/dcddirif.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/CMakeLists.txt
           dcmsr/libsrc/Makefile.in
           dcmsr/libsrc/dsrtypes.cc

- Added support for Supplement 131 (Implant Templates) to data dictionary.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

- Added support for new directory record types IMPLANT, IMPLANT GROUP and
  IMPLANT ASSY from Supplement 131 (Implant Templates).
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdirrec.cc

- Added support for checking the value multiplicity "9" (see Supplement 131).
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvrae.h
           dcmdata/include/dcmtk/dcmdata/dcvras.h
           dcmdata/include/dcmtk/dcmdata/dcvrat.h
           dcmdata/include/dcmtk/dcmdata/dcvrcs.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvrfd.h
           dcmdata/include/dcmtk/dcmdata/dcvrfl.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlo.h
           dcmdata/include/dcmtk/dcmdata/dcvrpn.h
           dcmdata/include/dcmtk/dcmdata/dcvrsh.h
           dcmdata/include/dcmtk/dcmdata/dcvrsl.h
           dcmdata/include/dcmtk/dcmdata/dcvrss.h
           dcmdata/include/dcmtk/dcmdata/dcvrtm.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h
           dcmdata/include/dcmtk/dcmdata/dcvrul.h
           dcmdata/include/dcmtk/dcmdata/dcvrus.h
           dcmdata/libsrc/dcelem.cc
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h

- Rebuilt Makefile dependencies.
  Affects: dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/libsrc/Makefile.dep
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmjpls/apps/Makefile.dep
           dcmjpls/libsrc/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsign/libsrc/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep
           dcmsr/tests/Makefile.dep
           dcmtls/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep
           oflog/libsrc/Makefile.dep

**** Changes from 2010.11.04 (riesmeier)

- Made sure that options --exec/rename-on-eostudy are never used with --fork on
  Windows systems (since this does not work due to the current implementation).
  Affects: dcmnet/apps/storescp.cc

- Changed mode of parameter dcmfile-in from multi-optional to multi-mandatory.
  Affects: dcmpstat/apps/dcmpsprt.cc
           dcmpstat/docs/dcmpsprt.man

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: dcmpstat/apps/dcmpsprt.cc

- Moved settings for default configuration and support data directory, and
  used same default values for non-Windows systems as for GNU autoconf.
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMakeLists.txt

- Removed superfluous path separator.
  Affects: doxygen/CMakeLists.txt

**** Changes from 2010.11.03 (schlachter)

- Fixed some more warnings by gcc with by additional flags.
  Affects: dcmjpeg/libijg12/jcdiffct.c
           dcmjpeg/libijg12/jcmarker.c
           dcmjpeg/libijg12/jdmarker.c
           dcmjpeg/libijg12/jdscale.c
           dcmjpeg/libijg16/jcdiffct.c
           dcmjpeg/libijg16/jcmarker.c
           dcmjpeg/libijg16/jdmarker.c
           dcmjpeg/libijg16/jdscale.c
           dcmjpeg/libijg8/jcdiffct.c
           dcmjpeg/libijg8/jcmarker.c
           dcmjpeg/libijg8/jdmarker.c
           dcmjpeg/libijg8/jdscale.c
           dcmjpls/libcharls/header.cc
           dcmjpls/libsrc/djcparam.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/tests/msgserv.cc
           dcmsign/apps/dcmsign.cc

**** Changes from 2010.11.03 (riesmeier)

- Since the pseudo-lossless encoder is not guaranteed to result in lossless
  compression, the modifications to the DICOM header are treated in the same
  way as for lossy compression (e.g Lossy Compression Flag is set to "01").
  Affects: dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/libsrc/djcodece.cc
           dcmjpeg/libsrc/djenclol.cc
           dcmjpeg/libsrc/djencsv1.cc

**** Changes from 2010.11.02 (riesmeier)

- Added special handling for data elements that are associated with different
  VRs (according to the data dictionary) when read with an implicit transfer
  syntax, e.g. PixelPaddingValue or WaveformData.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcitem.cc

- Enhanced output to debug logger: Added more details in case of failures.
  Affects: dcmimgle/libsrc/didocu.cc

**** Changes from 2010.11.01 (schlachter)

- Fixed some compiler warnings reported by gcc with additional flags.
  Affects: dcmdata/apps/mdfdsman.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/libi2d/i2d.cc
           dcmdata/libsrc/dcpath.cc
           dcmdata/libsrc/dcrleccd.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/tests/tstpread.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/libsrc/dcuserid.cc
           dcmnet/libsrc/dfindscu.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrdbs.cc
           dcmwlm/apps/wlcefs.cc
           dcmwlm/tests/wltest.cc
           dcmwlm/wwwapps/writwlst.cc
           oflog/libsrc/patlay.cc
           oflog/libsrc/unixsock.cc
           ofstd/include/dcmtk/ofstd/ofaptr.h

- Moved variable declarations in front of their loop.
  Affects: dcmimgle/libsrc/dcmimage.cc
           dcmjpeg/libsrc/djutils.cc
           ofstd/libsrc/ofstd.cc

**** Changes from 2010.10.29 (riesmeier)

- Removed new --print-color option for Windows system since the ANSI escape
  codes are not supported by the standard command shell.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Fixed issue when sending a message with an empty dataset. Now, a warning
  message is reported to the log and an error code is returned in such cases.
  Thanks to Peter Klotz <<EMAIL>> for the report.
  Affects: dcmnet/libsrc/dimse.cc

- Added new option for colored output of the textual dump.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Added support for colored output to the print() method.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dctypes.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrui.cc

**** Changes from 2010.10.28 (riesmeier)

- Implemented missing rendering code for sigmoid VOI LUT function.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h

- Added more trace log messages which might be helpful for detailed debugging.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h

**** Changes from 2010.10.27 (riesmeier)

- Added getFloat64Vector() method which allows for retrieving the stored
  floating point values more efficiently (especially when there are many
  values). Thanks to forum user "budric" for the original report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/libsrc/dcvrds.cc

- Rebuilt Makefile dependencies.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/tests/Makefile.dep

**** Changes from 2010.10.25 (onken)

- Removed superfluous NO_IOS_BASE_ASSIGN definition for MinGW since it is
  always set if standard c++ includes are available on the system.
  Affects: CMake/osconfig.h.in

**** Changes from 2010.10.21 (riesmeier)

- Replaced tab characters by spaces.
  Affects: ofstd/libsrc/oflist.cc

- Added private undefined copy constructor and assignment operator in order to
  avoid compiler warnings (reported by gcc with additional flags).
  Affects: dcmimage/include/dcmtk/dcmimage/diargimg.h
           dcmimage/include/dcmtk/dcmimage/dipalimg.h

- Removed redundant definition of logger variable for libxml.
  Affects: dcmdata/apps/xml2dcm.cc

- Renamed variable to avoid warning reported by gcc with additional flags.
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: dcmdata/include/dcmtk/dcmdata/dcovlay.h

- Added missing API documentation and slightly reformatted source code.
  Affects: dcmdata/include/dcmtk/dcmdata/dcovlay.h

- Added virtual destructor in order to avoid warnings reported by gcc with
  additional flags.
  Affects: dcmdata/include/dcmtk/dcmdata/dctagkey.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2010.10.20 (riesmeier)

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcchrstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrae.cc
           dcmdata/libsrc/dcvras.cc
           dcmdata/libsrc/dcvrat.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrda.cc
           dcmdata/libsrc/dcvrds.cc
           dcmdata/libsrc/dcvrdt.cc
           dcmdata/libsrc/dcvrfd.cc
           dcmdata/libsrc/dcvrfl.cc
           dcmdata/libsrc/dcvris.cc
           dcmdata/libsrc/dcvrlo.cc
           dcmdata/libsrc/dcvrlt.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrof.cc
           dcmdata/libsrc/dcvrpn.cc
           dcmdata/libsrc/dcvrpobw.cc
           dcmdata/libsrc/dcvrsh.cc
           dcmdata/libsrc/dcvrsl.cc
           dcmdata/libsrc/dcvrss.cc
           dcmdata/libsrc/dcvrst.cc
           dcmdata/libsrc/dcvrtm.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/dcvrul.cc
           dcmdata/libsrc/dcvrulup.cc
           dcmdata/libsrc/dcvrus.cc
           dcmdata/libsrc/dcvrut.cc

- Renamed method to avoid warnings reported by gcc with additional flags.
  Affects: dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/libsrc/dcfilefo.cc

- Added missing API documentation.
  Affects: dcmdata/include/dcmtk/dcmdata/dcfilefo.h

**** Changes from 2010.10.20 (schlachter)

- Made sure isalpha() & friends are only called with valid arguments.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/mkdeftag.cc
           dcmimgle/apps/dcod2lum.cc
           dcmimgle/apps/dconvlum.cc
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/didispfn.cc
           dcmjpeg/libsrc/djutils.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/libsrc/dcasccff.cc
           dcmnet/libsrc/diutil.cc
           dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmsign/apps/dcmsign.cc
           dcmsr/libsrc/dsrtypes.cc
           oflog/libsrc/property.cc
           ofstd/libsrc/ofdatime.cc
           ofstd/libsrc/ofstd.cc
           ofstd/libsrc/ofstring.cc

- Also made sure toupper() and tolower() get a valid argument.
  Affects: dcmnet/libsrc/dcasccff.cc
           dcmnet/libsrc/scp.cc
           dcmpstat/libsrc/dvpscf.cc
           ofstd/libsrc/ofconfig.cc
           ofstd/libsrc/ofstd.cc

**** Changes from 2010.10.18 (schlachter)

- Correctly read bytestreams on 64-bit platforms.
  Affects: dcmjpls/libcharls/util.h

- Fixed ABS() macro.
  Affects: dcmjpls/libcharls/util.h

**** Changes from 2010.10.14 (riesmeier)

- Added support for "new" SR Storage SOP Classes to storeRequest().
  Affects: dcmqrdb/libsrc/dcmqrdbi.cc

- Fixed wording on the use of variables.
  Affects: oflog/etc/logger.cfg

- Added missing CVS log to end of file.
  Affects: oflog/etc/filelog.cfg

- Removed the word "Kuratorium" from the name of the OFFIS institute.
  Affects: dcmdata/docs/datadict.txt

- Updated copyright header. Added reference to COPYRIGHT file.
  Affects: <all modules>/*.cc
           <all modules>/*.h
           dcmdata/data/dcm2xml.dtd
           dcmdata/data/dicom.dic
           dcmdata/data/diconde.dic
           dcmdata/data/private.dic
           dcmdata/libsrc/dcdictbi.nul
           dcmdata/libsrc/vrscanl.l
           dcmdata/libsrc/vrscanl.c
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmpstat/etc/dcmpstat.cfg
           dcmpstat/etc/printers.cfg
           dcmsr/data/dsr2xml.xsd
           dcmsr/data/report.css
           dcmsr/data/reportx.css
           dcmtls/tests/dcmtk_ca.pl
           dcmwlm/perl/*.ph
           dcmwlm/perl/*.pl
           oflog/etc/filelog.cfg
           oflog/etc/logger.cfg

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Updated copyright date.
  Affects: dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmftest.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/docs/dcod2lum.man
           dcmimgle/docs/dconvlum.man
           dcmnet/docs/echoscu.man
           dcmnet/docs/termscu.man
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/docs/dcmqridx.man
           dcmsign/docs/dcmsign.man

**** Changes from 2010.10.13 (riesmeier)

- Added introducing sentence on Log4cplus library used for oflog.
  Affects: oflog/docs/LICENSE

- Separated license text of the CharLS library from general "readme" text.
  Added:   dcmjpls/docs/License.txt
           dcmjpls/docs/readme.txt
  Removed: dcmjpls/docs/charls-readme.txt

- Added explaining sentence on modules that are not part of the free toolkit.
  Affects: COPYRIGHT

- Removed text for "dcmjp2k" module which is not part of the free toolkit.
  Affects: COPYRIGHT

- Revised copyright text to make clear that the DCMTK uses the BSD License.
  Affects: COPYRIGHT

- Replaced copyright header since source code has been rewritten completely.
  Previously: "Copyright (C) 1993/1994, OFFIS, Oldenburg University and CERIUM"
  Affects: dcmnet/libsrc/dimdump.cc

**** Changes from 2010.10.12 (riesmeier)

- Added man page file for the new command line tool "dcml2pnm".
  Added: doxygen/manpages/man1/dcml2pnm.1

- Removed wrong reference to JPEG output format.
  Affects: dcmjpls/docs/dcml2pnm.man

**** Changes from 2010.10.12 (schlachter)

- Make dcml2pnm build correctly with cmake.
  Affects: dcmjpls/apps/CMakeLists.txt

- Added dcml2pnm which is a dcmjpls-enabled dcm2pnm.
  Added:   dcmjpls/apps/dcml2pnm.cc
           dcmjpls/docs/dcml2pnm.man
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmjpls/apps/CMakeLists.txt
           dcmjpls/apps/Makefile.dep
           dcmjpls/apps/Makefile.in
           dcmjpls/docs/dcmjpls.dox
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libsrc/Makefile.dep

- Added working implementation of DJLSDecoderBase::decodeFrame().
  Affects: dcmjpls/libsrc/djcodecd.cc

- Avoid redundant findAndGet*() calls.
  Affects: dcmjpls/include/dcmtk/dcmjpls/djcodecd.h
           dcmjpls/libsrc/djcodecd.cc

**** Changes from 2010.10.08 (schlachter)

- Fixed an invalid copyright date.
  Affects: ofstd/tests/tvec.cc

- Documented that we now have our own OFVector class.
  Affects: config/docs/macros.txt

- Use OFVector instead of using std::vector directly.
  Affects: dcmjpls/libcharls/context.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/intrface.cc
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/procline.h
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/streams.h

- Implement OFVector.
  Added:   ofstd/tests/tvec.cc
  Affects: ofstd/include/dcmtk/ofstd/ofvector.h
           ofstd/tests/Makefile.dep
           ofstd/tests/Makefile.in

- Updated documentation for HAVE_STL macro.
  Affects: config/docs/macros.txt

- Removed an invalid function which isn't part of std::auto_ptr.
  Affects: ofstd/include/dcmtk/ofstd/ofaptr.h

- Fixed an invalid use of OFauto_ptr.
  Affects: oflog/libsrc/oflog.cc

- Added macro HAVE_STL_AUTO_PTR which defines OFauto_ptr to std::auto_ptr.
  Affects: config/docs/macros.txt
           ofstd/include/dcmtk/ofstd/ofaptr.h

- Fixed all doxygen warnings for OFPair and OFauto_ptr.
  Affects: ofstd/include/dcmtk/ofstd/ofaptr.h
           ofstd/include/dcmtk/ofstd/ofmap.h

- Make OFString(NULL) construct an empty string. Previously, this was the
  default, but it was changed to match the behavior of std::string.
  This adds a new define USE_NULL_SAFE_OFSTRING which is always defined by
  our Makefiles. Passing NULL to OFString now also generates a warning in
  DEBUG builds.
  Affects: CMakeLists.txt
           config/Makefile.def.in
           config/docs/macros.txt
           ofstd/libsrc/ofstring.cc

- Don't directly include system header files.
  Affects: dcmjpls/libcharls/config.h
           dcmjpls/libcharls/context.h
           dcmjpls/libcharls/deftrait.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/intrface.h
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/procline.h
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/streams.h
           dcmjpls/libcharls/util.h

**** Changes from 2010.10.08 (riesmeier)

- Enhanced documentation on pixel-related parameters.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h

**** Changes from 2010.10.07 (riesmeier)

- Fixed minor Doxygen API documentation issues (added backslash in order to
  avoid that the short description ends at the first period).
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/include/dcmtk/dcmnet/scu.h

- Removed leading underscore characters from preprocessor symbols (reserved).
  Affects: dcmpstat/include/dcmtk/dcmpstat/dcmpstat.h
           dcmpstat/include/dcmtk/dcmpstat/dvcache.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsab.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsabl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsall.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscu.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscul.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsda.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsdal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsdef.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsfs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsga.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgr.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgrl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsib.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsibl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsmsg.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsov.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsovl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspr.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsri.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsril.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrsl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssv.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssvl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstx.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstxl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstyp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvw.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvwl.h

**** Changes from 2010.10.06 (riesmeier)

- Replaced numeric tags by pre-defined tag names for old ACR/NEMA palettes.
  Affects: dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dipalimg.cc

- Use DCM_UndefinedTagKey if no explanation tag can be specified for a LUT.
  Affects: dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dipalimg.cc
           dcmimgle/libsrc/diluptab.cc

- Removed definition of HAVE_STD_STRING for non-Windows systems.
  Affects: CMake/osconfig.h.in

- Added text on the fact that the --key option also supports attribute paths.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/docs/img2dcm.man

- Introduced meaningful sub groups for the processing command line options.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/docs/img2dcm.man

**** Changes from 2010.10.05 (riesmeier)

- Added preliminary support for VOI LUT function. Please note, however, that
  the sigmoid transformation is not yet implemented.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/docs/dcm2pnm.man
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/dimoimg3.cc
           dcmimgle/libsrc/dimoimg4.cc
           dcmimgle/libsrc/dimoimg5.cc
           dcmjpeg/docs/dcmj2pnm.man

- Output more information on the image, e.g. value of PresentationLUTShape.
  Also slightly changed the error handling for some image transformations.
  Affects: dcmimage/apps/dcm2pnm.cc

- In debug mode, output more details on overlay plane to the logger.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h

- Output information on conversion process to the logger (debug mode).
  Affects: dcmimage/include/dcmtk/dcmimage/dicoopxt.h

- Added section listing the supported input transfer syntaxes.
  Affects: dcmimage/docs/dcm2pnm.man
           dcmjpeg/docs/dcmj2pnm.man

- Also remove PixelPaddingRangeLimit element from the dataset (if required).
  Affects: dcmimage/libsrc/diquant.cc
           dcmimgle/libsrc/diimage.cc

- Fixed various Doxygen API documentation issues.
  Affecs: dcmdata/include/dcmtk/dcmdata/dcdatset.h
          dcmimage/include/dcmtk/dcmimage/dicoopxt.h
          dcmimgle/include/dcmtk/dcmimgle/dibaslut.h
          dcmimgle/include/dcmtk/dcmimgle/diimage.h
          ofstd/include/dcmtk/ofstd/ofset.h
          ofstd/include/dcmtk/ofstd/ofsetit.h

**** Changes from 2010.10.05 (schlachter)

- Removed a unused copy-constructor and a unused operator=.
  Affects: dcmnet/include/dcmtk/dcmnet/dcuserid.h
           dcmnet/libsrc/dcuserid.cc

- Fixed all remaining warnings from -Wall -Wextra -pedantic.
  Affects: dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djutils.cc
           dcmnet/libsrc/dcuserid.cc

- Re-apply build fixes for MSVC6.
  Affects: dcmjpls/libcharls/intrface.h
           dcmjpls/libcharls/scan.h

- Removed Sint64 and Uint64 since there is no 64bit int available everywhere.
  Affects: ofstd/include/dcmtk/ofstd/oftypes.h

- Update dcmjpls to newest CharLS snapshot.
  Added:   dcmjpls/libcharls/pubtypes.h
  Affects: dcmjpls/apps/Makefile.dep
           dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmjpls/docs/charls-readme.txt
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/clrtrans.h
           dcmjpls/libcharls/config.h
           dcmjpls/libcharls/context.h
           dcmjpls/libcharls/ctxtrmod.h
           dcmjpls/libcharls/decodstr.h
           dcmjpls/libcharls/deftrait.h
           dcmjpls/libcharls/encodstr.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/header.h
           dcmjpls/libcharls/intrface.cc
           dcmjpls/libcharls/intrface.h
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/lltraits.h
           dcmjpls/libcharls/lokuptbl.h
           dcmjpls/libcharls/procline.h
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/streams.h
           dcmjpls/libcharls/util.h
           dcmjpls/libsrc/Makefile.dep
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc

**** Changes from 2010.10.04 (riesmeier)

- Introduced new substitution variable "#r" for the calling presentation
  address (i.e. the hostname or IP address of the peer storage SCU).
  Thanks to Gerhard Holzinger <<EMAIL>> for the proposal.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Fixed issue with codec registry when compiled on Linux x86_64 with "configure
  --disable-threads" (replaced "#ifdef _REENTRANT" by "#ifdef WITH_THREADS").
  Thanks to forum user "mask" for the original report and Andreas Barth
  <<EMAIL>> for the suggested fix.
  Affects: dcmdata/include/dcmtk/dcmdata/dccodec.h
           dcmdata/libsrc/dccodec.cc

- Replaced "#ifdef _REENTRANT" by "#ifdef WITH_THREADS" where appropriate (i.e.
  in all cases where OFMutex, OFReadWriteLock, etc. are used).
  Affects: dcmdata/include/dcmtk/dcmdata/dcdict.h
           dcmdata/include/dcmtk/dcmdata/dcistrmf.h
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcistrmf.cc
           dcmdata/libsrc/dcuid.cc
           dcmimgle/include/dcmtk/dcmimgle/diobjcou.h
           ofstd/include/dcmtk/ofstd/ofconsol.h
           ofstd/include/dcmtk/ofstd/ofglobal.h
           ofstd/libsrc/ofconsol.cc

- Further enhancements on BlendingSequence (e.g. check whether it exists before
  trying to copy certain data elements from it).
  Affects: dcmdata/libsrc/dcddirif.cc

- Undefine macro FAR before defining it in order to avoid compiler warnings
  (C4005 - macro redefinition) reported by VisualStudio 2008.
  Affects: dcmjpeg/libijg12/jmorecfg12.h
           dcmjpeg/libijg16/jmorecfg16.h
           dcmjpeg/libijg8/jmorecfg8.h

- Added explicit type cast in order to keep VisualStudio 2008 quiet.
  Affects: dcmdata/tests/tstpread.cc
           dcmsign/libsrc/simaccon.cc

- Fixed various Doxygen API documentation issues.
  Affecs: dcmdata/include/dcmtk/dcmdata/dcddirif.h
          dcmsr/include/dcmtk/dcmsr/dsrtypes.h

- Fixed typo.
  Affects: dcmjpeg/docs/dcmcjpeg.man

**** Changes from 2010.10.01 (riesmeier)

- Added support for the BlendingSequence required for directory records of the
  Blending Softcopy Presentation State Storage SOP Class.
  Affects: dcmdata/libsrc/dcddirif.cc

- Added new helper function findAndInsertCopyOfElement().
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcitem.cc

- Added support for new non-image Storage SOP Classes that require the new
  directory record type MEASUREMENT. Also fixed issues with other record types.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Added new directory record type MEASUREMENT from Supplement 144.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcdirrec.cc

**** Changes from 2010.10.01 (schlachter)

- Fix some code duplication in the FileAppenders.
  Affects: oflog/libsrc/fileap.cc

- Added an explicit "private" to work around AIX 5.2's IBM compiler.
  Thanks to Michael Kraemer from GSI for the report.
  Affects: oflog/include/dcmtk/oflog/spi/factory.h

- Fixed most compiler warnings from -Wall -Wextra -pedantic in dcmdata.
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcrleccd.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcxfer.cc
           dcmdata/libsrc/vrscani.h

- Fixed most compiler warnings in remaining modules.
  Affects: dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmtls/include/dcmtk/dcmtls/tlsscu.h

**** Changes from 2010.09.30 (riesmeier)

- Added support for new non-image Storage SOP Classes that require the new
  directory record types PALETTE and SURFACE. Also updated existing records.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Added new directory record types HL7 STRUC DOC, PALETTE and SURFACE.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcdirrec.cc

- Added two Model UIDs from Supplement 118 (Application Hosting).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcuid.cc

- Added support for the Spectacle Prescription Report IOD.
  Added:   dcmsr/include/dcmtk/dcmsr/dsrspecc.h
           dcmsr/libsrc/dsrspecc.cc
  Affects: dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/CMakeLists.txt
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/Makefile.dep
           dcmsr/libsrc/Makefile.in

- Added support for the Macular Grid Thickness and Volume Report IOD.
  Added:   dcmsr/include/dcmtk/dcmsr/dsrmaccc.h
           dcmsr/libsrc/dsrmaccc.cc
  Affects: dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/CMakeLists.txt
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/Makefile.dep
           dcmsr/libsrc/Makefile.in

- Renamed class and enumeration related to the Key Object Selection Document.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrkeycc.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrkeycc.cc
           dcmsr/libsrc/dsrtypes.cc

- Fixed incomplete comment.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h

- Fixed typo in the name of a Storage SOP Class (copied from DICOM part 6).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcuid.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmqrdb/docs/dcmqrscp.man

- Added missing call to DIMSE_dumpMessage_start() for C-GET-RQ message.
  Affects: dcmnet/libsrc/dimdump.cc

**** Changes from 2010.09.29 (riesmeier)

- Enhanced checking and reporting of standard violations in write() methods.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrimgfr.cc
           dcmsr/libsrc/dsrreftn.cc
           dcmsr/libsrc/dsrsc3gr.cc
           dcmsr/libsrc/dsrscogr.cc
           dcmsr/libsrc/dsrtcodt.cc
           dcmsr/libsrc/dsrtcosp.cc
           dcmsr/libsrc/dsrtcoto.cc
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/dsrwavch.cc

- Added support for the recently introduced, optional PreliminaryFlag.
  Affects: dcmsr/data/dsr2xml.xsd
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrtypes.cc

- Used more specific "moduleName" for getAndCheckElementFromDataset() and
  checkElementValue().
  Affects: dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrreftn.cc
           dcmsr/libsrc/dsrsoprf.cc

- Enhanced comments on attributes from Enhanced General Equipment Module.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h

- Fixed wrong by-reference relationships in constraint checker for Colon CAD.
  Affects: dcmsr/libsrc/dsrcolcc.cc

**** Changes from 2010.09.28 (riesmeier)

- Added support for Enhanced General Equipment Module which is required for
  both X-Ray Radiation Dose SR and Colon CAD SR.
  Affects: dcmsr/data/dsr2xml.xsd
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrtypes.cc

- Added support for Colon CAD SR which requires a new value type (SCOORD3D).
  Added:   dcmsr/include/dcmtk/dcmsr/dsrcolcc.h
           dcmsr/include/dcmtk/dcmsr/dsrsc3gr.h
           dcmsr/include/dcmtk/dcmsr/dsrsc3tn.h
           dcmsr/include/dcmtk/dcmsr/dsrsc3vl.h
           dcmsr/libsrc/dsrcolcc.cc
           dcmsr/libsrc/dsrsc3gr.cc
           dcmsr/libsrc/dsrsc3tn.cc
           dcmsr/libsrc/dsrsc3vl.cc
  Affects: dcmsr/apps/Makefile.dep
           dcmsr/data/dsr2xml.xsd
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/CMakeLists.txt
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/Makefile.dep
           dcmsr/libsrc/Makefile.in
           dcmsr/tests/Makefile.dep

- Added new non-image Storage SOP Classes that do not require a new directory
  record type (e.g. SR DOCUMENT and WAVEFORM).
  Affects: dcmdata/libsrc/dcddirif.cc

- Added VL Whole Slide Microscopy Image Storage SOP Class (Supplement 145).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcuid.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmqrdb/docs/dcmqrscp.man

- Made sure that no NULL pointer is passed to the OFString constructor.
  Affects: dcmsr/libsrc/dsrxmld.cc

- Updated comment on the requirements for POLYLINE according to CP-233.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrscovl.h
           dcmsr/libsrc/dsrscovl.cc

- Use new isEmpty() method instead of length in order to determine whether the
  element value is empty (e.g. for checking the presence of type 3 attributes).
  Affects: dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2010.09.27 (riesmeier)

- Added comment to retired transfer syntaxes and changed order of transfer
  syntax definitions according to their UID value.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h

- Updated list of SOP Class UIDs and well-known SOP Instance UIDs according to
  the current edition of the DICOM standard (including final text supplements).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcuid.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmqrdb/docs/dcmqrscp.man

- Added newly introduced multi-frame image SOP classes to DVD MPEG2 profile.
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2010.09.24 (riesmeier)

- Compared names of SOP Class UIDs with 2009 edition of the DICOM standard. The
  resulting name changes are mainly caused by the fact that the corresponding
  SOP Class is now retired.
  NB: Both users and developers should be aware of these changes and modify
  their scripts and/or source code accordingly. This is a one-time effort.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcuid.cc
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/dimomod.cc
           dcmjpeg/libsrc/djcodecd.cc
           dcmjpeg/libsrc/djcodece.cc
           dcmjpls/libsrc/djcodecd.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmpstat/libsrc/dcmpstat.cc
           dcmpstat/libsrc/dviface.cc
           dcmpstat/libsrc/dvpsib.cc
           dcmpstat/libsrc/dvpssp.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrsrv.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/libsrc/dsrtypes.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.09.23 (riesmeier)

- Removed outdated comment copied from a previous edition of the DICOM standard
  and removed references to CP 359 and 571.
  Affects: dcmsr/libsrc/dsrcomcc.cc

**** Changes from 2010.09.22 (riesmeier)

- Moved evaluation version of non-public DICOM tools to new module "dcmeval".
  Affects: CMakeLists.txt
           config/changext

- Added evaluation versions of non-public DICOM tools "mppsscu" and "stcomscu".
  Affects: config/changext

**** Changes from 2010.09.16 (schlachter)

- Log DUL errors on the ERROR level instead of INFO.
  Affects: dcmnet/apps/storescp.cc

- Handle empty PDVs correctly again.
  Affects: dcmnet/libsrc/dulfsm.cc

**** Changes from 2010.09.15 (riesmeier)

- Added definition of XML encoding transfer syntax (Supplement 114) and JPIP
  referenced transfer syntaxes (Supplement 106).
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/include/dcmtk/dcmdata/dcxfer.h
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcxfer.cc

**** Changes from 2010.09.14 (schlachter)

- Verify the length fields in the PDUs that we receive.
  Affects: dcmnet/include/dcmtk/dcmnet/dcuserid.h
           dcmnet/libsrc/dcuserid.cc
           dcmnet/libsrc/dulparse.cc

- Log DUL errors on the INFO level instead of DEBUG.
  Affects: dcmnet/apps/storescp.cc

**** Changes from 2010.09.14 (riesmeier)

- Added support for Supplement 145 (Whole Slide Microscopic Image IOD and SOP
  Classes) to DICOM data dictionary.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

- Fixed issue with missing characters (e.g. apostrophe) in created text files.
  Affects: doxygen/Makefile.in

- Added support for CP 650, 988, 1010 and 1024 to DICOM data dictionary.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2010.09.13 (riesmeier)

- Fixed issue with non-blocking mode in Move SCU (given timeout was ignored).
  Thanks to forum user "focus" for the original report.
  Affects: dcmnet/apps/movescu.cc

**** Changes from 2010.09.10 (riesmeier)

- Revised warning message to indicate that existing DICOM file is overwritten.
  Affects: dcmnet/apps/storescp.cc

**** Changes from 2010.09.09 (riesmeier)

- Removed unused (or never used?) configuration entries.
  Affects: dcmqrdb/docs/dcmqrcnf.txt
           dcmqrdb/etc/dcmqrscp.cfg
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcnf.h
           dcmqrdb/libsrc/dcmqrcnf.cc

- Further code clean-up and minor changes to log messages.
  Affects: dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrsrv.cc

- Made log messages more consistent. Replaced '\n' by OFendl where appropriate.
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/libsrc/dcmqrcbf.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrcbs.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrsrv.cc
           dcmqrdb/libsrc/dcmqrtis.cc

- Added support for Supplement 146 (Ophthalmic Visual Field Static Perimetry
  Measurements Storage SOP Class) to DICOM data dictionary and fixed typos in
  recent additions for Supplement 144.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

- Fixed wrong typecast on callback data in progressCallback() function.
  Thanks to forum user "Shaeto" for the report and suggested fix.
  Affects: dcmnet/libsrc/dfindscu.cc

- Added support for "MPEG2 Main Profile @ High Level" transfer syntax.
  Affects: dcmnet/apps/echoscu.cc

- Fixed typo in OFCondition constants for SCP/SCU role selection failures.
  Thanks to Peter Klotz <<EMAIL>> for the report.
  Affects: dcmnet/include/dcmtk/dcmnet/cond.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/cond.cc
           dcmnet/libsrc/dcasccfg.cc

**** Changes from 2010.09.08 (schlachter)

- Fix wrong use of logical invert. A bit-wise invert is meant here.
  Affects: dcmnet/libsrc/dulconst.cc

**** Changes from 2010.09.07 (schlachter)

- Fixed a missing addOption() call.
  Affects: dcmnet/apps/storescp.cc

- Fixed a possible read beyond the end of the PDV buffer.
  Affects: dcmnet/libsrc/dulfsm.cc

**** Changes from 2010.09.03 (schlachter)

- Make the VR scanner work on windows again.
  Affects: dcmdata/libsrc/vrscani.h
           dcmdata/libsrc/vrscanl.c
           dcmdata/libsrc/vrscanl.h
           dcmdata/libsrc/vrscanl.l

**** Changes from 2010.09.02 (riesmeier)

- Added support for "MPEG2 Main Profile @ High Level" transfer syntax.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/include/dcmtk/dcmdata/dcxfer.h
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcxfer.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmnet/libsrc/dimse.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrsrv.cc

- Added support for Supplement 144 (Ophthalmic Axial Measurements Storage SOP
  Classes) to DICOM data dictionary.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

- Added support for Supplement 120 (Extended Presentation States) to DICOM data
  dictionary and fixed some wrong definitions from previous version.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2010.09.02 (schlachter)

- Use longjmp() for error handling in the VR scanner.
  Added:   dcmdata/libsrc/vrscani.h
  Affects: dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/vrscan.cc
           dcmdata/libsrc/vrscanl.c
           dcmdata/libsrc/vrscanl.h
           dcmdata/libsrc/vrscanl.l

- Fix a compiler warning about an unused function.
  Affects: dcmdata/libsrc/vrscanl.c
           dcmdata/libsrc/vrscanl.h
           dcmdata/libsrc/vrscanl.l

- Added a test for UT VR with an invalid character.
  Affects: dcmdata/tests/tstchval.cc

- The VR scanner now only copies the input data once, not twice.
  Affects: dcmdata/include/dcmtk/dcmdata/vrscan.h
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/vrscan.cc

- Avoid a pointless data-copy in DcmElement::scanValue() if possible.
  Affects: dcmdata/libsrc/dcelem.cc

- Add the VR prefix in the scanner instead of adding it in the caller.
  Affects: dcmdata/include/dcmtk/dcmdata/vrscan.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/vrscan.cc

- Made the VR scanner reentrant again.
  Affects: dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/vrscan.cc
           dcmdata/libsrc/vrscanl.c
           dcmdata/libsrc/vrscanl.h
           dcmdata/libsrc/vrscanl.l

**** Changes from 2010.08.27 (onken)

- Fixed cmake compilation under unix according to changes regarding flex
  version transition.
  Affects: dcmdata/libsrc/CMakeLists.txt

**** Changes from 2010.08.26 (riesmeier)

- Fixed incorrect behavior of association acceptors during SCP/SCU role
  selection negotiation.
  Affects: dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/cond.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/cond.cc
           dcmnet/libsrc/dcasccfg.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulfsm.cc

- Introduced new global flag which allows for rejecting presentation contexts
  in case of an unsuccessful SCP/SCU role selection (disabled by default).
  Affects: dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/dul.cc

- Minor changes to output format of ACSE parameters.
  Affects: dcmnet/libsrc/assoc.cc

**** Changes from 2010.08.26 (schlachter)

- Ported vrscan from ancient flex++ to current flex version.
  Added:   dcmdata/libsrc/vrscanl.c
           dcmdata/libsrc/vrscanl.h
           dcmdata/libsrc/vrscanl.l
  Affects: dcmdata/include/dcmtk/dcmdata/vrscan.h
           dcmdata/libsrc/Makefile.in
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/vrscan.cc

- The FileAppender was using the wrong variable for a check.
  Affects: oflog/libsrc/fileap.cc

- Added a test that breaks DcmUnlimitedText. This has to be fixed.
  Affects: dcmdata/tests/tstchval.cc

**** Changes from 2010.08.25 (riesmeier)

- Output details on ACSE response parameters in case the association is
  rejected because of "No Acceptable Presentation Contexts".
  Affects: dcmnet/libsrc/scp.cc

**** Changes from 2010.08.24 (schlachter)

- Fixed some resource leaks in dcmtls (FDs and memory was leaked).
  Thanks to Peter Klotz <<EMAIL>> for submitting a patch.
  Affects: dcmnet/libsrc/dcmtrans.cc
           dcmtls/libsrc/tlstrans.cc

- Fixed a NULL pointer dereference if ASC_acknowledgeAssociation() was called
  without a previous ASC_acceptContextsWithPreferredTransferSyntaxes().
  Thanks to Peter Klotz <<EMAIL>> for finding and fixing
  this.
  Affects: dcmnet/libsrc/dulconst.cc

**** Changes from 2010.08.24 (onken)

- Added -fPIC C compiler flag neede to compile under 64 bit linux.
  Thanks to Steve Pieper <<EMAIL>> for the hint.
  Affects: CMakeLists.txt

**** Changes from 2010.08.23 (eichelberg)

- Minor changes needed for compilation on MSVC6
  Affects: dcmdata/tests/tstpath.cc
           dcmdata/tests/tstpread.cc

**** Changes from 2010.08.19 (schlachter)

- Made OFString follow the C++ standard for std::string::assign().
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h
           ofstd/libsrc/ofstring.cc

- Fixed a comparision that checks for a valid buffer.
  Affects: ofstd/libsrc/ofstd.cc

- Removed unused variables.
  Affects: dcmjpls/libsrc/djcodecd.cc

**** Changes from 2010.08.18 (riesmeier)

- Revised warning message to indicate that existing pixel data raw files are
  never overwritten.
  Affects: dcmdata/libsrc/dcvrobow.cc

- Added const specifier to char pointers where appropriate. Thanks to forum
  user "takeos" for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/libsrc/dcdicdir.cc

**** Changes from 2010.08.13 (riesmeier)

- Fixed wrong call to DcmItem::putAndInsertSint32() for setting the value of
  NumberOfFrames (0028,0008) which has a value representation of IS (not SL).
  Thanks to Peter Klotz <<EMAIL>> for the original report.
  Affects: dcmimage/libsrc/dicoimg.cc

**** Changes from 2010.08.12 (onken)

- Fixed MinGW compilation also for building with cmake.
  Affects: CMake/GenerateDCMTKConfigure.cmake
           oflog/libsrc/winsock.cc

- Make sure that configure/make works on current MinGW systems.
  Affects: oflog/libsrc/factory.cc

**** Changes from 2010.08.12 (riesmeier)

- Made sure that "arpa/inet.h" is only included when available on the system.
  Affects: oflog/libsrc/unixsock.cc

- Added explicit type cast for fourth parameter of setsockopt() which is
  required for some systems.
  Affects: oflog/libsrc/unixsock.cc

**** Changes from 2010.08.10 (schlachter)

- Fixed some cases where dcmFindNameOfUID() returning NULL could cause crashes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/mkdictbi.cc
           dcmnet/apps/storescu.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/scu.cc

- Removed undefined function printFileErrorMessage().
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h

- Fixed some unlikely problems with NULL pointers.
  Affects: ofstd/libsrc/ofconapp.cc
           ofstd/libsrc/ofstd.cc

**** Changes from 2010.08.10 (riesmeier)

- Re-added newline character removed by last commit and moved another newline
  to the right place.
  Affects: dcmdata/tests/tstpath.cc

**** Changes from 2010.08.09 (onken)

- Fixed path test.
  Affects: dcmdata/tests/tstpath.cc

- Added CMakeLists.txt for dcmdata tests.
  Affects: dcmdata/CMakeLists.txt
           dcmdata/tests/CMakeLists.txt

**** Changes from 2010.08.09 (riesmeier)

- Updated data dictionary to 2009 edition of the DICOM standard. From now on,
  the official "keyword" is used for the attribute name which results in a
  number of minor changes (e.g. "PatientsName" is now called "PatientName").
  NB: Both users and developers should be aware of these changes and modify
  their scripts and/or source code accordingly. This is a one-time effort.
  Affects: dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/data/dicom.dic
           dcmdata/data/dumppat.txt
           dcmdata/docs/dcmdata.dox
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/img2dcm.man
           dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/include/dcmtk/dcmdata/dctag.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/libi2d/i2d.cc
           dcmdata/libsrc/dccodec.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdictzz.cc
           dcmdata/tests/tstpath.cc
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/docs/findscu.man
           dcmnet/docs/storescp.man
           dcmpstat/include/dcmtk/dcmpstat/dcmpstat.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssp.h
           dcmpstat/libsrc/dcmpstat.cc
           dcmpstat/libsrc/dviface.cc
           dcmpstat/libsrc/dvpsib.cc
           dcmpstat/libsrc/dvpssp.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqridx.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrtis.h
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrtis.cc
           dcmsign/libsrc/sicreapr.cc
           dcmsr/docs/dcmsr.dox
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/libsrc/dsrdoc.cc
           dcmwlm/docs/wlmscpfs.man
           dcmwlm/include/dcmtk/dcmwlm/wlds.h
           dcmwlm/include/dcmtk/dcmwlm/wlfsim.h
           dcmwlm/libsrc/wlds.cc
           dcmwlm/libsrc/wlfsim.cc
           dcmwlm/wwwapps/readoviw.cc
           dcmwlm/wwwapps/readwlst.cc
           dcmwlm/wwwapps/writwlst.cc

- Made sure that no NULL pointer is passed to the OFString constructor. Thanks
  to Peter Klotz <<EMAIL>> for the report and patch.
  Affects: dcmsr/libsrc/dsrdoctn.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.08.06 (schlachter)

- Fixed some more compiler warnings.
  Affects: dcmimage/libsrc/dipipng.cc
           dcmnet/libsrc/dul.cc
           ofstd/include/dcmtk/ofstd/ofmap.h

**** Changes from 2010.08.05 (schlachter)

- Fixed some warnings from -Wold-style-cast.
  Affects: dcmdata/libi2d/i2dbmps.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/libsrc/dcistrmz.cc
           dcmdata/tests/tstpread.cc
           dcmtls/libsrc/tlslayer.cc
           dcmtls/libsrc/tlstrans.cc
           oflog/include/dcmtk/oflog/oflog.h
           oflog/libsrc/sockbuff.cc
           oflog/libsrc/unixsock.cc
           ofstd/tests/toffile.cc

**** Changes from 2010.07.30 (onken)

- Added error code for new (non-public) module dcmppscu.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h

**** Changes from 2010.07.26 (riesmeier)

- Changed value of _XOPEN_SOURCE for FreeBSD systems to 600 in order to enable
  certain features of unistd.h. Thanks to Pietro Cerutti <<EMAIL>> for
  the report and suggested patch.
  Affects: config/configure
           config/configure.in

**** Changes from 2010.07.27 (onken)

- Changed lately introduced defines for uchar, ushort, uint, ulong to typedefs
  to be more consistend with old cfunix.h behaviour and to not collide with QT.
  Thanks to Peter Klotz <<EMAIL>> for hint and suggested
  patch.
  Affects: CMake/osconfig.h.in

**** Changes from 2010.07.26 (riesmeier)

- Made sure that no NULL pointer is passed to the OFString constructor. Thanks
  to Peter Klotz <<EMAIL>> for the report and patch.
  Affects: dcmimgle/libsrc/dimoimg.cc

- Fixed typo (and revised documentation on the OFSTRING_GUARD macro).
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h

**** Changes from 2010.07.23 (riesmeier)

- Added reference to extended sample configuration file to oflog documentation.
  Affects: oflog/docs/oflog.dox

**** Changes from 2010.07.22 (riesmeier)

- Made sure that the size of the buffer for partial access to pixel data is
  always an even number of bytes.
  Thanks to Jeroen Eggermont <<EMAIL>> for the original report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h

**** Changes from 2010.07.21 (onken)

- Added some compiler definitions for unix systems that were not taken
  over into cmake for unix. Added some missing include directories (did
  not have any effect, so far).
  Affects: CMakeLists.txt

- Added checks for uchar, ushort, uint, ulong, longlong and ulonglong needed
  by private dcmjp2k module. Also introduced corresponding typedefs as needed
  by dcmjp2k.
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h

**** Changes from 2010.07.21 (riesmeier)

- Introduced new guard macro that makes sure that a C string is never NULL.
  Useful when passing a C string to a OFString constructor or an output stream.
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h

- Enhanced check of input directory variable (also check for empty string).
  Affects: dcmdata/apps/dcmgpdir.cc

- Made sure that no NULL pointer is passed to the OFString constructor.
  Affects: dcmdata/libsrc/dcddirif.cc

- Made sure that no NULL pointer is passed to the OFString constructor. This
  occurred when option --recurse was used without --pattern, or option
  --no-recurse was used without --input-directory.
  Affects: dcmdata/apps/dcmgpdir.cc

- Fixed CVS log entry and updated copyright date.
  Affects: dcmimgle/libsrc/dimoimg.cc

- Fixed memory leak when using processNextFrames(): DiOverlay object was
  created multiple times. Thanks to forum user "takeos" for the report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/libsrc/dimoimg.cc

- Made sure that no NULL pointer is passed to the OFString constructor. This
  occurred when option --scan-directories was used without --scan-pattern.
  Affects: dcmdata/apps/dcmdump.cc
           dcmnet/apps/storescu.cc

- Fixed possibly wrong calculation of rangeToBits() for negative values.
  Thanks to forum user "takeos" for the report and suggested fix.
  Affects: dcmimgle/libsrc/diutils.cc

**** Changes from 2010.07.16 (onken)

- Fixed VC6 compilation and worked on Borland support.
  Re-inserted some compiler definitions that were in old cfwin32.h but
  are missing in the newly introduced CMake compilation mechanism:
  Added some Visual Studio specific checks disabling some warnings
  (4284 for <= VC6, 4996 and 4351 for > VC6). Added define for HAVE_VSNPRINTF
  (<= VC6). Added _MT definition for Borland with threads. Moved some
  compiler-specific textblocks in order to keep related definitions together.
  Affects: CMake/osconfig.h.in

- Introduced flag to increase internal compiler heap for VC6 since some systems
  fail to compile dcmimgle with the default setting.
  Affects: CMakeLists.txt

**** Changes from 2010.07.16 (schlachter)

- Only generate a single CMakeOutput.log and CMakeError.log. Previously, our own
  code used a different path than CMake itself and we got four log files.
  Affects: CMake/CheckFunctionWithHeaderExists.cmake
           CMake/GenerateDCMTKConfigure.cmake

- Update the affected CMake file in config/changext.
  Affects: config/changext

- Added a check to CMake for a broken 'typename' implementation.
  Added:   CMake/dcmtkTestTypename.cc
  Affects: CMake/GenerateDCMTKConfigure.cmake
           CMake/osconfig.h.in

**** Changes from 2010.07.07 (onken)

- Changed binary OR to logical OR (error introduced by last commit).
  Affects: dcmdata/libsrc/dcddirif.cc

- Added Ophthalmic Photography SOP Classes also to SCP list.
  Affects: doxygen/manpages/man1/dcmqrscp.1

- Corrected names of two Ophthalmic Photography SOP Classes. Thanks to Peter
  Klotz <<EMAIL>> for the hint and patch.
  Affects: dcmqrdb/docs/dcmqrscp.man
           doxygen/manpages/man1/dcmqrscp.1

- Added Ophthalmic Tomography Image Storage to list of supported SOP classes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcuid.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/etc/storescp.cfg
           dcmnet/etc/storescu.cfg
           dcmqrdb/docs/dcmqrscp.man
           doxygen/manpages/man1/dcmqrscp.1
           doxygen/manpages/man1/dcmqrscp.1
           doxygen/manpages/man1/storescp.1
           doxygen/manpages/man1/storescu.1

**** Changes from 2010.07.06 (onken)

- Fixed status flag in dictionary denoting that a dictionary is loaded which
  was not set if a single dictionary was loaded explicitly with
  loadDictionary(). In that case isDictionaryLoaded() returned OFFalse where
  OFTrue would be correct. Thanks to forum user "Chichon" for the bug report.
  Affects: dcmdata/libsrc/dcdict.cc

- Fixed memory leaked in extended negotiaton code. Thanks to Peter Klotz
  <<EMAIL>> for the hint and patch.
  Affects: dcmnet/libsrc/extneg.cc

**** Changes from 2010.07.05 (schlachter)

- Remove some duplicate checks from the cmake files.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Always define STDIO_NAMESPACE to :: on window to make MSVC6 happy.
  Affects: CMake/osconfig.h.in

**** Changes from 2010.07.02 (schlachter)

- Include the correct headers while checking for std::vsnprintf and vfprintf.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Added comment explaining why an even buffer size is required.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/libsrc/dcpixel.cc

**** Changes from 2010.07.02 (riesmeier)

- Introduced new macro "DONT_LOAD_EXTERNAL_DICTIONARIES" that allows for
  loading the builtin dictionary only (at application start), i.e. ignore the
  environment variable DCMDICTPATH and the default dictionary files.
  Thanks to forum user "Niels Dekker" for the suggestion.
  Affects: config/docs/macros.txt
           dcmdata/libsrc/dcdict.cc

- Fixed typo: Changed "SUPRESS_CREATE_STAMP" to "SUPPRESS_CREATE_STAMP".
  Affects: config/docs/macros.txt
           dcmdata/libsrc/mkdeftag.cc
           dcmdata/libsrc/mkdictbi.cc

- Moved CMAKE_POLICY settings in order to avoid warnings during configure.
  Affects: CMakeLists.txt

**** Changes from 2010.07.01 (riesmeier)

- Moved SeriesDescription (0008,103E) from General Series to SR Document Series
  Module (according to CP 703).
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/libsrc/dsrdoc.cc

- Replaced "OFString::length() == 0" by "OFString::empty()".
  Affects: dcmdata/libsrc/dcpath.cc

- Removed superfluous curly brackets.
  Affects: dcmdata/libsrc/dcpath.cc

**** Changes from 2010.07.01 (onken)

- Revised osconfig.h generation: Fixed pthread test by including pthread.h into
  compiled test program. Added test for std::vsnprintf which sets
  HAVE_PROTOTYPE_STD__VSNPRINTF. Slightly adapted and explained chekcs for
  socklen_t and std::ios_base::openmode. Turned useless(?) library searches
  for iostream, nsl and socket into comments.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Simplified and fixed 3rd-party library support (a second time...).
  Affects: CMakeLists.txt
           CMake/3rdparty.cmake
           CMake/GenerateDCMTKConfigure.cmake

- Fixed 3rd-party compiler switches for windows (libraries not found are now
  reliably disabled if no path is given). Also added some status messages
  printed in CMake configure step.
  Affects: CMakeLists.txt
           CMake/3rdparty.cmake
           CMake/GenerateDCMTKConfigure.cmake

**** Changes from 2010.06.30 (riesmeier)

- Added missing end statement to close a verbatim block.
  Affects: dcmpstat/docs/dcmprscu.man

**** Changes from 2010.06.29 (riesmeier)

- Added new module "dcmstcom" as an optional extension to the documentation.
  Affects: doxygen/htmldocs.dox
           doxygen/htmldocs.cfg

- Re-added old feature that was present in previous versions of the CMake
  files: commented definition of DCMTK_BUILD_DATE and non-public modules.
  Affects: CMakeLists.txt

- Re-added old feature that was present in previous versions of the CMake
  files: set policy CMP0003 to "old" in order to work around warnings about
  escaped quotes in ADD_DEFINITIONS statements.
  Affects: CMakeLists.txt

**** Changes from 2010.06.29 (schlachter)

- Avoid an unneeded call to strlen().
  Affects: ofstd/libsrc/ofconapp.cc

- Fix a typo in my last commit.
  Affects: ofstd/libsrc/ofconapp.cc

**** Changes from 2010.06.28 (riesmeier)

- Introduced explicit type casts in order to compile with new gcc versions on
  MinGW/MSYS. Thanks to forum user "marlam" for the report and suggested fix.
  Affects: ofstd/libsrc/ofthread.cc

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: ofstd/libsrc/ofthread.cc

**** Changes from 2010.06.25 (schlachter)

- Correctly check for std::vsnprintf and std::vfprintf.
  The old check always suceeded.
  Affects: config/aclocal.m4
           config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in

- Don't allow OFString(NULL) because it is also forbidden for std::string.
  Affects: ofstd/libsrc/ofstring.cc

- Fixed issues with compiling with HAVE_STD_STRING.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdicent.h
           dcmdata/libsrc/dctagkey.cc
           dcmimgle/apps/dcod2lum.cc
           dcmimgle/libsrc/diciefn.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc
           dcmnet/libsrc/dccfuidh.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmsign/libsrc/siprivat.cc
           oflog/libsrc/hierarchy.cc
           oflog/libsrc/socketap.cc
           oflog/libsrc/threads.cc
           oflog/libsrc/timehelp.cc
           oflog/libsrc/unixsock.cc

**** Changes from 2010.06.24 (riesmeier)

- Revised comment to make clear that the parameter "presID" shall never be 0
  for the sendACTIONRequest() method.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h

- Added check on whether the presentation context ID of command and data set are
  identical. Made sure that received dataset is deleted when an error occurs.
  Used more appropriate error conditions / return codes. Further code cleanup.
  Affects: dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

**** Changes from 2010.06.23 (onken)

- Added building of doxygen and manpage documentation for cmake under unix.
  Affects: CMakeLists.txt
           doxygen/CMakeLists.txt
           CMake/3rdparty.cmake

- Forced 3rd party libraries to be turned off during cmake configuration if
  library was not found or library path not set.
  Affects: CMake/3rdparty.cmake

- Fixed default (private and standard) dictionary path for windows and linux
  in cmake configuration.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Removed doubled cmake check for windows.h and winsock.h.
  Affects: CMake/GenerateDCMTKConfigure.cmake

- Removed superfluous CMakeLists.txt.
  Affects: config/CMakeLists.txt
  Removed: config/include/dcmtk/config/CMakeLists.txt

- Re-established compatibility to CMake version 2.4. Inserted warning if newer
  cmake version than the one last tested in OFFIS is used (old behaviour).
  Set package date to "CVS" to be consistent with old behaviour. Changed
  install directories to be consistent with unix and old windows behaviour.
  Added cmake policy (CMP0003) to avoid cmake warning when libraries with full
  path are linked to executable. Added installation of general files
  (COPYRIGHT, FAQ, HISTORY, VERSION, ANNOUNCE.355, CHANGES.355).
  Affects: CMakeLists.txt

- Removed obsolete cfwin32.h.in.
  Affects: config/include/dcmtk/config/cfwin32.h.in
           config/include/dcmtk/config/osconfig.h

**** Changes from 2010.06.23 (riesmeier)

- Moved log output to another code line in order to avoid a possible NULL
  pointer dereference. Thanks to forum user "Niels Dekker" for the report.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2010.06.22 (riesmeier)

- Introduced new enumeration type to be used for closeAssociation().
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scu.cc
           dcmtls/include/dcmtk/dcmtls/tlsscu.h
           dcmtls/libsrc/tlsscu.cc

- Added support for handling N-EVENT-REPORT request.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/libsrc/scp.cc

- Added support for stopping after the current association is finished.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/libsrc/scp.cc

- Further code cleanup. Renamed some methods, variables, types and so on.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

**** Changes from 2010.06.22 (onken)

- Renamed additional CMake C++ files to DCMTK's C++ standard file
  extension .cc.
  Removed:  CMake/dcmtkTestCharSignedness.cxx
            CMake/dcmtkTestBoolType.cxx
            CMake/dcmtkTestPThreadType.cxx
  Added:    CMake/dcmtkTestCharSignedness.cc
            CMake/dcmtkTestBoolType.cc
            CMake/dcmtkTestPThreadType.cc
  Affects:  CMake/GenerateDCMTKConfigure.cmake

- Added experimental cmake support for unix systems and adapted windows build
  configuration. Thanks to CTK and especially Kitware people for help.
  Added:   CMake/3rdparty.cmake
           CMake/CheckFunctionWithHeaderExists.cmake
           CMake/GenerateDCMTKConfigure.cmake
           CMake/dcmtkTestCharSignedness.cxx
           CMake/GenerateDCMTKConfigure.cmake
           CMake/CheckFunctionWithHeaderExists.cmake
           CMake/dcmtkTestBoolType.cxx
           CMake/dcmtkTestPThreadType.cxx
           CMake/osconfig.h.in
  Affects: CMakeLists.txt
           dcmdata/apps/CMakeLists.txt
           dcmimage/apps/CMakeLists.txt
           dcmimgle/apps/CMakeLists.txt
           dcmjpeg/apps/CMakeLists.txt
           dcmjpls/apps/CMakeLists.txt
           dcmnet/apps/CMakeLists.txt
           dcmpstat/CMakeLists.txt
           dcmpstat/apps/CMakeLists.txt
           dcmqrdb/apps/CMakeLists.txt
           cmsign/apps/CMakeLists.txt
           dcmsr/apps/CMakeLists.txt
           dcmsr/tests/CMakeLists.txt
           dcmwlm/apps/CMakeLists.txt
           oflog/libsrc/CMakeLists.txt

**** Changes from 2010.06.21 (riesmeier)

- Fixed issue with InternalLoggingEvent constructor when parameter "function_"
  is NULL. This caused a crash when using any of the command line tools with
  option --arguments and compiled with HAVE_STD_STRING (use "string" class).
  Affects: oflog/include/dcmtk/oflog/spi/logevent.h

- Removed wrong warning message on an optional parameter hiding another one.
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2010.06.18 (riesmeier)

- Added support for the SCP/SCU role selection negotiation.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/libsrc/scp.cc

- Made sure that the responding application entity title is set.
  Affects: dcmnet/libsrc/scp.cc

- Changed some error conditions / return codes to more appropriate values.
  Further revised logging output. Use DimseCondition::dump() where appropriate.
  Affects: dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

**** Changes from 2010.06.18 (schlachter)

- Added undefined assignment operator to PrintHelper to work around
  a warning with VC2008. Thanks to forum user "Yves Neumann" for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h

**** Changes from 2010.06.17 (riesmeier)

- Moved output of "No Acceptable Presentation Contexts" message to another code
  line in order to be visible at all (if appropriate).
  Affects: dcmwlm/libsrc/wlmactmg.cc

- Fixed wrong logger macro (was DCMNET_INFO instead of DCMWLM_INFO).
  Affects: dcmwlm/libsrc/wlmactmg.cc

- Aligned SCP class with existing SCU class. Some further code cleanups.
  Changed default profile from "Default" to "DEFAULT". Revised documentation.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/libsrc/scp.cc

- Added preliminary support for N-EVENT-REPORT to DcmSCU. Some further code
  cleanups and enhancements. Renamed some methods. Revised documentation.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scu.cc

- Rebuilt Makefile dependencies.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/tests/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep

**** Changes from 2010.06.16 (riesmeier)

- Added type cast to integer variable in order to avoid compiler warnings
  reported by VisualStudio 2008 with warning level 4 (highest).
  Thanks to forum user "Yves Neumann" for the report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diovpln.h
           dcmimgle/libsrc/diovpln.cc

- Removed inline declaration from place() function.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diovpln.h

- Introduced a new timeout of 60 seconds for the recv() function in order to
  make sure that the association is terminated if the receiver looses the
  connection to the sender. This can be disabled by DISABLE_RECV_TIMEOUT.
  Thanks to Mark Smith <<EMAIL>> for the original report.
  Affects: config/docs/macros.txt
           dcmnet/libsrc/dul.cc

**** Changes from 2010.06.15 (schlachter)

- Print a warning in the file appender if the specified
  maxFileSize is too low. This is r1383 from log4cplus.
  Affects: oflog/libsrc/fileap.cc

- Don't fail to compile if libxml does not support schemas.
  Affects: dcmsr/libsrc/dsrxmld.cc

**** Changes from 2010.06.15 (riesmeier)

- Added extended sample configuration file for the logger.
  Added:   oflog/etc/filelog.cfg
  Affects: oflog/etc/Makefile.in

**** Changes from 2010.06.14 (riesmeier)

- Fixed typo in event description table.
  Affects: dcmnet/libsrc/dulfsm.cc

- Slightly modified output of DIMSE_dumpMessage_end().
  Affects: dcmnet/libsrc/dimdump.cc

- Minor fixes to log messages and log levels.
  Affects: dcmnet/libsrc/scp.cc

**** Changes from 2010.06.14 (onken)

- Improved efficiency of finding an appropriate presentation context.
  Thanks to Evgeny Sabelskiy <<EMAIL>> for the suggestion.
  Affects: dcmnet/libsrc/assoc.cc

**** Changes from 2010.06.11 (riesmeier)

- On Windows systems, the binary mode for stdout is now disabled by default
  since this causes newlines in textual output to be converted to LF only.
  Affects: config/docs/macros.txt
           dcmdata/libsrc/cmdlnarg.cc

**** Changes from 2010.06.11 (schlachter)

- Added test for i2d's BMP code.
  Added:   dcmdata/tests/i2dbmp.cc
  Affects: dcmdata/tests/Makefile.dep
           dcmdata/tests/Makefile.in

- Add recent change to each module's configure script to the template, too.
  Affects: config/templates/configure.mod

- Removed unused yield() which was trying to use pthread_yield().
  Affects: oflog/include/dcmtk/oflog/helpers/threads.h
           oflog/libsrc/threads.cc

- Use OFStandard::sleep() in oflog.
  Affects: oflog/include/dcmtk/oflog/helpers/sleep.h
           oflog/libsrc/Makefile.dep
           oflog/libsrc/sleep.cc

**** Changes from 2010.06.09 (riesmeier)

- Added number for new module "dcmstcom", used for module-specific error codes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h

- Moved some annoying log messages from level DEBUG to TRACE (because the same
  information is usually reported in a better way by some other means).
  Affects: dcmnet/libsrc/dimse.cc

- Used new approach on how to output variable information to the syntax usage.
  Affects: dcmnet/apps/storescu.cc

- Moved check on input files behind the line where the resource identifier is
  printed.
  Affects: dcmnet/apps/storescu.cc

- Added preliminary support for N-ACTION to DcmSCU. Some further code cleanups
  and enhancements.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h

- Removed references to CP 767.
  Affects: dcmsr/libsrc/dsrchecc.cc
           dcmsr/libsrc/dsrmamcc.cc

- Fixed wrong option text introduced with last commit.
  Affects: dcmnet/apps/storescu.cc

**** Changes from 2010.06.08 (onken)

- Added C-FIND functionality to DcmSCU. Some code cleanups. Fixed
  memory leak sometimes occuring during association configuration.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scu.cc

**** Changes from 2010.06.08 (schlachter)

- Correctly calculate the row length for images with bpp below 8.
  Affects: dcmdata/libi2d/i2dbmps.cc

- Check for premature file ending while reading the pixel data.
  Affects: dcmdata/libi2d/i2dbmps.cc

**** Changes from 2010.06.08 (riesmeier)

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.06.07 (riesmeier)

- Added new method that allows for loading the meta-header only.
  Affects: dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/libsrc/dcmetinf.cc

- Added new file read mode that allows for loading the meta-header only.
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcfilefo.cc

**** Changes from 2010.06.07 (onken)

- Fixed crash in dcmimage's TIFF plugin on some windows systems
  that was caused by closing the output file more than once.
  Affects: dcmimage/libsrc/dipitiff.cc

- Added Visual Studio 10 (2010) to the list of supported Windows compilers.
  Affects: CMakeLists.txt

**** Changes from 2010.06.04 (schlachter)

- Fixed oflog compilation when configure was called with --disable-threads.
  Affects: oflog/include/dcmtk/oflog/helpers/threadcf.h
           oflog/libsrc/Makefile.dep
           oflog/libsrc/strhelp.cc
           oflog/libsrc/threads.cc

- Don't trip over spaces in the absolute path to the code.
  Affects: config/configure
           config/configure.in
           config/confmod
           config/confmod.in
           dcmdata/configure
           dcmimage/configure
           dcmimgle/configure
           dcmjpeg/configure
           dcmjpls/configure
           dcmnet/configure
           dcmpstat/configure
           dcmqrdb/configure
           dcmsign/configure
           dcmsr/configure
           dcmtls/configure
           dcmwlm/configure
           doxygen/configure
           oflog/configure
           ofstd/configure

- Fixed a warning with VisualStudio 2008 about an implicit cast.
  Affects: dcmdata/libi2d/i2dbmps.cc

- Added class OFReadWriteLocker which simplifies unlocking OFReadWriteLocks.
  Affects: ofstd/include/dcmtk/ofstd/ofthread.h
           ofstd/libsrc/ofthread.cc
           ofstd/tests/tstthred.cc

- Removed an outdated comment.
  Affects: ofstd/libsrc/ofthread.cc

- Use new OFReadWriteLocker to avoid dead locks when we return from a codec
  via an exception. Thanks to forum user "Niels Dekker" for the fix.
  Affects: dcmdata/libsrc/dccodec.cc

**** Changes from 2010.06.04 (riesmeier)

- Added support for option --exec-sync to Unix systems (before: Windows only).
  Thanks to forum user "eludias" for the patch.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Added links to experimental DICOM SCU and SCP classes.
  Affects: dcmnet/docs/dcmnet.dox

**** Changes from 2010.06.03 (riesmeier)

- Replaced calls to strerror() by new helper function OFStandard::strerror()
  which results in using the thread safe version of strerror() if available.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/libsrc/cmdlnarg.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcostrmf.cc
           dcmdata/libsrc/dcuid.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/libsrc/dvpshlp.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrptb.cc
           dcmqrdb/libsrc/dcmqrsrv.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/tests/wltest.cc
           ofstd/libsrc/ofthread.cc

- Fixed issues on Windows platforms introduced with last commit.
  Affects: dcmdata/libsrc/cmdlnarg.cc
           dcmnet/libsrc/dul.cc

- Rebuilt Makefile dependencies.
  Affects: dcmdata/libsrc/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           ofstd/libsrc/Makefile.dep

**** Changes from 2010.06.02 (riesmeier)

- Replaced calls to strerror() by new helper function OFStandard::strerror()
  which results in using the thread safe version of strerror() if available.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/libsrc/dcompat.cc
           dcmnet/libsrc/dimse.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulfsm.cc

- Replaced use of sprintf() on a static char array by class OFString.
  Affects: dcmnet/libsrc/dulfsm.cc

- Replaced use of sprintf() on a static char array by class OFString or
  OFOStringStream respectively.
  Affects: dcmnet/libsrc/dul.cc

- Slightly modified some condition texts for reasons of consistency.
  Affects: dcmnet/libsrc/dul.cc

- Slightly modified some log messages and levels for reasons of consistency.
  Affects: dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

- Added #warning statement for systems where HAVE_STRERROR is not defined.
  Affects: dcmnet/libsrc/dcompat.cc

- Introduced new helper function strerror() which is used as a wrapper to the
  various approaches found on different systems.
  Affects: dcmdata/libsrc/dcddirif.cc
           ofstd/include/dcmtk/ofstd/offile.h
           ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Appended missing OFStringStream_ends to the end of output streams because
  this is required when OFOStringStream is mapped to ostrstream.
  Affects: dcmdata/libsrc/dcddirif.cc
           dcmpstat/tests/msgserv.cc
           dcmtls/libsrc/tlslayer.cc
           dcmtls/libsrc/tlstrans.cc
           dcmwlm/libsrc/wlmactmg.cc
           ofstd/libsrc/ofstd.cc

- Rebuilt Makefile dependencies.
  Affects: ofstd/tests/Makefile.dep
           dcmdata/apps/Makefile.dep
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/tests/Makefile.dep

**** Changes from 2010.06.01 (onken)

- Added some comments and line breaks (improved code readability)
  Affects: dcmjpeg/include/dcmtk/dcmjpeg/djcparam.h
           dcmjpeg/libsrc/djcodece.cc
           dcmjpeg/libsrc/djencext.cc

**** Changes from 2010.06.01 (schlachter)

- Added support for indexed-color BMP images (bit depths 1, 4 and 8).
  Affects: dcmdata/docs/img2dcm.man
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libi2d/i2dbmps.cc

- Generate a better error message if an image exceeds 65535 rows or columns.
  Affects: dcmdata/libi2d/i2dbmps.cc

**** Changes from 2010.05.31 (riesmeier)

- Fixed incorrect handling of SpecificCharacterSet attribute in C-FIND request
  and response messages. This also makes sure that option --keep-char-set now
  really returns the character set provided in the worklist file (if needed).
  Affects: dcmwlm/docs/wlmscpfs.man
           dcmwlm/libsrc/wlds.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/libsrc/wlfsim.cc

- Added a warning message if the C-FIND request contains the SpecificCharacter
  Set attribute because its value is not used for matching.
  Affects: dcmwlm/libsrc/wlds.cc

- Slightly modified some log messages, e.g. replaced '\n' by OFendl.
  Affects: dcmwlm/libsrc/wlds.cc

**** Changes from 2010.05.28 (riesmeier)

- Changed logger name from "dcmtk.dcmdata.mdfconen" to "dcmtk.apps.dcmodify".
  Affects: dcmdata/apps/mdfconen.cc

- Changed logger name from "dcmtk.apps.wlcefs" to "dcmtk.apps.wlmscpfs".
  Affects: dcmwlm/apps/wlcefs.cc

**** Changes from 2010.05.27 (riesmeier)

- Added missing basic offset table to "true lossless" mode (if enabled).
  Thanks to forum user "bwiklak" for the original report.
  Affects: dcmjpeg/libsrc/djcodece.cc

- Revised wording of log message.
  Affects: dcmjpeg/libsrc/djcodece.cc

- Added debug message with details on created basic offset table (if any).
  Affects: dcmdata/libsrc/dcpxitem.cc

- Re-added comment that was accidentally removed by the last commit.
  Affects: dcmdata/libsrc/dcrleccd.cc

**** Changes from 2010.05.26 (riesmeier)

- Use correct delete statement for char array buffer, i.e. "delete[]".
  Affects: oflog/libsrc/fileap.cc

- Replaced checking of macro WIN32 by _WIN32.
  Affects: oflog/libsrc/fileap.cc

**** Changes from 2010.05.25 (schlachter)

- Added support for 16bpp BMP images to libi2d.
  Affects: dcmdata/docs/img2dcm.man
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/libi2d/i2dbmps.cc

- Added a missing include before the use of int64_t.
  Affects: ofstd/include/dcmtk/ofstd/oftypes.h

**** Changes from 2010.05.23 (riesmeier)

- Added two new private tags from OCULUS Pentacam 1.17 conformance statement.
  Affects: dcmdata/data/private.dic
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2010.05.21 (schlachter)

- Added support for 32bpp BMP images to libi2d.
  Affects: dcmdata/docs/img2dcm.man
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/libi2d/i2dbmps.cc

- Replaced DU_fileSize() with OFStandard::getFileSize().
  Affects: dcmnet/apps/storescu.cc
           dcmnet/include/dcmtk/dcmnet/diutil.h
           dcmnet/libsrc/dimstore.cc
           dcmnet/libsrc/diutil.cc

**** Changes from 2010.05.21 (riesmeier)

- Fixed issue with incorrectly encoded RLE images: Now, if the RLE decoder is
  finished but has produced insufficient data, the remaining pixels of the
  image are filled with the value of the last pixel. Applies to decodeFrame().
  Thanks to Jeroen Eggermont <<EMAIL>> for the bug report and fix.
  Affects: dcmdata/libsrc/dcrleccd.cc

- Added useful log messages on various levels to decode() and decodeFrame().
  Affects: dcmdata/libsrc/dcrleccd.cc

- Fixed wrong use of if statement which prevented option --insert from working.
  Affects: dcmdata/apps/mdfconen.cc

**** Changes from 2010.05.20 (riesmeier)

- Added new method for determining the size of a given file (in bytes).
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Use new OFStandard::getFileSize() method where appropriate.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/apps/xml2dcm.cc

- Added support for reading the value of insert/modify statements from a file.
  Affects: dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/mdfdsman.h
           dcmdata/docs/dcmodify.man

- Slightly modified log messages and log levels in order to be more consistent.
  Affects: dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfdsman.cc

- Removed some unnecessary include directives.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfconen.h
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/mdfdsman.h

**** Changes from 2010.05.19 (riesmeier)

- Revised explanatory text on development snapshot (aka version 3.5.5).
  Affects: INSTALL

**** Changes from 2010.05.18 (riesmeier)

- Slightly modified log messages and log levels in order to be more consistent.
  Affects: dcmnet/libsrc/dfindscu.cc
           dcmwlm/libsrc/wlds.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/libsrc/wlfsim.cc
           dcmwlm/libsrc/wlmactmg.cc

- Replaced '\n' by OFendl in log messages.
  Affects: dcmnet/apps/echoscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/libsrc/dfindscu.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/libsrc/wlmactmg.cc

**** Changes from 2010.05.18 (schlachter)

- Use OFStringStream_ends at the end of a OFStringStream.
  Affects: oflog/include/dcmtk/oflog/logmacro.h
           oflog/include/dcmtk/oflog/streams.h

**** Changes from 2010.05.14 (schlachter)

- Added new function OFLog::reconfigure() which reinterprets the logger config
  file. This can e.g. be useful for logging to a different file after fork().
  Affects: oflog/include/dcmtk/oflog/oflog.h
           oflog/libsrc/oflog.cc

**** Changes from 2010.05.11 (riesmeier)

- Made error output in source code examples more consistent with new RT module.
  Affects: dcmsr/docs/dcmsr.dox

- Removed unused directory settings for the PNG and TIFF library.
  Affects: dcmjpls/apps/CMakeLists.txt

**** Changes from 2010.05.10 (riesmeier)

- Added warning message when output file already exists. Output details on
  error condition if output file cannot be written.
  Affects: dcmnet/apps/storescp.cc

- Added private tags from OCULUS Pentacam 1.17 conformance statement.
  Affects: dcmdata/data/private.dic
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2010.05.07 (riesmeier)

- Added new option that allows to disable the overwriting of compiler flags.
  Thanks to Niels Dekker <<EMAIL>> for the suggestion.
  Affects: CMakeLists.txt

**** Changes from 2010.05.07 (schlachter)

- Add new define OFTypename which only expands to "typename"
  if "HAVE_TYPENAME" is defined.
  Affects: ofstd/include/dcmtk/ofstd/oftypes.h

- Use OFTypename for OFLIST_TYPENAME.
  Affects: ofstd/include/dcmtk/ofstd/oflist.h

- Work around some problem with MSVC6.
  Affects: dcmjpls/libcharls/scan.h

**** Changes from 2010.05.05 (riesmeier)

- Fixed wrong conflict check of command line options: now --true-lossless is
  only enabled by default for JPEG lossless output transfer syntaxes.
  Affects: dcmjpeg/apps/dcmcjpeg.cc

- Use type cast macros (e.g. OFstatic_cast) where appropriate.
  Affects: dcmjpeg/apps/dcmcjpeg.cc

**** Changes from 2010.05.04 (riesmeier)

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.05.03 (riesmeier)

- Added type cast to integer variable in order to avoid compiler warnings
  reported by VisualStudio 2010.
  Affects: ofstd/libsrc/ofstring.cc

**** Changes from 2010.04.29 (onken)

- Made SCU class independent from dcmtls, i.e. outsourced TLS API. Added
  direct API support for sending C-STORE requests. Further API changes and
  some bugs fixed.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scu.cc

- Made dcmnet classes independent from dcmtls module for linking.
  Affects: dcmnet/libsrc/Makefile.dep

- Added function for responding to storage requests to SCP class.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/libsrc/scp.cc

- Added debug message noting the presentation context a command set is sent on.
  Affects: dcmnet/libsrc/dimse.cc

- Added debug message on client side noting failed TLS client handshake.
  Affects: dcmnet/libsrc/dulfsm.cc

- Added new class DcmTLSSCU as base class for developing TLS-enabled SCUs.
  Added:   dcmtls/include/dcmtk/dcmtls/tlsscu.h
           dcmtls/libsrc/tlsscu.cc
  Affects: dcmtls/libsrc/CMakeLists.txt
           dcmtls/libsrc/Makefile.dep
           dcmtls/libsrc/Makefile.in

- Added debug message noting the initiation of a TLS client handshake.
  Affects: dcmtls/libsrc/tlstrans.cc

**** Changes from 2010.04.29 (riesmeier)

- Use printError() method for command line parsing errors only. In all other
  cases use log output.
  Affects: dcmpstat/tests/msgserv.cc

- Fixed issue with syntax usage (e.g. layout and formatting).
  Affects: dcmpstat/tests/msgserv.cc

- Added flag for exclusive options to --help.
  Affects: dcmpstat/tests/msgserv.cc

- Fixed typo in log message.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/libsrc/dcmpstat.cc

**** Changes from 2010.04.28 (riesmeier)

- Added type cast to integer variable in order to avoid compiler warnings
  reported by VisualStudio 2005.
  Affects: ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofstd.cc

- Fixed wrong use of comparison operator in if statement.
  Affects: ofstd/libsrc/ofstd.cc

**** Changes from 2010.04.27 (schlachter)

- Worked around various compiler warnings with convertIntegerToString() and
  unsigned integer types.
  Affects: oflog/include/dcmtk/oflog/helpers/strhelp.h

**** Changes from 2010.04.26 (schlachter)

- Fixed some minor doxygen warnings.
  Affects: oflog/include/dcmtk/oflog/fileap.h
           oflog/include/dcmtk/oflog/loglevel.h
           oflog/include/dcmtk/oflog/oflog.h
           oflog/include/dcmtk/oflog/spi/logevent.h
           ofstd/include/dcmtk/ofstd/ofaptr.h
           ofstd/include/dcmtk/ofstd/ofconfig.h
           ofstd/include/dcmtk/ofstd/offile.h
           ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/include/dcmtk/ofstd/ofstring.h

**** Changes from 2010.04.26 (riesmeier)

- Added initial definitions for using the STL vector class. Please note that
  there is currently no alternative implementation to this standard class.
  Added:   ofstd/include/dcmtk/ofstd/ofvector.h
  Affects: config/docs/macros.txt

**** Changes from 2010.04.23 (schlachter)

- Added configure checks for getaddrinfo().
  Affects: config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h.in

- Updated oflog to log4cplus revision 1200 from PRODUCTION_1_0_x branch.
  Added:   oflog/include/dcmtk/oflog/winconap.h
           oflog/libsrc/winconap.cc
  Affects: COPYRIGHT
           oflog/docs/LICENSE
           oflog/etc/logger.cfg
           oflog/include/dcmtk/oflog/appender.h
           oflog/include/dcmtk/oflog/config.h
           oflog/include/dcmtk/oflog/config/defines.h
           oflog/include/dcmtk/oflog/config/win32.h
           oflog/include/dcmtk/oflog/configrt.h
           oflog/include/dcmtk/oflog/consap.h
           oflog/include/dcmtk/oflog/fileap.h
           oflog/include/dcmtk/oflog/fstreams.h
           oflog/include/dcmtk/oflog/helpers/apndimpl.h
           oflog/include/dcmtk/oflog/helpers/lloguser.h
           oflog/include/dcmtk/oflog/helpers/loglog.h
           oflog/include/dcmtk/oflog/helpers/pointer.h
           oflog/include/dcmtk/oflog/helpers/property.h
           oflog/include/dcmtk/oflog/helpers/sleep.h
           oflog/include/dcmtk/oflog/helpers/sockbuff.h
           oflog/include/dcmtk/oflog/helpers/socket.h
           oflog/include/dcmtk/oflog/helpers/strhelp.h
           oflog/include/dcmtk/oflog/helpers/syncppth.h
           oflog/include/dcmtk/oflog/helpers/syncprims.h
           oflog/include/dcmtk/oflog/helpers/syncpwin.h
           oflog/include/dcmtk/oflog/helpers/threadcf.h
           oflog/include/dcmtk/oflog/helpers/threads.h
           oflog/include/dcmtk/oflog/helpers/timehelp.h
           oflog/include/dcmtk/oflog/hierarchy.h
           oflog/include/dcmtk/oflog/hierlock.h
           oflog/include/dcmtk/oflog/layout.h
           oflog/include/dcmtk/oflog/logger.h
           oflog/include/dcmtk/oflog/loglevel.h
           oflog/include/dcmtk/oflog/logmacro.h
           oflog/include/dcmtk/oflog/ndc.h
           oflog/include/dcmtk/oflog/ntelogap.h
           oflog/include/dcmtk/oflog/nullap.h
           oflog/include/dcmtk/oflog/oflog.h
           oflog/include/dcmtk/oflog/socketap.h
           oflog/include/dcmtk/oflog/spi/apndatch.h
           oflog/include/dcmtk/oflog/spi/factory.h
           oflog/include/dcmtk/oflog/spi/filter.h
           oflog/include/dcmtk/oflog/spi/logevent.h
           oflog/include/dcmtk/oflog/spi/logfact.h
           oflog/include/dcmtk/oflog/spi/logimpl.h
           oflog/include/dcmtk/oflog/spi/objreg.h
           oflog/include/dcmtk/oflog/spi/rootlog.h
           oflog/include/dcmtk/oflog/streams.h
           oflog/include/dcmtk/oflog/syslogap.h
           oflog/include/dcmtk/oflog/tstring.h
           oflog/include/dcmtk/oflog/windebap.h
           oflog/libsrc/CMakeLists.txt
           oflog/libsrc/Makefile.dep
           oflog/libsrc/apndimpl.cc
           oflog/libsrc/appender.cc
           oflog/libsrc/config.cc
           oflog/libsrc/consap.cc
           oflog/libsrc/factory.cc
           oflog/libsrc/fileap.cc
           oflog/libsrc/filter.cc
           oflog/libsrc/globinit.cc
           oflog/libsrc/hierarchy.cc
           oflog/libsrc/hierlock.cc
           oflog/libsrc/layout.cc
           oflog/libsrc/lloguser.cc
           oflog/libsrc/logevent.cc
           oflog/libsrc/logger.cc
           oflog/libsrc/logimpl.cc
           oflog/libsrc/loglevel.cc
           oflog/libsrc/loglog.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/ntelogap.cc
           oflog/libsrc/nullap.cc
           oflog/libsrc/objreg.cc
           oflog/libsrc/patlay.cc
           oflog/libsrc/pointer.cc
           oflog/libsrc/property.cc
           oflog/libsrc/rootlog.cc
           oflog/libsrc/sleep.cc
           oflog/libsrc/sockbuff.cc
           oflog/libsrc/socket.cc
           oflog/libsrc/socketap.cc
           oflog/libsrc/strhelp.cc
           oflog/libsrc/syncprims.cc
           oflog/libsrc/syslogap.cc
           oflog/libsrc/threads.cc
           oflog/libsrc/timehelp.cc
           oflog/libsrc/unixsock.cc
           oflog/libsrc/windebap.cc
           oflog/libsrc/winsock.cc

**** Changes from 2010.04.23 (riesmeier)

- Added new method to all VR classes which checks whether the stored value
  conforms to the VR definition and to the specified VM.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvrae.h
           dcmdata/include/dcmtk/dcmdata/dcvras.h
           dcmdata/include/dcmtk/dcmdata/dcvrat.h
           dcmdata/include/dcmtk/dcmdata/dcvrcs.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvrfd.h
           dcmdata/include/dcmtk/dcmdata/dcvrfl.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlo.h
           dcmdata/include/dcmtk/dcmdata/dcvrlt.h
           dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/include/dcmtk/dcmdata/dcvrof.h
           dcmdata/include/dcmtk/dcmdata/dcvrpn.h
           dcmdata/include/dcmtk/dcmdata/dcvrsh.h
           dcmdata/include/dcmtk/dcmdata/dcvrsl.h
           dcmdata/include/dcmtk/dcmdata/dcvrss.h
           dcmdata/include/dcmtk/dcmdata/dcvrst.h
           dcmdata/include/dcmtk/dcmdata/dcvrtm.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h
           dcmdata/include/dcmtk/dcmdata/dcvrul.h
           dcmdata/include/dcmtk/dcmdata/dcvrus.h
           dcmdata/include/dcmtk/dcmdata/dcvrut.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrae.cc
           dcmdata/libsrc/dcvras.cc
           dcmdata/libsrc/dcvrat.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrda.cc
           dcmdata/libsrc/dcvrds.cc
           dcmdata/libsrc/dcvrdt.cc
           dcmdata/libsrc/dcvrfd.cc
           dcmdata/libsrc/dcvrfl.cc
           dcmdata/libsrc/dcvris.cc
           dcmdata/libsrc/dcvrlo.cc
           dcmdata/libsrc/dcvrlt.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrof.cc
           dcmdata/libsrc/dcvrpn.cc
           dcmdata/libsrc/dcvrsh.cc
           dcmdata/libsrc/dcvrsl.cc
           dcmdata/libsrc/dcvrss.cc
           dcmdata/libsrc/dcvrst.cc
           dcmdata/libsrc/dcvrtm.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/dcvrul.cc
           dcmdata/libsrc/dcvrus.cc
           dcmdata/libsrc/dcvrut.cc

- Enhanced checking of element values using the new DcmElement::checkValue()
  method.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

- Specify an appropriate default value for the "vm" parameter of checkValue().
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvrae.h
           dcmdata/include/dcmtk/dcmdata/dcvras.h
           dcmdata/include/dcmtk/dcmdata/dcvrat.h
           dcmdata/include/dcmtk/dcmdata/dcvrcs.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvrfd.h
           dcmdata/include/dcmtk/dcmdata/dcvrfl.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlo.h
           dcmdata/include/dcmtk/dcmdata/dcvrlt.h
           dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/include/dcmtk/dcmdata/dcvrof.h
           dcmdata/include/dcmtk/dcmdata/dcvrpn.h
           dcmdata/include/dcmtk/dcmdata/dcvrsh.h
           dcmdata/include/dcmtk/dcmdata/dcvrsl.h
           dcmdata/include/dcmtk/dcmdata/dcvrss.h
           dcmdata/include/dcmtk/dcmdata/dcvrst.h
           dcmdata/include/dcmtk/dcmdata/dcvrtm.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h
           dcmdata/include/dcmtk/dcmdata/dcvrul.h
           dcmdata/include/dcmtk/dcmdata/dcvrus.h
           dcmdata/include/dcmtk/dcmdata/dcvrut.h

- Renamed static helper function checkValue() to checkStringValue().
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/tests/tstchval.cc

- Disable compiler warnings about "new behavior" when initializing the elements
  of an array (reported by VisualStudio 2005 and newer).
  Affects: config/include/dcmtk/config/cfwin32.h.in

- On Windows systems, use binary mode for stdout in order to be more consistent
  with common Unix behavior, e.g. useful for command line tools like dcm2pnm.
  Thanks to forum user "gurbuz" for the hint.
  Affects: dcmdata/libsrc/cmdlnarg.cc

**** Changes from 2010.04.22 (riesmeier)

- Added support for further VM values ("1-8", "1-99", "16", "32") to be
  checked.
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcvrae.h
           dcmdata/include/dcmtk/dcmdata/dcvras.h
           dcmdata/include/dcmtk/dcmdata/dcvrcs.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlo.h
           dcmdata/include/dcmtk/dcmdata/dcvrpn.h
           dcmdata/include/dcmtk/dcmdata/dcvrsh.h
           dcmdata/include/dcmtk/dcmdata/dcvrtm.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h
           dcmdata/libsrc/dcelem.cc

- Revised misleading parameter documentation for the checkValue() method.
  Affects: dcmdata/include/dcmtk/dcmdata/dcvrae.h
           dcmdata/include/dcmtk/dcmdata/dcvras.h
           dcmdata/include/dcmtk/dcmdata/dcvrcs.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlo.h
           dcmdata/include/dcmtk/dcmdata/dcvrpn.h
           dcmdata/include/dcmtk/dcmdata/dcvrsh.h
           dcmdata/include/dcmtk/dcmdata/dcvrtm.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h

**** Changes from 2010.04.21 (onken)

- Extended img2dcm documentation to also reflect ability to read BMP data.
  Affects: dcmdata/docs/img2dcm.man

**** Changes from 2010.04.16 (riesmeier)

- Further enhanced computation of buffer size when using partial read access
  to pixel data. Now also some rare cases of BitsAllocated are supported.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h

- Updated latest tested CMake version to 2.8.1.
  Affects: CMakeLists.txt

**** Changes from 2010.04.15 (riesmeier)

- Fixed possibly wrong computation of a buffer size when using partial read
  access to pixel data. This could lead to a crash under certain conditions.
  Thanks to Jeroen Eggermont <<EMAIL>> for the bug report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h

**** Changes from 2010.04.09 (schlachter)

- Fix some warnings about variables shadowing other variables.
  Affects: oflog/include/dcmtk/oflog/spi/logevent.h

- Don't initialize our string twice in OFString::reserve().
  Affects: ofstd/libsrc/ofstring.cc

**** Changes from 2010.03.31 (onken)

- Make configure script reflect the recent changes to configure.in regarding
  compilation under Mac OS X.
  Affects: config/configure

**** Changes from 2010.03.26 (riesmeier)

- Fixed missing initialization of color palette LUTs in case of invalid
  images. Thanks to Niels Dekker <<EMAIL>> for the bug
  report and suggested fix.
  Affects: dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dipalimg.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.03.25 (riesmeier)

- Added check for errors during while loop in computeGroupLengthAndPadding().
  Thanks to Peter Klotz <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/dcsequen.cc

- Removed redundant variable declaration in order to avoid a warning message
  reported by gcc 4.1.2.
  Affects: dcmdata/libsrc/dclist.cc

- Use return value of getTag() for stream output where possible.
  Affects: dcmdata/libsrc/dcitem.cc

- Made log messages more consistent within this module.
  Affects: dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc

**** Changes from 2010.03.25 (onken)

- Pixel data is now already marked with the correct transfer syntax in memory
  not only when writing to disk. This permits conversion in memory, e. g. for
  sending the converted DICOM images directly over the network.
  Affects: dcmdata/libi2d/i2d.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h

**** Changes from 2010.03.24 (riesmeier)

- Added new options for the color space conversion during decompression based
  on the color model that is "guessed" by the underlying JPEG library (IJG).
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/include/dcmtk/dcmjpeg/djutils.h
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc

- Added missing command line options for color space conversion.
  Affects: dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmjpeg/docs/dcmj2pnm.man

- Removed macro DETERMINE_OUTPUT_COLOR_SPACE_FROM_IJG_GUESS which is now
  obsolete because of new options for color space conversion during
  decompression.
  Affects: config/docs/macros.txt

- Fixed minor formatting issues in API documentation.
  Affects: dcmjpeg/include/dcmtk/dcmjpeg/djcparam.h

**** Changes from 2010.03.24 (onken)

- Fixed compilation problem introduced with recent assignment operator changes.
  Affects: dcmdata/libsrc/dcitem.cc

- Fixed memory leak in assignment operators of DcmItem and DcmSequenceOfItems.
  Replaced all code occurences of cleaning all elements from internal lists to
  newly introduced function in DcmList.
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcsequen.cc

- Introduced new function to delete all elements (including memory de-allocation)
  from DcmList.
  Affects: dcmdata/include/dcmtk/dcmdata/dclist.h
           dcmdata/libsrc/dclist.cc

**** Changes from 2010.03.23 (onken)

- Fixed compilation under Mac OS X when not using standard includes by adding
  a _DARWIN_C_SOURCE define. Thanks to Guido Lorenz for the suggested patch.
  Affects: config/configure.in

- Fixed compilation on Mac OS X with threads being enabled.
  Affects: oflog/libsrc/threads.cc

**** Changes from 2010.03.23 (riesmeier)

- Use printError() method for command line parsing errors only. After the
  resource identifier has been printed to the log stream use "oflog" instead.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmgpdir.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc

**** Changes from 2010.03.12 (riesmeier)

- Enhanced log output on incomplete worklist files (in debug mode).
  Affects: dcmwlm/libsrc/wlfsim.cc

- Changed some log messages from level "info" to "warn".
  Affects: dcmwlm/libsrc/wlfsim.cc

- Use return value of getTag() for stream output where possible.
  Affects: dcmwlm/libsrc/wlfsim.cc

- Replaced tab characters by spaces and removed redundant space characters.
  Affects:  dcmwlm/data/wlistdb/OFFIS/wklist1.dump
            dcmwlm/data/wlistdb/OFFIS/wklist10.dump
            dcmwlm/data/wlistdb/OFFIS/wklist2.dump
            dcmwlm/data/wlistdb/OFFIS/wklist3.dump
            dcmwlm/data/wlistdb/OFFIS/wklist4.dump
            dcmwlm/data/wlistdb/OFFIS/wklist5.dump
            dcmwlm/data/wlistdb/OFFIS/wklist6.dump
            dcmwlm/data/wlistdb/OFFIS/wklist7.dump
            dcmwlm/data/wlistdb/OFFIS/wklist8.dump
            dcmwlm/data/wlistdb/OFFIS/wklist9.dump

**** Changes from 2010.03.11 (schlachter)

- Use _vsnprintf() on MSVC6 since it doesn't know vsnprintf().
  Affects: config/include/dcmtk/config/cfwin32.h.in
           ofstd/include/dcmtk/ofstd/ofstdinc.h

- Add a couple of workarounds for MSVC6 weirdnesses.
  Affects: dcmjpls/libcharls/lltraits.h
           dcmjpls/libcharls/scan.h

**** Changes from 2010.03.09 (schlachter)

- Don't use InterlockedExchangePointer() which is not available
  on older windows versions.
  Affects: oflog/libsrc/threads.cc

- Added a workaround for an API change to InterlockedCompareExchange().
  Affects: config/include/dcmtk/config/cfwin32.h.in
           oflog/libsrc/winsock.cc

- Added a workaround of MSVC6 which doesn't like "typename" in a place
  where everything else requires it.
  Affects: oflog/libsrc/factory.cc

- Added Sint64 and Uint64 typedefs.
  Affects: ofstd/include/dcmtk/ofstd/oftypes.h

- Use Sint64 and Uint64 instead of long long.
  Affects: dcmjpls/libcharls/CMakeLists.txt
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/Makefile.in
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/util.h

- Use OFPair instead of directly std::pair.
  Affects: dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/scan.h

**** Changes from 2010.03.08 (riesmeier)

- Changed OFListIterator to OFListConstIterator in order to compile when
  HAVE_STL is defined.
  Affects: dcmnet/libsrc/dcmtk/scp.cc

**** Changes from 2010.03.08 (schlachter)

- Don't use "using" to fix compilation with MSVC6.
  Affects: oflog/include/dcmtk/oflog/oflog.h

- Use the correct variable for checking left-align mode.
  Affects: oflog/libsrc/patlay.cc

**** Changes from 2010.03.05 (riesmeier)

- Fixed issue with processNextFrames() when called with non-default parameter.
  Thanks to Michael Doppler <<EMAIL>> for the bug report and fix.
  Affects: dcmimgle/libsrc/diimage.cc

**** Changes from 2010.03.05 (schlachter)

- Fixed possible memory leak in case of error during construction
  of sub-items. This was found with cppcheck.
  Affects: dcmnet/libsrc/dulconst.cc

- Remove some pointless uses of STD_NAMESPACE where they caused issues
  with MSVC6. Thanks to forum user "liuxinming" for finding these.
  Affects: oflog/libsrc/config.cc
           oflog/libsrc/fileap.cc
           oflog/libsrc/property.cc
           oflog/libsrc/sockbuff.cc

**** Changes from 2010.03.04 (riesmeier)

- Fixed possible issue with read locks on global data dictionary.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc

- Use return value of getTag() for stream output where possible.
  Affects: dcmdata/apps/dcmdump.cc

**** Changes from 2010.03.03 (riesmeier)

- Fixed compilation issue with call to getlogin_r().
  Thanks to forum user "parameter" for the report.
  Affects: dcmdata/libsrc/mkdeftag.cc
           dcmdata/libsrc/mkdictbi.cc

- Fixed issue with linkage of "mkdeftag" and "mkdictbi" introduced with change
  to new logging mechanism.
  Affects: dcmdata/libsrc/Makefile.in

- Output return value of readTagAndLength() to the log only in case of error.
  Affects: dcmdata/libsrc/dcsequen.cc

- Use return value of getTag() for stream output where possible.
  Affects: dcmdata/libsrc/dcsequen.cc

**** Changes from 2010.03.02 (riesmeier)

- Enhanced comments of some methods, e.g. processNextFrames().
  Affects: dcmimgle/include/dcmtk/dcmimgle/dcmimage.h

**** Changes from 2010.03.01 (riesmeier)

- Updated copyright date of DCMTK and CharLS library section.
  Affects: COPYRIGHT

**** Changes from 2010.03.01 (schlachter)

- Renamed include guards to avoid name clash with e.g. dcmjpeg.
  Affects: dcmjpls/include/dcmtk/dcmjpls/djcodecd.h
           dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/include/dcmtk/dcmjpls/djdecode.h
           dcmjpls/include/dcmtk/dcmjpls/djencode.h
           dcmjpls/include/dcmtk/dcmjpls/djlsutil.h
           dcmjpls/include/dcmtk/dcmjpls/djrparam.h

- Removed some unnecessary include directives in the headers.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/include/dcmtk/dcmdata/dccodec.h
           dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdicent.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dcistrmf.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dclist.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/include/dcmtk/dcmdata/dcrleccd.h
           dcmdata/include/dcmtk/dcmdata/dcrleerg.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcstack.h
           dcmdata/include/dcmtk/dcmdata/dcswap.h
           dcmdata/include/dcmtk/dcmdata/dctagkey.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/include/dcmtk/dcmdata/dcvr.h
           dcmdata/include/dcmtk/dcmdata/dcvrda.h
           dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/include/dcmtk/dcmdata/dcvrdt.h
           dcmdata/include/dcmtk/dcmdata/dcvris.h
           dcmdata/include/dcmtk/dcmdata/dcvrlt.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/dccodec.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcrlecce.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcvr.cc
           dcmdata/tests/Makefile.dep
           dcmimage/apps/Makefile.dep
           dcmimage/include/dcmtk/dcmimage/dicoflt.h
           dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimage/include/dcmtk/dcmimage/dicoopxt.h
           dcmimage/include/dcmtk/dcmimage/dicopx.h
           dcmimage/include/dcmtk/dcmimage/dicopxt.h
           dcmimage/include/dcmtk/dcmimage/dicorot.h
           dcmimage/include/dcmtk/dcmimage/dicosct.h
           dcmimage/include/dcmtk/dcmimage/dihsvpxt.h
           dcmimage/include/dcmtk/dcmimage/dipalpxt.h
           dcmimage/include/dcmtk/dcmimage/dipipng.h
           dcmimage/include/dcmtk/dcmimage/dipitiff.h
           dcmimage/include/dcmtk/dcmimage/diybrpxt.h
           dcmimage/include/dcmtk/dcmimage/diyf2pxt.h
           dcmimage/include/dcmtk/dcmimage/diyp2pxt.h
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/apps/dconvlum.cc
           dcmimgle/include/dcmtk/dcmimgle/dicrvfit.h
           dcmimgle/include/dcmtk/dcmimgle/didispfn.h
           dcmimgle/include/dcmtk/dcmimgle/diflipt.h
           dcmimgle/include/dcmtk/dcmimgle/digsdfn.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/diinpx.h
           dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimo1img.h
           dcmimgle/include/dcmtk/dcmimgle/dimo2img.h
           dcmimgle/include/dcmtk/dcmimgle/dimocpt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoflt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimomod.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopx.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopx.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimorot.h
           dcmimgle/include/dcmtk/dcmimgle/dimosct.h
           dcmimgle/include/dcmtk/dcmimgle/diobjcou.h
           dcmimgle/include/dcmtk/dcmimgle/diovdat.h
           dcmimgle/include/dcmtk/dcmimgle/diovlay.h
           dcmimgle/include/dcmtk/dcmimgle/diovpln.h
           dcmimgle/include/dcmtk/dcmimgle/dipixel.h
           dcmimgle/include/dcmtk/dcmimgle/dirotat.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/include/dcmtk/dcmimgle/displint.h
           dcmimgle/include/dcmtk/dcmimgle/ditranst.h
           dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/Makefile.dep
           dcmimgle/libsrc/didispfn.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc
           dcmjpls/apps/Makefile.dep
           dcmjpls/libsrc/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           dcmnet/libsrc/dimcmd.cc
           dcmnet/libsrc/dimse.cc
           dcmnet/libsrc/diutil.cc
           dcmpstat/apps/Makefile.dep
           dcmpstat/include/dcmtk/dcmpstat/dvpsibl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsspl.h
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/libsrc/dvsighdl.cc
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsign/apps/dcmsign.cc
           dcmsign/libsrc/Makefile.dep
           dcmsign/libsrc/dcsignat.cc
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep
           dcmsr/tests/Makefile.dep
           dcmtls/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/libsrc/wlds.cc
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep
           oflog/libsrc/Makefile.dep
           ofstd/include/dcmtk/ofstd/ofaptr.h
           ofstd/include/dcmtk/ofstd/ofset.h
           ofstd/include/dcmtk/ofstd/oftimer.h

**** Changes from 2010.02.26 (schlachter)

- Fixed compilation on windows. We don't want dllimport.
  Affects: dcmjpls/libcharls/intrface.h

- Fixed a minor compiler warning with gcc 4.1.1.
  Affects: dcmjpls/libcharls/scan.h

- Fixed a compiler warning with MSVC about unsafe casts.
  Affects: dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/libsrc/djcodece.cc

**** Changes from 2010.02.25 (riesmeier)

- Added "dcmjpls" to the list of public modules and merged CHANGES file.
  Added:   doxygen/manpages/man1/dcmcjpls.1
           doxygen/manpages/man1/dcmdjpls.1
  Removed: dcmjpls/CHANGES
  Affects: CMakeLists.txt
           config/modules
           doxygen/htmldocs.dox
           Makefile
           README

- Fixed minor layout and formatting issues.
  Affects: dcmjpls/docs/dcmcjpls.man
           dcmjpls/docs/dcmdjpls.man

- Fixed issue with element values which exceed the maximum of a 16-bit length
  field. Thanks to Peter Klotz <<EMAIL>> for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcerror.cc
           dcmdata/libsrc/dcobject.cc

**** Changes from 2010.02.25 (schlachter)

- Added manpages for the apps and documentation for the whole module.
  Added:   dcmjpls/docs/dcmcjpls.man
           dcmjpls/docs/dcmdjpls.man
           dcmjpls/docs/dcmjpls.dox

- Fix doxygen comments in a couple of places.
  Affects: dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/include/dcmtk/dcmjpls/djdecode.h

- Updated to latest CharLS version.
  Affects: dcmjpls/docs/charls-readme.txt
           dcmjpls/include/dcmtk/dcmjpls/djlsutil.h
           dcmjpls/libcharls/clrtrans.h
           dcmjpls/libcharls/decodstr.h
           dcmjpls/libcharls/deftrait.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/header.h
           dcmjpls/libcharls/intrface.cc
           dcmjpls/libcharls/intrface.h
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/scan.h
           dcmjpls/libsrc/djerror.h
           dcmjpls/libsrc/djutils.cc

- Removed outdated patch.
  Affects: dcmjpls/docs/charls-readme.txt
  Removed: dcmjpls/docs/charls.patch

**** Changes from 2010.02.23 (riesmeier)

- Added new helper function which determines whether an integer representation
  is signed or unsigned.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/diutils.cc

- Added trace log message which outputs the internal representation for
  monochrome images (number of bits and signed/unsigned).
  Affects: dcmimgle/libsrc/dimomod.cc

**** Changes from 2010.02.22 (schlachter)

- Remove some unneeded includes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dclist.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dchashdi.cc
           dcmdata/libsrc/dcistrmb.cc
           dcmdata/libsrc/dcistrmz.cc
           dcmdata/libsrc/dcostrmb.cc
           dcmdata/libsrc/dcostrmf.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcvr.cc
           dcmjpeg/libsrc/dipijpeg.cc
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc
           dcmsign/include/dcmtk/dcmsign/sicert.h
           dcmsign/include/dcmtk/dcmsign/sitstamp.h
           dcmsr/include/dcmtk/dcmsr/dsrcsidl.h
           dcmsr/include/dcmtk/dcmsr/dsrsoprf.h
           dcmtls/libsrc/tlslayer.cc
           dcmtls/libsrc/tlstrans.cc
           oflog/include/dcmtk/oflog/streams.h
           oflog/libsrc/consap.cc
           oflog/libsrc/loglog.cc

**** Changes from 2010.02.19 (riesmeier)

- Fixed wrong log output (error message when status is "normal").
  Thanks to Peter Klotz <<EMAIL>> for the original report.
  Affects: dcmnet/apps/movescu.cc

- Added private ASTM tags for DICONDE (including CT and DR).
  Affects: dcmdata/data/diconde.dic

**** Changes from 2010.02.18 (riesmeier)

- Added preliminary version of a DICONDE (Digital Imaging and Communication in
  Nondestructive Evaluation) data dictionary. Currently, only the standard tags
  are included. The private ASTM tags will be added later.
  Added:   dcmdata/data/diconde.dic
  Affects: dcmdata/data/CMakeLists.txt
           dcmdata/data/Makefile.in

**** Changes from 2010.02.15 (riesmeier)

- Fixed wrong output in debug mode (error message when status is "success").
  Affects: dcmwlm/libsrc/wlmactmg.cc

- Fixed wrong output in verbose mode (string pointer instead of string value).
  Affects: dcmwlm/libsrc/wlfsim.cc

- Replaced remaining CERR output by DCMWLM_ERROR() macro.
  Affects: dcmwlm/libsrc/wlmactmg.cc

- Fixed typo.
  Affects: dcmwlm/data/wlistdb/README

**** Changes from 2010.02.05 (riesmeier)

- Fixed issue with double locking of ofConsole.
  Affects: dcmdata/apps/mdfconen.cc

- Fixed inconsistent source code formatting.
  Affects: dcmdata/apps/mdfconen.cc

- Updated reference to row numbers in comments (based on the current edition
  of the DICOM standard). Added missing "else" statement.
  Affects: dcmsr/libsrc/dsrxrdcc.cc

**** Changes from 2010.02.04 (riesmeier)

- Fixed issue with experimental macro ON_THE_FLY_COMPRESSION.
  Thanks to Henning Meyer <<EMAIL>> for reporting this.
  Affects: dcmnet/apps/storescu.cc

- Fixed minor issues with log output, e.g. redundant line breaks.
  Affects: dcmnet/libsrc/dimse.cc

**** Changes from 2010.01.26 (schlachter)

- Use DIMSE_dumpMessage() for some more log messages.
  Affects: dcmnet/libsrc/dfindscu.cc
           dcmwlm/libsrc/wlmactmg.cc

**** Changes from 2010.01.21 (riesmeier)

- Added stream variant of method convertToMarkupString().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Changed implementation of convertToMarkupString(). This helper function is
  now based on the new stream variant which is much more efficient when
  converting large text strings with many special characters.
  Affects: ofstd/libsrc/ofstd.cc

- Switched to new stream variant of method convertToMarkupString() where
  appropriate.
  Affects: dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcvrds.cc
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2010.01.20 (schlachter)

- Added OFStandard::getProcessID().
  Affects: dcmdata/libsrc/dcuid.cc
           dcmnet/apps/storescp.cc
           dcmnet/include/dcmtk/dcmnet/dcompat.h
           dcmnet/libsrc/dcompat.cc
           ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Added variables for the appname, date, time, hostname and pid to logger.cfg.
  Affects: oflog/docs/oflog.dox
           oflog/etc/logger.cfg
           oflog/libsrc/oflog.cc

**** Changes from 2010.01.20 (riesmeier)

- Made sure that the calling and called AE title strings are always
  null-terminated.
  Thanks to Per Inge Mathisen <<EMAIL>> for the report and
  suggested fix.
  Affects: dcmnet/libsrc/dulparse.cc

- Fixed minor typos and manually added missing CVS log entries.
  Affects: oflog/etc/logger.cfg

**** Changes from 2010.01.19 (schlachter)

- Made file names fit into 8.3 format.
  Affects: dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/Makefile.in
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/util.h
           dcmjpls/libsrc/Makefile.dep
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djerror.h
  Added:   dcmjpls/libcharls/clrtrans.h
           dcmjpls/libcharls/ctxtrmod.h
           dcmjpls/libcharls/decodstr.h
           dcmjpls/libcharls/deftrait.h
           dcmjpls/libcharls/encodstr.h
           dcmjpls/libcharls/intrface.cc
           dcmjpls/libcharls/intrface.h
           dcmjpls/libcharls/lltraits.h
           dcmjpls/libcharls/lokuptbl.h
           dcmjpls/libcharls/procline.h
  Removed: dcmjpls/libcharls/colortransform.h
           dcmjpls/libcharls/contextrunmode.h
           dcmjpls/libcharls/decoderstrategy.h
           dcmjpls/libcharls/defaulttraits.h
           dcmjpls/libcharls/encoderstrategy.h
           dcmjpls/libcharls/interface.cc
           dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/lookuptable.h
           dcmjpls/libcharls/losslesstraits.h
           dcmjpls/libcharls/processline.h

**** Changes from 2010.01.14 (riesmeier)

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2010.01.13 (schlachter)

- Removed use of UNICODE define, it caused more problems than it solved.
  Affects: oflog/include/dcmtk/oflog/config/defines.h
           oflog/include/dcmtk/oflog/fstreams.h
           oflog/include/dcmtk/oflog/socketap.h
           oflog/include/dcmtk/oflog/streams.h
           oflog/include/dcmtk/oflog/tstring.h
           oflog/libsrc/fileap.cc
           oflog/libsrc/property.cc
           oflog/libsrc/sockbuff.cc
           oflog/libsrc/socketap.cc
           oflog/libsrc/strhelp.cc
           oflog/libsrc/timehelp.cc

- Removed pointless stdafx files.
  Affects: dcmjpls/libcharls/CMakeLists.txt
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/Makefile.in
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/interface.cc
           dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/jpegls.cc
  Removed: dcmjpls/libcharls/stdafx.cc
           dcmjpls/libcharls/stdafx.h

- Fix build on windows.
  Affects: dcmjpls/libcharls/config.h
           dcmjpls/libcharls/interface.h

**** Changes from 2010.01.07 (riesmeier)

- Added tests for old time format "HH:MM:SS.FFFFFF".
  Affects: dcmdata/tests/tstchval.cc

**** Changes from 2010.01.06 (schlachter)

- Fix a use-after-free bug in OFConfigFile on invalid config files.
  Affects: ofstd/libsrc/ofconfig.cc

**** Changes from 2010.01.05 (schlachter)

- Made sure OFString always null-terminates its C-Strings.
  Affects: ofstd/libsrc/ofstring.cc

**** Changes from 2010.01.05 (riesmeier)

- Moved determination of pixel data length to the body of a logging macro
  (since the length value is only used for logging purposes).
  Affects: dcmimgle/libsrc/didocu.cc

**** Changes from 2010.01.04 (riesmeier)

- Added new method getDirNameFromPath() and enhanced existing method
  getFilenameFromPath().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Fixed issue with --sort-conc-studies option by slightly reworking the file
  and directory name string handling (use helper functions from "ofstd").
  Thanks to forum user "semi-newbie" for the bug report.
  Affects: dcmnet/apps/storescp.cc

- Added info log message that shows the name of the stored DICOM file.
  Affects: dcmnet/apps/storescp.cc

**** Changes from 2009.12.23 (riesmeier)

- Changed output of option --arguments.
  Affects: oflog/libsrc/oflog.cc

- Added support for getting the name of the program, i.e. the value of argv[0].
  Affects: ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc

**** Changes from 2009.12.21 (riesmeier)

- Rebuilt Makefile dependencies since the files "oflogfil.cc" and "oflogfil.h"
  were removed.
  Affects: ofstd/libsrc/Makefile.dep

**** Changes from 2009.12.21 (onken)

- Fixed API documentation to keep doxygen quiet.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/include/dcmtk/dcmnet/scu.h

- Added documentation and refactored / enhanced some code.
  Affects: dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/dcmtk/scp.cc
           dcmnet/libsrc/dcmtk/scu.cc

**** Changes from 2009.12.21 (schlachter)

- Removed class OFLogFile which is no longer used.
  Affects: ofstd/libsrc/Makefile.in
           ofstd/libsrc/CMakeLists.txt
  Removed: ofstd/include/dcmtk/ofstd/oflogfil.h
           ofstd/libsrc/oflogfil.cc

- Removed unwanted libxml2 header files.
  Affects: dcmdata/apps/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep

**** Changes from 2009.12.18 (riesmeier)

- Fixed issue with beta version of OpenSSL 1.0 (const declaration changed).
  Thanks to forum user "Vrenn" for the report and suggested fix.
  Affects: dcmtls/libsrc/tlslayer.cc

**** Changes from 2009.12.17 (riesmeier)

- Enhanced CMake project file by disabling external support libraries if
  corresponding header files could not be found.
  Affects: CMakeLists.txt

**** Changes from 2009.12.17 (onken)

- Fixed scu and scp base class compile issues.
  Affects: dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scu.cc

**** Changes from 2009.12.16 (onken)

- Added base classes for SCU and SCP implementations.
  Affects: dcmnet/CMakeLists.txt
           dcmnet/libsrc/CMakeLists.txt
           dcmnet/libsrc/Makefile.dep
           dcmnet/libsrc/Makefile.in
  Added:   dcmnet/include/dcmtk/dcmnet/scp.h
           dcmnet/include/dcmtk/dcmnet/scu.h
           dcmnet/libsrc/scp.cc
           dcmnet/libsrc/scu.cc

- Fixed bug in SCU/SCP role negotation. Thanks to Evgeny Sabelskiy
  <<EMAIL>> for the report and suggested fix.
  Affects: dcmnet/libsrc/dulconst.cc

**** Changes from 2009.12.16 (riesmeier)

- Slightly modified description of command line option --logfile.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/docs/dcmprscp.man

**** Changes from 2009.12.15 (schlachter)

- Re-added and fixed the command line option --logfile.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/docs/dcmprscp.man
           dcmpstat/libsrc/dviface.cc

- Fixed an unsafe usage of OFString.
  Affects: dcmpstat/libsrc/dvpssp.cc

- Fixes some issues with --logfile and the config's log options.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/include/dcmtk/dcmpstat/dvpsdef.h
           dcmpstat/libsrc/dcmpstat.cc
           dcmpstat/libsrc/dviface.cc

**** Changes from 2009.12.11 (riesmeier)

- Changed description of command line option --dump.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man

- Moved command line option --dump to another section.
  Affects: dcmpstat/apps/dcmprscu.cc
           dcmpstat/docs/dcmprscu.man

- Removed old command line option --logfile.
  Affects: dcmpstat/docs/dcmprscp.man

- Fixed small inconsistencies in syntax usage and log output.
  Affects: dcmqrdb/apps/dcmqridx.cc

- Removed old command line option --logfile when executing external processes.
  Affects: dcmpstat/libsrc/dviface.cc

- Slightly modified log messages and log levels.
  Affects: dcmpstat/libsrc/dviface.cc

**** Changes from 2009.12.10 (riesmeier)

- Fixed small issue in log output.
  Affects: dcmsr/libsrc/dsrtypes.cc

**** Changes from 2009.12.09 (schlachter)

- Fixed include path which was still using an old file name.
  Affects: dcmpstat/libsrc/dviface.cc

- Rebuilt Makefile dependencies.
  Affects: oflog/libsrc/Makefile.dep
           dcmdata/apps/Makefile.dep
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/tests/Makefile.dep
           dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/libsrc/Makefile.dep
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmjpls/apps/Makefile.dep
           dcmjpls/libsrc/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/libsrc/dviface.cc
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsign/libsrc/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep
           dcmsr/tests/Makefile.dep
           dcmtls/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep

**** Changes from 2009.12.08 (schlachter)

- Renaming files to make them fit into 8.3 format.
  Affects: oflog/include/dcmtk/oflog/appender.h
           oflog/include/dcmtk/oflog/config.h
           oflog/include/dcmtk/oflog/factory.h
           oflog/include/dcmtk/oflog/filter.h
           oflog/include/dcmtk/oflog/helpers/socket.h
           oflog/include/dcmtk/oflog/helpers/syncprims.h
           oflog/include/dcmtk/oflog/hierarchy.h
           oflog/include/dcmtk/oflog/layout.h
           oflog/include/dcmtk/oflog/logger.h
           oflog/include/dcmtk/oflog/ndc.h
           oflog/libsrc/CMakeLists.txt
           oflog/libsrc/Makefile.in
           oflog/libsrc/appender.cc
           oflog/libsrc/factory.cc
           oflog/libsrc/filter.cc
           oflog/libsrc/hierarchy.cc
           oflog/libsrc/layout.cc
           oflog/libsrc/logger.cc
           oflog/libsrc/loglevel.cc
           oflog/libsrc/oflog.cc
           oflog/libsrc/sleep.cc
           oflog/libsrc/threads.cc
  Removed: oflog/include/dcmtk/oflog/configurator.h
           oflog/include/dcmtk/oflog/consoleappender.h
           oflog/include/dcmtk/oflog/fileappender.h
           oflog/include/dcmtk/oflog/helpers/appenderattachableimpl.h
           oflog/include/dcmtk/oflog/helpers/logloguser.h
           oflog/include/dcmtk/oflog/helpers/socketbuffer.h
           oflog/include/dcmtk/oflog/helpers/stringhelper.h
           oflog/include/dcmtk/oflog/helpers/syncprims-pthreads.h
           oflog/include/dcmtk/oflog/helpers/syncprims-win32.h
           oflog/include/dcmtk/oflog/helpers/thread-config.h
           oflog/include/dcmtk/oflog/helpers/timehelper.h
           oflog/include/dcmtk/oflog/hierarchylocker.h
           oflog/include/dcmtk/oflog/loggingmacros.h
           oflog/include/dcmtk/oflog/nteventlogappender.h
           oflog/include/dcmtk/oflog/nullappender.h
           oflog/include/dcmtk/oflog/socketappender.h
           oflog/include/dcmtk/oflog/spi/appenderattachable.h
           oflog/include/dcmtk/oflog/spi/loggerfactory.h
           oflog/include/dcmtk/oflog/spi/loggerimpl.h
           oflog/include/dcmtk/oflog/spi/loggingevent.h
           oflog/include/dcmtk/oflog/spi/objectregistry.h
           oflog/include/dcmtk/oflog/spi/rootlogger.h
           oflog/include/dcmtk/oflog/syslogappender.h
           oflog/include/dcmtk/oflog/win32debugappender.h
           oflog/libsrc/appenderattachableimpl.cc
           oflog/libsrc/configurator.cc
           oflog/libsrc/consoleappender.cc
           oflog/libsrc/fileappender.cc
           oflog/libsrc/global-init.cc
           oflog/libsrc/hierarchylocker.cc
           oflog/libsrc/loggerimpl.cc
           oflog/libsrc/loggingevent.cc
           oflog/libsrc/logloguser.cc
           oflog/libsrc/nteventlogappender.cc
           oflog/libsrc/nullappender.cc
           oflog/libsrc/objectregistry.cc
           oflog/libsrc/patternlayout.cc
           oflog/libsrc/rootlogger.cc
           oflog/libsrc/socket-unix.cc
           oflog/libsrc/socket-win32.cc
           oflog/libsrc/socketappender.cc
           oflog/libsrc/socketbuffer.cc
           oflog/libsrc/stringhelper.cc
           oflog/libsrc/syslogappender.cc
           oflog/libsrc/timehelper.cc
           oflog/libsrc/win32debugappender.cc
  Added:   oflog/include/dcmtk/oflog/configrt.h
           oflog/include/dcmtk/oflog/consap.h
           oflog/include/dcmtk/oflog/fileap.h
           oflog/include/dcmtk/oflog/helpers/apndimpl.h
           oflog/include/dcmtk/oflog/helpers/lloguser.h
           oflog/include/dcmtk/oflog/helpers/sockbuff.h
           oflog/include/dcmtk/oflog/helpers/strhelp.h
           oflog/include/dcmtk/oflog/helpers/syncppth.h
           oflog/include/dcmtk/oflog/helpers/syncpwin.h
           oflog/include/dcmtk/oflog/helpers/threadcf.h
           oflog/include/dcmtk/oflog/helpers/timehelp.h
           oflog/include/dcmtk/oflog/hierlock.h
           oflog/include/dcmtk/oflog/logmacro.h
           oflog/include/dcmtk/oflog/ntelogap.h
           oflog/include/dcmtk/oflog/nullap.h
           oflog/include/dcmtk/oflog/socketap.h
           oflog/include/dcmtk/oflog/spi/apndatch.h
           oflog/include/dcmtk/oflog/spi/logevent.h
           oflog/include/dcmtk/oflog/spi/logfact.h
           oflog/include/dcmtk/oflog/spi/logimpl.h
           oflog/include/dcmtk/oflog/spi/objreg.h
           oflog/include/dcmtk/oflog/spi/rootlog.h
           oflog/include/dcmtk/oflog/syslogap.h
           oflog/include/dcmtk/oflog/windebap.h
           oflog/libsrc/apndimpl.cc
           oflog/libsrc/config.cc
           oflog/libsrc/consap.cc
           oflog/libsrc/fileap.cc
           oflog/libsrc/globinit.cc
           oflog/libsrc/hierlock.cc
           oflog/libsrc/lloguser.cc
           oflog/libsrc/logevent.cc
           oflog/libsrc/logimpl.cc
           oflog/libsrc/ntelogap.cc
           oflog/libsrc/nullap.cc
           oflog/libsrc/objreg.cc
           oflog/libsrc/patlay.cc
           oflog/libsrc/rootlog.cc
           oflog/libsrc/sockbuff.cc
           oflog/libsrc/socketap.cc
           oflog/libsrc/strhelp.cc
           oflog/libsrc/syslogap.cc
           oflog/libsrc/timehelp.cc
           oflog/libsrc/unixsock.cc
           oflog/libsrc/windebap.cc
           oflog/libsrc/winsock.cc

**** Changes from 2009.12.08 (riesmeier)

- Removed unused command line option --show-pc.
  Affects: dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man

- Slightly modified some log messages.
  Affects: dcmnet/libsrc/dimdump.cc

- Fixed use of wrong logging macro.
  Affects: dcmnet/libsrc/dul.cc

- Fixed inconsistent source code formatting.
  Affects: dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/dul.cc

**** Changes from 2009.12.04 (riesmeier)

- Updated copyright date as well as the name of the OFFIS institute and
  department.
  Affects: COPYRIGHT

- Restructured log output in order to avoid compiler errors with VisualStudio.
  Affects: dcmsr/apps/xml2dsr.cc

- Moved some log messages from debug to trace level.
  Affects: dcmdata/libsrc/dcsequen.cc

- Slightly modified some log messages.
  Affects: dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcostrmb.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvr.cc
           dcmdata/libsrc/dcvrobow.cc

**** Changes from 2009.12.02 (riesmeier)

- Make sure that dcmSOPClassUIDToModality() never returns NULL when passed to
  the log stream in order to avoid an application crash.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrtis.cc

- Slightly modified output of progress bar.
  Affects: dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrcbs.cc

- Sightly modified source code formatting.
  Affects: dcmqrdb/libsrc/dcmqrdbi.cc

**** Changes from 2009.12.02 (schlachter)

- Completely removed the old dcmdata debug mechanism.
  Removed: dcmdata/include/dcmtk/dcmdata/dcdebug.h
  Affects: dcmsign/apps/dcmsign.cc
           dcmsign/apps/Makefile.dep

- Corrected build failures on windows.
  Affects: dcmnet/libsrc/dcompat.cc
           dcmwlm/libsrc/wlmactmg.cc

**** Changes from 2009.12.01 (riesmeier)

- Added new command line option --verbose-pc that allows for showing the
  presentation contexts in verbose mode.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Sightly modified log messages.
  Affects: dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc

- Fixed wrong documentation of command line option.
  Affects: dcmnet/docs/findscu.man
           dcmnet/docs/storescu.man

- Use helper function checkDependence() where appropriate.
  Affects: dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc

- Fixed issue with syntax usage (e.g. layout and formatting).
  Affects: dcmnet/apps/findscu.cc

**** Changes from 2009.11.27 (riesmeier)

- Sightly modified log messages.
  Affects: dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc

- Fixed various issues with syntax usage (e.g. layout and formatting).
  Affects: dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man

- Added settings for and documentation on ".progress" loggers.
  Affects: oflog/etc/logger.cfg

- Replaced remaining tabs by spaces.
  Affects: dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmpssnd.cc

- Updated latest tested CMake version to 2.8.0.
  Affects: CMakeLists.txt

- Removed unused subdirectory "tests" from CMake project file.
  Affects: dcmimgle/CMakeLists.txt
           dcmpstat/CMakeLists.txt

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Removed "dcmavi" module from list of public DCMTK modules.
  Affects: CMakeLists.txt

**** Changes from 2009.11.26 (riesmeier)

- Moved some log messages from debug to trace level.
  Affects: dcmnet/libsrc/dimse.cc

**** Changes from 2009.11.26 (onken)

- Added better error message to dcmodify in case write permissions for creating
  the backup file are missing.
  Thanks to Karen Rei Pease <<EMAIL>> for reporting.
  Affects: dcmdata/apps/mdfconen.cc

**** Changes from 2009.11.25 (riesmeier)

- Adapted code for new approach to access individual frames of a DICOM image.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/include/dcmtk/dcmimage/diargimg.h
           dcmimage/include/dcmtk/dcmimage/dicmyimg.h
           dcmimage/include/dcmtk/dcmimage/dihsvimg.h
           dcmimage/include/dcmtk/dcmimage/dipalimg.h
           dcmimage/include/dcmtk/dcmimage/dirgbimg.h
           dcmimage/include/dcmtk/dcmimage/diybrimg.h
           dcmimage/include/dcmtk/dcmimage/diyf2img.h
           dcmimage/include/dcmtk/dcmimage/diyp2img.h
           dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dicmyimg.cc
           dcmimage/libsrc/dihsvimg.cc
           dcmimage/libsrc/dipalimg.cc
           dcmimage/libsrc/dirgbimg.cc
           dcmimage/libsrc/diybrimg.cc
           dcmimage/libsrc/diyf2img.cc
           dcmimage/libsrc/diyp2img.cc
           dcmimgle/include/dcmtk/dcmimgle/didocu.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/diinpx.h
           dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/diinpx.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/diovpln.cc
           dcmjpeg/include/dcmtk/dcmjpeg/ddpiimpl.h
           dcmjpeg/libsrc/ddpiimpl.cc

- Added new method which allows for processing the frames of a multi-frame
  image successively.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dcmimage.h

- Fixed issue with attributes that use a value representation of US or SS.
  Affects: dcmimgle/include/dcmtk/dcmimgle/didocu.h
           dcmimgle/libsrc/didocu.cc

- Fixed issue with LUT Descriptor elements using the value representation SS.
  Affects: dcmimgle/libsrc/diluptab.cc

- The getString() method now returns the Defined Term of the attribute
  PhotometricInterpretation.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/libsrc/dcmimage.cc

- Added list of Defined Terms for the attribute PhotometricInterpretation.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h

- Added new source and header file for the module logger.
  Added:   dcmimage/include/dcmtk/dcmimage/dilogger.h
           dcmimage/libsrc/dilogger.cc
  Affects: dcmimage/include/dcmtk/dcmimage/dicopx.h
           dcmimage/include/dcmtk/dcmimage/diqttype.h
           dcmimage/libsrc/CMakeLists.txt
           dcmimage/libsrc/dicoimg.cc
           dcmimage/libsrc/diquant.cc
           dcmimage/libsrc/Makefile.in

- Removed inclusion of header file "ofconsol.h".
  Affects: dcmimage/include/dcmtk/dcmimage/dihsvpxt.h
           dcmimage/include/dcmtk/dcmimage/dipalpxt.h
           dcmimage/include/dcmtk/dcmimage/diyf2pxt.h
           dcmimage/include/dcmtk/dcmimage/diyp2pxt.h
           dcmimage/libsrc/diqtctab.cc
           dcmimage/libsrc/diquant.cc
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/apps/dconvlum.cc
           dcmimgle/libsrc/diciefn.cc
           dcmimgle/libsrc/dicielut.cc
           dcmimgle/libsrc/didispfn.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmimgle/libsrc/digsdlut.cc
           dcmimgle/libsrc/diovpln.cc
           dcmimgle/libsrc/diutils.cc

- Revised logging messages.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/libsrc/diluptab.cc
           dcmimgle/libsrc/diovpln.cc

- Added more logging messages.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmjpeg/libsrc/djcodecd.cc
           dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/diovpln.cc

- Rebuilt Makefile dependencies.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/libsrc/Makefile.dep
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep

- Removed unused implementation of method determineDecompressedColorModel().
  Affects: dcmjpls/libsrc/djcodecd.cc

**** Changes from 2009.11.24 (schlachter)

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmpstat/apps/Makefile.dep
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmpstat/include/dcmtk/dcmpstat/dcmpstat.h
           dcmpstat/include/dcmtk/dcmpstat/dviface.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsab.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsabl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsall.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscf.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscu.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscul.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsda.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsdal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsdef.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsfs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsga.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgr.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgrl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpshlp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsib.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsibl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsov.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsovl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspr.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsprt.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsri.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsril.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrsl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssv.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssvl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstat.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstx.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstxl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstyp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvw.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvwl.h
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/libsrc/dcmpstat.cc
           dcmpstat/libsrc/dviface.cc
           dcmpstat/libsrc/dvpsab.cc
           dcmpstat/libsrc/dvpsabl.cc
           dcmpstat/libsrc/dvpsal.cc
           dcmpstat/libsrc/dvpsall.cc
           dcmpstat/libsrc/dvpscf.cc
           dcmpstat/libsrc/dvpscu.cc
           dcmpstat/libsrc/dvpscul.cc
           dcmpstat/libsrc/dvpsda.cc
           dcmpstat/libsrc/dvpsdal.cc
           dcmpstat/libsrc/dvpsfs.cc
           dcmpstat/libsrc/dvpsga.cc
           dcmpstat/libsrc/dvpsgal.cc
           dcmpstat/libsrc/dvpsgl.cc
           dcmpstat/libsrc/dvpsgll.cc
           dcmpstat/libsrc/dvpsgr.cc
           dcmpstat/libsrc/dvpsgrl.cc
           dcmpstat/libsrc/dvpshlp.cc
           dcmpstat/libsrc/dvpsib.cc
           dcmpstat/libsrc/dvpsibl.cc
           dcmpstat/libsrc/dvpsov.cc
           dcmpstat/libsrc/dvpsovl.cc
           dcmpstat/libsrc/dvpspl.cc
           dcmpstat/libsrc/dvpspl2.cc
           dcmpstat/libsrc/dvpspll.cc
           dcmpstat/libsrc/dvpspr.cc
           dcmpstat/libsrc/dvpsprt.cc
           dcmpstat/libsrc/dvpsri.cc
           dcmpstat/libsrc/dvpsril.cc
           dcmpstat/libsrc/dvpsrs.cc
           dcmpstat/libsrc/dvpsrsl.cc
           dcmpstat/libsrc/dvpssp.cc
           dcmpstat/libsrc/dvpsspl.cc
           dcmpstat/libsrc/dvpssv.cc
           dcmpstat/libsrc/dvpssvl.cc
           dcmpstat/libsrc/dvpstat.cc
           dcmpstat/libsrc/dvpstx.cc
           dcmpstat/libsrc/dvpstxl.cc
           dcmpstat/libsrc/dvpsvl.cc
           dcmpstat/libsrc/dvpsvll.cc
           dcmpstat/libsrc/dvpsvw.cc
           dcmpstat/libsrc/dvpsvwl.cc
           dcmpstat/tests/Makefile.dep
           dcmpstat/tests/msgserv.cc

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmwlm/CMakeLists.txt
           dcmwlm/apps/Makefile.dep
           dcmwlm/apps/wlcefs.cc
           dcmwlm/apps/wlcefs.h
           dcmwlm/docs/wlmscpfs.man
           dcmwlm/include/dcmtk/dcmwlm/wlds.h
           dcmwlm/include/dcmtk/dcmwlm/wlfsim.h
           dcmwlm/include/dcmtk/dcmwlm/wlmactmg.h
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/libsrc/wlds.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/libsrc/wlfsim.cc
           dcmwlm/libsrc/wlmactmg.cc
           dcmwlm/tests/Makefile.dep
           dcmwlm/tests/wltest.cc
           dcmwlm/wwwapps/Makefile.dep
           dcmwlm/wwwapps/writwlst.cc

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmqrdb/CMakeLists.txt
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbg.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbm.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcnf.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdba.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdbi.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqropt.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrptb.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrsrv.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrtis.h
           dcmqrdb/libsrc/Makefile.dep
           dcmqrdb/libsrc/dcmqrcbf.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrcbs.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqropt.cc
           dcmqrdb/libsrc/dcmqrptb.cc
           dcmqrdb/libsrc/dcmqrsrv.cc
           dcmqrdb/libsrc/dcmqrtis.cc

**** Changes from 2009.11.20 (onken)

- Fixed bug in JPEG true lossless encoder which prohibited the generation
  of a new SOP Instance UID if desired by the user or if image is
  converted to Secondary Capture.
  Affects: dcmjpeg/libsrc/djcodece.cc

**** Changes from 2009.11.18 (schlachter)

- Remove the unused string_append_iterator.
  Affects: oflog/include/dcmtk/oflog/helpers/stringhelper.h

- Use more than just the INFO log level.
  Affects: dcmjpeg/include/dcmtk/dcmjpeg/djdijg12.h
           dcmjpeg/include/dcmtk/dcmjpeg/djdijg16.h
           dcmjpeg/include/dcmtk/dcmjpeg/djdijg8.h
           dcmjpeg/include/dcmtk/dcmjpeg/djeijg12.h
           dcmjpeg/include/dcmtk/dcmjpeg/djeijg16.h
           dcmjpeg/include/dcmtk/dcmjpeg/djeijg8.h
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc

- Fix compiler errors due to removal of DUL_Debug() and DIMSE_Debug().
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmwlm/apps/wlcefs.cc

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmtls/include/dcmtk/dcmtls/tlslayer.h
           dcmtls/include/dcmtk/dcmtls/tlstrans.h
           dcmtls/libsrc/Makefile.dep
           dcmtls/libsrc/tlslayer.cc
           dcmtls/libsrc/tlstrans.cc

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/cond.h
           dcmnet/include/dcmtk/dcmnet/dcmtrans.h
           dcmnet/include/dcmtk/dcmnet/dfindscu.h
           dcmnet/include/dcmtk/dcmnet/dimse.h
           dcmnet/include/dcmtk/dcmnet/diutil.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/Makefile.dep
           dcmnet/libsrc/Makefile.in
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/cond.cc
           dcmnet/libsrc/dcmtrans.cc
           dcmnet/libsrc/dcompat.cc
           dcmnet/libsrc/dcuserid.cc
           dcmnet/libsrc/dfindscu.cc
           dcmnet/libsrc/dimdump.cc
           dcmnet/libsrc/dimfind.cc
           dcmnet/libsrc/dimget.cc
           dcmnet/libsrc/dimmove.cc
           dcmnet/libsrc/dimse.cc
           dcmnet/libsrc/diutil.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulparse.cc
           dcmnet/libsrc/dulpriv.h

**** Changes from 2009.11.17 (riesmeier)

- Added new method that allows for determining the color model of the
  decompressed image.
  Affects: dcmdata/include/dcmtk/dcmdata/dccodec.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcrleccd.h
           dcmdata/include/dcmtk/dcmdata/dcrlecce.h
           dcmdata/libsrc/dccodec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcrleccd.cc
           dcmdata/libsrc/dcrlecce.cc
           dcmjpeg/include/dcmtk/dcmjpeg/djcodecd.h
           dcmjpeg/include/dcmtk/dcmjpeg/djcodece.h
           dcmjpeg/libsrc/djcodecd.cc
           dcmjpeg/libsrc/djcodece.cc
           dcmjpls/include/dcmtk/dcmjpls/djcodecd.h
           dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc

- Added new enum value for missing photometric interpretation value.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h

- Added new configuration flags for the upcoming support of partial access to
  pixel data, i.e. without decompressing/loading a complete multi-frame image.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h

**** Changes from 2009.11.17 (schlachter)

- Make OFLogger::getChainedLogLevel() accessible.
  Affects: oflog/include/dcmtk/oflog/oflog.h

**** Changes from 2009.11.13 (riesmeier)

- Fixed minor issues in log output.
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc

- Fixed wrong header include (now, "oflog.h" instead of "ofconsol.h" required).
  Affects: dcmdata/libsrc/cmdlnarg.cc

**** Changes from 2009.11.12 (riesmeier)

- Fixed issue with --accept-all command line option which caused the other
  --prefer-xxx options to be ignored under certain conditions.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc

**** Changes from 2009.11.11 (riesmeier)

- Fixed incorrect description of command line option.
  Affects: dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man

**** Changes from 2009.11.10 (schlachter)

- Fix compilation on windows.
  Affects: dcmdata/libsrc/cmdlnarg.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dchashdi.cc

**** Changes from 2009.11.06 (riesmeier)

- Changed documentation on log level "trace".
  Affects: oflog/include/dcmtk/oflog/oflog.h

**** Changes from 2009.11.04 (schlachter)

- Updated Makefile dependencies.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsign/libsrc/Makefile.dep
           dcmtls/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep

- Added missing oflog include directory and library.
  Affects: dcmnet/CMakeLists.txt
           dcmnet/apps/CMakeLists.txt
           dcmnet/apps/Makefile.in
           dcmnet/libsrc/Makefile.in
           dcmpstat/tests/Makefile.in
           dcmqrdb/CMakeLists.txt
           dcmqrdb/apps/CMakeLists.txt
           dcmqrdb/apps/Makefile.in
           dcmqrdb/libsrc/Makefile.in
           dcmsign/libsrc/Makefile.in
           dcmtls/CMakeLists.txt
           dcmtls/libsrc/Makefile.in
           dcmwlm/CMakeLists.txt
           dcmwlm/apps/CMakeLists.txt
           dcmwlm/apps/Makefile.in
           dcmwlm/libsrc/Makefile.in
           dcmwlm/tests/Makefile.in
           dcmwlm/wwwapps/Makefile.in

- Switched to logging mechanism provided by the "new" oflog module.
  Removed: dcmdata/libsrc/dcdebug.cc
  Affects: dcmdata/CMakeLists.txt
           dcmdata/apps/Makefile.dep
           dcmdata/apps/Makefile.in
           dcmdata/apps/dcm2pdf.cc
           dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmftest.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dcmodify.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfconen.h
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/mdfdsman.h
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/data/private.dic
           dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/img2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmdata/include/dcmtk/dcmdata/cmdlnarg.h
           dcmdata/include/dcmtk/dcmdata/dccodec.h
           dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/include/dcmtk/dcmdata/dcdebug.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdicent.h
           dcmdata/include/dcmtk/dcmdata/dcfcache.h
           dcmdata/include/dcmtk/dcmdata/dcistrma.h
           dcmdata/include/dcmtk/dcmdata/dcistrmf.h
           dcmdata/include/dcmtk/dcmdata/dclist.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcofsetl.h
           dcmdata/include/dcmtk/dcmdata/dcostrma.h
           dcmdata/include/dcmtk/dcmdata/dcostrmb.h
           dcmdata/include/dcmtk/dcmdata/dcostrmf.h
           dcmdata/include/dcmtk/dcmdata/dcovlay.h
           dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/include/dcmtk/dcmdata/dcpcache.h
           dcmdata/include/dcmtk/dcmdata/dcrleccd.h
           dcmdata/include/dcmtk/dcmdata/dcrlecce.h
           dcmdata/include/dcmtk/dcmdata/dcrlecp.h
           dcmdata/include/dcmtk/dcmdata/dcrledec.h
           dcmdata/include/dcmtk/dcmdata/dcrledrg.h
           dcmdata/include/dcmtk/dcmdata/dcrleerg.h
           dcmdata/include/dcmtk/dcmdata/dcrlerp.h
           dcmdata/include/dcmtk/dcmdata/dcswap.h
           dcmdata/include/dcmtk/dcmdata/dctag.h
           dcmdata/include/dcmtk/dcmdata/dctk.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/include/dcmtk/dcmdata/dcvm.h
           dcmdata/include/dcmtk/dcmdata/dcvrat.h
           dcmdata/include/dcmtk/dcmdata/dcvrfd.h
           dcmdata/include/dcmtk/dcmdata/dcvrfl.h
           dcmdata/include/dcmtk/dcmdata/dcvrof.h
           dcmdata/include/dcmtk/dcmdata/dcvrsl.h
           dcmdata/include/dcmtk/dcmdata/dcvrss.h
           dcmdata/include/dcmtk/dcmdata/dcvrul.h
           dcmdata/include/dcmtk/dcmdata/dcvrulup.h
           dcmdata/include/dcmtk/dcmdata/dcvrus.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplnsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplvlp.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libi2d/Makefile.in
           dcmdata/libi2d/i2d.cc
           dcmdata/libi2d/i2dbmps.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/libi2d/i2dplnsc.cc
           dcmdata/libi2d/i2dplsc.cc
           dcmdata/libi2d/i2dplvlp.cc
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/Makefile.in
           dcmdata/libsrc/cmdlnarg.cc
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdicent.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcdictbi.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dchashdi.cc
           dcmdata/libsrc/dcistrma.cc
           dcmdata/libsrc/dcistrmb.cc
           dcmdata/libsrc/dcistrmz.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dclist.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcostrma.cc
           dcmdata/libsrc/dcostrmb.cc
           dcmdata/libsrc/dcostrmf.cc
           dcmdata/libsrc/dcostrmz.cc
           dcmdata/libsrc/dcpath.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcrleccd.cc
           dcmdata/libsrc/dcrlecce.cc
           dcmdata/libsrc/dcrlecp.cc
           dcmdata/libsrc/dcrledrg.cc
           dcmdata/libsrc/dcrleerg.cc
           dcmdata/libsrc/dcrlerp.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcstack.cc
           dcmdata/libsrc/dcswap.cc
           dcmdata/libsrc/dctagkey.cc
           dcmdata/libsrc/dctypes.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcvm.cc
           dcmdata/libsrc/dcvr.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrof.cc
           dcmdata/libsrc/dcxfer.cc
           dcmdata/libsrc/mkdeftag.cc
           dcmdata/libsrc/mkdictbi.cc
           dcmdata/libsrc/vrscan.cc
           dcmdata/tests/Makefile.dep
           dcmdata/tests/Makefile.in
           dcmdata/tests/tstpath.cc
           dcmdata/tests/tstpread.cc

**** Changes from 2009.11.03 (riesmeier)

- Switched to old behavior: Output input filename to COUT and not to the log
  stream.
  Affects: dcmsr/apps/dsrdump.cc

**** Changes from 2009.11.02 (riesmeier)

- Added missing oflog include directory.
  Affects: dcmpstat/CMakeLists.txt

- Added missing oflog related link libraries and directory.
  Affects: dcmdata/apps/CMakeLists.txt
           dcmimage/apps/CMakeLists.txt
           dcmimgle/apps/CMakeLists.txt
           dcmpstat/apps/CMakeLists.txt
           dcmsr/apps/CMakeLists.txt
           dcmsr/tests/CMakeLists.txt

- Fixed private tags from Circle Cardiovascular Imaging cmr42 3.0.
  Affects: dcmdata/data/private.dic

- Changed forward declaration from "class" to "struct" in order to avoid
  warning messages reported by VisualStudio 2008.
  Affects: dcmwlm/include/dcmtk/dcmwlm/wlfsim.h

**** Changes from 2009.10.30 (riesmeier)

- Added check on the presence of the Concept Name Code Sequence for the root
  CONTAINER.
  Affects: dcmsr/libsrc/dsrdoctn.cc

- Output processing details in verbose mode and not as a warning message.
  Affects: dcmsr/libsrc/dsrdoctr.cc

- Option --processing-details now requires verbose mode.
  Affects: dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsrdump.cc

**** Changes from 2009.10.28 (riesmeier)

- Fixed minor issues in log output.
  Affects: dcmimgle/apps/dconvlum.cc
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/diciefn.cc
           dcmimgle/libsrc/dicielut.cc
           dcmimgle/libsrc/didispfn.cc
           dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmimgle/libsrc/digsdlut.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/diluptab.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/dimomod.cc
           dcmimgle/libsrc/diovlay.cc
           dcmimgle/libsrc/diovlimg.cc
           dcmimgle/libsrc/diovpln.cc
           dcmimgle/libsrc/diutils.cc
           dcmimgle/include/dcmtk/dcmimgle/diflipt.h
           dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoflt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimorot.h
           dcmimgle/include/dcmtk/dcmimgle/dimosct.h
           dcmimgle/include/dcmtk/dcmimgle/dirotat.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/include/dcmtk/dcmimgle/diutils.h

**** Changes from 2009.10.28 (schlachter)

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/CMakeLists.txt
           dcmimgle/apps/Makefile.dep
           dcmimgle/apps/Makefile.in
           dcmimgle/apps/dcmdspfn.cc
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/include/dcmtk/dcmimgle/diflipt.h
           dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoflt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimorot.h
           dcmimgle/include/dcmtk/dcmimgle/dimosct.h
           dcmimgle/include/dcmtk/dcmimgle/dirotat.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/Makefile.dep
           dcmimgle/libsrc/Makefile.in
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/diciefn.cc
           dcmimgle/libsrc/dicielut.cc
           dcmimgle/libsrc/didispfn.cc
           dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmimgle/libsrc/digsdlut.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/diluptab.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/dimomod.cc
           dcmimgle/libsrc/diovlay.cc
           dcmimgle/libsrc/diovlimg.cc
           dcmimgle/libsrc/diovpln.cc
           dcmimgle/libsrc/diutils.cc
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/libsrc/Makefile.dep

- Add a check for vsnprintf() to configure. This is used in dcmsr.
  Affects: config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h.in

- Use a local variable for buffering libxml error messages instead of a
  static one.
  Affects: dcmsr/libsrc/dsrxmld.cc

**** Changes from 2009.10.14 (riesmeier)

- Slightly modified output of some log messages (avoid creation of temporary
  strings).
  Affects: dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrtypes.cc

- Output image details in verbose mode to new log stream (instead of CERR).
  Affects: dcmimage/apps/dcm2pnm.cc

- Moved option --image-info to a new sub section in the syntax usage.
  Affects: dcmimage/docs/dcm2pnm.man
           dcmjpeg/docs/dcmj2pnm.man

- Fixed minor issues in log output. Also updated copyright date (if required).
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/include/dcmtk/dcmimage/dicoflt.h
           dcmimage/include/dcmtk/dcmimage/dicorot.h
           dcmimage/include/dcmtk/dcmimage/dicosct.h
           dcmimage/include/dcmtk/dcmimage/dihsvpxt.h
           dcmimage/include/dcmtk/dcmimage/dipalpxt.h
           dcmimage/include/dcmtk/dcmimage/diyf2pxt.h
           dcmimage/include/dcmtk/dcmimage/diyp2pxt.h
           dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dicoimg.cc
           dcmimage/libsrc/dicopx.cc
           dcmimage/libsrc/dipalimg.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrsoprf.cc
           dcmsr/libsrc/dsrxmld.cc

**** Changes from 2009.10.13 (schlachter)

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmpstat/apps/Makefile.in
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/libsrc/Makefile.in
           dcmpstat/libsrc/dviface.cc
           dcmsr/CMakeLists.txt
           dcmsr/apps/Makefile.dep
           dcmsr/apps/Makefile.in
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrcodtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcodvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcomtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcomvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcontn.h
           dcmsr/include/dcmtk/dcmsr/dsrcsidl.h
           dcmsr/include/dcmtk/dcmsr/dsrdattn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/include/dcmtk/dcmsr/dsrdtitn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgfr.h
           dcmsr/include/dcmtk/dcmsr/dsrimgtn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgvl.h
           dcmsr/include/dcmtk/dcmsr/dsrnumtn.h
           dcmsr/include/dcmtk/dcmsr/dsrnumvl.h
           dcmsr/include/dcmtk/dcmsr/dsrpnmtn.h
           dcmsr/include/dcmtk/dcmsr/dsrreftn.h
           dcmsr/include/dcmtk/dcmsr/dsrscogr.h
           dcmsr/include/dcmtk/dcmsr/dsrscotn.h
           dcmsr/include/dcmtk/dcmsr/dsrscovl.h
           dcmsr/include/dcmtk/dcmsr/dsrsoprf.h
           dcmsr/include/dcmtk/dcmsr/dsrstrvl.h
           dcmsr/include/dcmtk/dcmsr/dsrtcodt.h
           dcmsr/include/dcmtk/dcmsr/dsrtcosp.h
           dcmsr/include/dcmtk/dcmsr/dsrtcotn.h
           dcmsr/include/dcmtk/dcmsr/dsrtcoto.h
           dcmsr/include/dcmtk/dcmsr/dsrtcovl.h
           dcmsr/include/dcmtk/dcmsr/dsrtextn.h
           dcmsr/include/dcmtk/dcmsr/dsrtimtn.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/include/dcmtk/dcmsr/dsruidtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavch.h
           dcmsr/include/dcmtk/dcmsr/dsrwavtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavvl.h
           dcmsr/include/dcmtk/dcmsr/dsrxmld.h
           dcmsr/libsrc/Makefile.dep
           dcmsr/libsrc/Makefile.in
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcomtn.cc
           dcmsr/libsrc/dsrcomvl.cc
           dcmsr/libsrc/dsrcontn.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdattn.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrdtitn.cc
           dcmsr/libsrc/dsrimgfr.cc
           dcmsr/libsrc/dsrimgtn.cc
           dcmsr/libsrc/dsrimgvl.cc
           dcmsr/libsrc/dsrnumtn.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrpnmtn.cc
           dcmsr/libsrc/dsrreftn.cc
           dcmsr/libsrc/dsrscogr.cc
           dcmsr/libsrc/dsrscotn.cc
           dcmsr/libsrc/dsrscovl.cc
           dcmsr/libsrc/dsrsoprf.cc
           dcmsr/libsrc/dsrstrvl.cc
           dcmsr/libsrc/dsrtcodt.cc
           dcmsr/libsrc/dsrtcosp.cc
           dcmsr/libsrc/dsrtcotn.cc
           dcmsr/libsrc/dsrtcoto.cc
           dcmsr/libsrc/dsrtcovl.cc
           dcmsr/libsrc/dsrtextn.cc
           dcmsr/libsrc/dsrtimtn.cc
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/dsruidtn.cc
           dcmsr/libsrc/dsrwavch.cc
           dcmsr/libsrc/dsrwavtn.cc
           dcmsr/libsrc/dsrwavvl.cc
           dcmsr/libsrc/dsrxmld.cc
           dcmsr/tests/Makefile.dep
           dcmsr/tests/Makefile.in
           dcmsr/tests/mkreport.cc

- Added the DcmObject::PrintHelper helper class. This class can be used to
  print DcmObject instances with oflog's logging macros.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmimage/CMakeLists.txt
           dcmimage/apps/Makefile.dep
           dcmimage/apps/Makefile.in
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimage/include/dcmtk/dcmimage/dicoflt.h
           dcmimage/include/dcmtk/dcmimage/dicorot.h
           dcmimage/include/dcmtk/dcmimage/dicosct.h
           dcmimage/include/dcmtk/dcmimage/dihsvpxt.h
           dcmimage/include/dcmtk/dcmimage/dipalpxt.h
           dcmimage/include/dcmtk/dcmimage/diqttype.h
           dcmimage/include/dcmtk/dcmimage/diquant.h
           dcmimage/include/dcmtk/dcmimage/diyf2pxt.h
           dcmimage/include/dcmtk/dcmimage/diyp2pxt.h
           dcmimage/libsrc/Makefile.dep
           dcmimage/libsrc/Makefile.in
           dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dicoimg.cc
           dcmimage/libsrc/dicopx.cc
           dcmimage/libsrc/dipalimg.cc
           dcmimage/libsrc/diquant.cc
           dcmjpeg/apps/Makefile.dep

**** Changes from 2009.10.12 (riesmeier)

- Changed prefix of UIDs created for series and studies (now using constants
  SITE_SERIES_UID_ROOT and SITE_STUDY_UID_ROOT which are supposed to be used
  in these cases).
  Affects: dcmdata/apps/pdf2dcm.cc

- Fixed wrong wording.
  Affects: dcmdata/docs/dcmodify.man
           dcmdata/docs/img2dcm.man

**** Changes from 2009.10.07 (schlachter)

- Switched to logging mechanism provided by the "new" oflog module.
  Affects: dcmjpeg/apps/CMakeLists.txt
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/apps/Makefile.in
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmjpeg/include/dcmtk/dcmjpeg/djcparam.h
           dcmjpeg/include/dcmtk/dcmjpeg/djdecode.h
           dcmjpeg/include/dcmtk/dcmjpeg/djencode.h
           dcmjpeg/include/dcmtk/dcmjpeg/djutils.h
           dcmjpeg/libsrc/CMakeLists.txt
           dcmjpeg/libsrc/Makefile.dep
           dcmjpeg/libsrc/Makefile.in
           dcmjpeg/libsrc/dipijpeg.cc
           dcmjpeg/libsrc/djcodece.cc
           dcmjpeg/libsrc/djcparam.cc
           dcmjpeg/libsrc/djdecode.cc
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc
           dcmjpeg/libsrc/djencode.cc
           dcmjpeg/libsrc/djutils.cc
           dcmjpls/apps/CMakeLists.txt
           dcmjpls/apps/Makefile.dep
           dcmjpls/apps/Makefile.in
           dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmjpls/include/dcmtk/dcmjpls/djcodecd.h
           dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/include/dcmtk/dcmjpls/djdecode.h
           dcmjpls/include/dcmtk/dcmjpls/djencode.h
           dcmjpls/include/dcmtk/dcmjpls/djlsutil.h
           dcmjpls/include/dcmtk/dcmjpls/djrparam.h
           dcmjpls/libsrc/CMakeLists.txt
           dcmjpls/libsrc/Makefile.dep
           dcmjpls/libsrc/Makefile.in
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djcparam.cc
           dcmjpls/libsrc/djdecode.cc
           dcmjpls/libsrc/djencode.cc
           dcmjpls/libsrc/djerror.h
           dcmjpls/libsrc/djrparam.cc
           dcmjpls/libsrc/djutils.cc

**** Changes from 2009.10.07 (riesmeier)

- Fixed typo.
  Affects: dcmdata/docs/dcmgpdir.man
           dcmjpeg/docs/dcmmkdir.man

**** Changes from 2009.10.01 (schlachter)

- Documented LOG4CPLUS_DISABLE_TRACE and other similar macros.
  Affects: config/docs/macros.txt

**** Changes from 2009.09.30 (riesmeier)

- Minor fixes to the comments.
  Affects: oflog/etc/logger.cfg

**** Changes from 2009.09.30 (schlachter)

- Improve oflog's sample configuration file to be a usable default. You now get
  "--verbose"-like output on the console and a log file "dcmtk.log" containing
  complete debug output. Timestamps are included.
  Affects: oflog/etc/logger.cfg

- Make dcmpstat's include headers self-sufficient by including all
  needed headers directly and stop using dctk.h
  Affects: dcmpstat/apps/Makefile.dep
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/include/dcmtk/dcmpstat/dviface.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsab.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsabl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsall.h
           dcmpstat/include/dcmtk/dcmpstat/dvpscul.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsdal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsga.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgal.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsgrl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpshlp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsibl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsovl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspr.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsprt.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsri.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsril.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrs.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsrsl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssp.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsspl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssv.h
           dcmpstat/include/dcmtk/dcmpstat/dvpssvl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstxl.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvll.h
           dcmpstat/include/dcmtk/dcmpstat/dvpsvwl.h
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/libsrc/dvpsab.cc
           dcmpstat/libsrc/dvpsabl.cc
           dcmpstat/libsrc/dvpsal.cc
           dcmpstat/libsrc/dvpshlp.cc
           dcmpstat/libsrc/dvpspl.cc
           dcmpstat/libsrc/dvpsri.cc
           dcmpstat/libsrc/dvpsril.cc
           dcmpstat/libsrc/dvpsrs.cc
           dcmpstat/libsrc/dvpsrsl.cc
           dcmpstat/libsrc/dvpssv.cc
           dcmpstat/libsrc/dvpssvl.cc
           dcmpstat/libsrc/dvpsvwl.cc

- Make dcmwlm's include headers self-sufficient by including all
  needed headers directly.
  Affects: dcmwlm/apps/Makefile.dep
           dcmwlm/include/dcmtk/dcmwlm/wlds.h
           dcmwlm/include/dcmtk/dcmwlm/wlfsim.h
           dcmwlm/include/dcmtk/dcmwlm/wlmactmg.h
           dcmwlm/include/dcmtk/dcmwlm/wltypdef.h
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep

- Stop including dctk.h in libi2d's header files.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/apps/img2dcm.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplnsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplvlp.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libi2d/i2d.cc
           dcmdata/libi2d/i2dbmps.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/libi2d/i2dplnsc.cc
           dcmdata/libi2d/i2dplsc.cc
           dcmdata/libi2d/i2dplvlp.cc

**** Changes from 2009.09.29 (schlachter)

- Include only the needed headers in dcuserid.h and not dctk.h.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/include/dcmtk/dcmnet/dcuserid.h
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep

- Fix an invalid iterator use in OFMap. A iterator was used after it was passed
  to erase().
  Affects: ofstd/include/dcmtk/ofstd/ofmap.h

- Add a test case which verifies some of OFMap's implementation.
  Added:   ofstd/tests/tmap.cc
  Affects: ofstd/tests/Makefile.in
           ofstd/tests/Makefile.dep

- Include only the needed headers in dcpath.h and dcpath.cc.
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/dcpath.cc
           dcmdata/tests/Makefile.dep
           dcmnet/libsrc/Makefile.dep

**** Changes from 2009.09.29 (riesmeier)

- Updated Makefile dependencies.
  Affects: dcmdata/libsrc/Makefile.dep
           oflog/libsrc/Makefile.dep
           ofstd/libsrc/Makefile.dep

- Removed superfluous empty line.
  Affects: dcmdata/include/dcmtk/dcmdata/CMakeLists.txt

**** Changes from 2009.09.28 (riesmeier)

- Introduced new member variable that stores the current length of the string.
  This yields in a significant performance improvement when compiled in debug
  mode.
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h
           ofstd/libsrc/ofstring.cc

- Added support for strings that contain null bytes ('\0') in order to be more
  compliant with the standard C++ string class.
  Affects: ofstd/libsrc/ofstring.cc
           ofstd/tests/tstring.cc
           ofstd/tests/tstring.exp

- Fixed typo in error message. Reformatted source code.
  Affects: dcmjpeg/libsrc/djcodece.cc

- Moved general purpose definition file from module dcmdata to ofstd, and
  added new defines in order to make the usage easier.
  Added:   ofstd/include/dcmtk/ofstd/ofdefine.h
  Removed: dcmdata/include/dcmtk/dcmdata/dcdefine.h
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/include/dcmtk/dcmdata/dctk.h
           dcmdata/libi2d/Makefile.dep
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/Makefile.dep
           dcmdata/tests/Makefile.dep
           dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/libsrc/Makefile.dep
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmjpls/apps/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/dccfenmp.cc
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep
           dcmsr/tests/Makefile.dep
           dcmtls/libsrc/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep
           ofstd/libsrc/Makefile.dep
           ofstd/tests/Makefile.dep

- Simplified code based on the new header file "ofdefine.h".
  Affects: ofstd/include/dcmtk/ofstd/ofbmanip.h

- Updated makefile dependencies.
  Affects: dcmjpls/libcharls/Makefile.dep
           dcmjpls/libsrc/Makefile.dep

**** Changes from 2009.09.25 (riesmeier)

- Introduced new general helper function moveMem() which allows for copying
  overlapping memory areas.
  Affects: ofstd/include/dcmtk/ofstd/ofbmanip.h

- Fixed issue in assign() method with overlapping memory areas (e.g. when using
  self-assignment of a string or copying a sub-string to itself).
  Affects: ofstd/libsrc/Makefile.dep
           ofstd/libsrc/ofstring.cc

**** Changes from 2009.09.24 (eichelberg)

- Fixed bug in class OFFile that caused undefined behaviour
  once fclose() failed
  Affects: ofstd/include/dcmtk/ofstd/offile.h

**** Changes from 2009.09.22 (riesmeier)

- Added documentation on supported query keys.
  Affects: dcmqrdb/docs/dcmqrscp.man

- Renamed "ti" to "dcmqrti".
  Affects: config/docs/macros.txt

**** Changes from 2009.09.21 (riesmeier)

- Fixed issue when compiled with configure --disable-threads.
  Affects: oflog/include/dcmtk/oflog/loggingmacros.h
           oflog/libsrc/stringhelper.cc

- Removed superfluous semicolon after calling OFSTRINGSTREAM_GETOFSTRING etc.
  Affects: oflog/include/dcmtk/oflog/loggingmacros.h
           oflog/libsrc/appender.cc
           oflog/libsrc/configurator.cc
           oflog/libsrc/fileappender.cc
           oflog/libsrc/patternlayout.cc
           oflog/libsrc/threads.cc

**** Changes from 2009.09.18 (riesmeier)

- Added private tags from Circle Cardiovascular Imaging cmr42 3.0 and SonoWand
  Invite 2.1.1 conformance statement.
  Affects: dcmdata/data/private.dic
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2009.09.17 (riesmeier)

- Added parentheses around && within || in order to avoid compiler warnings.
  Affects: dcmpstat/libsrc/dvpstat.cc

- Used static cast operator where appropriate and changed suffix from "L" to
  "UL" for large integer constants.
  Affects: dcmdata/libi2d/i2djpgs.cc

- Now using locking mechanism provided by class OFConsole for cout and cerr.
  Affects: oflog/include/dcmtk/oflog/streams.h
           oflog/libsrc/consoleappender.cc
           oflog/libsrc/loglog.cc

- Made sure that log4cplus' internal quiet mode is always set appropriately.
  Affects: oflog/libsrc/oflog.cc

- Fixed typo in comment.
  Affects: oflog/etc/logger.cfg

**** Changes from 2009.09.16 (riesmeier)

- Introduced new placeholder for the pattern layout: %i can be used to output
  the process ID of the process generating the log entry.
  Affects: oflog/libsrc/patternlayout.cc

- Added documentation on placeholders %h, %H, %P and %i.
  Affects: oflog/etc/logger.cfg

- Added missing documentation on placeholders %P and %i.
  Affects: oflog/include/dcmtk/oflog/layout.h

- Added reference to sample configuration file to oflog documentation.
  Affects: oflog/docs/oflog.dox

- Added missing documentation on parameters.
  Affects: oflog/include/dcmtk/oflog/oflog.h

- Changed OFLogger's copy constructor: use "const &" for the parameter.
  Affects: oflog/include/dcmtk/oflog/oflog.h
           oflog/libsrc/oflog.cc

- Minor fixes in comments.
  Affects: oflog/include/dcmtk/oflog/oflog.h
           oflog/libsrc/patternlayout.cc

**** Changes from 2009.09.15 (riesmeier)

- Removed alternative implementation of getOFStringArray(). Now, always the
  OFStringStream approach is used.
  Affects: dcmdata/libsrc/dcvrobow.cc

- Fixed issue with dangling character pointer (memory freed before copied).
  Affects: oflog/libsrc/socket-unix.cc

- Changed implementation of getHostname() in order to be more consistent with
  the Unix version.
  Affects: oflog/libsrc/socket-win32.cc

- Changed implementation of string variant of encodeBase64(). This helper
  function is now based on the stream variant of encodeBase64() because the
  previous implementation had an issue with HAVE_STD_STRING defined.
  Affects: ofstd/libsrc/ofstd.cc

- Added missing oflog include directory.
  Affects: dcmsign/CMakeLists.txt

- Added initialization of Winsock DLL in order to use gethostname() et al.
  Affects: oflog/libsrc/oflog.cc

- Check whether HAVE_GETHOSTNAME and HAVE_GETHOSTBYNAME are defined before
  using the corresponding functions.
  Affects: oflog/libsrc/socket-unix.cc

- Enhanced implementation of writeXML() by writing hex numbers directly to the
  output stream instead of creating a temporary string first.
  Affects: dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcvrobow.cc

- Fixed issue with class OFauto_ptr: Default copy constructor and assignment
  operator could lead to double deletion of objects.
  Affects: oflog/libsrc/patternlayout.cc
           ofstd/include/dcmtk/ofstd/ofaptr.h

- Removed redundant output of log message prefix "Error: " and "Warning: ".
  Affects: dcmsign/apps/dcmsign.cc

- Fixed incorrect output of public key bits (expected string instead of int).
  Affects: dcmsign/apps/dcmsign.cc

- Updated Makefile dependencies.
  Affects: dcmsign/apps/Makefile.dep

**** Changes from 2009.09.14 (riesmeier)

- Introduced new placeholder for the pattern layout: %P can be used to output
  only the first character of the log level. Used for the default layout.
  Affects: oflog/etc/logger.cfg
           oflog/docs/oflog.dox
           oflog/libsrc/oflog.cc
           oflog/libsrc/patternlayout.cc

- Added new property "FormatEachLine" to the pattern layout which allows for
  correctly formatting multi-line messages (i.e. with repeated log prefix).
  Affects: oflog/include/dcmtk/oflog/layout.h
           oflog/libsrc/patternlayout.cc

- Slightly changed evaluation of log-related command line options.
  Affects: oflog/libsrc/oflog.cc

- Removed (now) unused helper function toLogMode().
  Affects: oflog/docs/oflog.dox
           oflog/include/dcmtk/oflog/oflog.h
           oflog/libsrc/oflog.cc

**** Changes from 2009.09.08 (eichelberg)

- Fixed bug affecting decompression of images with 8 bits/sample on big
  endian machines
  Affects: dcmjpls/libsrc/djcodecd.cc

**** Changes from 2009.09.07 (riesmeier)

- Moved --arguments option and corresponding output to oflog module in order
  to use the correct output stream.
  Affects: dcmsign/apps/dcmsign.cc
           oflog/libsrc/oflog.cc

- Moved output of resource identifier back from oflog to the application.
  Affects: dcmsign/apps/dcmsign.cc
           oflog/libsrc/oflog.cc

- Fixed issue with --quiet mode.
  Affects: oflog/libsrc/oflog.cc

- Use helper function checkDependence() where appropriate.
  Affects: dcmsign/apps/dcmsign.cc

- Converted Windows line breaks to Unix format.
  Affects: dcmimgle/data/monitor.lut
           dcmimgle/data/scanner.lut
           dcmjpeg/docs/dcmjpeg.dox
           dcmpstat/docs/dcmpstat.dox
           dcmpstat/include/dcmtk/dcmpstat/dvcache.h
           dcmsign/docs/dcmsign.dox
           dcmsr/data/report.css
           dcmsr/data/reportx.css
           dcmtls/docs/ciphers.txt
           dcmtls/docs/randseed.txt
           oflog/docs/LICENSE

**** Changes from 2009.09.04 (eichelberg)

- Disabled warning C4284 on VC6, needed for recent additions to class OFList.
  Affects: config/include/dcmtk/config/cfwin32.h.in

- Minor const iterator related changes needed to compile with VC6 with HAVE_STL
  Affects: dcmdata/apps/mdfconen.cc
           dcmdata/libi2d/i2d.cc
           dcmdata/libsrc/dcpath.cc
           dcmnet/libsrc/dfindscu.cc
           dcmnet/libsrc/lst.cc
           dcmpstat/include/dcmtk/dcmpstat/dvcache.h
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrsoprf.cc

- Updated libcharls in module dcmjpls to CharLS revision 27770.
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmjpls/docs/charls-readme.txt
           dcmjpls/docs/charls.patch
           dcmjpls/libcharls/colortransform.h
           dcmjpls/libcharls/config.h
           dcmjpls/libcharls/context.h
           dcmjpls/libcharls/contextrunmode.h
           dcmjpls/libcharls/decoderstrategy.h
           dcmjpls/libcharls/encoderstrategy.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/interface.cc
           dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/lookuptable.h
           dcmjpls/libcharls/losslesstraits.h
           dcmjpls/libcharls/processline.h
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/stdafx.h
           dcmjpls/libcharls/streams.h
           dcmjpls/libcharls/util.h
           dcmjpls/libsrc/djcodece.cc

**** Changes from 2009.09.04 (riesmeier)

- Avoid starting configure recursively for all modules when using exclusive
  commandline options like --help=short or --version.
  Affects: configure
           config/rootconf

- Changed default behavior of the logger: output log messages to stderr (not
  stdout) and flush stream immediately; removed "EARLY STARTUP" prefix from
  messages which was only used for testing purposes.
  Affects: oflog/libsrc/oflog.cc

- Replaced abort() by a more "friendly" return statement.
  Affects: oflog/libsrc/loggerimpl.cc

- Output all --version information to COUT (and not to CERR).
  Affects: dcmnet/apps/echoscu.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc

- Adapted module for DCMTK's new unified logging support.
  Affects: dcmsign/apps/CMakeLists.txt
           dcmsign/apps/Makefile.in
           dcmsign/apps/dcmsign.cc
           dcmsign/docs/dcmsign.man

- Changed warning message into an informational message (as it was before).
  Affects: dcmsign/apps/dcmsign.cc

**** Changes from 2009.09.03 (riesmeier)

- Updated reference to DICOM standard, fixed typo and minor formatting issues.
  Affects: dcmnet/docs/asconfig.txt

- Updated copyright date and made .cc and .nul files more consistent.
  Affects: dcmdata/libsrc/dcdictbi.cc
           dcmdata/libsrc/dcdictbi.nul

- Fixed various issues when compiled with configure --disable-threads.
  Affects: oflog/include/dcmtk/oflog/helpers/thread-config.h
           oflog/libsrc/stringhelper.cc

- Replaced "std::" by "STD_NAMESPACE ".
  Affects: oflog/include/dcmtk/oflog/ndc.h
           oflog/include/dcmtk/oflog/tstring.h
           oflog/include/dcmtk/oflog/helpers/loglog.h
           oflog/include/dcmtk/oflog/helpers/pointer.h
           oflog/include/dcmtk/oflog/helpers/property.h
           oflog/include/dcmtk/oflog/helpers/stringhelper.h
           oflog/libsrc/appender.cc
           oflog/libsrc/configurator.cc
           oflog/libsrc/fileappender.cc
           oflog/libsrc/hierarchy.cc
           oflog/libsrc/logger.cc
           oflog/libsrc/loggerimpl.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/patternlayout.cc
           oflog/libsrc/property.cc
           oflog/libsrc/socket-unix.cc
           oflog/libsrc/socket-win32.cc
           oflog/libsrc/socketbuffer.cc
           oflog/libsrc/stringhelper.cc
           oflog/libsrc/syncprims.cc
           oflog/libsrc/threads.cc

- Removed trailing spaces and empty lines at the end of the file.
  Affects: oflog/include/dcmtk/oflog/appender.h
           oflog/include/dcmtk/oflog/configurator.h
           oflog/include/dcmtk/oflog/consoleappender.h
           oflog/include/dcmtk/oflog/fileappender.h
           oflog/include/dcmtk/oflog/fstreams.h
           oflog/include/dcmtk/oflog/hierarchy.h
           oflog/include/dcmtk/oflog/hierarchylocker.h
           oflog/include/dcmtk/oflog/layout.h
           oflog/include/dcmtk/oflog/logger.h
           oflog/include/dcmtk/oflog/loggingmacros.h
           oflog/include/dcmtk/oflog/loglevel.h
           oflog/include/dcmtk/oflog/nteventlogappender.h
           oflog/include/dcmtk/oflog/nullappender.h
           oflog/include/dcmtk/oflog/socketappender.h
           oflog/include/dcmtk/oflog/streams.h
           oflog/include/dcmtk/oflog/syslogappender.h
           oflog/include/dcmtk/oflog/win32debugappender.h
           oflog/include/dcmtk/oflog/config/win32.h
           oflog/include/dcmtk/oflog/helpers/appenderattachableimpl.h
           oflog/include/dcmtk/oflog/helpers/logloguser.h
           oflog/include/dcmtk/oflog/helpers/sleep.h
           oflog/include/dcmtk/oflog/helpers/socket.h
           oflog/include/dcmtk/oflog/helpers/socketbuffer.h
           oflog/include/dcmtk/oflog/helpers/syncprims-pthreads.h
           oflog/include/dcmtk/oflog/helpers/syncprims-win32.h
           oflog/include/dcmtk/oflog/helpers/syncprims.h
           oflog/include/dcmtk/oflog/helpers/timehelper.h
           oflog/include/dcmtk/oflog/spi/appenderattachable.h
           oflog/include/dcmtk/oflog/spi/factory.h
           oflog/include/dcmtk/oflog/spi/filter.h
           oflog/include/dcmtk/oflog/spi/loggerfactory.h
           oflog/include/dcmtk/oflog/spi/loggerimpl.h
           oflog/include/dcmtk/oflog/spi/loggingevent.h
           oflog/include/dcmtk/oflog/spi/objectregistry.h
           oflog/include/dcmtk/oflog/spi/rootlogger.h
           oflog/libsrc/appenderattachableimpl.cc
           oflog/libsrc/consoleappender.cc
           oflog/libsrc/factory.cc
           oflog/libsrc/filter.cc
           oflog/libsrc/global-init.cc
           oflog/libsrc/hierarchylocker.cc
           oflog/libsrc/layout.cc
           oflog/libsrc/loggingevent.cc
           oflog/libsrc/loglevel.cc
           oflog/libsrc/logloguser.cc
           oflog/libsrc/nullappender.cc
           oflog/libsrc/sleep.cc
           oflog/libsrc/socketappender.cc
           oflog/libsrc/syslogappender.cc

- Added missing include for the definition of STD_NAMESPACE.
  Affects: oflog/include/dcmtk/oflog/helpers/stringhelper.h

- Fixed issue with getOFStringArray() when using standard C++ string class.
  Affects: dcmdata/libsrc/dcvrobow.cc

**** Changes from 2009.09.02 (riesmeier)

- Revised documentation of parameter "pos" for some putAndInsertXXX() functions
  in order to make clear what the possible range of values is.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h

**** Changes from 2009.08.31 (riesmeier)

- Fixed bug in group length computation which could cause a segmentation fault
  for incorrectly encoded DICOM datasets (with illegal group length elements).
  Thank to Evgeny Sabelskiy <<EMAIL>> for the original
  report.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2009.08.26 (riesmeier)

- Added parentheses around && within || in order to avoid warnings reported by
  gcc 4.3.2.
  Affects: oflog/libsrc/configurator.cc
           oflog/libsrc/socket-unix.cc
           ofstd/libsrc/ofstd.cc
           dcmdata/libsrc/dcddirif.cc
           dcmpstat/libsrc/dvpstat.cc

- Added suffix "L" to large integer constants in order to avoid warnings
  reported by gcc 4.3.2.
  Affects: dcmdata/libi2d/i2djpgs.cc

- Added check on size of long in order to avoid warnings reported by gcc 4.3.2.
  Affects: dcmdata/libsrc/dcpath.cc

- Added parentheses around + or - in operand of & in order to avoid warnings
  reported by gcc 4.3.2.
  Affects: dcmimgle/libsrc/dimoimg.cc
           dcmimage/include/dcmtk/dcmimage/dicopxt.h

- Added explicit braces to avoid ambiguous else (reported by gcc 4.3.2).
  Affects: dcmpstat/libsrc/dvpsrs.cc

- Added new command line options --write-new-meta-info and --no-invalid-groups.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmconv.man

**** Changes from 2009.08.25 (riesmeier)

- Added new methods which remove all data elements with an invalid group number
  from the meta information header, dataset and/or fileformat.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcmetinf.cc

- Fixed wrong comment on clear() method.
  Affects: dcmdata/include/dcmtk/dcmdata/dcfilefo.h

**** Changes from 2009.08.24 (riesmeier)

- Fixed wrong/misleading comments.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h

**** Changes from 2009.08.21 (riesmeier)

- Added new 'writeMode' which does not update the meta header. This could be
  useful for tools like dump2dcm and xml2dcm.
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcfilefo.cc

- Added parameter 'writeMode' to save/write methods which allows for specifying
  whether to write a dataset or fileformat as well as whether to update the
  file meta information or to create a new file meta information header.
  Affects: dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcfilefo.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmqrdb/libsrc/dcmqrcbs.cc
           dcmsr/apps/xml2dsr.cc

- Removed unused option 'opt_oDataset'.
  Affects: dcmpstat/apps/dcmpsmk.cc
           dcmsign/apps/dcmsign.cc

- Only update file meta information if required (use new file write mode).
  Affects: dcmjpeg/apps/dcmdjpeg.cc

- Added check making sure that a DICOMDIR file is never compressed.
  Affects: dcmdata/apps/dcmcrle.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpls/apps/dcmcjpls.cc

- Use helper function checkDependence() and checkConflict() where appropriate.
  Affects: dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmimage/apps/dcmquant.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpls/apps/dcmdjpls.cc

- Made error messages more consistent with other compression tools.
  Affects: dcmdata/apps/dcmcrle.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpls/apps/dcmcjpls.cc

- Replaced tabs by spaces and updated copyright date.
  Affects: dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbf.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbg.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbm.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbs.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcnf.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdba.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdbi.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdbs.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqropt.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrptb.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrsrv.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrtis.h
           dcmqrdb/libsrc/dcmqrcbf.cc
           dcmqrdb/libsrc/dcmqrcbg.cc
           dcmqrdb/libsrc/dcmqrcbm.cc
           dcmqrdb/libsrc/dcmqrcnf.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrdbs.cc
           dcmqrdb/libsrc/dcmqropt.cc
           dcmqrdb/libsrc/dcmqrptb.cc
           dcmqrdb/libsrc/dcmqrsrv.cc
           dcmqrdb/libsrc/dcmqrtis.cc

- Added type cast to integer variable in order to avoid compiler warnings
  reported by MSVC.
  Affects: dcmjpls/apps/dcmcjpls.cc

**** Changes from 2009.08.20 (eichelberg)

- Fixed include files needed on Solaris, removed unneeded compiler pragmas
  Affects: dcmjpls/libcharls/util.h

- Updated libcharls in module dcmjpls to CharLS revision 26807.
  Added:   dcmjpls/libcharls/config.h
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmjpls/docs/charls-readme.txt
           dcmjpls/docs/charls.patch
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/decoderstrategy.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/interface.cc
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/util.h
           dcmjpls/libsrc/Makefile.dep

**** Changes from 2009.08.20 (riesmeier)

- Updated oflog module documention.
  Affects: oflog/docs/oflog.dox

- Added documentation on log level values.
  Affects: oflog/include/dcmtk/oflog/oflog.h

- Added more checks when reading a log configuration from file.
  Affects: oflog/libsrc/oflog.cc

- Enhanced comments in oflog sample config file.
  Affects: oflog/etc/logger.cfg

- Updated Makefile dependencies.
  Affects: oflog/libsrc/Makefile.dep

- Various minor fixes needed for Sun Studio 11 on Solaris.
  Affects: oflog/include/dcmtk/oflog/helpers/thread-config.h
           oflog/include/dcmtk/oflog/helpers/threads.h
           oflog/libsrc/appender.cc
           oflog/libsrc/configurator.cc
           oflog/libsrc/fileappender.cc
           oflog/libsrc/logger.cc
           oflog/libsrc/loggerimpl.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/patternlayout.cc
           oflog/libsrc/rootlogger.cc
           oflog/libsrc/socket.cc
           oflog/libsrc/threads.cc

**** Changes from 2009.08.19 (eichelberg)

- Fixed static/extern linkage declaration
  Affects: dcmqrdb/libsrc/dcmqrdbi.cc

- Unlike some other compilers, Sun Studio 11 on Solaris declares longjmp()
  and qsort() in namespace std. Added code to handle this case.
  Affects: dcmjpeg/libsrc/dipijpeg.cc
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc
           dcmimage/libsrc/diqtpbox.cc

- Added additional includes needed for Sun Studio 11 on Solaris.
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcfilefo.cc
           dcmimage/include/dcmtk/dcmimage/diqtpbox.h
           dcmnet/apps/storescp.cc
           dcmnet/libsrc/dul.cc
           dcmpstat/tests/msgserv.cc
           dcmwlm/wwwapps/preplock.cc
           ofstd/include/dcmtk/ofstd/oflist.h
           ofstd/include/dcmtk/ofstd/oftime.h
           ofstd/libsrc/ofdatime.cc
           ofstd/libsrc/oftime.cc

- Function passed as 4th parameter to qsort() now declared extern "C",
  needed for Sun Studio 11 on Solaris.
  Affects: dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrtis.cc

- Fixed parameter that was declared as const in the implementation and
  as non-const in the class declaration.
  Affects: dcmdata/libsrc/dcpath.cc

- Removed trailing commata in lists to avoid warnings on Sun Studio 11.
  Affects: dcmjpls/libcharls/decoderstrategy.h
           dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/losslesstraits.h

- Removed static string constant which causes a compile error on Sun Studio 11
  due to the specific way template expansions are handled on this compiler.
  Affects: dcmjpls/libcharls/header.cc

**** Changes from 2009.08.19 (riesmeier)

- Added new string helper functions toUpper() and toLower().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Added iterator declarations and required methods.
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h

- Added missing dereference operator.
  Affects: ofstd/include/dcmtk/ofstd/oflist.h

- Removed redundant call to 'mkinstalldirs'.
  Affects: ofstd/include/Makefile.in

- Added new class OFauto_ptr required for upcoming module "oflog".
  Added:   ofstd/include/dcmtk/ofstd/ofaptr.h

- Added new class OFMap required for upcoming module "oflog".
  Added:   ofstd/include/dcmtk/ofstd/ofmap.h
  Affects: config/docs/macros.txt

- Added new configure tests for upcoming module "oflog".
  Affects: config/configure.in
           config/configure
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h.in

- Added new module "oflog" which is based on log4cplus.
  Added:   oflog/CMakeLists.txt
           oflog/Makefile.in
           oflog/configure
           oflog/data/Makefile.in
           oflog/docs/LICENSE
           oflog/docs/Makefile.in
           oflog/etc/CMakeLists.txt
           oflog/etc/Makefile.in
           oflog/etc/logger.cfg
           oflog/include/Makefile.in
           oflog/include/dcmtk/oflog/CMakeLists.txt
           oflog/include/dcmtk/oflog/appender.h
           oflog/include/dcmtk/oflog/config.h
           oflog/include/dcmtk/oflog/configurator.h
           oflog/include/dcmtk/oflog/consoleappender.h
           oflog/include/dcmtk/oflog/fileappender.h
           oflog/include/dcmtk/oflog/fstreams.h
           oflog/include/dcmtk/oflog/hierarchy.h
           oflog/include/dcmtk/oflog/hierarchylocker.h
           oflog/include/dcmtk/oflog/layout.h
           oflog/include/dcmtk/oflog/logger.h
           oflog/include/dcmtk/oflog/loggingmacros.h
           oflog/include/dcmtk/oflog/loglevel.h
           oflog/include/dcmtk/oflog/ndc.h
           oflog/include/dcmtk/oflog/nteventlogappender.h
           oflog/include/dcmtk/oflog/nullappender.h
           oflog/include/dcmtk/oflog/oflog.h
           oflog/include/dcmtk/oflog/socketappender.h
           oflog/include/dcmtk/oflog/streams.h
           oflog/include/dcmtk/oflog/syslogappender.h
           oflog/include/dcmtk/oflog/tstring.h
           oflog/include/dcmtk/oflog/win32debugappender.h
           oflog/include/dcmtk/oflog/config/defines.h
           oflog/include/dcmtk/oflog/config/macosx.h
           oflog/include/dcmtk/oflog/config/win32.h
           oflog/include/dcmtk/oflog/helpers/appenderattachableimpl.h
           oflog/include/dcmtk/oflog/helpers/loglog.h
           oflog/include/dcmtk/oflog/helpers/logloguser.h
           oflog/include/dcmtk/oflog/helpers/pointer.h
           oflog/include/dcmtk/oflog/helpers/property.h
           oflog/include/dcmtk/oflog/helpers/sleep.h
           oflog/include/dcmtk/oflog/helpers/socket.h
           oflog/include/dcmtk/oflog/helpers/socketbuffer.h
           oflog/include/dcmtk/oflog/helpers/stringhelper.h
           oflog/include/dcmtk/oflog/helpers/syncprims-pthreads.h
           oflog/include/dcmtk/oflog/helpers/syncprims-win32.h
           oflog/include/dcmtk/oflog/helpers/syncprims.h
           oflog/include/dcmtk/oflog/helpers/thread-config.h
           oflog/include/dcmtk/oflog/helpers/threads.h
           oflog/include/dcmtk/oflog/helpers/timehelper.h
           oflog/include/dcmtk/oflog/spi/appenderattachable.h
           oflog/include/dcmtk/oflog/spi/factory.h
           oflog/include/dcmtk/oflog/spi/filter.h
           oflog/include/dcmtk/oflog/spi/loggerfactory.h
           oflog/include/dcmtk/oflog/spi/loggerimpl.h
           oflog/include/dcmtk/oflog/spi/loggingevent.h
           oflog/include/dcmtk/oflog/spi/objectregistry.h
           oflog/include/dcmtk/oflog/spi/rootlogger.h
           oflog/libsrc/CMakeLists.txt
           oflog/libsrc/Makefile.dep
           oflog/libsrc/Makefile.in
           oflog/libsrc/appender.cc
           oflog/libsrc/appenderattachableimpl.cc
           oflog/libsrc/configurator.cc
           oflog/libsrc/consoleappender.cc
           oflog/libsrc/factory.cc
           oflog/libsrc/fileappender.cc
           oflog/libsrc/filter.cc
           oflog/libsrc/global-init.cc
           oflog/libsrc/hierarchy.cc
           oflog/libsrc/hierarchylocker.cc
           oflog/libsrc/layout.cc
           oflog/libsrc/logger.cc
           oflog/libsrc/loggerimpl.cc
           oflog/libsrc/loggingevent.cc
           oflog/libsrc/loglevel.cc
           oflog/libsrc/loglog.cc
           oflog/libsrc/logloguser.cc
           oflog/libsrc/ndc.cc
           oflog/libsrc/nteventlogappender.cc
           oflog/libsrc/nullappender.cc
           oflog/libsrc/objectregistry.cc
           oflog/libsrc/oflog.cc
           oflog/libsrc/patternlayout.cc
           oflog/libsrc/pointer.cc
           oflog/libsrc/property.cc
           oflog/libsrc/rootlogger.cc
           oflog/libsrc/sleep.cc
           oflog/libsrc/socket-unix.cc
           oflog/libsrc/socket-win32.cc
           oflog/libsrc/socket.cc
           oflog/libsrc/socketappender.cc
           oflog/libsrc/socketbuffer.cc
           oflog/libsrc/stringhelper.cc
           oflog/libsrc/syncprims.cc
           oflog/libsrc/syslogappender.cc
           oflog/libsrc/threads.cc
           oflog/libsrc/timehelper.cc
           oflog/libsrc/win32debugappender.cc
           oflog/tests/Makefile.in
  Affects: config/modules
           doxygen/htmldocs.dox
           CMakeLists.txt
           COPYRIGHT
           Makefile
           README

- Added copyright note of new module "dcmjpls".
  Affects: COPYRIGHT

- Added missing sub-directories for "install includes".
  Added:   oflog/include/dcmtk/oflog/config/CMakeLists.txt
           oflog/include/dcmtk/oflog/helpers/CMakeLists.txt
           oflog/include/dcmtk/oflog/spi/CMakeLists.txt
  Affects: oflog/include/Makefile.in
           oflog/include/dcmtk/oflog/CMakeLists.txt

- Added missing sub-directory for "ofstd" header files.
  Affects: oflog/CMakeLists.txt

- Added initial version of a module description for Doxygen documentation.
  Added:   oflog/docs/oflog.dox

- Some minor fixes.
  Affects: oflog/etc/logger.cfg

- Removed unconditional "#define LOG4CPLUS_HAVE_GETTIMEOFDAY".
  Affects: oflog/include/dcmtk/oflog/config/defines.h

- Various fixes to make oflog compile on Windows (VisualStudio 2005).
  Affects: oflog/include/dcmtk/oflog/config/win32.h
           oflog/libsrc/nteventlogappender.cc
           oflog/libsrc/socket-win32.cc
           oflog/libsrc/threads.cc
           oflog/libsrc/timehelper.cc
           oflog/libsrc/win32debugappender.cc

- Do not include <time.h> directly, use wrapper file "ofstdinc.h" instead.
  Affects: oflog/include/dcmtk/oflog/config/win32.h
           oflog/libsrc/timehelper.cc

**** Changes from 2009.08.12 (onken)

- Fixed minor formatting issues and bug that caused some images being
  converted upside down.
  Affects: dcmdata/libi2d/i2dbmps.cc

**** Changes from 2009.08.11 (riesmeier)

- Slightly modified code in order to avoid warning messages on MSVC compiler.
  Affects: dcmdata/apps/pdf2dcm.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2009.08.10 (eichelberg)

- Added working implementation of DcmRLECodecDecoder::decodeFrame().
  Thanks to Tom Clune for contributing this code.
  Affects: dcmdata/libsrc/dcrleccd.cc

- CMake now recognizes the compiler name when spelled in uppercase
  or lowercase (e.g. CL.EXE or cl.exe). Needed to make VC6 work.
  Also updated default for libtiff to v3.8.2.
  Affects: CMakeLists.txt

- All decompression codecs now replace NumberOfFrames if larger than one
  or present in the original image.
  Affects: dcmdata/libsrc/dcrleccd.cc
           dcmjpeg/libsrc/djcodecd.cc
  Affects: dcmjpls/libsrc/djcodecd.cc

- Cleaned up Makefile dependencies: Some files contained dependencies
  from the libxml include files which cause compilation to file when compiling
  without libxml support.
  Affects: dcmdata/apps/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep

- Some modifications needed to make class OFFile compile on MinGW.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

**** Changes from 2009.08.10 (riesmeier)

- Added VERSION file. Current version number of the snapshot for internal
  development purposes is "3.5.5_CVS" and "3.5.5_YYYYMMDD" (date) for publicly
  available snapshots.
  Added:   VERSION
  Affects: CMakeLists.txt
           Makefile
           config/rootconf

- Replaced '\n' by OFendl where appropriate.
  Affects: dcmdata/apps/pdf2dcm.cc

- Use helper functions checkDependence() and checkConflict() where appropriate.
  Affects: dcmdata/apps/pdf2dcm.cc

**** Changes from 2009.08.07 (eichelberg)

- Fixed some seek offset computations in SEEK_END tests
  Affects: ofstd/tests/toffile.cc

**** Changes from 2009.08.07 (riesmeier)

- Changed package version scheme: development snapshots now receive an odd
  number at the last position of the version number, i.e. currently it is
  "3.5.5". The macro PACKAGE_DATE is set to "CVS" by default but will be
  replaced by the current date for publicly available snapshots. From now on,
  official releases will always have an even number at the last position.
  Affects: CMakeLists.txt
           INSTALL
           config/configure
           config/configure.in

- Removed package version suffix "+" from development snapshots.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h

- Added missing relationship content constraint introduced with DICOM 2007.
  Affects: dcmsr/libsrc/dsrchecc.cc
           dcmsr/libsrc/dsrmamcc.cc

- Use new isEmpty() method instead of length in order to determine whether the
  element value is empty (e.g. for checking the presence of type 1 attributes).
  Affects: dcmsr/libsrc/dsrtypes.cc

- Fixed incorrect implementation of find_first_not_of() and find_last_not_of().
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h
           ofstd/libsrc/ofstring.cc

- Enhanced isEmpty() method by checking whether the data element value consists
  of non-significant characters only.
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrae.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrda.cc
           dcmdata/libsrc/dcvrds.cc
           dcmdata/libsrc/dcvrdt.cc
           dcmdata/libsrc/dcvris.cc
           dcmdata/libsrc/dcvrlo.cc
           dcmdata/libsrc/dcvrpn.cc
           dcmdata/libsrc/dcvrsh.cc
           dcmdata/libsrc/dcvrtm.cc
           dcmdata/libsrc/dcvrui.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2009.08.06 (eichelberg)

- Fixed bug in DUL_ReceiveAssociationRQ() that under very rare circumstances
  caused ASC_receiveAssociation() to return successfully although no DICOM
  association request had been received.
  Thanks to Markus Sabin <<EMAIL>> for the bug report and bug fix.
  Affects: dcmnet/libsrc/dul.cc

- Use of std::nothrow delete now conditional on compiler support for this
  feature
  Affects: dcmdata/libsrc/dcelem.cc

- Added configure test that checks for a std::nothrow version of operator
  delete. VC6 has a std::nothrow version of operator new, but not of operator
  delete.
  Affects: config/aclocal.m4
           config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h.in

- Fixed bug that caused TIFF export in dcm2pnm to fail on Win32 for newer
  releases of libtiff (3.7.4 and newer)
  Affects: dcmimage/libsrc/dipitiff.cc

**** Changes from 2009.08.05 (eichelberg)

- Added missing escape sequences in manpage text, updated copyright
  Affects: dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmftest.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/img2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/docs/dcod2lum.man
           dcmimgle/docs/dconvlum.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/docs/dcmsign.man
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmwlm/docs/wlmscpfs.man

- Fixed bug that caused dcmqrscp to accept image transmissions to read-only
  storage areas when multiple presentation contexts were proposed for a
  single SOP class.
  Affects: dcmqrdb/libsrc/dcmqrsrv.cc

- Modified some output messages to make their meaning clearer.
  Affects: dcmqrdb/libsrc/dcmqrdbi.cc

**** Changes from 2009.08.05 (riesmeier)

- Replaced numeric tag by pre-defined tag name (DCM_PixelDataProviderURL).
  Affects: dcmdata/libi2d/i2d.cc

- Fixed various inconsistencies in condition text values and log messages.
  Affects: dcmdata/libi2d/i2d.cc

- Fixed various issues with syntax usage (e.g. layout and formatting).
  Affects: dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/docs/dcmdrle.man
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man

- Made syntax usage more consistent with other DCMTK compression tools.
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc

- Fixed wrong reference to JPEG-LS implementation and added revision number.
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc

**** Changes from 2009.08.04 (riesmeier)

- Consistently use non-throwing version of "new" and "delete[]" (if available)
  in order to avoid memory creation/deletion mismatches.
  Thanks to Andreas Barth <<EMAIL>> for the report
  and suggested fix.
  Affects: dcmdata/libsrc/dcelem.cc

- Added optional parameter to printCStore() functions that allows for printing
  the Presentation Context ID.
  Affects: dcmnet/include/dcmtk/dcmnet/dimse.h
           dcmnet/libsrc/dimdump.cc

- Added output of Presentation Context ID of the C-STORE message in debug mode.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc

- Replaced remaining tabs by spaces.
  Affects: dcmnet/include/dcmtk/dcmnet/dimse.h

**** Changes from 2009.08.04 (eichelberg)

- Fixed incorrect code meaning string.
  Thanks to Mathieu Malaterre for the bug report.
  Affects: dcmjpeg/libsrc/djcodece.cc

**** Changes from 2009.08.03 (eichelberg)

- In order to reduce the probability of generating duplicate UIDs when one
  process is spawned immediately after termination of another one, and both
  use the same process ID and generate UIDs within the same second of
  system time (which may happen if a command line tool is implemented that
  just generates and prints a single UID and then terminates, and that tool
  is called repeatedly from a shell script), initialize the counter
  component of the UID with a sub-second fraction from the system time
  (microseconds on Posix systems, milliseconds on Win32).
  Affects: dcmdata/libsrc/dcuid.cc

- Fixed possible buffer overflow in UID generation code when UID root too
  long. Now printing warning message to stderr when truncating a UID.
  Affects: dcmdata/libsrc/dcuid.cc

- Fixed resource leak in the code creating forked child processes.
  Thanks to forum user "rhinojunk" for the bug report and fix.
  Affects: dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/dul.cc

- Fixed application crash due to dereferenced NULL pointer that affected
  some SCP applications when connected by a non-DICOM tool such as nmap.
  Thanks to forum user "parigaud" for the bug report.
  Affects: dcmnet/libsrc/assoc.cc

**** Changes from 2009.08.03 (riesmeier)

- Added methods that check whether a given string value conforms to the VR and
  VM definitions of the DICOM standards.
  Added:   dcmdata/tests/tstchval.cc
  Affects: dcmdata/include/dcbytstr.h
           dcmdata/include/dcelem.h
           dcmdata/include/dcerror.h
           dcmdata/include/dcvrae.h
           dcmdata/include/dcvras.h
           dcmdata/include/dcvrcs.h
           dcmdata/include/dcvrda.h
           dcmdata/include/dcvrds.h
           dcmdata/include/dcvrdt.h
           dcmdata/include/dcvris.h
           dcmdata/include/dcvrlo.h
           dcmdata/include/dcvrlt.h
           dcmdata/include/dcvrpn.h
           dcmdata/include/dcvrsh.h
           dcmdata/include/dcvrst.h
           dcmdata/include/dcvrtm.h
           dcmdata/include/dcvrui.h
           dcmdata/include/dcvrut.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcerror.cc
           dcmdata/libsrc/dcvrae.cc
           dcmdata/libsrc/dcvras.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrda.cc
           dcmdata/libsrc/dcvrds.cc
           dcmdata/libsrc/dcvrdt.cc
           dcmdata/libsrc/dcvris.cc
           dcmdata/libsrc/dcvrlo.cc
           dcmdata/libsrc/dcvrlt.cc
           dcmdata/libsrc/dcvrpn.cc
           dcmdata/libsrc/dcvrsh.cc
           dcmdata/libsrc/dcvrst.cc
           dcmdata/libsrc/dcvrtm.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/dcvrut.cc
           dcmdata/tests/Makefile.dep
           dcmdata/tests/Makefile.in
           dcmpstat/apps/Makefile.in
           dcmpstat/apps/dcmpschk.cc

- Moved flex++ generated lexical scanner from module "dcmpstat" to "dcmdata".
  Added:   dcmdata/include/dcmtk/dcmdata/vrscan.h
           dcmdata/libsrc/vrscan.cc
  Removed: dcmpstat/apps/vrscan.cc
           dcmpstat/apps/vrscan.h
  Affects: dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/Makefile.in
           dcmpstat/apps/CMakeLists.txt
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/Makefile.dep
           dcmpstat/apps/Makefile.in

- Added support for checking the data type UT (Unlimited Text).
  Affects: dcmpstat/apps/dcmpschk.cc

- Added non-public modules "dcmjpls" and "dcmloco".
  Affects: CMakeLists.txt

- Replaced checking of macro WIN32 by _WIN32.
  Affects: dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/stdafx.h
           dcmjpls/libcharls/util.h

- Fixed various issues and inconsistencies in the CMake project files.
  Affects: dcmjpls/apps/CMakeLists.txt
           dcmjpls/libsrc/CMakeLists.txt

**** Changes from 2009.07.31 (eichelberg)

- Line interleaved JPEG-LS mode now default. This mode works correctly
  when decompressing images with the LOCO-I reference implementation.
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/libsrc/djcodece.cc

- Updated makefile dependencies
  Affects: dcmjpls/apps/Makefile.dep
           dcmjpls/libsrc/Makefile.dep

- Added codec parameter and command line options that allow to control
  the interleave mode used in the JPEG-LS bitstream when compressing
  color images.
  Affects: dcmjpls/apps/dcmcjpls.cc
           dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/include/dcmtk/dcmjpls/djencode.h
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djcparam.cc
           dcmjpls/libsrc/djencode.cc

- Added more detailed error messages, minor code clean-up
  Added:   dcmjpls/libsrc/djerror.h
  Affects: dcmjpls/include/dcmtk/dcmjpls/djlsutil.h
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djutils.cc

**** Changes from 2009.07.29 (eichelberg)

- Defined new constant OFM_dcmloco
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h

- Initial release of module dcmjpls, a JPEG-LS codec for DCMTK based on CharLS
  Added:   dcmjpls/CMakeLists.txt
           dcmjpls/Makefile.in
           dcmjpls/configure
           dcmjpls/apps/CMakeLists.txt
           dcmjpls/apps/Makefile.dep
           dcmjpls/apps/Makefile.in
           dcmjpls/apps/dcmcjpls.cc
           dcmjpls/apps/dcmdjpls.cc
           dcmjpls/data/Makefile.in
           dcmjpls/docs/Makefile.in
           dcmjpls/docs/charls-readme.txt
           dcmjpls/docs/charls.patch
           dcmjpls/etc/Makefile.in
           dcmjpls/include/Makefile.in
           dcmjpls/include/dcmtk/dcmjpls/CMakeLists.txt
           dcmjpls/include/dcmtk/dcmjpls/djcodecd.h
           dcmjpls/include/dcmtk/dcmjpls/djcodece.h
           dcmjpls/include/dcmtk/dcmjpls/djcparam.h
           dcmjpls/include/dcmtk/dcmjpls/djdecode.h
           dcmjpls/include/dcmtk/dcmjpls/djencode.h
           dcmjpls/include/dcmtk/dcmjpls/djlsutil.h
           dcmjpls/include/dcmtk/dcmjpls/djrparam.h
           dcmjpls/libcharls/CMakeLists.txt
           dcmjpls/libcharls/Makefile.dep
           dcmjpls/libcharls/Makefile.in
           dcmjpls/libcharls/colortransform.h
           dcmjpls/libcharls/context.h
           dcmjpls/libcharls/contextrunmode.h
           dcmjpls/libcharls/decoderstrategy.h
           dcmjpls/libcharls/defaulttraits.h
           dcmjpls/libcharls/encoderstrategy.h
           dcmjpls/libcharls/header.cc
           dcmjpls/libcharls/header.h
           dcmjpls/libcharls/interface.cc
           dcmjpls/libcharls/interface.h
           dcmjpls/libcharls/jpegls.cc
           dcmjpls/libcharls/lookuptable.h
           dcmjpls/libcharls/losslesstraits.h
           dcmjpls/libcharls/processline.h
           dcmjpls/libcharls/scan.h
           dcmjpls/libcharls/stdafx.cc
           dcmjpls/libcharls/stdafx.h
           dcmjpls/libcharls/streams.h
           dcmjpls/libcharls/util.h
           dcmjpls/libsrc/CMakeLists.txt
           dcmjpls/libsrc/Makefile.dep
           dcmjpls/libsrc/Makefile.in
           dcmjpls/libsrc/djcodecd.cc
           dcmjpls/libsrc/djcodece.cc
           dcmjpls/libsrc/djcparam.cc
           dcmjpls/libsrc/djdecode.cc
           dcmjpls/libsrc/djencode.cc
           dcmjpls/libsrc/djrparam.cc
           dcmjpls/libsrc/djutils.cc
           dcmjpls/tests/Makefile.in

**** Changes from 2009.07.28 (riesmeier)

- Fixed wrong definition of value representation DS (Decimal String): According
  to the referenced standard (ANSI X3.9 - 1978), the sign symbol after the "E"
  or "e" is optional. Copied lexical scanner files from "dcmcheck" module.
  Affects: dcmpstat/apps/vrscan.cc
           dcmpstat/apps/vrscan.h

- Added support for dubious date checking (year before 1850 or after 2050).
  Affects: dcmpstat/apps/dcmpschk.cc

**** Changes from 2009.07.27 (riesmeier)

- Fixed possible memory leak in method addElementToDataset().
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2009.07.23 (riesmeier)

- Enhanced wording and consistency for new path notation of the --key option.
  Affects: dcmnet/docs/findscu.man

**** Changes from 2009.07.16 (onken)

- Added img2dcm input plugin for the BMP graphics format (at the moment only
  support for 24 Bit RGB).
  Thanks to Uli Schlachter <<EMAIL>> for the code contribution.
  Added:    dcmdata/include/dcmtk/dcmdata/libi2d/i2dbmps.h
            dcmdata/libi2d/i2dbmps.cc
  Affects:  dcmdata/docs/img2dcm.man
            dcmdata/libi2d/CMakeLists.txt
            dcmdata/libi2d/Makefile.dep
            dcmdata/libi2d/Makefile.in

- Extended Image2Dcm engine to also work for uncompressed pixel data input.
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/libi2d/i2d.cc

- Changed comment (and thus semantic of parameter for output transfer syntax).
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h

**** Changes from 2009.07.13 (onken)

- Removed misleading comment about dcmnet DIMSE return code and changed
  corresponding OFCondition check from EC_Normal to .good().
  Thanks to Peter Klotz <<EMAIL>> for the suggestion.
  Affects: dcmnet/apps/echoscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/libsrc/dfindscu.cc

**** Changes from 2009.07.08 (onken)

- Moved override key functionality to DcmPathProcessor.
  Affects: dcmnet/include/dcmtk/dcmnet/dfindscu.h
           dcmnet/libsrc/dfindscu.cc

- Added path functionality for --key option and lets the code make use
  of the DcmPath classes.
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/libi2d/i2d.cc
           dcmdata/apps/img2dcm.cc

- Added override key functionality used by tools like findscu to the more
  central DcmPathProcessor class.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/libsrc/dcpath.cc

**** Changes from 2009.07.08 (onken)

- Added support for specifying tag paths as override keys.
  Affects: dcmnet/apps/findscu.cc
           dcmnet/docs/findscu.man
           dcmnet/include/dcmtk/dcmnet/dfindscu.h
           dcmnet/libsrc/dfindscu.cc

- Added note that inserting new private tags that are not known to the
  dictionary may fail when combined with specific attribute values.
  Affects: dcmdata/docs/dcmodify.man

- Adapted dcmodify to changes in DcmPath API.
  Affects: dcmdata/apps/mdfdsman.cc

- Cleaned up code for private reservation checking and added option for
  disabling item wildcards for searching/creating tag paths.
  Affects: dcmdata/libsrc/dcpath.cc
           dcmdata/include/dcmtk/dcmdata/dcpath.h

**** Changes from 2009.06.30 (riesmeier)

- Fixed incorrect wording.
  Affects: config/docs/macros.txt

**** Changes from 2009.06.29 (riesmeier)

- Use full path to "cfunix.h" file in order to be more consistent with Windows
  systems.
  Thanks to Peter Klotz <<EMAIL>> for the suggestion.
  Affects: config/include/dcmtk/config/osconfig.h

- Changed definition of macro "WIN32" in order to avoid compiler warning (MS).
  Thanks to Peter Klotz <<EMAIL>> for the suggestion.
  Affects: config/include/dcmtk/config/cfwin32.h

**** Changes from 2009.06.25 (riesmeier)

- Replaced abbreviated spelling ("expl." -> "explicit") in manpage.
  Affects: dcmdata/docs/dcmconv.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmsr/docs/xml2dsr.man

**** Changes from 2009.06.24 (onken)

- Adapted sndfile library/include paths to libsndfile's standard
  installation directory structure for windows.
  Affects: CMakeLists.txt

**** Changes from 2009.06.18 (onken)

- Clarified img2dcm documentation regarding --study-from and --series-from.
  Affects: dcmdata/docs/img2dcm.man

**** Changes from 2009.06.18 (riesmeier)

- Fixed wrong output in debug mode (probably cut and paste error).
  Affects: dcmnet/libsrc/dimse.cc

**** Changes from 2009.06.12 (riesmeier)

- Added reference to Wiki system and obfuscated e-mail address.
  Affects: README
           doxygen/htmldoc.dox

- Added reference to "data" subdirectories.
  Affects: doxygen/htmldoc.dox

**** Changes from 2009.06.11 (riesmeier)

- Fixed issue with "Date" and "DateTime" Schema definitions: the "second" part
  was not only optional but multi-optional (used '*' instead of '?').
  Thanks to David Clunie <<EMAIL>> for pointing this out.
  Affects: dcmsr/data/dsr2xml.xsd

**** Changes from 2009.06.04 (riesmeier)

- Added new flag that can be used to avoid wrong warning messages (in debug
  mode) that an option has possibly never been checked.
  Affects: dcmdata/apps/mdfconen.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmpstat/apps/dcmpsprt.cc
           ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc

- Added note on --pstate that this option can be specified multiple times.
  Affects: dcmpstat/apps/dcmpsprt.cc
           dcmpstat/docs/dcmpsprt.man

- Report a warning on all data elements in the meta-header which have an
  incorrect group number, i.e. everything but 0x0002.
  Affects: dcmdata/libsrc/dcmetinf.cc

- Added new parsing flag that allows for ignoring the value of File Meta
  Information Group Length (0002,0000).
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcmetinf.cc

- Added new command line option that allows for ignoring the value of File Meta
  Information Group Length (0002,0000).
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man

- Fixed wrong name of command line option: used --impl-oversized instead of
  --write-oversized.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmconv.man

- Fixed minor source code formatting issues.
  Affects: dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dctag.cc

- Fixed typo.
  Affects: dcmdata/docs/img2dcm.man
           dcmdata/libsrc/dcfilefo.cc

**** Changes from 2009.06.02 (riesmeier)

- Made CMake support for optional features (such as external libraries) on
  Windows more consistent with GNU autoconf mechanism on Unix, i.e. add defines
  to cfwin32.h instead of the project settings.
  Thanks to Peter Klotz <<EMAIL>> for the suggestion.
  Affects: CMakeLists.txt
           config/include/dcmtk/config/cfwin32.h.in

**** Changes from 2009.05.29 (onken)

- Fixed exception thrown by later WSACleanup call because of socket handle
  being (apparently) not closed correctly after socket handle duplication.
  Thanks to Yves Neumann <<EMAIL>> for the bug report and
  suggested fix.
  Affects: dcmnet/libsrc/dul.cc

**** Changes from 2009.05.28 (riesmeier)

- Fixed issue with substitution variables "#a" and "#c": In some cases, the AE
  titles from the wrong association were used during execute on end-of-study.
  Thanks to Per Mathisen <<EMAIL>> for the report.
  Affects: dcmnet/apps/storescp.cc

- Fixed various Doxygen API documentation issues.
  Affects: dcmpstat/include/dcmtk/dcmpstat/dcmpstat.h
           dcmpstat/include/dcmtk/dcmpstat/dviface.h
           dcmpstat/include/dcmtk/dcmpstat/dvpstat.h

**** Changes from 2009.05.26 (riesmeier)

- Updated latest tested CMake version to 2.6.4.
  Affects: CMakeLists.txt

**** Changes from 2009.05.15 (riesmeier)

- Made output of directory record in "tree mode" more consistent with the rest
  of the textual dump (print flag = PF_showTreeStructure).
  Affects: dcmdata/libsrc/dcdirrec.cc

- Fixed wrong comments in print() method.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2009.05.14 (riesmeier)

- Added new tags from Supplement 43 (Storage of 3D Ultrasound Images) to data
  dictionary.
  Affects: dcmdata/data/dicom.dic
           dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2009.05.13 (riesmeier)

- Fixed possible integer overflow for images with very large pixel data in
  method getUncompressedFrameSize() for the rare case that BitsAllocated is
  not a multiple of 8.
  Affects: dcmdata/libsrc/dcelem.cc

**** Changes from 2009.05.11 (eichelberg)

- Minor fix in DcmElement::getUncompressedFrameSize for the rare case that
  BitsAllocated is not 8 or 16. Also the method now returns the true frame
  size without any pad byte.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/libsrc/dcelem.cc

- DcmPixelData::getUncompressedFrame() now works with uncompressed multi-frame
  images with odd frame size.
  Affects: dcmdata/libsrc/dcpixel.cc

- DcmPixelData::getUncompressedFrame() now returns color model also
  for uncompressed images.
  Affects: dcmdata/libsrc/dcpixel.cc

**** Changes from 2009.05.11 (riesmeier)

- Fixed typo.
  Affects: dcmdata/docs/xml2dcm.man

**** Changes from 2009.05.07 (riesmeier)

- Added new command line options that allow for generating new Study/Series/SOP
  Instance UIDs (incl. an option for overwriting existing values).
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmsr/apps/xml2dsr.cc
           dcmsr/docs/xml2dsr.man

- Added new command line options that allow for ignoring the file meta
  information and for updating particular file meta information.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/docs/dump2dcm.man

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Removed email addresses.
  Affects: ANNOUNCE.355

- Fixed minor layout issues.
  Affects: dcmdata/docs/pdf2dcm.man

**** Changes from 2009.05.06 (riesmeier)

- Fixed outdated reference to "test.cfg" in header comment.
  Affects: dcmpstat/etc/printers.cfg

- Fixed minor layout issues.
  Affects: dcmpstat/etc/dcmpstat.cfg

**** Changes from 2009.05.04 (eichelberg)

- Fixed bug in DcmPixelItem::writeSignatureFormat() that caused pixel data
  to be removed from the dataset when a digital signature was generated
  for a compressed image.
  Affects: dcmdata/libsrc/dcpxitem.cc

**** Changes from 2009.04.30 (riesmeier)

- Fixed issue in writeXML(): Used wrong byte order for 16-bit data on systems
  with big endian byte-ordering (wrong parameter order for swapBytes() call).
  Affects: dcmdata/libsrc/dcvrobow.cc

- Fixed memory leak in putElementContent() for base64 encoded data.
  Affects: dcmdata/apps/xml2dcm.cc

- Avoid swapping the byte-ording back to the original state in printPixel().
  Affects: dcmdata/libsrc/dcvrobow.cc

- Update the byte order variable in writeXML() and printPixel() if necessary.
  Affects: dcmdata/libsrc/dcvrobow.cc

**** Changes from 2009.04.29 (riesmeier)

- Fixed memory leak in method verifyCurrent().
  Thanks to forum user "yeganeh" for the report.
  Affects: dcmsign/libsrc/dcsignat.cc

- Load all data into memory before signing the document, otherwise the pixel
  data would be empty for compressed images.
  Thanks to Mathieu Malaterre <<EMAIL>> for the report.
  Affects: dcmsign/apps/dcmsign.cc

- Fixed small issue regarding the console output when verifying a signature.
  Affects: dcmsign/apps/dcmsign.cc

- Fixed minor inconsistencies regarding layout/formatting in syntax usage.
  Affects: dcmnet/docs/storescp.man
           dcmpstat/docs/dcmmklut.man
           dcmsign/apps/dcmsign.cc
           dcmsign/docs/dcmsign.man

**** Changes from 2009.04.27 (riesmeier)

- Added comment on absolute path names e.g. in UNC syntax.
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Added further test for UNC syntax in path expressions.
  Affects: ofstd/tests/tofstd.cc

**** Changes from 2009.04.24 (riesmeier)

- Added new command line option --fork in order to explicitly indicate what
  the default behavior is (= multi-processing).
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man

- Fixed minor inconsistencies regarding layout/formatting in syntax usage.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/img2dcm.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescu.man
           dcmpstat/docs/dcmprscu.man
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/apps/dcmsign.cc
           dcmsign/docs/dcmsign.man
           dcmwlm/docs/wlmscpfs.man
           dcmwlm/docs/wwwapp.txt

- Removed empty lines at the end of the file and trailing spaces.
  Affects: dcmtls/docs/ciphers.txt
           dcmtls/docs/randseed.txt

**** Changes from 2009.04.23 (riesmeier)

- Changed the condition under which the process is terminated in inetd mode
  (the behavior should be identical but now the source code is more clear).
  Affects: dcmnet/apps/storescp.cc

- Fixed minor layout and formatting issues.
  Affects: dcmnet/docs/storescp.man
           dcmsign/docs/dcmsign.man

**** Changes from 2009.04.22 (riesmeier)

- Made inetd code and documentation more consistent with ppsscpfs.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

**** Changes from 2009.04.21 (riesmeier)

- Added new compatibility flag CIF_UseAbsolutePixelRange which changes the way
  the internal representation of monochrome images is determined.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/dimomod.cc

- Added method getUsedBits() which allows for retrieving the number of bits
  actually used to store the output data.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimomod.h
           dcmimgle/libsrc/dimomod.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Fixed typo.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/libsrc/dsrdoctn.cc

- Fixed minor inconsistencies in manpage / syntax usage.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/img2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/apps/dcmdspfn.cc
           dcmimgle/docs/dcmdspfn.man
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/apps/dcmsign.cc
           dcmsign/docs/dcmsign.man
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2009.04.20 (riesmeier)

- Fixed issue with wrong BitsStored value in writeImageToDataset(). Thanks to
  Marc Schlimbach <<EMAIL>> for the original report.
  Affects: dcmimgle/libsrc/dimoimg.cc

- Added new helper function getRepresentationBits().
  Affects: dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/diutils.cc

- Added method that checks whether the tag key is a valid group length element.
  Affects: dcmdata/include/dcmtk/dcmdata/dctagkey.h

- Fixed wrong section title.
  Affects: dcmjpeg/docs/dcmcjpeg.man
           dcmdata/docs/dcmcrle.man

- Fixed typo.
  Affects: dcmdata/libi2d/i2djpgs.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/tests/tstpath.cc

**** Changes from 2009.04.17 (riesmeier)

- Added new command line option --single-process in order to explicitly
  indicate what the default behavior is (no multi-processing).
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Added new command line option --fork in order to explicitly indicate what
  the default behavior is (multi-processing).
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2009.04.07 (riesmeier)

- Fixed issue with unknown UID in uid2name().
  Thanks to Evgeny Sabelskiy <<EMAIL>> for the report and
  suggested fix.
  Affects: dcmnet/libsrc/dimdump.cc

- Updated description section in man page.
  Affects: dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man

**** Changes from 2009.04.06 (riesmeier)

- Also check whether the output directory is writable if it's the current
  directory (".").
  Affects: dcmnet/apps/storescp.cc

**** Changes from 2009.04.03 (riesmeier)

- Avoid setting the value of PixelSpacing and PixelAspectRatio twice.
  Affects: dcmimgle/libsrc/diimage.cc

- Added check whether neither scaling nor clipping is required.
  Affects: dcmimage/apps/dcmscale.cc

- Do not write PixelAspectRatio for square pixels (width == height).
  Affects: dcmimgle/libsrc/diimage.cc

**** Changes from 2009.04.01 (riesmeier)

- Made all XML attributes that are not required by xml2dcm optional, i.e.
  "card", "len" and "vm".
  Affects: dcmdata/data/dcm2xml.dtd

**** Changes from 2009.03.31 (onken)

- Small fix regarding lossy compression parameters.
  Affects: dcmdata/libi2d/i2djpgs.cc

- Changed implementation of lossy compression attribute detection and writing.
  Affects: dcmdata/libi2d/i2d.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h

- Added NULL pointer check.
  Affects: dcmdata/apps/img2dcm.cc

- Attribute "Lossy Image Compression" is now written per default if
  source image already had a lossy encoding. Thanks to Mathieu Malaterre
  <<EMAIL>> for the suggestion.
  Affects: dcmdata/libi2d/i2d.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h

- Added possibility to also insert type 2 elements with a default value
  when automatically inserting missing values (feature currently not in use).
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h

**** Changes from 2009.03.27 (onken)

- Attribute "Pixel Aspect Ratio" (as found in JFIF header) is now written
  to DICOM dataset if not equal to 1. Thanks to Mathieu Malaterre
  <<EMAIL>> for the suggestion.
  Affects: dcmdata/libi2d/i2d.cc

**** Changes from 2009.03.26 (riesmeier)

- Added checks on JPEG data in order to avoid crash when pixel item is empty.
  Thanks to Mathieu Malaterre <<EMAIL>> for the report.
  Affects: dcmjpeg/libsrc/djcodecd.cc

**** Changes from 2009.03.25 (riesmeier)

- Added new method isEmpty() to DICOM object, item and sequence class.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Added new module "dcmrt".
  Affects: CMakeLists.txt
           doxygen/htmldocs.cfg
           doxygen/htmldocs.dox

- Fixed inconsistency regarding parameter name.
  Affects: dcmsign/docs/dcmsign.man

- Added parentheses in order to keep MSVC quiet.
  Affects: dcmdata/apps/dcm2xml.cc

**** Changes from 2009.03.24 (onken)

- Removed doubled result variable declaration in true lossless encoder which may
  have led the function to return success even in case of errors.
  Thanks to forum user "popu" for the bug report.
  Affects: dcmjpeg/libsrc/djcodece.cc

**** Changes from 2009.03.19 (riesmeier)

- Added documentation on supported input and output transfer syntaxes.
  Affects: dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man

- Added more explicit message in case input transfer syntax is not supported.
  Affects: dcmdata/apps/dcmdrle.cc
           dcmjpeg/apps/dcmdjpeg.cc

- Replaced '\n' by OFendl where appropriate.
  Affects: dcmdata/apps/dcmcrle.cc
           dcmjpeg/apps/dcmcjpeg.cc

- Fixed inconsistency regarding parameter name.
  Affects: dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man

- Fixed issue with Doxygen documentation.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h

**** Changes from 2009.03.18 (riesmeier)

- Fixed "number overflow" issue with error message on non-standard VR.
  Affects: dcmdata/libsrc/dcitem.cc

- Removed redundant call to OFString constructor.
  Affects: dcmdata/apps/dcmdump.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2009.03.13 (riesmeier)

- Added new helper function getFilenameFromPath().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Added check on pixel data directory (option --write-pixel), i.e. whether it
  exists and whether it is writable.
  Affects: dcmdata/apps/dcmdump.cc

- Added support for option --write-pixel when --search is used to print
  selected tags only.
  Thanks to forum user "spasmous" for pointing this out.
  Affects: dcmdata/apps/dcmdump.cc

**** Changes from 2009.03.12 (riesmeier)

- Added OFFile to the list of main classes.
  Affects: ofstd/docs/ofstd.dox

- Fixed various Doxygen API documentation issues.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Updated latest tested CMake version to 2.6.3.
  Affects: CMakeLists.txt

- Fixed typo in section on Presentation Contexts.
  Affects: dcmnet/docs/asconfig.txt

**** Changes from 2009.03.11 (riesmeier)

- Added support for specifying the filename of the DTD on the command line.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man

- Added command line option for quiet mode (print no warnings and errors).
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man

- Updated configuration directory to "/usr/local/etc/dcmtk/dcmqrscp.cfg".
  Affects: dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrset.txt

**** Changes from 2009.03.10 (riesmeier)

- Added note on optional support for extended negotiation.
  Affects: dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man

- Fixed typo and updated reference to configuration directory.
  Affects: dcmnet/docs/asconfig.txt

**** Changes from 2009.03.06 (riesmeier)

- Made error/warning messages and verbose output more consistent with movescu.
  Affects: dcmnet/apps/storescp.cc

- Made error/warning messages and verbose output more consistent with storescp.
  Affects: dcmnet/apps/movescu.cc

- Changed output from stderr to CERR and from stdout to COUT.
  Affects:dcmnet/apps/movescu.cc

- Added check whether output directory is writable (not only existent).
  Affects: dcmnet/apps/storescp.cc

- Made use of debug mode (instead of verbose mode) where appropriate.
  Affects: dcmnet/apps/storescp.cc

- Added further documentation on --sort-on-study-uid and --sort-on-patientname.
  Affects: dcmnet/docs/storescp.man

- Output details on DIMSE fragments (PDUs) only if DEBUG is defined.
  Affects: dcmnet/libsrc/dimse.cc

- Minor cleanup of output messages.
  Affects: dcmnet/libsrc/dimse.cc

**** Changes from 2009.03.05 (riesmeier)

- Added new command line option --sort-on-study-uid.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Renamed command line option --sort-on-patientsname to --sort-on-patientname.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Slightly reworked (simplified) code on the creation of subdirectory names.
  Affects: dcmnet/apps/storescp.cc

- Made warning and error messages more consistent throughout the tool.
  Affects: dcmnet/apps/storescp.cc

- Changed output from stderr to CERR and from stdout to COUT.
  Affects: dcmnet/apps/storescp.cc

- Introduced general OFStandard methods where appropriate.
  Affects: dcmnet/apps/storescp.cc

- Fixed description of command line option.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmconv.man
           dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

**** Changes from 2009.03.04 (onken)

- Fixed typo.
  Affects: dcmdata/apps dcmconv.cc
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Fixed typo.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmconv.man

- Added support for the new parser's global sequence/item length overflow flag.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmconv.man

- Added checks for sequence and item lengths which prevents overflow in length
  field, if total length of contained items (or sequences) exceeds
  32-bit length field. Also introduced new flag (default: enabled)
  for writing in explicit length mode, which allows for automatically
  switching encoding of only that very sequence/item to undefined
  length coding (thus permitting to actually write the file).
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcerror.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Added helper function that checks whether a given Uint32 addition would
  result in an overflow.
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h

**** Changes from 2009.03.02 (riesmeier)

- Fixed issue with incorrectly encoded file meta information header consisting
  of one data element only (e.g. TransferSyntaxUID).
  Thanks to Mathieu Malaterre <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/dcmetinf.cc

- Fixed bug that caused the print flag PF_convertToMarkup always being set.
  Affects: dcmdata/libsrc/dcbytstr.cc

- Added attribute name to debug output / error messages where appropriate.
  Affects: dcmwlm/libsrc/wlds.cc

- Restructured command line options (be more consistent with other tools).
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2009.02.27 (riesmeier)

- Added new command line option --word-align which was previously the default
  for all bit values.
  Affects: dcmpstat/apps/dcmmklut.cc
           dcmpstat/docs/dcmmklut.man

- Changed behavior of option --byte-align which previously implied --bits 8.
  Instead, --bits 8 now implies --byte-align (if not specified manually).
  Affects: dcmpstat/apps/dcmmklut.cc
           dcmpstat/docs/dcmmklut.man

- Added note on further restrictions on the LUT structure in particular IODs.
  Affects: dcmpstat/docs/dcmmklut.man

**** Changes from 2009.02.26 (riesmeier)

- Enhanced warning message text.
  Affects: dcmimgle/libsrc/diluptab.cc

**** Changes from 2009.02.25 (riesmeier)

- Fixed file locking issue that prevented DICOMDIR files with large element
  values (e.g. pixel data inside an IconImageSequence) from being renamed.
  Thanks to Takeo Satomi <<EMAIL>> for the bug report and
  suggested fix.
  Affects: dcmdata/libsrc/dcdicdir.cc

- Removed wrong comment (apparently copied from other class).
  Affects: dcmdata/include/dcmtk/dcmdata/dcwcache.h

**** Changes from 2009.02.23 (riesmeier)

- Fixed issue with checking of DICOM input files (wrong handling of OFCondition
  return value).
  Affects: dcmdata/libsrc/dcddirif.cc

- Fixed man page formatting.
  Affects: dcmjpeg/docs/dcmj2pnm.man

**** Changes from 2009.02.18 (eichelberg)

- Minor changes needed for VC6
  Affects: dcmdata/libi2d/i2djpgs.cc
           dcmdata/libsrc/dcitem.cc

**** Changes from 2009.02.16 (onken)

- Fixed bug that caused incorrect error message when parsing undefined length
  sequences inside defined length items. Thanks to Mathieu Malaterre
  <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2009.02.13 (riesmeier)

- Added private undefined copy constructor and assignment operator in order to
  avoid compiler warnings (reported by gcc with additional flags).
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Added initialization of member variables to class OFConfigFileCursor in order
  to avoid compiler warnings (reported by gcc with additional flags).
  Affects: ofstd/libsrc/ofconfig.cc

**** Changes from 2009.02.12 (riesmeier)

- Never update value of ImagerPixelSpacing when image is scaled, use
  PixelSpacing instead.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/libsrc/diimage.cc

- Added support for NominalScannedPixelSpacing in order to determine the pixel
  aspect ratio (used for the new SC image IODs).
  Affects: dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/libsrc/diimage.cc

**** Changes from 2009.02.11 (riesmeier)

- Introduced new error code EC_FileMetaInfoHeaderMissing.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/libsrc/dcerror.cc
           dcmdata/libsrc/dcfilefo.cc

- Renamed option --stop-at-elem to --stop-after-elem and fixed typo.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man

**** Changes from 2009.02.11 (onken)

- Added global parser flag permitting to stop parsing after a specific
  element was parsed on dataset level (useful for removing garbage at
  end of file).
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/include/dcmtk/dcmdata/dcobject.h

**** Changes from 2009.02.09 (riesmeier)

- Allowed option --compression-level also with --propose-deflated (storescu).
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man

**** Changes from 2009.02.06 (riesmeier)

- Added support for JPEG-LS and MPEG2 transfer syntaxes.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/libsrc/dcmqrcbm.cc

- Fixed minor inconsistencies with regard to transfer syntaxes.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/libsrc/dcmqrcbm.cc

- Added support for writing files with deflated transfer syntax.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/docs/movescu.man
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man

- Added documentation of supported transfer syntaxes (esp. compression).
  Affects: dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmqrdb/docs/dcmqrscp.man

- Added new option that allows for accepting all supported transfer syntaxes.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man

- Call OFConsoleApplication::checkDependence() where appropriate.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc

- Reworked checking of command line options for user identify negotiation.
  Affects: dcmnet/apps/storescu.cc

- Fixed type mismatches reported by MSVC introduced with OFFile class.
  Affects: dcmnet/libsrc/dimse.cc

**** Changes from 2009.02.05 (onken)

- Make usage of global "ignore parsing errors" flag in case of elements
  being larger than rest of available input. However, if enabled, the
  parser ignores any elements coming after such an input-exceeding
  element. Minor code clarifications.
  Affects: dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcitem.cc

**** Changes from 2009.02.05 (riesmeier)

- Made new error messages more consistent with existing messages.
  Affects: dcmdata/libsrc/dcitem.cc

- Added reload method to data dictionary class.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdict.h
           dcmdata/libsrc/dcdict.cc

- Added re-initialization of missing member variables in clear() method.
  Affects: dcmdata/libsrc/dcdict.cc

- Output name of loaded data dictionary files in debug mode.
  Affects: dcmdata/libsrc/dcdict.cc

**** Changes from 2009.02.04 (onken)

- Removed German comment and removed "magic number".
  Affects: dcmdata/libsrc/dcsequen.cc

- Introduced global flag that, if enabled, tells the parser to continue
  parsing if possible.
  Affects: dcmdata/libsrc/dcobject.cc
           dcmdata/include/dcmtk/dcmdata/dcobject.h

- Changed parser to make use of the new error ignoring flag when parsing.
  Added check (makes use of new flag) that notes whether an element's value is
  specified larger than the surrounding item (applicable for explicit length
  coding).
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcerror.cc
           dcmdata/include/dcmtk/dcmdata/dcerror.h

- Added command line options making use of the parser's error ignoring flag.
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man

**** Changes from 2009.02.04 (riesmeier)

- Fixed issue with compressed frames of odd length (possibly wrong values in
  basic offset table).
  Thanks to Christian Schwerin <<EMAIL>> for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc

- Introduced new error code EC_InvalidBasicOffsetTable.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/libsrc/dcerror.cc

- Fixed small issue with syntax usage layout.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Introduced new syntax usage output subsection "loading".
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Minor fixes and code restructuring (without changing the semantics).
  Affects: dcmdata/libsrc/dcitem.cc

- Fixed various type mismatches reported by MSVC introduced with OFFile class.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcwcache.cc

- Fixed various layout and formatting issues.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/include/dcmtk/dcmdata/dcwcache.h
           dcmdata/libsrc/dcerror.cc
           dcmdata/libsrc/dcobject.cc

- Uncommented name of unused parameter in order to avoid compiler warnings.
  Affects: dcmdata/libsrc/dcpixseq.cc

**** Changes from 2009.01.30 (riesmeier)

- Updated makefile templates in order to be consistent with the current
  Makefiles.
  Affects: config/templates/Makefile.lib
           config/templates/Makefile.mod
           config/templates/Makefile.src

- Updated description in order to be consistent with the current structure.
  Affects: config/docs/modules.txt

- Added missing macros, updated existing entries.
  Affects: config/docs/macros.txt

- Removed local define "NODISPLAY" which is not used anymore.
  Affects: dcmwlm/wwwapps/Makefile.in

- Updated man pages.
  Affects: doxygen/manpages/man1/*

- Fixed bug in hasRepresentation() which returned the wrong status in case of
  compressed pixel data.
  Thanks to Roelant Visser <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/dcpixel.cc

- Removed definition of unused macro "windows".
  Thanks to Nils Gladitz <<EMAIL>> for the report.
  Affects: config/include/dcmtk/config/cfwin32.h.in

- Replaced checking of macro WIN32 by _WIN32.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Replaced checking of macro WINDOWS by _WIN32.
  Affects: dcmtls/libsrc/tlslayer.cc

- Uncommented initialization of array variables in order to avoid compiler
  warnings reported by VisualStudio 2005.
  Affects: dcmqrdb/include/dcmtk/dcmqrdb/dcmqridx.h

**** Changes from 2009.01.29 (onken)

- Fixed compilation under windows (missing memzero) by including dcdefine.h.
  Affects: dcmdata/libsrc/dcuid.cc

- Fixed length overflow in case of private attributes having maximum length
  values. Minor code simplifications.
  Affects: dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcbytstr.cc

- Added global parsing option that allows for reading private attributes in
  implicit encoding having a maximum length to be read as sequences instead
  of relying on the dictionary.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmconv.man
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcitem.cc

**** Changes from 2009.01.29 (riesmeier)

- Fixed issue with array initialization on x64 platform with Windows Vista and
  VisualStudio 2008.
  Thanks to Svilen Nedev <<EMAIL>> for the report and suggested fix.
  Affects: dcmdata/libsrc/dcuid.cc

- Fixed issue with missing invalidation of socket variable during close method.
  Please note that this is only required if the connection objects exists after
  the TCP/IP connection has been closed (which is currently not the case).
  Thanks to Evgeny Sabelskiy <<EMAIL>> for the report and
  suggested fix.
  Affects: dcmnet/include/dcmtk/dcmnet/dcmtrans.h
           dcmnet/libsrc/dcmtrans.cc

- Replaced remaining tab characters by spaces. Fixed source code formatting.
  Affects: dcmnet/libsrc/dul.cc

- Added "data" subdirectory to description of directory structure.
  Affects: config/docs/dirstruc.txt

**** Changes from 2009.01.27 (onken)

- Fixed some minor inconsistencies in dcmodify manpage.
  Affects: dcmdata/docs/dcmodify.man

**** Changes from 2009.01.16 (onken)

- Code simplification for VR checking.
  Affects: dcmdata/apps/mdfdsman.cc

- Fixed dcmodify help output.
  Affects: dcmdata/apps/mdfconen.cc

- Completed doxygen documentation for libi2d.
  Affects: dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplnsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplsc.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2dplvlp.h

**** Changes from 2009.01.15 (onken)

- Reworked dcmodify to work with the new DcmPath classes for supporting
  wildcard paths and automatic insertion of missing attributes and items.
  Added options for private tag handling, modification of UN values and
  for ignoring errors resulting from missing tags during modify and erase
  operations. Further cleanups.
  Affects: dcmdata/apps/dcmodify.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/mdfconen.h
           dcmdata/apps/mdfdsman.h
           dcmdata/docs/dcmodify.man

- Added options for handling of private tags and fixed bug for deleting
  tags on main level.
  Affects: dcmdata/libsrc/dcpath.cc
           dcmdata/include/dcmtk/dcmdata/dcpath.h

- Added convenience methods for private tag handling. Added doxygen
  documentation for not or badly documented member functions.
  Affects:  dcmdata/include/dcmtk/dcmdata/dctagkey.h

**** Changes from 2009.01.15 (riesmeier)

- Added support for optional JPEG 2000 decompression (currently uncommented).
  Affects: dcmdata/apps/dcmgpdir.cc

- Added check whether (possibly required) JPEG 2000 decoder is registered.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Do not reject compressed images if corresponding decoder is not registered
  but no icon images are to be created anyway.
  Affects: dcmdata/libsrc/dcddirif.cc

- Added reference to "data" subdirectories to README file.
  Affects: README

- Changed the way the private dictionary "private.dic" is enabled on Unix
  systems using "configure --with-private-tags". Now, DCM_DICT_DEFAULT_PATH
  is set accordingly instead of appending the file to the default "dicom.dic".
  Affects: config/configure
           config/configure.in
           config/Makefile.def.in
           dcmdata/libsrc/Makefile.in

- Moved dictionary files from "libsrc" to "data" subdirectory.
  Added:   dcmdata/data/dicom.dic
           dcmdata/data/private.dic
  Removed: dcmdata/libsrc/dicom.dic
           dcmdata/libsrc/private.dic
  Affects: dcmdata/Makefile.in
           dcmdata/data/CMakeLists.txt
           dcmdata/data/Makefile.in
           dcmdata/docs/datadict.txt
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.in

- Make sure the environment variable DCMDICTPATH is not evaluated when creating
  source code and header files from data dictionaries.
  Affects: dcmdata/libsrc/Makefile.in

- Changed formatting of comment header.
  Affects: dcmdata/libsrc/mkdeftag.cc
           dcmdata/libsrc/mkdictbi.cc

- Added type cast to integer variable in order to avoid compiler warning.
  Affects: dcmdata/libsrc/mkdeftag.cc

- Append "private.dic" to DCM_DICT_DEFAULT_PATH if private dictionary enabled.
  Affects: CMakeLists.txt
           dcmtk/config/include/dcmtk/config/cfwin32.h.in

**** Changes from 2009.01.13 (riesmeier)

- Changed minimum required CMake version from 2.4 to 2.4.8.
  Affects: CMakeLists.txt

- Slightly updated installation file for the changes of the current snapshot.
  Affects: INSTALL

**** Changes from 2009.01.12 (onken)

- Fixed iterators to also compile with STL classes being enabled.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/libsrc/dcpath.cc

**** Changes from 2009.01.08 (riesmeier)

- Changed value of DCMTK_PREFIX to $INSTALL_PREFIX as specified in CMake GUI.
  Affects: config/include/dcmtk/config/cfwin32.h.in

- Changed value of DCM_DICT_DEFAULT_PATH to $INSTALL_DATDIR\dicom.dic in order
  to avoid loading of unwanted/unexpected dictionary files. This also conforms
  more to the existing documentation.
  Affects: config/include/dcmtk/config/cfwin32.h.in

- Added (currently uncommented) values for DEFAULT_CONFIGURATION_DIR and
  DEFAULT_SUPPORT_DATA_DIR.
  Affects: config/include/dcmtk/config/cfwin32.h.in

- Added support for generating native Windows installation paths.
  Affects: CMakeLists.txt

**** Changes from 2009.01.07 (riesmeier)

- Avoid double output of resource identifier for forked children (Win32).
  Affects: dcmnet/apps/storescp.cc
           dcmwlm/apps/wlcefs.cc

- Avoid double output of "auto correction" notice for forked children (Win32).
  Affects: dcmwlm/libsrc/wlmactmg.cc

**** Changes from 2009.01.06 (riesmeier)

- Made command line option --quiet visible by default (not only in experimental
  mode).
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Added new command line options for changing the output format (tree structure
  vs. indented output).
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Reworked print() output format for option PF_showTreeStructure.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dctypes.cc

- Added note on default (indented) output format of dcmdump.
  Affects: dcmdata/docs/dump2dcm.man

**** Changes from 2009.01.05 (riesmeier)

- Fixed bug in DcmHashDictIterator::operator!=() introduced with last commit.
  Reverted to old implementation.
  Affects: dcmdata/include/dcmtk/dcmdata/dchashdi.h

- Added global flag that allows for reading incorrectly encoded DICOM datasets
  where particular data elements are encoded with a differing transfer syntax
  (Implicit VR Little endian instead of Explicit VR encoding as declared).
  Thanks to Jose Salavert Torres <<EMAIL>> for the sample file.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc

- Added command line options that allow for reading incorrectly encoded DICOM
  datasets where particular data elements are encoded with a differing transfer
  syntax (Implicit VR Little endian instead of Explicit VR encoding).
  Affects: dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmdump.man

**** Changes from 2008.12.19 (riesmeier)

- Fixed wrong string comparison which caused a compiler error on openSUSE 11.1.
  Thanks to Stephan Karacson <<EMAIL>> for the bug report.
  Affects: dcmsr/libsrc/dsrdoc.cc

- Fixed bug in DcmHashDictIterator::operator!=() - wrong comparison operand.
  Thanks to Niels Dekker <<EMAIL>> and Jeroen Eggermont
  for the bug report and suggested fix.
  Affects: dcmdata/include/dcmtk/dcmdata/dchashdi.h

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2008.12.12 (onken)

- Fixed doxgen documentation.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpath.h

- Fixed memory leak in path searching function.
  Affects: dcmdata/libsrc/dcpath.cc

- Moved path access functions to separate classes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.in
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/tests/tstpath.cc
  Added:   dcmdata/include/dcmtk/dcmdata/dcpath.h
           dcmdata/libsrc/dcpath.cc

**** Changes from 2008.12.11 (riesmeier)

- Enhanced method checkElementValue(), e.g. added support for type 1C elements.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2008.12.08 (riesmeier)

- Added number for new module "dcmrt", used for module-specific error codes.
  Affects: dcmdata/include/dcmtk/dcerror.h

**** Changes from 2008.12.05 (onken)

- Added and inserted new error code numbers for findOrCreatePath() functions.
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcerror.cc

- Splitted findOrCreatePath() function API for also offering a simple API
  for non-wildcard searches.
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h

- Changed test application to test splitted findOrCreatePath() API.
  Affects: dcmdata/tests/tstpath.cc

**** Changes from 2008.12.04 (onken)

- Changed findOrCreatePath() to also support wildcard as item numbers.
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h

- Extended application to also test new findOrCreatePath() wildcard features.
  Affects: dcmdata/tests/tstpath.cc

**** Changes from 2008.12.03 (riesmeier)

- Updated CMake project file for use with CMake 2.6 (uses CMAKE_POLICY).
  Affects: CMakeLists.txt

- Added check for Microsoft compiler; report a warning if other compiler used.
  Affects: CMakeLists.txt

- Added initial support for "Microsoft Visual Studio 9 2008" compiler.
  Affects: CMakeLists.txt

- Added compiler flag /EHsc for Microsoft Visual Studio 8 and 9 in order to
  avoid compiler warning on system header using CMake 2.6.
  Affects: CMakeLists.txt

- Added new non-public module "dcmwave".
  Affects: CMakeLists.txt

**** Changes from 2008.11.26 (riesmeier)

- Slightly changed behavior of newDicomElement(): return error code if new
  element could not be created (e.g. because memory is exhausted).
  Affects: dcmdata/libsrc/dcitem.cc

- Updated documentation of newDicomElement() in order to reflect the current
  implementation.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/include/dcmtk/dcmdata/dcitem.h

**** Changes from 2008.11.21 (onken)

- Changed implementation of findOrCreatePath() to make use of function
  newDicomElement() which also knows how to handle EVRs like ox correctly.
  Affects: dcmdata/libsrc/dcitem.cc
           dcmdata/tests/tstpath.cc

**** Changes from 2008.11.20 (riesmeier)

- Added new command line option --output-directory to specify the directory
  where received objects are stored.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/docs/movescu.man

- Moved command line option --output-directory to "output" section and made
  syntax description more consistent with other DCMTK tools.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

**** Changes from 2008.11.18 (riesmeier)

- Fixed issue with incorrectly encoded overlay planes (wrong values for
  OverlayBitsAllocated and OverlayBitPosition). Thanks to Mathieu Malaterre
  <<EMAIL>> for the report and faulty sample image.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/diovlay.h
           dcmimgle/include/dcmtk/dcmimgle/diovpln.h
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/diovlay.cc
           dcmimgle/libsrc/diovpln.cc

**** Changes from 2008.11.06 (riesmeier)

- Fixed inconsistent name of attribute (0040,B020) WaveformAnnotationSequence.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc
           dcmdata/libsrc/dicom.dic

**** Changes from 2008.11.03 (riesmeier)

- Fixed wrong check of odd/even length in method createValueFromTempFile().
  Affects: dcmdata/libsrc/dcelem.cc

- Added method createValueFromTempFile() - overrides method in DcmElement.
  Affects: dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/libsrc/dcpixel.cc

- Fixed typo.
  Affects: dcmdata/libsrc/dccodec.cc

- Made documentation of --compression-level more consistent with other options.
  Affects: dcmdata/docs/dcmconv.man
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man

- Added ZLIB related output options --write-xfer-deflated, --compression-level.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmsr/apps/xml2dsr.cc
           dcmsr/docs/xml2dsr.man

- Removed "option block" encapsulation from option --compression-level.
  Affects: dcmdata/apps/dcmconv.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmsr/apps/xml2dsr.cc

- Added experimental support for importing very large binary files (e.g. pixel
  data) using new createValueFromTempFile() method. Disabled by default.
  Affects: dcmdata/apps/dump2dcm.cc

**** Changes from 2008.11.03 (onken)

- Fixed typo. Thanks to Mathieu Malaterre <<EMAIL>> for
  the report.
  Affects: dcmjpeg/docs/dcmcjpeg.man

**** Changes from 2008.10.29 (riesmeier)

- Fixed minor inconsistencies.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/docs/img2dcm.man

**** Changes from 2008.10.28 (riesmeier)

- Output detailed error message in case of "premature end of stream".
  Affects: dcmdata/libsrc/dcelem.cc

**** Changes from 2008.10.24 (riesmeier)

- Made man pages more consistent with the command line tool.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/docs/dcm2pnm.man
           dcmjpeg/docs/dcmj2pnm.man

**** Changes from 2008.10.23 (riesmeier)

- Fixed minor formatting issues.
  Affects: dcmimage/docs/dcm2pnm.man

**** Changes from 2008.10.15 (onken)

- Added test program for testing DcmItem's and DcmSequenceOfItem's path access
  features.
  Affects: dcmtk/dcmdata/tests/Makefile.dep
           dcmtk/dcmdata/tests/Makefile.in
           dcmtk/dcmdata/tests/tstpath.cc

- Fixed incorrect behaviour in findOrCreatePath() if sequence is the last
  attribute to be inserted.
  Affects: dcmtk/dcmdata/libsrc/dcitem.cc

- Added findOrCreatePath() functions which allow for finding or creating a
  hierarchy of sequences, items and attributes according to a given "path"
  string.
  Affects: dcmtk/dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmtk/dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmtk/dcmdata/libsrc/dcitem.cc
           dcmtk/dcmdata/libsrc/dcsequen.cc

**** Changes from 2008.10.13 (onken)

- Added check for libsndfile to windows build configuration header.
  Affects: config/include/dcmtk/config/cfwin32.h.in

**** Changes from 2008.10.09 (riesmeier)

- Made Makefile more consistent with other DCMTK modules.
  Affects: dcmdata/libsrc/Makefile.in
           dcmimgle/libsrc/Makefile.in

**** Changes from 2008.10.09 (onken)

- Added check for libsndfile needed by dcmwave for building with configure
  and CMake.
  Affects: CMakeLists.txt
           config/configure
           config/configure.in
           config/Makefile.def.in
           config/include/dcmtk/config/cfunix.h.in

**** Changes from 2008.10.07 (onken)

- Fixed possible memory leak in user identity classes and added code for
  accessing user identity from the server's side. Thanks to "Pim"
  <<EMAIL>> for the contribution.
  Affects: dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/dcuserid.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/dcuserid.cc

**** Changes from 2008.09.30 (onken)

- Added dcmppscu to list of private modules.
  Affects: CMakeLists.txt

**** Changes from 2008.09.26 (riesmeier)

- Changed the way exclusive command line options are checked.
  Affects: dcmwlm/apps/wlcefs.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2008.09.25 (riesmeier)

- Print expanded command line arguments to stderr and not to stdout.
  Affects: ofstd/include/dcmtk/ofstd/ofconapp.h
           ofstd/libsrc/ofconapp.cc

- Added method for printing the resource identifier of an application.
  Affects: dcmdata/apps/dcmdump.cc
           ofstd/include/dcmtk/ofstd/ofconapp.h
           ofstd/libsrc/ofconapp.cc

- Added support for printing the expanded command line arguments.
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dcmodify.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/img2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmdata/tests/tstpread.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/apps/dcmdspfn.cc
           dcmimgle/docs/dcmdspfn.man
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/apps/dcmsign.cc
           dcmsign/docs/dcmsign.man
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

- Always output the resource identifier of the command line tool in debug mode.
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dcmodify.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/img2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/tests/tstpread.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimgle/apps/dcmdspfn.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmsign/apps/dcmsign.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc

- Updated comment header (list of supported profiles).
  Affects: dcmjpeg/apps/dcmmkdir.cc

- Moved output of resource identifier in order to avoid printing the same
  information twice.
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dcmodify.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmscale.cc
           dcmimgle/apps/dcmdspfn.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc

- Moved checking on presence of the data dictionary.
  Affects: dcmdata/apps/img2dcm.cc

**** Changes from 2008.09.24 (riesmeier)

- Added new command line option --update-meta-info that allows for updating
  particular information in the file meta-header (e.g. SOP instance UID).
  Affects: dcmdata/apps/xml2dcm.cc
           dcmdata/docs/xml2dcm.man

- Added support for printing the expanded command line arguments to standard
  output stream.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man
           ofstd/include/dcmtk/ofstd/ofconapp.h
           ofstd/libsrc/ofconapp.cc

- Fixed typo in output of getStatusString().
  Affects: ofstd/libsrc/ofcmdln.cc

- Always output the resource identifier of the command line tool in debug mode.
  Affects: dcmdata/apps/dcmdump.cc

- Fixed typo in comment.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcitem.cc

- Updated man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2008.09.08 (riesmeier)

- Fixed typo in OFCondition text string.
  Affects: dcmnet/include/dcmtk/dcmnet/cond.h
           dcmnet/libsrc/cond.cc

- Added missing newline in debug output.
  Affects: dcmnet/libsrc/dulfsm.cc

**** Changes from 2008.08.28 (onken)

- Introduced deleteFile() method.
  Affects: ofstd/libsrc/ofstd.cc
           ofstd/include/dcmtk/ofstd/ofstd.h

- Added #ifdef to combineDirAndFilename() to only check for absolute paths with
  drive letter when on Windows.
  Affects: ofstd/libsrc/ofstd.cc

- Modified combineDirAndFilename() to also recognize Windows absolute paths
  starting with a drive letter.
  Affects: ofstd/libsrc/ofstd.cc

**** Changes from 2008.08.15 (eichelberg)

- Decoder now gracefully handles the case of faulty images where value of
  NumberOfFrames is larger than the number of compressed fragments, if and only
  if there is just a single fragment per frame.
  Affects: dcmdata/libsrc/dcrleccd.cc
           dcmjpeg/libsrc/djcodecd.cc

- Added a few Philips private tags
  Affects: dcmdata/libsrc/private.dic
           dcmdata/libsrc/dcdictzz.cc

- Under certain conditions (odd length compressed pixel data fragments)
  class DcmOtherByteOtherWord needs to load the attribute value into main
  memory during a write() operation, in order to add a pad byte. A new flag
  compactAfterTransfer now makes sure that the memory is released once the
  write operation has finished, so that only a single fragment at a time
  needs to fully reside in memory.
  Affects: dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/libsrc/dcvrobow.cc

- Added type cast to fix a warning
  Affects: dcmdata/include/dcmtk/dcmdata/dcdict.h

**** Changes from 2008.07.17 (riesmeier)

- Fixed issue with NumberOfFrames element in writeImageToDataset().
  Affects: dcmimgle/libsrc/dimoimg.cc

**** Changes from 2008.07.17 (riesmeier)

- Replaced wrong use of assignment operator by new copyFrom() method.
  Affects: dcmsr/libsrc/dsrtypes.cc

- Replaced call to getSequenceFromDataset() by getElementFromDataset().
  Affects: dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcomvl.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrsoprf.cc

- Removed getSequenceFromDataset() function.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2008.07.17 (onken)

- Implemented copyFrom() method for complete DcmObject class hierarchy, which
  permits setting an instance's value from an existing object. Implemented
  assignment operator where necessary.
  Affects:  dcmdata/libsrc/dcbytstr.cc
            dcmdata/libsrc/dcchrstr.cc
            dcmdata/libsrc/dcdatset.cc
            dcmdata/libsrc/dcdictbi.cc
            dcmdata/libsrc/dcdirrec.cc
            dcmdata/libsrc/dcelem.cc
            dcmdata/libsrc/dcfilefo.cc
            dcmdata/libsrc/dcitem.cc
            dcmdata/libsrc/dcmetinf.cc
            dcmdata/libsrc/dcpixel.cc
            dcmdata/libsrc/dcpixseq.cc
            dcmdata/libsrc/dcpxitem.cc
            dcmdata/libsrc/dcsequen.cc
            dcmdata/libsrc/dcvrae.cc
            dcmdata/libsrc/dcvras.cc
            dcmdata/libsrc/dcvrat.cc
            dcmdata/libsrc/dcvrcs.cc
            dcmdata/libsrc/dcvrda.cc
            dcmdata/libsrc/dcvrds.cc
            dcmdata/libsrc/dcvrdt.cc
            dcmdata/libsrc/dcvrfd.cc
            dcmdata/libsrc/dcvrfl.cc
            dcmdata/libsrc/dcvris.cc
            dcmdata/libsrc/dcvrlo.cc
            dcmdata/libsrc/dcvrlt.cc
            dcmdata/libsrc/dcvrobow.cc
            dcmdata/libsrc/dcvrof.cc
            dcmdata/libsrc/dcvrpn.cc
            dcmdata/libsrc/dcvrpobw.cc
            dcmdata/libsrc/dcvrsh.cc
            dcmdata/libsrc/dcvrsl.cc
            dcmdata/libsrc/dcvrss.cc
            dcmdata/libsrc/dcvrst.cc
            dcmdata/libsrc/dcvrtm.cc
            dcmdata/libsrc/dcvrui.cc
            dcmdata/libsrc/dcvrul.cc
            dcmdata/libsrc/dcvrulup.cc
            dcmdata/libsrc/dcvrus.cc
            dcmdata/libsrc/dcvrut.cc
            dcmdata/include/dcmtk/dcbytstr.h
            dcmdata/include/dcmtk/dcchrstr.h
            dcmdata/include/dcmtk/dcdatset.h
            dcmdata/include/dcmtk/dcdirrec.h
            dcmdata/include/dcmtk/dcelem.h
            dcmdata/include/dcmtk/dcfilefo.h
            dcmdata/include/dcmtk/dcitem.h
            dcmdata/include/dcmtk/dcmetinf.h
            dcmdata/include/dcmtk/dcobject.h
            dcmdata/include/dcmtk/dcovlay.h
            dcmdata/include/dcmtk/dcpixel.h
            dcmdata/include/dcmtk/dcpixseq.h
            dcmdata/include/dcmtk/dcpxitem.h
            dcmdata/include/dcmtk/dcsequen.h
            dcmdata/include/dcmtk/dcvrae.h
            dcmdata/include/dcmtk/dcvras.h
            dcmdata/include/dcmtk/dcvrat.h
            dcmdata/include/dcmtk/dcvrcs.h
            dcmdata/include/dcmtk/dcvrda.h
            dcmdata/include/dcmtk/dcvrds.h
            dcmdata/include/dcmtk/dcvrdt.h
            dcmdata/include/dcmtk/dcvrfd.h
            dcmdata/include/dcmtk/dcvrfl.h
            dcmdata/include/dcmtk/dcvris.h
            dcmdata/include/dcmtk/dcvrlo.h
            dcmdata/include/dcmtk/dcvrlt.h
            dcmdata/include/dcmtk/dcvrobow.h
            dcmdata/include/dcmtk/dcvrof.h
            dcmdata/include/dcmtk/dcvrpn.h
            dcmdata/include/dcmtk/dcvrpobw.h
            dcmdata/include/dcmtk/dcvrsh.h
            dcmdata/include/dcmtk/dcvrsl.h
            dcmdata/include/dcmtk/dcvrss.h
            dcmdata/include/dcmtk/dcvrst.h
            dcmdata/include/dcmtk/dcvrtm.h
            dcmdata/include/dcmtk/dcvrui.h
            dcmdata/include/dcmtk/dcvrul.h
            dcmdata/include/dcmtk/dcvrulup.h
            dcmdata/include/dcmtk/dcvrus.h
            dcmdata/include/dcmtk/dcvrut.h

- Added error constant for upcoming dcmwave module.
  Affects: dcmdata/include/dcmtk/dcerror.h

- Updated copyFrom() documentation.
  Affects:  dcmdata/include/dcmtk/dcbytstr.h
            dcmdata/include/dcmtk/dcchrstr.h
            dcmdata/include/dcmtk/dcdatset.h
            dcmdata/include/dcmtk/dcdirrec.h
            dcmdata/include/dcmtk/dcelem.h
            dcmdata/include/dcmtk/dcfilefo.h
            dcmdata/include/dcmtk/dcitem.h
            dcmdata/include/dcmtk/dcmetinf.h
            dcmdata/include/dcmtk/dcobject.h
            dcmdata/include/dcmtk/dcovlay.h
            dcmdata/include/dcmtk/dcpixel.h
            dcmdata/include/dcmtk/dcpixseq.h
            dcmdata/include/dcmtk/dcpxitem.h
            dcmdata/include/dcmtk/dcsequen.h
            dcmdata/include/dcmtk/dcvrae.h
            dcmdata/include/dcmtk/dcvras.h
            dcmdata/include/dcmtk/dcvrat.h
            dcmdata/include/dcmtk/dcvrcs.h
            dcmdata/include/dcmtk/dcvrda.h
            dcmdata/include/dcmtk/dcvrds.h
            dcmdata/include/dcmtk/dcvrdt.h
            dcmdata/include/dcmtk/dcvrfd.h
            dcmdata/include/dcmtk/dcvrfl.h
            dcmdata/include/dcmtk/dcvris.h
            dcmdata/include/dcmtk/dcvrlo.h
            dcmdata/include/dcmtk/dcvrlt.h
            dcmdata/include/dcmtk/dcvrobow.h
            dcmdata/include/dcmtk/dcvrof.h
            dcmdata/include/dcmtk/dcvrpn.h
            dcmdata/include/dcmtk/dcvrpobw.h
            dcmdata/include/dcmtk/dcvrsh.h
            dcmdata/include/dcmtk/dcvrsl.h
            dcmdata/include/dcmtk/dcvrss.h
            dcmdata/include/dcmtk/dcvrst.h
            dcmdata/include/dcmtk/dcvrtm.h
            dcmdata/include/dcmtk/dcvrui.h
            dcmdata/include/dcmtk/dcvrul.h
            dcmdata/include/dcmtk/dcvrulup.h
            dcmdata/include/dcmtk/dcvrus.h
            dcmdata/include/dcmtk/dcvrut.h

**** Changes from 2008.07.15 (riesmeier)

- Removed unused function OFStandard::stringMatchesCharacterSet().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

**** Changes from 2008.07.11 (riesmeier)

- Fixed typo in API documentation.
  Affects: dcmdata/apps/mdfdsman.h
           dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h

**** Changes from 2008.07.10 (riesmeier)

- Fixed layout of the usage output (--help).
  Affects: dcmnet/apps/findscu.cc

**** Changes from 2008.06.27 (riesmeier)

- Fixed condition that could lead to a wrong error message in method
  checkElementValue().
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrtypes.cc

**** Changes from 2008.06.23 (riesmeier)

- Added check on value representation of data elements copied from the
  referenced DICOM file to the DICOMDIR (compare VR with data dictionary).
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Fixed inconsistencies in Doxygen API documentation.
  Affects: dcmdata/apps/mdfconen.h
           dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dcistrmb.h
           dcmdata/include/dcmtk/dcmdata/dcistrmz.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/include/dcmtk/dcmdata/dcostrmz.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcrlecp.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvr.h
           dcmdata/include/dcmtk/dcmdata/dcxfer.h

**** Changes from 2008.06.20 (riesmeier)

- Added check for retired SOP Class 'X-Ray Angiographic Bi-Plane Image Storage'
  since the Modality LUT should not be applied to the pixel data in this case.
  Affects: dcmimgle/libsrc/dimomod.cc

**** Changes from 2008.06.03 (eichelberg)

- DcmDirectoryRecord::getFileOffset() is now const and public.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcdirrec.cc

**** Changes from 2008.05.30 (eichelberg)

- Increased output precision to 8 (FLT_DIG+2) when converting an FL element to string.
  Thanks to Niels Dekker <<EMAIL>> for the suggestion.
  Affects: dcmdata/libsrc/dcvrfl.cc

**** Changes from 2008.05.29 (eichelberg)

- Experimental implementation of decodeFrame method for
  JPEG decoder added.
  Affects: dcmjpeg/libsrc/djcodecd.cc

- Implemented new method DcmPixelData::getUncompressedFrame
  that permits frame-wise access to compressed and uncompressed
  objects without ever loading the complete object into main memory.
  For this new method to work with compressed images, all classes derived from
  DcmCodec need to implement a new method decodeFrame(). For now, only
  dummy implementations returning an error code have been defined.
  Affects: dcmdata/include/dcmtk/dcmdata/dccodec.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcrleccd.h
           dcmdata/include/dcmtk/dcmdata/dcrlecce.h
           dcmdata/libsrc/dccodec.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcrleccd.cc
           dcmdata/libsrc/dcrlecce.cc
           dcmjpeg/include/dcmtk/dcmjpeg/djcodecd.h
           dcmjpeg/include/dcmtk/dcmjpeg/djcodece.h
           dcmjpeg/libsrc/djcodece.cc

- Implemented new method createValueFromTempFile that allows the content of
  a temporary file to be set as the new value of a DICOM element.
  Also added a new method compact() that removes the value field if the
  value field can still be reconstructed from file. For large attribute
  value the file reference is now kept in memory even when the value has
  been loaded once. Finally, added new helper method getUncompressedFrameSize
  that computes the size of an uncompressed frame for a given dataset.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/libsrc/dcelem.cc

- Implemented new classes DcmTempFileHandler and DcmInputTempFileStreamFactory
  that perform thread-safe reference counted life cycle management of a
  temporary file and are needed for DcmElement temporary file extensions to come.
  Affects: dcmdata/include/dcmtk/dcmdata/dcistrmf.h
           dcmdata/libsrc/dcistrmf.cc

- Fixed compile error on Cygwin where no wide-char FILE functions are available
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Updated Makefile dependencies
  Affects: */*/Makefile.dep

**** Changes from 2008.05.21 (riesmeier)

- Fixed bug in c't scaling algorithm (expandPixel) which could cause a crash
  (possible integer underflow/overflow).
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Added copyright note on the "pbmplus" image scaling algorithm.
  Affects: COPYRIGHT

- Added new constructor and new setDateTime() method.
  Affects: ofstd/include/dcmtk/ofstd/ofdatime.h
           ofstd/libsrc/ofdatime.cc

- Reimplemented tests for OFDate, OFTime and OFDateTime classes.
  Affects: ofstd/tests/tofdatim.cc

**** Changes from 2008.05.20 (riesmeier)

- Added new bilinear and bicubic scaling algorithms for image magnification.
  Thanks to Eduard Stanescu <<EMAIL>> for his contribution.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmscale.man
           dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/dimo1img.h
           dcmimgle/include/dcmtk/dcmimgle/dimo2img.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmjpeg/docs/dcmj2pnm.man

- Added new method that clears the destination image (set pixel values to 0).
  Affects: dcmimgle/include/dcmtk/dcmimgle/ditranst.h

- Now the c't scaling algorithm is used as a fallback if the preferred
  algorithm with interpolation is not applicable.
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Fixed bug in c't scaling algorithm (reducePixel) which could cause a crash.
  Thanks to Alexander Fedotov <<EMAIL>> for the bug report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Fixed issue with wrong image aspect ratio in combined scaling/clipping mode.
  Affects: dcmimgle/libsrc/dcmimage.cc

- Allow width and height of the clipping area to be 0 (compute automatically).
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmscale.cc

- Enhanced error message when scaling process or grayscale conversion fail.
  Affects: dcmimage/apps/dcm2pnm.cc

- Use the pbmplus scaling algorithm as the second best fallback if the c't
  algorithm cannot be used (e.g. up and down-scaling on different axes).
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Fixed issue with signed pixel data in bicubic interpolation algorithm.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimosct.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/libsrc/dimoimg.cc

- Replaced macro call by inline function (approx. same performance).
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Modified code to avoid warning message on MSVC compiler (implicit type
  conversion).
  Affects: dcmsr/apps/dsr2html.cc

- Fixed small issue in bicubic image scaling algorithm (in clipping mode).
  Affects: dcmimgle/include/dcmtk/dcmimgle/discalet.h

- Added more checks on parameters for combined scaling and clipping.
  Affects: dcmimgle/libsrc/dcmimage.cc

**** Changes from 2008.05.19 (riesmeier)

- Added new command line option that enables reading of SR documents with
  unknown/missing relationship type(s).
  Affects: dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsrdump.man

- Added new flag that enables reading of SR documents with unknown/missing
  relationship type(s).
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrtypes.cc

- Changed parameters of checkByReferenceRelationships() method.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrtypes.cc

- Reset flag for all content items indicating whether they are target of a
  by-reference relationship (required for an reproducible behavior).
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/libsrc/dsrdoctr.cc

- Fixed issue with wrong reference to a content item in an error message.
  Affects: dcmsr/libsrc/dsrdoctn.cc

- Fixed typo.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoctn.h

**** Changes from 2008.05.19 (onken)

- Corrected img2dcm documentation (set Secondary Capture as Default SOP Class
  and removed "SEE ALSO" section).
  Affects: dcmdata/docs/img2dcm.man

**** Changes from 2008.05.13 (riesmeier)

- Fixed issue with multi-frame color images: writeImageToDataset() used wrong
  format for color-by-plane output.
  Thanks to Michael Doppler <<EMAIL>> for the original report.
  Affects: dcmimage/include/dcmtk/dcmimage/dicopx.h
           dcmimage/include/dcmtk/dcmimage/dicopxt.h
           dcmimage/libsrc/dicoimg.cc

- Added new parameter to writeImageToDataset() in order to affect the planar
  configuration of the output image/dataset. Changed behaviour: By default,
  the output now uses the same planar configuration as the "original" image
  (previously: always color-by-plane).
  Affects: dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimage/libsrc/dicoimg.cc
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/libsrc/dimoimg.cc

**** Changes from 2008.04.30 (riesmeier)

- Moved package information like version number and release date to a central
  configuration file (one for GNU autoconf and one for CMake systems).
  Affects: CMakeLists.txt
           config/configure
           config/configure.in
           config/Makefile.def.in
           config/include/dcmtk/config/CMakeLists.txt
           config/include/dcmtk/config/cfunix.h.in
           dcmdata/include/dcmtk/dcmdata/dcuid.h
           doxygen/CMakeLists.txt
           doxygen/Makefile.in
           doxygen/htmldocs.cfg
           doxygen/manpages.cfg
  Added:   config/include/dcmtk/config/cfwin32.h.in
  Removed: config/include/dcmtk/config/cfwin32.h

- Added support for new compiler definition "DCMTK_BUILD_DATE" that can be used
  to distinguish binaries of the current development version from the official
  release.
  Affects: CMakeLists.txt
           config/configure.in
           config/Makefile.def.in
           dcmdata/include/dcmtk/dcmdata/dcuid.h

**** Changes from 2008.04.30 (eichelberg)

- DJEncoderRegistration::registerCodecs now by default enables the
  true lossless codec instead of pseudo-lossless.
  Affects: dcmjpeg/include/dcmtk/dcmjpeg/djencode.h

- Fixed compile errors due to changes in attribute tag names
  Affects: dcmdata/apps/pdf2dcm.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmnet/libsrc/dimcmd.cc
           dcmnet/libsrc/dimse.cc
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/libsrc/dviface.cc
           dcmpstat/libsrc/dvpsabl.cc
           dcmpstat/libsrc/dvpsall.cc
           dcmpstat/libsrc/dvpsgl.cc
           dcmpstat/libsrc/dvpsib.cc
           dcmpstat/libsrc/dvpsibl.cc
           dcmpstat/libsrc/dvpspll.cc
           dcmpstat/libsrc/dvpssp.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmsr/libsrc/dsrtcodt.cc
           dcmwlm/libsrc/wlds.cc
           dcmwlm/wwwapps/readwlst.cc
           dcmwlm/wwwapps/writwlst.cc

- Rebuilt data dictionary based on 2008 DICOM edition
  Affects: dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc
           dcmdata/libsrc/dicom.dic

- Added private tags for creator 'PHILIPS IMAGING DD 001'
  Thanks to Mathieu Malaterre <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/private.dic

- Fixed memory leak in DIMSE_receiveDataSetInMemory when parameter dataObject
  was passed as NULL and an error condition occured.
  Thanks to Michael Doppler <<EMAIL>> for the report and fix.
  Affects: dcmnet/libsrc/dimse.cc

- Correctly initializing SO_LINGER timeout flag for socket closing, which was
  in undefined state in requestAssociationTCP
  Thanks to Michael Doppler <<EMAIL>> for the report and fix.
  Affects: dcmnet/libsrc/dulfsm.cc

**** Changes from 2008.04.29 (riesmeier)

- Harmonized capitalization of warning messages.
  Affects: dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdoc.cc

**** Changes from 2008.04.28 (riesmeier)

- Adapted OFStandard::checkForMarkupConversion() to the new behavior of
  parameter "convertNonASCII" of OFStandard::convertToMarkupString().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Fixed API documentation of OFStandard::convertToMarkupString().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h

**** Changes from 2008.04.28 (eichelberg)

- Implemented new command line option --quote-nonascii in dcmdump that quotes
  non-ASCII and control characters as XML markup.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- New flag DCMTypes::PF_convertToMarkup causes DcmObject::print to
  quote non-ASCII string content as XML markup.
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dctypes.cc

- OFStandard::convertToMarkupString now also converts control characters
  less than 32 and 127 (which does not exist in ASCII or ISO 8859)
  when run in "convert non-ASCII" or MM_HTML32 markup mode.
  Affects: ofstd/libsrc/ofstd.cc

**** Changes from 2008.04.25 (riesmeier)

- Include header file for XML Schemas only if Schema support is enabled.
  Affects: dcmsr/libsrc/dsrxmld.cc

**** Changes from 2008.04.23 (eichelberg)

- Added new method DcmXfer::isRetired that returns true for
  retired transfer syntaxes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcxfer.h
           dcmdata/libsrc/dcxfer.cc

**** Changes from 2008.04.18 (riesmeier)

- Added support for current directory (".") to searchDirectoryRecursively().
  Affects: ofstd/libsrc/ofstd.cc

- Added further tests for combineDirAndFilename().
  Affects: ofstd/tests/tofstd.cc

**** Changes from 2008.04.18 (onken)

- Renamed commandline option from -pw to --pass. Minor commandline option
  formatting changes.
  Affects: dcmnet/apps/storescu.cc
           dcmnet/docs/storescu.man

- Fixed broken Makefile dependencies caused by new User Identity code.
  Affects: dcmpstat/tests/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep

- Fixed missing fclose() in case fseek() fails.
  Affects: dcmnet/apps/storescu.cc

**** Changes from 2008.04.17 (onken)

- Reworked and extended User Identity Negotiation code.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/cond.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/CMakeLists.txt
           dcmnet/libsrc/Makefile.dep
           dcmnet/libsrc/Makefile.in
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/cond.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulparse.cc
           dcmnet/libsrc/dulstruc.h
  Added:   dcmnet/include/dcmtk/dcuserid.h
           dcmnet/libsrc/dcuserid.cc
  Removed: dcmnet/include/dcmtk/dcextusr.h
           dcmnet/libsrc/dcextusr.cc

- Added command line options for User Identity Negotiation.
  Affects: dcmnet/apps/storescu.cc
           dcmnet/docs/storescu.man

- Added check that prevents dcmnet from sending requests containing user
  information items that (in total) exceed the 65535 bytes limit required
  by the DICOM standard (2 byte length field).
  Affects: dcmnet/libsrc/dulconst.cc

**** Changes from 2008.04.17 (eichelberg)

- Method DcmObject::getLengthField now public, needed by dcmcheck.
  Affects: dcmdata/include/dcmtk/dcmdata/dcobject.h

**** Changes from 2008.04.16 (riesmeier)

- Added support for reverse search direction (left to right) to findOption().
  Affects: ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc

- Changed order of command line evaluation for option --search (now: from left
  to right).
  Affects: dcmdata/apps/dcmdump.cc

**** Changes from 2008.04.16 (eichelberg)

- class OFConfigFile now supports an ordered mode where multiple
  configuration files can be loaded and can replace entries of other.
  Also added function to print content of configuration in reloadable format.
  Affects: ofstd/include/dcmtk/ofstd/ofconfig.h
           ofstd/libsrc/ofconfig.cc

**** Changes from 2008.04.15 (eichelberg)

- Fixed endless recursion bug in the index file handling code when
  the index file does not exist.
  Affects: dcmqrdb/include/dcmtk/dcmqrdb/dcmqrdbi.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqridx.h
           dcmqrdb/libsrc/dcmqrdbi.cc

- Class OFConfigFile now supports flexible tree depths and configurable
  comment characters and can, therefore, fully replace the equivalent
  code in module dcmprint.
  Affects: ofstd/include/dcmtk/ofstd/ofconfig.h
           ofstd/libsrc/ofconfig.cc

**** Changes from 2008.04.09 (riesmeier)

- Fixed wrong use of variable in "for" loop which might cause a crash.
  Thanks to Michael Doppler <<EMAIL>> for the report and fix.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h

**** Changes from 2008.03.26 (riesmeier)

- Slightly revised documentation.
  Affects: dcmdata/docs/dcmodify.man

- Fixed various layout and formatting issues.
  Affects: dcmdata/apps/mdfconen.cc
           dcmdata/docs/dcmodify.man

**** Changes from 2008.03.17 (onken)

- Corrected "or" to "||".
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2008.03.11 (riesmeier)

- Fixed wrong comments.
  Affects: dcmsr/libsrc/dsrdoc.cc

- Fixed wrong CVS log entry.
  Affects: dcmsr/libsrc/dsrxrdcc.cc

- Fixed small formatting issue.
  Affects: dcmdata/docs/img2dcm.man

- Moved warning message on missing quotation mark when reading command files.
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2008.03.10 (onken)

- Changed OFListIterator to OFListConstIterator in order to compile when
  HAVE_STL is defined. Thanks to Markus Mertens <<EMAIL>> for
  the report.
  Affects: dcmdata/libi2d/i2djpgs.cc

**** Changes from 2008.03.10 (riesmeier)

- Added "ofstream.h" include in order to compile when HAVE_STL is defined.
  Thanks to Markus Mertens <<EMAIL>> for the report.
  Affects: ofstd/libsrc/ofstd.cc

**** Changes from 2008.03.03 (riesmeier)

- Enhanced support for quotation marks in command files.
  Affects: ofstd/libsrc/ofcmdln.cc

- Updated documentation on command files. Updated copyright note.
  Affects: dcmdata/docs/dcm2pdf.man
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmftest.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/img2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/docs/dcod2lum.man
           dcmimgle/docs/dconvlum.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/docs/dcmsign.man
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmwlm/docs/wlmscpfs.man

- Added up-to-date man pages.
  Affects: doxygen/manpages/man1/*

**** Changes from 2008.02.27 (riesmeier)

- Check HAVE_CHARP_STRERROR_R in order to use the correct version of
  strerror_r().
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2008.02.26 (riesmeier)

- Added new command line option that disables the mapping of well-known UID
  numbers to their associated names (e.g. transfer syntax or SOP class).
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Added new print flag that disables the mapping of well-known UID numbers to
  their associated names (e.g. transfer syntax or SOP class).
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dctypes.cc
           dcmdata/libsrc/dcvrui.cc

**** Changes from 2008.02.08 (eichelberg)

- Removed "GenericGroupLengthToEnd" from the built-in skeleton dictionary.
  Affects: dcmdata/libsrc/dcdict.cc

**** Changes from 2008.02.07 (eichelberg)

- Class OFFile now makes use of HAVE_CHARP_STRERROR_R to use the
  correct version of strerror_r.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Added configure test AC_CHECK_CHARP_STRERROR_R that checks whether strerror_r
  is the GNU version or the X/OPEN version.
  Affects: config/aclocal.m4
           config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h

**** Changes from 2008.02.06 (riesmeier)

- Added check of the return value of DcmElement::getXXX() methods.
  Affects: dcmimgle/libsrc/didocu.cc

**** Changes from 2008.02.04 (onken)

- Added missing VR lookup for function findTagFromName().
  Affects: dcmdata/libsrc/dctag.cc

**** Changes from 2008.01.23 (riesmeier)

- Restructured code in order to avoid empty output file in case the input file
  could not be read.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmsr/apps/dsr2xml.cc

**** Changes from 2008.01.16 (onken)

- Fixed some empty or doubled log messages in libi2d files.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/include/dcmtk/dcmdata/libi2d/i2d.h
           dcmdata/include/dcmtk/dcmdata/libi2d/i2doutpl.h
           dcmdata/libi2d/i2d.cc
           dcmdata/libi2d/i2djpgs.cc
           dcmdata/libi2d/i2dplnsc.cc
           dcmdata/libi2d/i2dplsc.cc
           dcmdata/libi2d/i2dplvlp.cc

- Fixed installation of doxygen documentation under Windows.
  Affects:  doxygen/CMakeLists.txt

- Moved library "i2dlib" from /dcmdata/libsrc/i2dlib to /dcmdata/libi2d
  and changed library name to "libi2d".
  Removed:  dcmdata/libsrc/i2dlib/CMakeLists.txt
            dcmdata/libsrc/i2dlib/Makefile.dep
            dcmdata/libsrc/i2dlib/Makefile.in
            dcmdata/libsrc/i2dlib/i2d.cc
            dcmdata/libsrc/i2dlib/i2djpgs.cc
            dcmdata/libsrc/i2dlib/i2dplnsc.cc
            dcmdata/libsrc/i2dlib/i2dplsc.cc
            dcmdata/libsrc/i2dlib/i2dplvlp.cc
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2d.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dimgs.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2djpgs.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2doutpl.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplsc.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplnsc.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplvlp.h
  Added:    dcmdata/libi2d/CMakeLists.txt
            dcmdata/libi2d/i2dlib/Makefile.dep
            dcmdata/libi2d/i2dlib/Makefile.in
            dcmdata/libi2d/i2dlib/i2d.cc
            dcmdata/libi2d/i2dlib/i2djpgs.cc
            dcmdata/libi2d/i2dlib/i2dplnsc.cc
            dcmdata/libi2d/i2dlib/i2dplsc.cc
            dcmdata/libi2d/i2dlib/i2dplvlp.cc
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2d.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dimgs.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2djpgs.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2doutpl.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplsc.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplnsc.h
            dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplvlp.h
  Affects:  dcmdata/Makefile.in
            dcmdata/apps/CMakeLists.txt
            dcmdata/apps/img2dcm.cc
            dcmdata/apps/Makefile.dep
            dcmdata/apps/Makefile.in
            dcmdata/include/Makefile.in
            dcmdata/include/dcmtk/dcmdata/CMakeLists.txt
            dcmdata/libsrc/Makefile.in
            dcmdata/libsrc/CMakeLists.txt

**** Changes from 2008.01.16 (riesmeier)

- Fixed issue with installation of HTML documentation.
  Affects: doxygen/CMakeLists.txt

**** Changes from 2008.01.16 (eichelberg)

- Minor configure update that removes certain compile warnings on Debian
  Affects: config/aclocal.m4
           config/configure

**** Changes from 2008.01.14 (riesmeier)

- Fixed minor inconsistencies.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/docs/dcmdump.man
           dcmdata/docs/img2dcm.man

**** Changes from 2008.01.11 (onken)

- Added various options to i2dlib. Changed logging to use a configurable
  logstream. Added output plugin for the new Multiframe Secondary Capture SOP
  Classes. Added mode for JPEG plugin to copy existing APPn markers (except
  JFIF). Changed img2dcm default behaviour to invent type1/type2 attributes (no
  need for templates any more). Added some bug fixes.
  Added:   dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplnsc.h
           dcmdata/libsrc/i2dlib/i2dplnsc.cc
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/docs/img2dcm.man
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2d.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2doutpl.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplsc.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplvlp.h
           dcmdata/libsrc/i2dlib/CMakeLists.txt
           dcmdata/libsrc/i2dlib/Makefile.in
           dcmdata/libsrc/i2dlib/i2d.cc
           dcmdata/libsrc/i2dlib/i2djpgs.cc
           dcmdata/libsrc/i2dlib/i2dplsc.cc
           dcmdata/libsrc/i2dlib/i2dplvlp.cc

**** Changes from 2008.01.08 (riesmeier)

- Fixed reference to wrong SR IOD in API documentation.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrxrdcc.h

- Fixed minor inconsistencies with part 6 of the DICOM standard.
  Thanks to Mathieu Malaterre <<EMAIL>> for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdeftag.h
           dcmdata/libsrc/dcdictzz.cc
           dcmdata/libsrc/dicom.dic

**** Changes from 2007.12.19 (riesmeier)

- Updated Doxygen configuration files to version 1.5.1.
  Affects: doxygen/htmldocs.cfg
           doxygen/manpages.cfg

- Updated GNU Autoconf to version 2.61.
  Affects: config/config.guess
           config/config.sub
           config/configure
           config/confmod
           config/install-sh

**** Changes from 2007.12.14 (riesmeier)

- Added reference to sample LUT in text format.
  Thanks to Gerold Gruber <<EMAIL>> for the hint.
  Affects: dcmpstat/docs/dcmmklut.man

**** Changes from 2007.12.11 (riesmeier)

- Fixed remaining inconsistencies regarding the documentation of the
  installation directories (new scheme).
  Affects: INSTALL
           config/docs/envvars.txt

**** Changes from 2007.12.11 (onken)

- Corrected some putAndInsert functions that returned wrong error code
  (EC_MemoryExhausted) in some cases.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2007.12.10 (riesmeier)

- Fixed compilation problem with gcc 4.1.2 on Linux x86_64.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimocpt.h

**** Changes from 2007.12.04 (riesmeier)

- Fixed small inconsistency in description of command line parameters.
  Affects: dcmdata/docs/dcmgpdir.man
           dcmjpeg/docs/dcmmkdir.man

**** Changes from 2007.11.30 (riesmeier)

- Updated relationship content constraints according to CP 767.
  Affects: dcmsr/libsrc/dsrchecc.cc
           dcmsr/libsrc/dsrmamcc.cc

**** Changes from 2007.11.29 (eichelberg)

- Write methods now handle large raw data elements (such as pixel data)
  without loading everything into memory. This allows very large images to
  be sent over a network connection, or to be copied without ever being
  fully in memory.
  Added:   dcmdata/include/dcmtk/dcmdata/dcwcache.h
           dcmdata/libsrc/dcwcache.cc
  Affects: dcmdata/apps/Makefile.dep
           dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/include/dcmtk/dcmdata/dcvrpobw.h
           dcmdata/include/dcmtk/dcmdata/dcxfer.h
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrpobw.cc
           dcmdata/libsrc/dcxfer.cc
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/Makefile.in
           dcmnet/libsrc/dimse.cc
           dcmsign/libsrc/simaccon.cc

- Updated doxygen API documentation
  Affects: dcmdata/include/dcmtk/dcmdata/cmdlnarg.h
           dcmdata/include/dcmtk/dcmdata/dcdebug.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/include/dcmtk/dcmdata/dclist.h
           dcmdata/include/dcmtk/dcmdata/dcovlay.h
           dcmdata/include/dcmtk/dcmdata/dcswap.h
           dcmdata/include/dcmtk/dcmdata/dctagkey.h
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/include/dcmtk/dcmdata/dcvm.h
           dcmdata/include/dcmtk/dcmdata/dcvr.h
           dcmdata/include/dcmtk/dcmdata/dcvrut.h

- Rebuilt Makefile dependencies
  Affects: dcmimage/apps/Makefile.dep
           dcmimage/libsrc/Makefile.dep
           dcmimgle/apps/Makefile.dep
           dcmimgle/libsrc/Makefile.dep
           dcmjpeg/apps/Makefile.dep
           dcmjpeg/libsrc/Makefile.dep
           dcmnet/apps/Makefile.dep
           dcmnet/libsrc/Makefile.dep
           dcmpstat/apps/Makefile.dep
           dcmpstat/libsrc/Makefile.dep
           dcmpstat/tests/Makefile.dep
           dcmqrdb/apps/Makefile.dep
           dcmqrdb/libsrc/Makefile.dep
           dcmsign/apps/Makefile.dep
           dcmsign/libsrc/Makefile.dep
           dcmsr/apps/Makefile.dep
           dcmsr/libsrc/Makefile.dep
           dcmsr/tests/Makefile.dep
           dcmwlm/apps/Makefile.dep
           dcmwlm/libsrc/Makefile.dep
           dcmwlm/tests/Makefile.dep
           dcmwlm/wwwapps/Makefile.dep
           ofstd/libsrc/Makefile.dep
           ofstd/tests/Makefile.dep

**** Changes from 2007.11.29 (riesmeier)

- Added support for Supplement 127 (CT Radiation Dose Reporting).
  Affects: dcmsr/libsrc/dsrxrdcc.cc

**** Changes from 2007.11.28 (riesmeier)

- Added support for "make install" to CMake project file.
  Affects: dcmwlm/data/CMakeLists.txt

- Updated CMake version number.
  Affects: CMakeLists.txt
           INSTALL

**** Changes from 2007.11.27 (riesmeier)

- Updated "change extension" shell script for current tool set.
  Affects: config/changext

**** Changes from 2007.11.26 (riesmeier)

- Added missing term "FILES" to install files command.
  Affects: dcmdata/data/CMakeLists.txt

- Fixed wrong installation directory.
  Affects: dcmdata/data/CMakeLists.txt
           dcmsr/data/CMakeLists.txt
           dcmwlm/data/CMakeLists.txt

- Explicitly list all files to be installed (in order to be more consistent
  with the "make install" approach on Unix systems).
  Affects: dcmimgle/data/CMakeLists.txt
           dcmpstat/data/CMakeLists.txt
           dcmsr/data/CMakeLists.txt

- Added ANNOUNCE file to list of files to be installed.
  Affects: CMakeLists.txt

**** Changes from 2007.11.23 (riesmeier)

- Added dummy of the ANNOUNCE file in order to avoid installation problems.
  Added:   ANNOUNCE.355

- Added reference to "make install-all".
  Affects: INSTALL

- Introduced "data" subdirectory in each module (used for read-only
  architecture independent data).
  Added:   dcmdata/data/CMakeLists.txt
           dcmdata/data/Makefile.in
           dcmdata/data/SC.dump
           dcmdata/data/VLP.dump
           dcmdata/data/dcm2xml.dtd
           dcmdata/data/dumppat.txt
           dcmimage/data/Makefile.in
           dcmimgle/data/CMakeLists.txt
           dcmimgle/data/Makefile.in
           dcmimgle/data/camera.lut
           dcmimgle/data/monitor.lut
           dcmimgle/data/printer.lut
           dcmimgle/data/scanner.lut
           dcmjpeg/data/Makefile.in
           dcmnet/data/Makefile.in
           dcmpstat/data/CMakeLists.txt
           dcmpstat/data/Makefile.in
           dcmpstat/data/philips.lut
           dcmqrdb/data/Makefile.in
           dcmsign/data/Makefile.in
           dcmsr/data/CMakeLists.txt
           dcmsr/data/Makefile.in
           dcmsr/data/dsr2xml.xsd
           dcmsr/data/report.css
           dcmsr/data/reportx.css
           dcmtls/data/Makefile.in
           dcmwlm/data/CMakeLists.txt
           dcmwlm/data/Makefile.in
           dcmwlm/wlistdb/OFFIS/*
           dcmwlm/wlistdb/README
           dcmwlm/wlistqry/*
           ofstd/data/Makefile.in
  Removed: dcmdata/apps/dcm2xml.dtd
           dcmdata/apps/dumppat.txt
           dcmimgle/tests/CMakeLists.txt
           dcmimgle/tests/camera.lut
           dcmimgle/tests/monitor.lut
           dcmimgle/tests/printer.lut
           dcmimgle/tests/scanner.lut
           dcmpstat/tests/CMakeLists.txt
           dcmpstat/tests/philips.lut
           dcmsr/apps/dsr2xml.xsd
           dcmsr/apps/report.css
           dcmsr/apps/reportx.css
           dcmwlm/wlistdb/*
           dcmwlm/wlistqry/*
  Affects: Makefile
           config/Makefile.in
           config/rootconf
           dcmdata/CMakeLists.txt
           dcmdata/Makefile.in
           dcmdata/apps/CMakeLists.txt
           dcmdata/apps/Makefile.in
           dcmdata/etc/CMakeLists.txt
           dcmdata/etc/Makefile.in
           dcmimage/Makefile.in
           dcmimgle/CMakeLists.txt
           dcmimgle/Makefile.in
           dcmimgle/tests/Makefile.in
           dcmjpeg/Makefile.in
           dcmnet/Makefile.in
           dcmpstat/CMakeLists.txt
           dcmpstat/Makefile.in
           dcmpstat/tests/Makefile.in
           dcmqrdb/Makefile.in
           dcmsign/Makefile.in
           dcmsr/CMakeLists.txt
           dcmsr/Makefile.in
           dcmsr/apps/CMakeLists.txt
           dcmsr/apps/Makefile.in
           dcmtls/Makefile.in
           dcmwlm/CMakeLists.txt
           dcmwlm/Makefile.in
           ofstd/Makefile.in

- Removed empty make dependencies file.
  Removed: dcmimage/tests/Makefile.dep
           dcmimgle/tests/Makefile.dep
           dcmjpeg/tests/Makefile.dep
           dcmqrdb/tests/Makefile.dep
           dcmsign/tests/Makefile.dep
           dcmtls/tests/Makefile.dep
  Affects: dcmimage/Makefile.in
           dcmimgle/Makefile.in
           dcmjpeg/Makefile.in
           dcmqrdb/Makefile.in
           dcmsign/Makefile.in
           dcmtls/Makefile.in

- Fixed minor layout issue.
  Affects: dcmdata/docs/Makefile.in
           dcmdata/include/Makefile.in
           dcmdata/libsrc/Makefile.in
           dcmdata/libsrc/i2dlib/Makefile.in
           dcmdata/tests/Makefile.in
           dcmimage/apps/Makefile.in
           dcmimage/docs/Makefile.in
           dcmimage/etc/Makefile.in
           dcmimage/include/Makefile.in
           dcmimage/libsrc/Makefile.in
           dcmimage/tests/Makefile.in
           dcmimgle/apps/Makefile.in
           dcmimgle/docs/Makefile.in
           dcmimgle/etc/Makefile.in
           dcmimgle/include/Makefile.in
           dcmimgle/libsrc/Makefile.in
           dcmjpeg/docs/Makefile.in
           dcmjpeg/etc/Makefile.in
           dcmjpeg/include/Makefile.in
           dcmjpeg/libijg12/Makefile.in
           dcmjpeg/libijg16/Makefile.in
           dcmjpeg/libijg8/Makefile.in
           dcmjpeg/libsrc/Makefile.in
           dcmjpeg/tests/Makefile.in
           dcmnet/apps/Makefile.in
           dcmnet/docs/Makefile.in
           dcmnet/include/Makefile.in
           dcmnet/libsrc/Makefile.in
           dcmnet/tests/Makefile.in
           dcmpstat/apps/Makefile.in
           dcmpstat/docs/Makefile.in
           dcmpstat/etc/Makefile.in
           dcmpstat/include/Makefile.in
           dcmpstat/jni/Makefile.in
           dcmpstat/libsrc/Makefile.in
           dcmqrdb/apps/Makefile.in
           dcmqrdb/docs/Makefile.in
           dcmqrdb/etc/Makefile.in
           dcmqrdb/include/Makefile.in
           dcmqrdb/libsrc/Makefile.in
           dcmqrdb/tests/Makefile.in
           dcmsign/apps/Makefile.in
           dcmsign/docs/Makefile.in
           dcmsign/etc/Makefile.in
           dcmsign/include/Makefile.in
           dcmsign/libsrc/Makefile.in
           dcmsign/tests/Makefile.in
           dcmsr/docs/Makefile.in
           dcmsr/etc/Makefile.in
           dcmsr/include/Makefile.in
           dcmsr/libsrc/Makefile.in
           dcmsr/tests/Makefile.in
           dcmtls/apps/Makefile.in
           dcmtls/docs/Makefile.in
           dcmtls/etc/Makefile.in
           dcmtls/include/Makefile.in
           dcmtls/libsrc/Makefile.in
           dcmtls/tests/Makefile.in
           dcmwlm/apps/Makefile.in
           dcmwlm/docs/Makefile.in
           dcmwlm/etc/Makefile.in
           dcmwlm/include/Makefile.in
           dcmwlm/libsrc/Makefile.in
           dcmwlm/tests/Makefile.in
           dcmwlm/wwwapps/Makefile.in
           doxygen/Makefile.in
           ofstd/docs/Makefile.in
           ofstd/etc/Makefile.in
           ofstd/include/Makefile.in
           ofstd/libsrc/Makefile.in
           ofstd/tests/Makefile.in

**** Changes from 2007.11.23 (eichelberg)

- Copy assignment operators in dcmdata now safe for self assignment.
  Affects: dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrpobw.cc

- Removed unwanted output on console when debug flag is not set.
  Affects: dcmdata/apps/mdfconen.cc

- Removed non-existing length to end attributes outside group 0008.
  Affects: dcmdata/libsrc/dicom.dic

**** Changes from 2007.11.15 (riesmeier)

- Introduced new markup mode for convertToMarkupString() that is used to
  distinguish between HTML, HTML 3.2, XHTML and XML.
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Enhanced convertToMarkupString() in order to be more compliant to the HTML
  specification (e.g. do not use "&apos;").
  Affects: ofstd/libsrc/ofstd.cc

- Added support for output in XHTML 1.1 format.
  Added:   dcmsr/apps/reportx.css
  Affects: dcmsr/apps/Makefile.in
           dcmsr/apps/dsr2html.cc
           dcmsr/docs/dsr2html.man
           dcmsr/include/dcmtk/dcmsr/dsrcodtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcodvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcomtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcomvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcontn.h
           dcmsr/include/dcmtk/dcmsr/dsrdattn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/include/dcmtk/dcmsr/dsrdtitn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgtn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgvl.h
           dcmsr/include/dcmtk/dcmsr/dsrnumtn.h
           dcmsr/include/dcmtk/dcmsr/dsrnumvl.h
           dcmsr/include/dcmtk/dcmsr/dsrpnmtn.h
           dcmsr/include/dcmtk/dcmsr/dsrreftn.h
           dcmsr/include/dcmtk/dcmsr/dsrscotn.h
           dcmsr/include/dcmtk/dcmsr/dsrscovl.h
           dcmsr/include/dcmtk/dcmsr/dsrstrvl.h
           dcmsr/include/dcmtk/dcmsr/dsrtcotn.h
           dcmsr/include/dcmtk/dcmsr/dsrtcovl.h
           dcmsr/include/dcmtk/dcmsr/dsrtextn.h
           dcmsr/include/dcmtk/dcmsr/dsrtimtn.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/include/dcmtk/dcmsr/dsruidtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavvl.h
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcontn.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdattn.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrdtitn.cc
           dcmsr/libsrc/dsrimgvl.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrpnmtn.cc
           dcmsr/libsrc/dsrscovl.cc
           dcmsr/libsrc/dsrstrvl.cc
           dcmsr/libsrc/dsrtcovl.cc
           dcmsr/libsrc/dsrtextn.cc
           dcmsr/libsrc/dsrtimtn.cc
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/dsrwavvl.cc

- Enhanced support for output in valid HTML 3.2 format. Migrated support for
  standard HTML from version 4.0 to 4.01 (strict).
  Affects: dcmsr/apps/report.css
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcontn.cc
           dcmsr/libsrc/dsrdattn.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrdtitn.cc
           dcmsr/libsrc/dsrimgvl.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrpnmtn.cc
           dcmsr/libsrc/dsrstrvl.cc
           dcmsr/libsrc/dsrtimtn.cc
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/dsrwavvl.cc

- Added meta header element for the generator of the HTML/XHTML document.
  Affects: dcmsr/libsrc/dsrdoc.cc

- Fixed coding style to be more consistent.
  Affects: dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/include/dcmtk/dcmsr/dsrcsidl.h
           dcmsr/include/dcmtk/dcmsr/dsrimgfr.h
           dcmsr/include/dcmtk/dcmsr/dsrscogr.h
           dcmsr/include/dcmtk/dcmsr/dsrsoprf.h
           dcmsr/include/dcmtk/dcmsr/dsrtcodt.h
           dcmsr/include/dcmtk/dcmsr/dsrtcosp.h
           dcmsr/include/dcmtk/dcmsr/dsrtcoto.h
           dcmsr/include/dcmtk/dcmsr/dsrwavch.h
           dcmsr/libsrc/dsrcomtn.cc
           dcmsr/libsrc/dsrcomvl.cc
           dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrimgfr.cc
           dcmsr/libsrc/dsrimgtn.cc
           dcmsr/libsrc/dsrnumtn.cc
           dcmsr/libsrc/dsrreftn.cc
           dcmsr/libsrc/dsrscogr.cc
           dcmsr/libsrc/dsrscotn.cc
           dcmsr/libsrc/dsrsoprf.cc
           dcmsr/libsrc/dsrtcodt.cc
           dcmsr/libsrc/dsrtcosp.cc
           dcmsr/libsrc/dsrtcotn.cc
           dcmsr/libsrc/dsrtcoto.cc
           dcmsr/libsrc/dsruidtn.cc
           dcmsr/libsrc/dsrwavch.cc
           dcmsr/libsrc/dsrwavtn.cc

- Removed preliminary patch for TID 4023 (since not yet approved). Documented
  modification required for CP 492 (has already been added by the last commit).
  Affects: dcmsr/libsrc/dsrmamcc.cc

**** Changes from 2007.11.08 (onken)

- Initial checkin of img2dcm application and corresponding library i2dlib.
  Added:   dcmdata/apps/img2dcm.cc
           dcmdata/libsrc/i2dlib/CMakeLists.txt
           dcmdata/libsrc/i2dlib/Makefile.dep
           dcmdata/libsrc/i2dlib/Makefile.in
           dcmdata/libsrc/i2dlib/i2d.cc
           dcmdata/libsrc/i2dlib/i2djpgs.cc
           dcmdata/libsrc/i2dlib/i2dplsc.cc
           dcmdata/libsrc/i2dlib/i2dplvlp.cc
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2d.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dimgs.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2djpgs.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2doutpl.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplsc.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/i2dplvlp.h
           dcmdata/include/dcmtk/dcmdata/i2dlib/CMakeLists.txt
           dcmdata/etc/CMakeLists.txt
           dcmdata/etc/SC.dump
           dcmdata/etc/VLP.dump
           dcmdata/docs/img2dcm.man
  Affects: dcmdata/CMakeLists.txt
           dcmdata/apps/CMakeLists.txt
           dcmdata/apps/Makefile.in
           dcmdata/apps/Makefile.dep
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.in
           dcmdata/include/Makefile.in
           dcmdata/include/dcmtk/dcmdata/CMakeLists.txt
           dcmdata/etc/Makefile.in

- Added manpages for dcm2pdf and img2dcm to doxygen documentation.
  Added missing link for img2dcm to dcmdata module overview.
  Added:   doxygen/manpages/man1/dcm2pdf.1
           doxygen/manpages/man1/img2dcm.1
  Affects: dcmdata/docs/dcmdata.dox

- Fixed some inconsistencies in documentation and a missing pointer
  initialization.
  Affects: dcmdata/apps/img2dcm.cc
           dcmdata/libsrc/i2dlib/i2dplsc.cc

**** Changes from 2007.11.06 (riesmeier)

- Report warning message if a query contains an empty sequence (without item).
  Affects: dcmwlm/libsrc/wlds.cc

- Added empty item to sequences with no item (required for a valid query).
  Affects: dcmwlm/wlistqry/wlistqry1.dump
           dcmwlm/wlistqry/wlistqry3.dump

- Reformatted sample query dump files (according to dcmdump output).
  Affects: dcmwlm/wlistqry/wlistqry0.dump
           dcmwlm/wlistqry/wlistqry1.dump
           dcmwlm/wlistqry/wlistqry10.dump
           dcmwlm/wlistqry/wlistqry11.dump
           dcmwlm/wlistqry/wlistqry12.dump
           dcmwlm/wlistqry/wlistqry2.dump
           dcmwlm/wlistqry/wlistqry3.dump
           dcmwlm/wlistqry/wlistqry4.dump
           dcmwlm/wlistqry/wlistqry5.dump
           dcmwlm/wlistqry/wlistqry6.dump
           dcmwlm/wlistqry/wlistqry7.dump
           dcmwlm/wlistqry/wlistqry8.dump
           dcmwlm/wlistqry/wlistqry9.dump

**** Changes from 2007.10.31 (eichelberg)

- Fixed bug in DICOMscope support code that caused the Print SCU to send
  wrong values for Decimate/Crop Behavior.
  Thanks to Keith Bailey <<EMAIL>> for the report.
  Affects: dcmpstat/libsrc/dvpsib.cc

**** Changes from 2007.10.25 (riesmeier)

- Removed short option -P for --no-port since this string is already used for
  the patient root information model (--patient).
  Thanks to Anibal Jodorcovsky <<EMAIL>> for the report.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/docs/movescu.man

**** Changes from 2007.10.24 (riesmeier)

- Added new command line option which prints a header with the filename only
  for those input files that contain one of the searched tags.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Fixed small layout and formatting issues in the dump output (missing
  indentation and leading zeros).
  Affects: dcmdata/apps/dcmdump.cc

- Replaced sprintf() call by an appropriate use of the C++ stream manipulators.
  Affects: dcmdata/apps/dcmdump.cc

**** Changes from 2007.10.23 (riesmeier)

- Fixed bug in writeImageToDataset() for images with BitsAllocated = 32.
  Thanks to Christian Fremgen <<EMAIL>> for the bug
  report.
  Affects: dcmimgle/libsrc/dimoimg.cc

- Fixed bug in writeFrameToDataset() for images with BitsAllocated = 32.
  Affects: dcmimgle/libsrc/diimage.cc

**** Changes from 2007.10.19 (onken)

- Fixed typo in debug output.
  Affects: dcmnet/libsrc/assoc.cc

**** Changes from 2007.10.19 (onken)

- Fixed bug in addOverrideKey() that caused problems when parsing a value in a
  tag-value combination if the value contained whitespace characters.
  Thanks to Paul Wijntjes <<EMAIL>> for the bug report.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/libsrc/dfindscu.cc

**** Changes from 2007.10.01 (riesmeier)

- Added support for searching directories recursively for DICOM files.
  Affects: dcmdata/apps/dcmdump.cc
           dcmdata/docs/dcmdump.man

- Enhanced experimental quiet mode.
  Affects: dcmdata/apps/dcmdump.cc

- Renamed command line option --pattern (+p) to --scan-pattern (+sp).
  Affects: dcmnet/apps/storescu.cc
           dcmnet/docs/storescu.man

- Fixed layout issue with --version output.
  Affects: dcmnet/apps/termscu.cc

**** Changes from 2007.09.28 (onken)

- Fixed crash of network code when processing association requests without any
  offered presentation contexts. Minor changes in source formatting.
  Thanks to Jesper Bojesen <<EMAIL>> for the bug report and
  suggested fix.
  Affects: dcmnet/libsrc/dulconst.cc

**** Changes from 2007.09.21 (onken)

- Changed foundVR() API and implementation to use Uint8* instead of char* to
  avoid calls to isalpha() with negative arguments (undef. behaviour/assertion)
  Thanks to Niels Dekker <<EMAIL>> for the bug report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcitem.cc

**** Changes from 2007.09.07 (onken)

- Added basic support for Extended Negotiation of User Identity.
  Affects: dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/dcextusr.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/CMakeLists.txt
           dcmnet/libsrc/Makefile.in
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/dcextusr.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulparse.cc
           dcmnet/libsrc/dulstruc.h

- Added #ifndef guard to header file.
  Affects: dcmnet/libsrc/dulstruc.h

**** Changes from 2007.09.04 (riesmeier)

- Fixed wrong typecast in createScale() that filtered out negative clipping
  coordinates for the top left hand corner.
  Thanks to J. Carnulfo <<EMAIL>> for the bug report and fix.
  Affects: dcmimgle/libsrc/dimo1img.cc

**** Changes from 2007.08.30 (riesmeier)

- Added further check on pixel pointer (possibly avoids crash under certain
  conditions).
  Thanks to Per Inge Mathisen <<EMAIL>> for the suggestion.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h

**** Changes from 2007.08.27 (riesmeier)

- Added further check on Overlay Type variable.
  Thanks to Per Inge Mathisen <<EMAIL>> for the suggestion.
  Affects: dcmimgle/libsrc/diovpln.cc

**** Changes from 2007.08.10 (eichelberg)

- Added new command line option --keep-char-set that returns any specific
  character set as encoded in the worklist file.
  Thanks to Steffen Benndorf <<EMAIL>> for the suggestion.
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man
           dcmwlm/include/dcmtk/dcmwlm/wltypdef.h
           dcmwlm/libsrc/wldsfs.cc

**** Changes from 2007.07.25 (riesmeier)

- Enhanced misleading documentation.
  Affects: dcmimgle/include/dcmtk/dcmimgle/didocu.h

- Fixed typo.
  Affects: dcmimgle/libsrc/diluptab.cc

**** Changes from 2007.07.13 (onken)

- Fixed some status code for DIMSE-N.
  Affects: dcmnet/libsrc/diutil.cc

**** Changes from 2007.07.12 (onken)

- Added status codes and corresponding printing routines for DIMSE-N.
  Affects: dcmnet/libsrc/diutil.cc
           dcmnet/include/dcmtk/dcmnet/diutil.h
           dcmnet/include/dcmtk/dcmnet/dimse.h

**** Changes from 2007.07.11 (riesmeier)

- Fixed layout and other minor issues of the usage output (--help).
  Affects: dcmdata/apps/dcm2pdf.cc
           dcmdata/docs/dcm2pdf.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/tests/tstpread.cc
           dcmnet/docs/storescp.man

- Added new tool dcm2pdf to the module's overview page.
  Affects: dcmdata/docs/dcmdata.dox

**** Changes from 2007.07.11 (eichelberg)

- Updated changext shell script for current tool set.
  Affects: config/changext

- Added new tool dcm2pdf that extracts a PDF document from a DICOM
  Encapsulated PDF file, i.e. is the counterpart to pdf2dcm.
  Added:   dcmdata/apps/apps/dcm2pdf.cc
           dcmdata/apps/docs/dcm2pdf.man
  Affects: dcmdata/apps/CMakeLists.txt
           dcmdata/apps/Makefile.dep
           dcmdata/apps/Makefile.in

- Added regression test for new method DcmElement::getPartialValue.
  Added:   dcmdata/tests/tstpread.cc
  Affects: dcmdata/tests/Makefile.dep
           dcmdata/tests/Makefile.in

- Initial release of new method DcmElement::getPartialValue which gives access
  to partial attribute values without loading the complete attribute value
  into memory, if kept in file.
  Added:   dcmdata/include/dcmtk/dcmdata/dcfcache.h
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/libsrc/Makefile.dep
           dcmdata/libsrc/dcelem.cc

- Changed the modality codes used for filename generation for the current and
  retired ultrasound multiframe SOP classes to USm and USf, respectively.
  Affects: dcmdata/libsrc/dcuid.cc

**** Changes from 2007.07.10 (onken)

- Fixed bug in windows multiprocess code that invalidated option values
  containing spaces. All cmdline arguments are now surrounded by double quotes
  when spawning a new process.
  Affects: dcmnet/libsrc/dul.cc

**** Changes from 2007.07.04 (riesmeier)

- Added support for binary data (e.g. pixel data) stored in a separate file.
  NB: This feature is not yet available in dcm2xml.
  Affects: dcmdata/apps/dcm2xml.dtd
           dcmdata/apps/xml2dcm.cc
           dcmdata/docs/xml2dcm.man

**** Changes from 2007.06.29 (eichelberg)

- Code clean-up: Most member variables in module dcmdata are now private,
  not protected anymore.
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcvrulup.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dcvrae.cc
           dcmdata/libsrc/dcvras.cc
           dcmdata/libsrc/dcvrat.cc
           dcmdata/libsrc/dcvrcs.cc
           dcmdata/libsrc/dcvrda.cc
           dcmdata/libsrc/dcvrds.cc
           dcmdata/libsrc/dcvrdt.cc
           dcmdata/libsrc/dcvrfd.cc
           dcmdata/libsrc/dcvrfl.cc
           dcmdata/libsrc/dcvris.cc
           dcmdata/libsrc/dcvrlo.cc
           dcmdata/libsrc/dcvrlt.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrpn.cc
           dcmdata/libsrc/dcvrpobw.cc
           dcmdata/libsrc/dcvrsh.cc
           dcmdata/libsrc/dcvrsl.cc
           dcmdata/libsrc/dcvrss.cc
           dcmdata/libsrc/dcvrst.cc
           dcmdata/libsrc/dcvrtm.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/dcvrul.cc
           dcmdata/libsrc/dcvrulup.cc
           dcmdata/libsrc/dcvrus.cc
           dcmdata/libsrc/dcvrut.cc

**** Changes from 2007.06.26 (riesmeier)

- Added new variant of encodeBase64() method that outputs directly to a stream
  (avoids using a memory buffer for large binary data).
  Affects: dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcvrobow.cc
           ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc
           ofstd/tests/tofstd.cc

**** Changes from 2007.06.14 (eichelberg)

- Updated changext script
  Affects: config/changext

**** Changes from 2007.06.13 (eichelberg)

- Added module code OFM_dcmjpls and some new error codes.
  Affects: dcmdata/include/dcmtk/dcmdata/dcerror.h
           dcmdata/libsrc/dcerror.cc

**** Changes from 2007.06.08 (riesmeier)

- Added new helper functions insertSequenceItem(), findAndDeleteSequenceItem().
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcitem.cc

- Replaced helper function findAndCopyElement() by new optional parameter
  'createCopy' in various findAndGetXXX() functions.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcitem.cc

- Fixed typo that caused MSVC to report an error.
  Affects: dcmdata/libsrc/dcitem.cc

**** Changes from 2007.06.07 (riesmeier)

- Enhanced support for very large binary data elements (by reducing the overall
  memory consumption). Thanks to Dennis Schlevoigt <<EMAIL>>
  for the original report.
  Affects: dcmdata/apps/dump2dcm.cc

- Added createUint8Array() and createUint16Array() methods.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcvrobow.cc

- Fixed incorrect comment.
  Affects: dcmdata/include/dcmtk/dcmdata/dcvrpobw.h

**** Changes from 2007.06.06 (onken)

- Fixed compilation for Mac OS X with making large file support function calls
  implicit for this OS (Mac OS X misleadingly defines _LARGEFILE64_SOURCE).
  Thanks to Jeff Solomon <<EMAIL>> for the bug report.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

**** Changes from 2007.05.24 (onken)

- Removed duplicate closing of socket handle in "windows multiprocess" code.
  Thanks to Vladimir Kharam <<EMAIL>> for the report.
  Affects: dcmnet/libsrc/dul.cc

**** Changes from 2007.05.11 (riesmeier)

- Enhanced debug output when detecting by-reference relationships to non-
  existing content items.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/libsrc/dsrdoctr.cc

**** Changes from 2007.04.26 (riesmeier)

- Fixed issue with SpecificCharacterSet attribute (treat as type 1C instead of
  type 1 when copying from the referenced DICOM file to the directory record).
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2007.03.16 (riesmeier)

- Introduced new flag that allows to select how to handle the BitsPerTableEntry
  value in the LUT descriptor (use, ignore or check).
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dipalimg.cc
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/diluptab.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/diutils.h
           dcmimgle/libsrc/diluptab.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/dimomod.cc

- Fixed problem with wrong interpretation of the palette descriptor in certain
  cases. Now the BitsPerTableEntry stored in the LUT descriptor is used by
  default. The previous behavior can be enabled using the ELM_CheckValue mode.
  Thanks to Ewald de Vries <<EMAIL>> for the report.
  Affects: dcmimgle/libsrc/diluptab.cc

- Added new command line option --check-lut-depth that can be used to re-enable
  the old behavior of how the third value of the LUT descriptor is treated.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/docs/dcm2pnm.man

- Fixed wrong warning message about unchecked options in debug mode.
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2007.03.12 (riesmeier)

- Consistently use COUT and CERR instead of stdout and stderr.
  Affects: dcmnet/apps/storescu.cc

- Added support for searching directories recursively for DICOM files.
  Affects: dcmnet/apps/storescu.cc
           dcmnet/docs/storescu.man

- Added support for common "input file format" options.
  Affects: dcmnet/apps/storescu.cc
           dcmnet/docs/storescu.man

- Updated debug code to correctly compile when all standard C++ classes remain
  in namespace std.
  Affects: dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulparse.cc

**** Changes from 2007.03.09 (riesmeier)

- Added support for missing VRs (SL, SS, UL, SS) to insertEmptyElement().
  Thanks to Razvan Costea-Barlutiu <<EMAIL>> for the report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/libsrc/dcitem.cc

- Added optional parameter "recurse" to searchDirectoryRecursively().
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Fixed wrong output of status variable after calling loadFile().
  Affects: dcmdata/apps/dcmdump.cc

- Updated documentation of dump input format.
  Affects: dcmdata/apps/dump2dcm.cc

- Fixed issue with new parameter "recurse" in searchDirectoryRecursively().
  Affects: ofstd/libsrc/ofstd.cc

**** Changes from 2007.03.07 (riesmeier)

- Fixed issue with uncompressed icon images in DICOM images with encapsulated
  transfer syntax. Thanks to Dennis Schlevoigt <<EMAIL>> for
  the original report.
  Affects: dcmdata/apps/dump2dcm.cc

- Fixed minor layout and formatting issues.
  Affects: dcmdata/docs/dump2dcm.man

**** Changes from 2007.02.23 (eichelberg)

- Fixed bug in DcmFileConsumer::avail introduced when converting the return
  type to offile_off_t, which is signed.
  Affects: dcmdata/libsrc/dcostrmf.cc

**** Changes from 2007.02.22 (riesmeier)

- Added support for compressed pixel data.
  Affects: dcmdata/apps/dump2dcm.cc

- Added new command line option --write-xfer-same.
  Affects: dcmdata/apps/dump2dcm.cc
           dcmdata/docs/dump2dcm.man

- Fixed inconsistent naming of transfer syntaxes (added space characters).
  Affects: dcmdata/libsrc/dcxfer.cc

**** Changes from 2007.02.21 (eichelberg)

- Increased output precision to 17 (DBL_DIG+2) when converting an FD element to
  string.
  Thanks to Niels Dekker <<EMAIL>> for the suggestion.
  Affects: dcmdata/libsrc/dcvrfd.cc

**** Changes from 2007.02.20 (riesmeier)

- Fixed wrong comment in compare() method.
  Affects: ofstd/include/dcmtk/ofstd/ofstring.h

- Added function that removes a given prefix from a pathname (e.g. root dir).
  Affects: ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Fixed wrong spelling in error message.
  Affects: dcmdata/libsrc/dcelem.cc

- Fixed wrong example in documentation.
  Affects: dcmnet/docs/asconfig.txt

**** Changes from 2007.02.19 (eichelberg)

- Refactored findscu code into class DcmFindSCU, which is now part of the dcmnet
  library, and a short command line tool that only evaluates command line
  parameters and then makes use of this class. This facilitates re-use of the
  findscu code in other applications.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/apps/findscu.cc
           dcmnet/libsrc/CMakeLists.txt
           dcmnet/libsrc/Makefile.dep
           dcmnet/libsrc/Makefile.in
  Added:   dcmnet/include/dcmtk/dcmnet/dfindscu.h
           dcmnet/libsrc/dfindscu.cc

- Removed calls to DcmObject::error()
  Affects: dcmnet/apps/movescu.cc
           dcmwlm/tests/wltest.cc

- Declaration of copy assignment operator now private, as it should be
  Affects: dcmdata/include/dcmtk/dcmdata/dcmetinf.h

- Removed searchErrors() methods that are not used anywhere and added
  error() methods only in the DcmObject subclasses where really used.
  Completed doxygen documentation of class DcmObject.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Namespace std is not imported into the default namespace anymore,
  unless DCMTK is compiled with macro USING_STD_NAMESPACE defined.
  Affects: config/docs/macros.txt
           ofstd/include/dcmtk/ofstd/ofstdinc.h
           ofstd/include/dcmtk/ofstd/ofstream.h

- When writing DICOM data to file, we now by default split fwrite() calls for
  very large attributes into multiple calls, none of which writes more than
  32 MBytes. This is a workaround to a bug in most MSVC environments (MSDN
  KB899149) and is hardly relevant performance-wise. Previous behaviour can
  be enforced by compiling with WRITE_VERY_LARGE_CHUNKS defined.
  Affects: config/docs/macros.txt
           dcmdata/libsrc/dcostrmf.cc

- Class DcmInputStream and related classes are now safe for use with
  large files (2 GBytes or more) if supported by compiler and operating system.
  Affects: dcmdata/dcmdata/include/dcmtk/dcmdata/dcistrma.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcistrmb.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcistrmf.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcistrmz.h
           dcmdata/dcmdata/libsrc/dcistrma.cc
           dcmdata/dcmdata/libsrc/dcistrmb.cc
           dcmdata/dcmdata/libsrc/dcistrmf.cc
           dcmdata/dcmdata/libsrc/dcistrmz.cc

- Added constructor to class OFFile that takes FILE * as argument.
  Affects: ofstd/include/dcmtk/ofstd/offile.h

- Class DcmOutputStream and related classes are now safe for use with
  large files (2 GBytes or more) if supported by compiler and operating system.
  Affects: dcmdata/dcmdata/include/dcmtk/dcmdata/dcostrma.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcostrmb.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcostrmf.h
           dcmdata/dcmdata/include/dcmtk/dcmdata/dcostrmz.h
           dcmdata/dcmdata/libsrc/dcostrma.cc
           dcmdata/dcmdata/libsrc/dcostrmb.cc
           dcmdata/dcmdata/libsrc/dcostrmf.cc
           dcmdata/dcmdata/libsrc/dcostrmz.cc
           dcmnet/libsrc/dimse.cc
           dcmsign/libsrc/simaccon.cc

**** Changes from 2007.02.08 (riesmeier)

- Need to determine global min/max value after copying pixel data.
  Thanks to Marc Schlimbach <<EMAIL>> for the original report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/dimocpt.h

- Added lower limit check in DiMonoImage::writeImageToDataset() calculating
  the value for bits stored.
  Affects: dcmimgle/libsrc/dimoimg.cc

**** Changes from 2007.02.02 (riesmeier)

- Added warning message when existing SOP instance is added to DICOMDIR in
  create or append mode.
  Affects: dcmdata/libsrc/dcddirif.cc

- Added error message when existing SOP instance is inconsistent with new
  directory record in update mode (e.g. different SOP class UID).
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Fixed incomplete warning message in update mode (filename was missing).
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

**** Changes from 2007.01.16 (riesmeier)

- Now treating PatientID, StudyID and SeriesNumber as type 1C elements instead
  of 1 in order to avoid unwanted overwriting with empty value in update mode.
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2007.01.12 (riesmeier)

- Removed ANNOUNCE.354 file from list of installation files. Changed default
  installation path to "dcmtk-3.5.4-cvs-win32-i386".
  Affects: CMakeLists.txt

**** Changes from 2007.01.10 (riesmeier)

- Added new option that enables support for retired SOP classes.
  Affects: dcmdata/apps/dcmgpdir.cc
           dcmdata/docs/dcmgpdir.man
           dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc
           dcmjpeg/docs/dcmmkdir.man

- Re-ordered and re-structured command line options.
  Affects: dcmdata/apps/dcmgpdir.cc
           dcmdata/docs/dcmgpdir.man
           dcmjpeg/docs/dcmmkdir.man

- Added missing transfer syntax to X-ray Angiographic DVD profile.
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2006.12.15 (riesmeier)

- Added new method that checks whether a DICOM object or element is affected
  by SpecificCharacterSet (0008,0005).
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcchrstr.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcchrstr.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Added new option that allows to update existing entries in a DICOMDIR. This
  also adds support for mixed media storage application profiles.
  Affects: dcmdata/apps/dcmgpdir.cc
           dcmdata/docs/dcmgpdir.man
           dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc
           dcmjpeg/docs/dcmmkdir.man

- Fixed wrong spelling of command line option which prevented the MPEG2-DVD
  application profile from working.
  Affects: dcmdata/apps/dcmgpdir.cc

- Changed name of enum value for the MPEG2-DVD application profile in order to
  be more consistent with other names.
  Affects: dcmdata/apps/dcmgpdir.cc
           dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Slightly revised handling of type 1, 1C and 2 elements in directory records.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Fixed bug in cardiac application profiles when checking the ImageType
  (0008,0008) for BIPLANE images.
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/libsrc/dcddirif.cc

- Added check whether the SpecificCharacterSet (0008,0005) is really required
  for a particular directory record.
  Affects: dcmdata/libsrc/dcddirif.cc

- Fixed bug that prevented Key Object Selection Documents from being added to
  a DICOMDIR.
  Affects: dcmdata/libsrc/dcddirif.cc

**** Changes from 2006.12.15 (onken)

- Changed member variable from char* to OFString and reintegrated correct
  intending of command line options, that were lost in of of last revisions.
  Removed (unchecked) command line options for group / sequence length
  calculations
  Affects: dcmwlm/apps/wlmscefs.cc
           dcmwlm/apps/wlmscefs.h
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2006.12.13 (riesmeier)

- Added new command line option that allows to check all data elements with
  string values for extended characters, not only those affected by Specific
  CharacterSet (0008,0005).
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man
           dcmsr/apps/dsr2xml.cc
           dcmsr/docs/dsr2xml.man

- Added new optional parameter "checkAllStrings" to method containsExtended
  Characters().
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcchrstr.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcchrstr.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcsequen.cc

- Removed dependence of the new command line option "--charset-check-all" from
  option "--charset-assume".
  Affects: dcmdata/apps/dcm2xml.cc

**** Changes from 2006.12.12 (riesmeier)

- Fixed wrong default value for the "dateTimeSeparator" parameter.
  Thanks to Jacob Foshee <<EMAIL>> for the report and suggested fix.
  Affects: ofstd/libsrc/ofdatime.cc

**** Changes from 2006.12.06 (riesmeier)

- Updated various citations according to the latest version of the DICOM
  standard (incl. CP 584). Removed references to a particular edition of the
  standard.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h

**** Changes from 2006.12.06 (onken)

- Added "--no-backup" option to prevent dcmodify from creating backup file.
  Affects: dcmdata/apps/mdfconen.h
           dcmdata/apps/mdfconen.cc
           dcmdata/docs/dcmodify.man

**** Changes from 2006.11.23 (onken)

- Made member variables private (before: protected)
  Affects: dcmdata/apps/mdfconen.h
           dcmdata/apps/mdfdsman.h

**** Changes from 2006.11.17 (riesmeier)

- Only compare stored and computed pixel count for "original" images that are
  directly loaded from DICOM files or datasets.
  Affects: dcmimage/libsrc/dicoimg.cc
           dcmimgle/libsrc/dimoimg.cc

- Mask out the upper 32 bits of gethostid()'s return value since on 64-bit
  Linux it is sign-extended to a 64-bit long.
  Thanks to Kevin Jamieson <<EMAIL>> for the report and suggested
  fix.
  Affects: dcmdata/libsrc/dcuid.cc

**** Changes from 2006.11.09 (riesmeier)

- Fixed possible program crash when processing multi-frame overlay data stored
  in data element OverlayData (60xx,3000).
  Thanks to Christian Fremgen <<EMAIL>> for the bug report and
  fix.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diovpln.h

- Added private tags from Siemens AXIOM Artis VB30 conformance statement.
  Affects: dcmdata/libsrc/private.dic
           dcmdata/libsrc/dcdictzz.cc

**** Changes from 2006.11.08 (eichelberg)

- Added DcmByteString::containsExtendedCharacters().
  Affects: dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/libsrc/dcbytstr.cc

**** Changes from 2006.10.27 (riesmeier)

- Fixed wrong name of configuration file.
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc

- Moved old announcement and change log for release 3.5.4 to docs.
  Removed: ANNOUNCE.354
           CHANGES.354
  Added:   docs/ANNOUNCE.354
           docs/CHANGES.354

- Created new change log.
  Added:   CHANGES.355

- Fixed layout and formatting issue.
  Affects: dcmnet/docs/storescp.man

- Fixed wrong name of the command line tool "dcmqridx".
  Affects: dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/docs/dcmqrset.txt

- Fixed problem with unknown (e.g. private) SOP Classes.
  Thanks to Anders Israelsson <<EMAIL>> for the original report.
  Affects: dcmnet/apps/storescp.cc

- Added new default parameter to dcmSOPClassUIDToModality() that allows for
  the specification of the return value in case the SOP Class is unknown.
  Affects: dcmdata/include/dcmtk/dcmdata/dcuid.h
           dcmdata/libsrc/dcuid.cc

- Fixed possible integer overflow for images with very large pixel data.
  Thanks to Peter Klotz <<EMAIL>> for the original report.
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/libsrc/dimoimg.cc

- Fixed wrong warning message about length of pixel data.
  Affects: dcmimage/libsrc/dicoimg.cc
           dcmimgle/libsrc/dimoimg.cc

**** Changes from 2006.10.13 (riesmeier)

- Enhanced performance of writeXML() for large multi-valued DS elements.
  Thanks to Adrian Vogt <<EMAIL>> for the original report.
  Affects: dcmdata/include/dcmtk/dcmdata/dcvrds.h
           dcmdata/libsrc/dcvrds.cc

- Added new helper function that allows to check whether the conversion to an
  HTML/XML markup string is required.
  Affects: dcmdata/libsrc/dcelem.cc
           ofstd/include/dcmtk/ofstd/ofstd.h
           ofstd/libsrc/ofstd.cc

- Renamed variable "string" to "stringVal".
  Affects: dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcvrui.cc

- Fixed wrong formatting.
  Affects: dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/libsrc/dcvrobow.cc

**** Changes from 2006.08.21 (eichelberg)

- Added configure tests and command line options to enable/disable
  long file support (LFS). Also added test that checks whether or not
  stdio functions like fopen are in namespace std when cstdio is included
  and one test that checks for the presence of strerror_r in string.h
  Affects: config/aclocal.m4
           config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h

- Added new class OFFile that provides a simple encapsulation layer for
  FILE based stream I/O and, in particular, provides long file support
  (LFS) if available on the underlying operating system platform through
  a single API.
  Added: ofstd/include/dcmtk/ofstd/offile.h

- Added test application that checks whether class OFFile can correctly
  process large files (> 4 GBytes), including emulations of fseek and ftell,
  which are non-trivial on certain platforms such as Win32.
  Added:   ofstd/tests/toffile.cc
  Affects: ofstd/tests/Makefile.dep
           ofstd/tests/Makefile.in

- Updated code to correctly compile when all standard C++ classes remain in
  namespace std.
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2006.08.16 (eichelberg)

- Updated all code in module dcmjpeg to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/libsrc/dipijpeg.cc
           dcmjpeg/libsrc/djcodece.cc
           dcmjpeg/libsrc/djdijg12.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djdijg8.cc
           dcmjpeg/libsrc/djeijg12.cc
           dcmjpeg/libsrc/djeijg16.cc
           dcmjpeg/libsrc/djeijg8.cc

**** Changes from 2006.08.16 (riesmeier)

- Removed superfluous "Version" prefix from PROJECT_NUMBER line.
  Affects: doxygen/manpages.cfg

**** Changes from 2006.08.16 (onken)

- Hid (internal) "--forked-child" option from user.
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2006.08.15 (eichelberg)

- Updated all code in module dcmdata to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmftest.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dcmodify.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/mdfdsman.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmdata/include/dcmtk/dcmdata/dcbytstr.h
           dcmdata/include/dcmtk/dcmdata/dcdatset.h
           dcmdata/include/dcmtk/dcmdata/dcdicdir.h
           dcmdata/include/dcmtk/dcmdata/dcdicent.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/include/dcmtk/dcmdata/dcelem.h
           dcmdata/include/dcmtk/dcmdata/dcfilefo.h
           dcmdata/include/dcmtk/dcmdata/dchashdi.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcmetinf.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/include/dcmtk/dcmdata/dcpixel.h
           dcmdata/include/dcmtk/dcmdata/dcpixseq.h
           dcmdata/include/dcmtk/dcmdata/dcpxitem.h
           dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/include/dcmtk/dcmdata/dctagkey.h
           dcmdata/include/dcmtk/dcmdata/dcvrat.h
           dcmdata/include/dcmtk/dcmdata/dcvrfd.h
           dcmdata/include/dcmtk/dcmdata/dcvrfl.h
           dcmdata/include/dcmtk/dcmdata/dcvrobow.h
           dcmdata/include/dcmtk/dcmdata/dcvrsl.h
           dcmdata/include/dcmtk/dcmdata/dcvrss.h
           dcmdata/include/dcmtk/dcmdata/dcvrui.h
           dcmdata/include/dcmtk/dcmdata/dcvrul.h
           dcmdata/include/dcmtk/dcmdata/dcvrus.h
           dcmdata/libsrc/cmdlnarg.cc
           dcmdata/libsrc/dcbytstr.cc
           dcmdata/libsrc/dcdatset.cc
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdebug.cc
           dcmdata/libsrc/dcdicdir.cc
           dcmdata/libsrc/dcdicent.cc
           dcmdata/libsrc/dcdict.cc
           dcmdata/libsrc/dcdirrec.cc
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcfilefo.cc
           dcmdata/libsrc/dchashdi.cc
           dcmdata/libsrc/dcistrmb.cc
           dcmdata/libsrc/dcistrmz.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcmetinf.cc
           dcmdata/libsrc/dcobject.cc
           dcmdata/libsrc/dcostrmb.cc
           dcmdata/libsrc/dcostrmf.cc
           dcmdata/libsrc/dcpixel.cc
           dcmdata/libsrc/dcpixseq.cc
           dcmdata/libsrc/dcpxitem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dctagkey.cc
           dcmdata/libsrc/dcuid.cc
           dcmdata/libsrc/dcvr.cc
           dcmdata/libsrc/dcvrat.cc
           dcmdata/libsrc/dcvrfd.cc
           dcmdata/libsrc/dcvrfl.cc
           dcmdata/libsrc/dcvrobow.cc
           dcmdata/libsrc/dcvrsl.cc
           dcmdata/libsrc/dcvrss.cc
           dcmdata/libsrc/dcvrui.cc
           dcmdata/libsrc/dcvrul.cc
           dcmdata/libsrc/dcvrus.cc
           dcmdata/tests/tvrdatim.cc
           ofstd/include/dcmtk/ofstd/ofstream.h

- Updated the code in module dcmtls to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmtls/include/dcmtk/dcmtls/tlslayer.h
           dcmtls/include/dcmtk/dcmtls/tlstrans.h
           dcmtls/libsrc/tlslayer.cc
           dcmtls/libsrc/tlstrans.cc

- Updated the code in module dcmnet to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/termscu.cc
           dcmnet/include/dcmtk/dcmnet/assoc.h
           dcmnet/include/dcmtk/dcmnet/dcmtrans.h
           dcmnet/include/dcmtk/dcmnet/dimse.h
           dcmnet/include/dcmtk/dcmnet/dul.h
           dcmnet/libsrc/assoc.cc
           dcmnet/libsrc/cond.cc
           dcmnet/libsrc/dcmtrans.cc
           dcmnet/libsrc/dcompat.cc
           dcmnet/libsrc/dimdump.cc
           dcmnet/libsrc/dimse.cc
           dcmnet/libsrc/dul.cc
           dcmnet/libsrc/dulconst.cc
           dcmnet/libsrc/dulfsm.cc
           dcmnet/libsrc/dulparse.cc

- Updated the code in module dcmqrdb to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/libsrc/dcmqrdbi.cc
           dcmqrdb/libsrc/dcmqrptb.cc

- Updated the code in module dcmwlm to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/libsrc/wlds.cc
           dcmwlm/libsrc/wldsfs.cc
           dcmwlm/libsrc/wlfsim.cc
           dcmwlm/libsrc/wlmactmg.cc
           dcmwlm/wwwapps/writwlst.cc

- Updated the code in module dcmimgle to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmimgle/apps/dcmdspfn.cc
           dcmimgle/apps/dcod2lum.cc
           dcmimgle/apps/dconvlum.cc
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/dicielut.h
           dcmimgle/include/dcmtk/dcmimgle/diflipt.h
           dcmimgle/include/dcmtk/dcmimgle/digsdlut.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/diinpxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoflt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/include/dcmtk/dcmimgle/dimoipxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopx.h
           dcmimgle/include/dcmtk/dcmimgle/dimoopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimopxt.h
           dcmimgle/include/dcmtk/dcmimgle/dimorot.h
           dcmimgle/include/dcmtk/dcmimgle/dimosct.h
           dcmimgle/include/dcmtk/dcmimgle/dirotat.h
           dcmimgle/include/dcmtk/dcmimgle/discalet.h
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/diciefn.cc
           dcmimgle/libsrc/dicielut.cc
           dcmimgle/libsrc/didispfn.cc
           dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmimgle/libsrc/digsdlut.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/diluptab.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmimgle/libsrc/dimomod.cc
           dcmimgle/libsrc/diovlay.cc
           dcmimgle/libsrc/diovlimg.cc
           dcmimgle/libsrc/diovpln.cc
           dcmimgle/libsrc/diutils.cc

- Updated the code in module dcmimage to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimage/include/dcmtk/dcmimage/dicoflt.h
           dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimage/include/dcmtk/dcmimage/dicoopx.h
           dcmimage/include/dcmtk/dcmimage/dicoopxt.h
           dcmimage/include/dcmtk/dcmimage/dicorot.h
           dcmimage/include/dcmtk/dcmimage/dicosct.h
           dcmimage/include/dcmtk/dcmimage/dihsvpxt.h
           dcmimage/include/dcmtk/dcmimage/dipalpxt.h
           dcmimage/include/dcmtk/dcmimage/diyf2pxt.h
           dcmimage/include/dcmtk/dcmimage/diyp2pxt.h
           dcmimage/libsrc/diargimg.cc
           dcmimage/libsrc/dicoimg.cc
           dcmimage/libsrc/dicopx.cc
           dcmimage/libsrc/dipalimg.cc
           dcmimage/libsrc/diquant.cc

- Updated the code in module dcmsr to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmsr/include/dcmtk/dcmsr/dsrcodtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcodvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcomtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcomvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcontn.h
           dcmsr/include/dcmtk/dcmsr/dsrcsidl.h
           dcmsr/include/dcmtk/dcmsr/dsrdattn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/include/dcmtk/dcmsr/dsrdtitn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgfr.h
           dcmsr/include/dcmtk/dcmsr/dsrimgtn.h
           dcmsr/include/dcmtk/dcmsr/dsrimgvl.h
           dcmsr/include/dcmtk/dcmsr/dsrnumtn.h
           dcmsr/include/dcmtk/dcmsr/dsrnumvl.h
           dcmsr/include/dcmtk/dcmsr/dsrpnmtn.h
           dcmsr/include/dcmtk/dcmsr/dsrreftn.h
           dcmsr/include/dcmtk/dcmsr/dsrscogr.h
           dcmsr/include/dcmtk/dcmsr/dsrscotn.h
           dcmsr/include/dcmtk/dcmsr/dsrscovl.h
           dcmsr/include/dcmtk/dcmsr/dsrsoprf.h
           dcmsr/include/dcmtk/dcmsr/dsrstrvl.h
           dcmsr/include/dcmtk/dcmsr/dsrtcodt.h
           dcmsr/include/dcmtk/dcmsr/dsrtcosp.h
           dcmsr/include/dcmtk/dcmsr/dsrtcotn.h
           dcmsr/include/dcmtk/dcmsr/dsrtcoto.h
           dcmsr/include/dcmtk/dcmsr/dsrtcovl.h
           dcmsr/include/dcmtk/dcmsr/dsrtextn.h
           dcmsr/include/dcmtk/dcmsr/dsrtimtn.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/include/dcmtk/dcmsr/dsruidtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavch.h
           dcmsr/include/dcmtk/dcmsr/dsrwavtn.h
           dcmsr/include/dcmtk/dcmsr/dsrwavvl.h
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcomtn.cc
           dcmsr/libsrc/dsrcomvl.cc
           dcmsr/libsrc/dsrcontn.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdattn.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrdtitn.cc
           dcmsr/libsrc/dsrimgfr.cc
           dcmsr/libsrc/dsrimgtn.cc
           dcmsr/libsrc/dsrimgvl.cc
           dcmsr/libsrc/dsrnumtn.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrpnmtn.cc
           dcmsr/libsrc/dsrreftn.cc
           dcmsr/libsrc/dsrscogr.cc
           dcmsr/libsrc/dsrscotn.cc
           dcmsr/libsrc/dsrscovl.cc
           dcmsr/libsrc/dsrsoprf.cc
           dcmsr/libsrc/dsrstrvl.cc
           dcmsr/libsrc/dsrtcodt.cc
           dcmsr/libsrc/dsrtcosp.cc
           dcmsr/libsrc/dsrtcotn.cc
           dcmsr/libsrc/dsrtcoto.cc
           dcmsr/libsrc/dsrtcovl.cc
           dcmsr/libsrc/dsrtextn.cc
           dcmsr/libsrc/dsrtimtn.cc
           dcmsr/libsrc/dsrtypes.cc
           dcmsr/libsrc/dsruidtn.cc
           dcmsr/libsrc/dsrwavch.cc
           dcmsr/libsrc/dsrwavtn.cc
           dcmsr/libsrc/dsrwavvl.cc

- Updated the code in module dcmsign to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmsign/apps/dcmsign.cc

- Updated the code in module dcmpstat to correctly compile when
  all standard C++ classes remain in namespace std.
  Affects: dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/apps/vrscan.h
           dcmpstat/include/dcmtk/dcmpstat/dvpspr.h
           dcmpstat/include/dcmtk/dcmpstat/dvsighdl.h
           dcmpstat/libsrc/dcmpstat.cc
           dcmpstat/libsrc/dviface.cc
           dcmpstat/libsrc/dvpsab.cc
           dcmpstat/libsrc/dvpsal.cc
           dcmpstat/libsrc/dvpscf.cc
           dcmpstat/libsrc/dvpsda.cc
           dcmpstat/libsrc/dvpsfs.cc
           dcmpstat/libsrc/dvpsga.cc
           dcmpstat/libsrc/dvpsgl.cc
           dcmpstat/libsrc/dvpsgr.cc
           dcmpstat/libsrc/dvpshlp.cc
           dcmpstat/libsrc/dvpsib.cc
           dcmpstat/libsrc/dvpsov.cc
           dcmpstat/libsrc/dvpspl.cc
           dcmpstat/libsrc/dvpspl2.cc
           dcmpstat/libsrc/dvpspll.cc
           dcmpstat/libsrc/dvpspr.cc
           dcmpstat/libsrc/dvpsprt.cc
           dcmpstat/libsrc/dvpsri.cc
           dcmpstat/libsrc/dvpsril.cc
           dcmpstat/libsrc/dvpsrs.cc
           dcmpstat/libsrc/dvpsrsl.cc
           dcmpstat/libsrc/dvpssp.cc
           dcmpstat/libsrc/dvpsspl.cc
           dcmpstat/libsrc/dvpssv.cc
           dcmpstat/libsrc/dvpstat.cc
           dcmpstat/libsrc/dvpstx.cc
           dcmpstat/libsrc/dvsighdl.cc
           dcmpstat/tests/msgserv.cc

**** Changes from 2006.08.15 (onken)

- Added WIN32 multiprocess mode capabilities to wlmscpfs.
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

- Removed superfluous output message.
  Affects: dcmwlm/libsrc/wlmactmg.cc

**** Changes from 2006.08.14 (eichelberg)

- Defined two new macros: STD_NAMESPACE is defined to std:: if the standard
  namespace exists and empty otherwise. OFendl is defined as std::endl if
  the standard namespace exists and as endl otherwise.
  Affects: ofstd/include/dcmtk/ofstd/ofstdinc.h
           ofstd/include/dcmtk/ofstd/ofstream.h

- Updated all code in module ofstd to correctly compile if the standard
  namespace has not included into the global one with a "using" directive.
  Affects: ofstd/include/dcmtk/ofstd/ofconsol.h
           ofstd/include/dcmtk/ofstd/ofdate.h
           ofstd/include/dcmtk/ofstd/ofdatime.h
           ofstd/include/dcmtk/ofstd/oflogfil.h
           ofstd/include/dcmtk/ofstd/ofstring.h
           ofstd/include/dcmtk/ofstd/oftime.h
           ofstd/libsrc/ofcmdln.cc
           ofstd/libsrc/ofconapp.cc
           ofstd/libsrc/ofconsol.cc
           ofstd/libsrc/ofdate.cc
           ofstd/libsrc/ofdatime.cc
           ofstd/libsrc/oflogfil.cc
           ofstd/libsrc/ofstring.cc
           ofstd/libsrc/oftime.cc
           ofstd/tests/tlist.cc
           ofstd/tests/tofdatim.cc
           ofstd/tests/tofstd.cc
           ofstd/tests/tstatof.cc
           ofstd/tests/tstftoa.cc
           ofstd/tests/tstlist.cc
           ofstd/tests/tstring.cc
           ofstd/tests/tststack.cc
           ofstd/tests/tstthred.cc

**** Changes from 2006.08.14 (onken)

- Added WIN32 multiprocess mode to wlmscpfs.
  Affects: dcmwlm/apps/wlcefs.cc
           dcmwlm/apps/wlcefs.h
           dcmwlm/libsrc/wlmactmg.cc
           dcmwlm/include/dcmtk/dcmwlm/wlmactmg.h

**** Changes from 2006.08.02 (eichelberg)

- Fixed bug in Makefile install target affecting MinGW and Cygwin.
  Thanks to Thies Jochimsen <<EMAIL>> for the bug report.
  Affects: dcmwlm/apps/Makefile.in

- Fixed bugs that prevented compiling the affected tools under MinGW
  Thanks to Thies Jochimsen <<EMAIL>> for the bug report.
  Affects: dcmdata/libsrc/mkdeftag.cc
           dcmdata/libsrc/mkdictbi.cc

- Added configure test for netapi32 library, needed for MinGW.
  Affects: config/configure
           config/configure.in

**** Changes from 2006.07.27 (onken)

- Fixed segfault in true lossless encoder in case of not enough pixel data.
  Minor corrections and enhancements regarding error handling and messages.
  Affects: dcmjpeg/libsrc/djcodece.cc

**** Changes from 2006.07.27 (riesmeier)

- Added support for DICOMDIR record type "STEREOMETRIC" (CP 628).
  Affects: dcmdata/include/dcmtk/dcmdata/dcddirif.h
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdirrec.cc

- Added full support for X-Ray Radiation Dose SR documents and Real World Value
  Mapping objects to DICOMDIR.
  Affects: dcmdata/libsrc/dcddirif.cc

- Renamed ERT_StructReport to ERT_SRDocument.
  Affects: dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcddirif.cc
           dcmdata/libsrc/dcdirrec.cc

- Changed parameter "exclusive" of method addOption() from type OFBool into an
  integer parameter "flags".
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimgle/apps/dcmdspfn.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/termscu.cc
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmsign/apps/dcmsign.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmwlm/apps/wlcefs.cc
           ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc

- Added new addOption() flag for internal options that are not shown in the
  syntax usage output. Used e.g. for the "--fork" option in storescp.
  Affects: ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc
           dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

- Prepended prefix "PF_" to parseLine() flags.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmftest.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/storescp.cc
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmpstat/tests/msgserv.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmsign/apps/dcmsign.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmwlm/apps/wlcefs.cc
           ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/include/dcmtk/ofstd/ofconapp.h
           ofstd/libsrc/ofcmdln.cc

- Option "--help" is no longer an exclusive option by default.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/apps/dcmconv.cc
           dcmdata/apps/dcmcrle.cc
           dcmdata/apps/dcmdrle.cc
           dcmdata/apps/dcmdump.cc
           dcmdata/apps/dcmgpdir.cc
           dcmdata/apps/dump2dcm.cc
           dcmdata/apps/mdfconen.cc
           dcmdata/apps/pdf2dcm.cc
           dcmdata/apps/xml2dcm.cc
           dcmimage/apps/dcm2pnm.cc
           dcmimage/apps/dcmquant.cc
           dcmimage/apps/dcmscale.cc
           dcmimgle/apps/dcmdspfn.cc
           dcmjpeg/apps/dcmcjpeg.cc
           dcmjpeg/apps/dcmdjpeg.cc
           dcmnet/apps/echoscu.cc
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/termscu.cc
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmmklut.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscp.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsmk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmpstat/apps/dcmpssnd.cc
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmsign/apps/dcmsign.cc
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/apps/xml2dsr.cc
           dcmwlm/apps/wlcefs.cc
           ofstd/include/dcmtk/ofstd/ofcmdln.h
           ofstd/libsrc/ofcmdln.cc

- Slightly changed behaviour of "exclusive" options like "--help" or
  "--version". Method parseLine() now returns PS_ExclusiveOption.
  Affects: ofstd/libsrc/ofcmdln.cc
           ofstd/libsrc/ofconapp.cc

- Made command line parameter "dcmfile-in" mandatory.
  Affects: dcmdata/apps/mdfconen.cc
           dcmdata/docs/dcmodify.man

- Print help text if no command line argument is specified. This is the default
  behaviour of most DCMTK tools.
  Affects: dcmdata/apps/mdfconen.cc

- Made naming conventions for command line parameters more consistent, e.g.
  used "dcmfile-in", "dcmfile-out" and "bitmap-out".
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/docs/dcm2pnm.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmnet/apps/findscu.cc
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescu.cc
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescu.man
           dcmpstat/apps/dcmmkcrv.cc
           dcmpstat/apps/dcmp2pgm.cc
           dcmpstat/apps/dcmprscu.cc
           dcmpstat/apps/dcmpschk.cc
           dcmpstat/apps/dcmpsprt.cc
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsprt.man
           dcmqrdb/apps/dcmqridx.cc
           dcmqrdb/docs/dcmqridx.man

- Added missing option "--version".
  Affects: dcmimgle/apps/dcmdspfn.cc

- Added optional library "LIBWRAP" to output of option "--version".
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmqrdb/apps/dcmqrscp.cc
           dcmwlm/apps/wlcefs.cc

- Removed option "ExpandWildcards" from parseComandLine() call.
  Affects: dcmnet/apps/termscu.cc

- Updated copyright date.
  Affects: dcmdata/apps/dcmodify.cc
           dcmimgle/apps/dcod2lum.cc
           dcmimgle/apps/dconvlum.cc
           dcmjpeg/apps/dcmj2pnm.cc

- Fixed typo.
  Affects: dcmimgle/libsrc/diimage.cc
           dcmqrdb/apps/dcmqrti.cc

- Fixed typo which caused compilation problems under Windows.
  Affects: ofstd/libsrc/ofcmdln.cc

**** Changes from 2006.07.25 (riesmeier)

- Added new command line options --always-expand-inline, --section-title-inline
  and --code-details-tooltip (according to new optional HTML rendering flags).
  Affects: dcmsr/apps/dsr2html.cc
           dcmsr/docs/dsr2html.man

- Added new optional flags for the HTML rendering of SR documents:
  HF_alwaysExpandChildrenInline, HF_useCodeDetailsTooltip and
  HF_renderSectionTitlesInline.
  Thanks to Gilles Mevel <<EMAIL>> for the original contribution.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrimgvl.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrscovl.cc
           dcmsr/libsrc/dsrtcovl.cc
           dcmsr/libsrc/dsrwavvl.cc
           dcmsr/libsrc/dsrtypes.cc

- Changed short option of --render-all-codes from +Ca to +Cc in order to avoid
  conflicts with option --charset-assume.
  Affects: dcmsr/apps/dsr2html.cc
           dcmsr/docs/dsr2html.man

- Enhanced robustness of reading methods by accepting SR documents where the
  value type of the root content item is absent or not "CONTAINER".
  Thanks to Gilles Mevel <<EMAIL>> for the original contribution.
  Affects: dcmsr/libsrc/dsrdoctr.cc

- Added new checking routine: The VerifyingObserverSequence is required if the
  value of VerificationFlag is "VERIFIED" (type 1C).
  Affects: dcmsr/libsrc/dsrdoc.cc

- Changed content and layout of document header in textual dump and HTML output,
  e.g. added StudyDescription and StudyID.
  Affects: dcmsr/libsrc/dsrdoc.cc

- Renamed member variable "ReferencedPerformedProcedureStepSequence" to
  "ReferencedPerformedProcedureStep" for consistency reasons.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrdoc.h

- Fixed minor layout and formatting issues.
  Affects: dcmsr/apps/dsr2xml.cc
           dcmsr/apps/dsrdump.cc
           dcmsr/docs/dsr2xml.man

**** Changes from 2006.07.24 (eichelberg)

- Fixed memory leak in private tag lookup cache.
  Thanks for Markus Erbacher <<EMAIL>> for reporting this bug.
  Affects: dcmdata/libsrc/dcpcache.cc

**** Changes from 2006.07.19 (riesmeier)

- Fixed wrong warning when HightBit = 0 and BitsStored = 1.
  Affects: dcmimgle/libsrc/diimage.cc

- Updated DCMTK_PREFIX and DCM_DICT_DEFAULT_PATH according to new directory
  scheme.
  Affects: config/include/dcmtk/config/cfwin32.h

- Added option WITH_PRIVATE_TAGS to CMake project file.
  Affects: CMakeLists.txt

**** Changes from 2006.07.18 (riesmeier)

- Added "#define" for the default directory where support data files reside.
  Affects: config/configure
           config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h

- Added support for "configure --pdfdir=<path>".
  Affects: config/Makefile.def.in

- Fixed minor layout and formatting issues.
  Affects: INSTALL

- Fixed minor inconsistencies in make file.
  Affects: dcmwlmxl/Makefile.in

**** Changes from 2006.07.17 (riesmeier)

- Updated GNU Autoconf to version 2.60 (with minor modifications to the
  installation directory scheme).
  Affects: config/autoall
           config/config.guess
           config/config.sub
           config/configure
           config/configure.in
           config/confmod
           config/confmod.in
           config/install-sh
           config/mkinstalldirs

- Updated CMake project files to version 2.4.2 of CMake.
  Affects: CMakeLists.txt
           INSTALL
           doxygen/CMakeLists.txt

- Revised installation directory scheme in order to be more compliant with the
  Filesystem Hierarchy Standard (FHS) and derived file system standards.
  Please note the changes after calling "make install".
  Affects: CMakeLists.txt
           INSTALL
           config/Makefile.def.in
           config/configure.in
           config/general.m4
           dcmdata/apps/CMakeLists.txt
           dcmdata/apps/Makefile.in
           dcmdata/docs/datadict.txt
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmftest.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmdata/libsrc/CMakeLists.txt
           dcmdata/libsrc/Makefile.in
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/docs/dcod2lum.man
           dcmimgle/docs/dconvlum.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrset.txt
           dcmqrdb/docs/dcmqrti.man
           dcmsign/docs/dcmsign.man
           dcmsr/apps/CMakeLists.txt
           dcmsr/apps/Makefile.in
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmwlm/docs/wlmscpfs.man

- Moved "$(DESTDIR)" from central Makefile.def to individual Makefiles
  (required for new installation directory scheme).
  Affects: Makefile
           config/Makefile.def.in
           config/rootconf
           dcmdata/apps/Makefile.in
           dcmdata/docs/Makefile.in
           dcmdata/include/Makefile.in
           dcmdata/libsrc/Makefile.in
           dcmimage/apps/Makefile.in
           dcmimage/include/Makefile.in
           dcmimage/libsrc/Makefile.in
           dcmimgle/apps/Makefile.in
           dcmimgle/include/Makefile.in
           dcmimgle/libsrc/Makefile.in
           dcmimgle/tests/Makefile.in
           dcmjpeg/apps/Makefile.in
           dcmjpeg/include/Makefile.in
           dcmjpeg/libsrc/Makefile.in
           dcmjpeg/libijg12/Makefile.in
           dcmjpeg/libijg16/Makefile.in
           dcmjpeg/libijg8/Makefile.in
           dcmnet/apps/Makefile.in
           dcmnet/docs/Makefile.in
           dcmnet/etc/Makefile.in
           dcmnet/include/Makefile.in
           dcmnet/libsrc/Makefile.in
           dcmpstat/apps/Makefile.in
           dcmpstat/include/Makefile.in
           dcmpstat/etc/Makefile.in
           dcmpstat/libsrc/Makefile.in
           dcmpstat/jni/Makefile.in
           dcmpstat/tests/Makefile.in
           dcmqrdb/apps/Makefile.in
           dcmqrdb/docs/Makefile.in
           dcmqrdb/etc/Makefile.in
           dcmqrdb/include/Makefile.in
           dcmqrdb/libsrc/Makefile.in
           dcmsign/apps/Makefile.in
           dcmsign/include/Makefile.in
           dcmsign/libsrc/Makefile.in
           dcmsr/apps/Makefile.in
           dcmsr/include/Makefile.in
           dcmsr/libsrc/Makefile.in
           dcmtls/docs/Makefile.in
           dcmtls/include/Makefile.in
           dcmtls/libsrc/Makefile.in
           dcmwlm/apps/Makefile.in
           dcmwlm/docs/Makefile.in
           dcmwlm/include/Makefile.in
           dcmwlm/libsrc/Makefile.in
           dcmwlm/wwwapps/Makefile.in
           doxygen/Makefile.in
           ofstd/include/Makefile.in
           ofstd/libsrc/Makefile.in

- Added "#define" for the default directory where configuration files reside.
  Affects: config/configure.in
           config/include/dcmtk/config/cfunix.h.in
           config/include/dcmtk/config/cfwin32.h

- Modified behaviour of option "--config" on Unix systems: By default, the file
  "dcmqrscp.cfg" in the configuration directory (e.g. "/usr/local/etc/dcmtk") is
  used.
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/apps/dcmqrti.cc
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man

- Corrected documentation of option "--max-pdu" (by default, the value from the
  configuration file is used).
  Affects: dcmqrdb/apps/dcmqrscp.cc
           dcmqrdb/docs/dcmqrscp.man

- Reworked file based on the fact that the shell script "config/mkinstalldirs"
  works recursively.
  Affects: config/Makefile.in
           dcmdata/include/Makefile.in
           dcmimage/include/Makefile.in
           dcmimgle/include/Makefile.in
           dcmjpeg/include/Makefile.in
           dcmnet/include/Makefile.in
           dcmpstat/include/Makefile.in
           dcmqrdb/include/Makefile.in
           dcmsign/include/Makefile.in
           dcmsr/include/Makefile.in
           dcmtls/include/Makefile.in
           ofstd/include/Makefile.in

- Added CHANGES and ANNOUNCE file to the list of files to be installed.
  Affects: CMakeLists.txt
           Makefile

- Extended file in order to install the "config" documentation.
  Affects: config/Makefile.in

- Added CMake project file in order to install the "config" documentation.
  Added:   config/docs/CMakeLists.txt
  Affects: config/CMakeLists.txt

- Fixed layout and formatting issues.
  Affects: configure
           config/docs/config.txt
           config/rootconf
           dcmjpeg/apps/dcmcjpeg.cc

- Replaced DCMTK version "3.5.4a" by "3.5.4 CVS".
  Affects: doxygen/htmldocs.cfg
           doxygen/manpages.cfg

**** Changes from 2006.07.14 (eichelberg)

- Added new command line option --sort-on-patientsname that sorts images into
  study folders named by the patient's name followed by a timestamp.
  Thanks to Prashant Nair <<EMAIL>> for the contribution
  on which the implementation is based.
  Affects: dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man

**** Changes from 2006.07.11 (riesmeier)

- Fixed wrong warning message about multiple Specific Character Set attributes
  in DICOMDIR files.
  Affects: dcmdata/apps/xml2dcm.cc

**** Changes from 2006.07.10 (riesmeier)

- Added support for 32-bit BMP images.
  Affects: dcmimage/apps/dcm2pnm.cc
           dcmimage/docs/dcm2pnm.man
           dcmimage/include/dcmtk/dcmimage/dicoimg.h
           dcmimage/libsrc/dicoimg.cc
           dcmimgle/include/dcmtk/dcmimgle/dcmimage.h
           dcmimgle/include/dcmtk/dcmimgle/diimage.h
           dcmimgle/include/dcmtk/dcmimgle/dimoimg.h
           dcmimgle/libsrc/dcmimage.cc
           dcmimgle/libsrc/diimage.cc
           dcmimgle/libsrc/dimoimg.cc
           dcmjpeg/docs/dcmj2pnm.man

- Fixed incorrect order of sample values in 32-bit DIB images.
  Thanks to Michael Doppler <<EMAIL>> for the report.
  Affects: dcmimage/include/dcmtk/dcmimage/dicopxt.h

**** Changes from 2006.07.03 (riesmeier)

- Fixed issue with handling of LUT data in DICOM objects with implicit VR.
  Thanks to Spencer Gunn <<EMAIL>> for the triggering
  report.
  Affects: dcmimgle/libsrc/didocu.cc

- Fixed layout and formatting issues.
  Affects: dcmdata/docs/dcmconv.man

**** Changes from 2006.06.28 (riesmeier)

- Added optional section for module "dcmpps".
  Affects: doxygen/htmldocs.cfg
           doxygen/htmldocs.dox

**** Changes from 2006.06.23 (eichelberg)

- Fixed incorrect default initialization of C-GET cancel struct.
  Thanks to Radoslaw Garbacz <<EMAIL>> for the bug report.
  Affects: dcmnet/libsrc/dimstore.cc

- All Store SCPs in DCMTK now store the source application entity title in the
  metaheader, both in normal and in bit-preserving mode.
  Thanks to Stefan Allers <<EMAIL>> for the suggestion and code.
  Affects: dcmnet/apps/Makefile.dep
           dcmnet/apps/movescu.cc
           dcmnet/apps/storescp.cc
           dcmnet/libsrc/dimse.cc
           dcmpstat/apps/dcmpsrcv.cc
           dcmqrdb/libsrc/Makefile.dep
           dcmqrdb/libsrc/dcmqrsrv.cc

**** Changes from 2006.06.15 (eichelberg)

- Added missing name to list of acknowledgements for release 3.5.4
  Affects: ANNOUNCE.354

- Slightly improved wording.
  Affects: INSTALL

- Updated configure test AC_CHECK_POINTER_TYPE to use a static_cast if
  available when trying to assign from an unknown type to unsigned long.
  Needed for Mac OS  X 10.3 with gcc 3.3.
  Affects: config/aclocal.m4
           config/configure
           config/configure.in

- Removed DOS line end character.
  Affects: config/include/dcmtk/config/cfunix.h.in

- Sample configuration file for storescp now prefers MPEG transfer syntax over
  uncompressed, if available.
  Affects: dcmnet/etc/storescp.cfg

**** Changes from 2006.06.01 (riesmeier)

- Removed explicit reference to a particular edition of the DICOM standard.
  Affects: dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h

**** Changes from 2006.05.30 (riesmeier)

- Added missing method containsExtendedCharacters().
  Affects: dcmdata/include/dcmtk/dcmdata/dcsequen.h
           dcmdata/libsrc/dcsequen.cc

- Modified comment of method containsExtendedCharacters().
  Affects: dcmdata/include/dcmtk/dcmdata/dcitem.h

**** Changes from 2006.05.11 (riesmeier)

- Added "offset" attribute to DICOMDIR record items.
  Affects: dcmdata/apps/dcm2xml.dtd
           dcmdata/include/dcmtk/dcmdata/dcdirrec.h
           dcmdata/libsrc/dcdirrec.cc

- Added new option that allows to omit the element name in the XML output.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man
           dcmdata/include/dcmtk/dcmdata/dctypes.h
           dcmdata/libsrc/dcelem.cc
           dcmdata/libsrc/dcsequen.cc
           dcmdata/libsrc/dctypes.cc

- Moved checkForNonASCIICharacters() from application to library.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/include/dcmtk/dcmdata/dcchrstr.h
           dcmdata/include/dcmtk/dcmdata/dcitem.h
           dcmdata/include/dcmtk/dcmdata/dcobject.h
           dcmdata/libsrc/dcchrstr.cc
           dcmdata/libsrc/dcitem.cc
           dcmdata/libsrc/dcobject.cc

- Moved containsExtendedCharacters() from dcmsr to dcmdata module.
  Affects: dcmsr/include/dcmtk/dcmsr/dsrcodtn.h
           dcmsr/include/dcmtk/dcmsr/dsrcodvl.h
           dcmsr/include/dcmtk/dcmsr/dsrcsidl.h
           dcmsr/include/dcmtk/dcmsr/dsrdoc.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctn.h
           dcmsr/include/dcmtk/dcmsr/dsrdoctr.h
           dcmsr/include/dcmtk/dcmsr/dsrnumtn.h
           dcmsr/include/dcmtk/dcmsr/dsrnumvl.h
           dcmsr/include/dcmtk/dcmsr/dsrpnmtn.h
           dcmsr/include/dcmtk/dcmsr/dsrsoprf.h
           dcmsr/include/dcmtk/dcmsr/dsrstrvl.h
           dcmsr/include/dcmtk/dcmsr/dsrtextn.h
           dcmsr/include/dcmtk/dcmsr/dsrtypes.h
           dcmsr/apps/dsr2html.cc
           dcmsr/apps/dsr2xml.cc
           dcmsr/libsrc/dsrcodtn.cc
           dcmsr/libsrc/dsrcodvl.cc
           dcmsr/libsrc/dsrcsidl.cc
           dcmsr/libsrc/dsrdoc.cc
           dcmsr/libsrc/dsrdoctn.cc
           dcmsr/libsrc/dsrdoctr.cc
           dcmsr/libsrc/dsrnumtn.cc
           dcmsr/libsrc/dsrnumvl.cc
           dcmsr/libsrc/dsrpnmtn.cc
           dcmsr/libsrc/dsrsoprf.cc
           dcmsr/libsrc/dsrstrvl.cc
           dcmsr/libsrc/dsrtextn.cc
           dcmsr/libsrc/dsrtypes.cc

- Added note on the current limitations when converting DICOMDIR files.
  Affects: dcmdata/docs/dump2dcm.man
           dcmdata/docs/xml2dcm.man

**** Changes from 2006.04.05 (riesmeier)

- Fixed issue with initialization of OFString member variables.
  Thanks to forum user "Aaron1" for the report.
  Affects: dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbg.h
           dcmqrdb/include/dcmtk/dcmqrdb/dcmqrcbm.h

**** Changes from 2006.03.29 (eichelberg)

- Added support for decompressing images with 16 bits/pixel compressed with
  a faulty lossless JPEG encoder that produces integer overflows in predictor 6.
  Affects: dcmjpeg/apps/dcmdjpeg.cc
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/include/dcmtk/dcmjpeg/djcparam.h
           dcmjpeg/include/dcmtk/dcmjpeg/djdecode.h
           dcmjpeg/libijg16/jdpred.c
           dcmjpeg/libijg16/jlossls16.h
           dcmjpeg/libijg16/jpeglib16.h
           dcmjpeg/libsrc/djcparam.cc
           dcmjpeg/libsrc/djdecode.cc
           dcmjpeg/libsrc/djdijg16.cc
           dcmjpeg/libsrc/djencode.cc

**** Changes from 2006.03.24 (riesmeier)

- Added full support for CP 545, i.e. allow by-reference HAS PROPERTIES
  relationships.
  Affects: dcmsr/libsrc/dsrmamcc.cc

**** Changes from 2006.03.23 (riesmeier)

- Added missing fclose() statement at the end of main().
  Thanks to forum user "ecoto" for the bug report and suggested fix.
  Affects: dcmdata/apps/dump2dcm.cc

**** Changes from 2006.03.23 (eichelberg)

- Fixed Win32 multi-process SCP code to also work correctly on Windows 2000.
  Thanks to Colby Dillion <<EMAIL>> for the bug fix.
  Affects: dcmnet/libsrc/dul.cc

**** Changes from 2006.03.17 (riesmeier)

- Fixed reference to a wrong version of the DCMTK. Revised line breaks.
  Affects: ANNOUNCE.354

- Updated copyright date.
  Affects: COPYRIGHT

**** Changes from 2006.02.23 (riesmeier)

- Added new default option --encode-hex.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man

- Fixed layout and formatting issues.
  Affects: dcmdata/apps/dcm2xml.cc
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmnet/apps/storescp.cc
           dcmnet/docs/storescp.man
           dcmsr/apps/dsr2xml.cc
           dcmsr/docs/dsr2xml.man
           dcmwlm/apps/wlcefs.cc
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2006.02.09 (riesmeier)

- Replaced OFIterator<> by OFListIterator() in order to compile if HAVE_STL
  is defined.
  Affects: ofstd/tests/tstlist.cc
           dcmdata/apps/mdfconen.cc

**** Changes from 2006.02.08 (riesmeier)

- Removed unused type definitions.
  Affects: dcmdata/include/dcmtk/dcmdata/dctypes.h

- Fixed minor layout, formatting and spelling issues.
  Affects: dcmdata/docs/dcmodify.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmqrdb/docs/dcmqrscp.man
           dcmwlm/docs/wlmscpfs.man

**** Changes from 2006.02.07 (riesmeier)

- Added note that on Windows systems a semicolon (";") is used as a separator
  for the DCMDICTPATH environment variable.
  Affects: dcmdata/docs/datadict.txt
           dcmdata/docs/dcm2xml.man
           dcmdata/docs/dcmconv.man
           dcmdata/docs/dcmcrle.man
           dcmdata/docs/dcmdrle.man
           dcmdata/docs/dcmdump.man
           dcmdata/docs/dcmgpdir.man
           dcmdata/docs/dcmodify.man
           dcmdata/docs/dump2dcm.man
           dcmdata/docs/pdf2dcm.man
           dcmdata/docs/xml2dcm.man
           dcmimage/docs/dcm2pnm.man
           dcmimage/docs/dcmquant.man
           dcmimage/docs/dcmscale.man
           dcmjpeg/docs/dcmcjpeg.man
           dcmjpeg/docs/dcmdjpeg.man
           dcmjpeg/docs/dcmj2pnm.man
           dcmjpeg/docs/dcmmkdir.man
           dcmnet/docs/echoscu.man
           dcmnet/docs/findscu.man
           dcmnet/docs/movescu.man
           dcmnet/docs/storescp.man
           dcmnet/docs/storescu.man
           dcmnet/docs/termscu.man
           dcmpstat/docs/dcmmkcrv.man
           dcmpstat/docs/dcmmklut.man
           dcmpstat/docs/dcmp2pgm.man
           dcmpstat/docs/dcmprscp.man
           dcmpstat/docs/dcmprscu.man
           dcmpstat/docs/dcmpschk.man
           dcmpstat/docs/dcmpsmk.man
           dcmpstat/docs/dcmpsprt.man
           dcmpstat/docs/dcmpsrcv.man
           dcmpstat/docs/dcmpssnd.man
           dcmqrdb/docs/dcmqridx.man
           dcmqrdb/docs/dcmqrscp.man
           dcmqrdb/docs/dcmqrti.man
           dcmsign/docs/dcmsign.man
           dcmsr/docs/dsr2html.man
           dcmsr/docs/dsr2xml.man
           dcmsr/docs/dsrdump.man
           dcmsr/docs/xml2dsr.man
           dcmwlm/docs/wlmscpfs.man

- Updated copyright date.
  Affects: dcmdata/docs/dcmftest.man
           dcmimgle/docs/dcmdspfn.man
           dcmimgle/docs/dcod2lum.man
           dcmimgle/docs/dconvlum.man

**** Changes from 2006.02.03 (riesmeier)

- Print help text if no command line argument is specified. This is the default
  behaviour of most DCMTK tools.
  Affects: dcmnet/apps/storescp.cc

- Fixed inconsistent source code layout.
  Affects: dcmimgle/libsrc/didocu.cc
           dcmimgle/libsrc/digsdfn.cc
           dcmnet/apps/storescp.cc
           dcmnet/apps/storescu.cc
           dcmqrdb/apps/dcmqrscp.cc

- Made makefiles more consistent with other modules.
  Affects: dcmdata/tests/Makefile.in
           dcmpstat/tests/Makefile.in
           dcmsr/tests/Makefile.in

**** Changes from 2006.02.02 (riesmeier)

- Added additional call to RANLIB for each installed library. Required for
  Mac OS X systems.
  Thanks to Thies Jochimsen <<EMAIL>> for the report.
  Affects: dcmdata/libsrc/Makefile.in
           dcmimage/libsrc/Makefile.in
           dcmimgle/libsrc/Makefile.in
           dcmjpeg/libsrc/Makefile.in
           dcmjpeg/libijg12/Makefile.in
           dcmjpeg/libijg16/Makefile.in
           dcmjpeg/libijg8/Makefile.in
           dcmnet/libsrc/Makefile.in
           dcmpstat/libsrc/Makefile.in
           dcmqrdb/libsrc/Makefile.in
           dcmsign/libsrc/Makefile.in
           dcmsr/libsrc/Makefile.in
           dcmtls/libsrc/Makefile.in
           dcmwlm/libsrc/Makefile.in
           ofstd/libsrc/Makefile.in

- Replaced "cd <directory> ; <command>" by "cd <directory> && <command>" in
  order to make sure that the <command> is only executed if the <directory>
  really exists.
  Affects: config/Makefile.in
           dcmdata/Makefile.in
           dcmdata/apps/Makefile.in
           dcmdata/include/Makefile.in
           dcmimage/Makefile.in
           dcmimage/apps/Makefile.in
           dcmimage/include/Makefile.in
           dcmimgle/Makefile.in
           dcmimgle/apps/Makefile.in
           dcmimgle/include/Makefile.in
           dcmjpeg/Makefile.in
           dcmjpeg/apps/Makefile.in
           dcmjpeg/include/Makefile.in
           dcmnet/Makefile.in
           dcmnet/apps/Makefile.in
           dcmnet/include/Makefile.in
           dcmpstat/Makefile.in
           dcmpstat/apps/Makefile.in
           dcmpstat/include/Makefile.in
           dcmqrdb/Makefile.in
           dcmqrdb/apps/Makefile.in
           dcmqrdb/include/Makefile.in
           dcmsign/Makefile.in
           dcmsign/apps/Makefile.in
           dcmsign/include/Makefile.in
           dcmsr/Makefile.in
           dcmsr/apps/Makefile.in
           dcmsr/include/Makefile.in
           dcmtls/Makefile.in
           dcmtls/apps/Makefile.in
           dcmtls/include/Makefile.in
           dcmwlm/Makefile.in
           dcmwlm/apps/Makefile.in
           dcmwlm/include/Makefile.in
           dcmwlm/wwwapps/Makefile.in
           doxygen/Makefile.in
           ofstd/Makefile.in
           ofstd/include/Makefile.in
           ofstd/tests/Makefile.in

- Added ANNOUNCE and CHANGES file to "make install".
  Affects: Makefile
           config/rootconf

- Removed installation of test program "wltest".
  Affects: dcmwlm/tests/Makefile.in

- Replaced Makefile target "libsrc-install-lib" by "libsrc-install" in order
  to be consistent with other modules.
  Affects: dcmdata/Makefile.in
           dcmimage/Makefile.in
           dcmimgle/Makefile.in
           dcmjpeg/Makefile.in
           dcmsr/Makefile.in
           dcmtls/Makefile.in

**** Changes from 2006.01.31 (onken)

- Fixed some commandline option checks in connection with true lossless
  switches.
  Affects: dcmjpeg/apps/dcmcjpeg.cc

**** Changes from 2006.01.30 (riesmeier)

- Updated man page to reflect support for additional transfer syntaxes.
  Affects: dcmqrdb/docs/dcmqrscp.man

- Fixed wrong hyphenation in man page.
  Affects: dcmnet/docs/termscu.man

**** Changes from 2006.01.27 (riesmeier)

- Updated document to reflect latest changes to the directory structure.
  Affects: INSTALL
           config/docs/config.txt
           config/docs/macros.txt
           config/docs/modules.txt

- Removed email address from CVS log.
  Affects: dcmwlm/include/dcmtk/dcmwlm/wlfsim.h
           dcmwlm/libsrc/wlfsim.cc

- Fixed issue with missing type 2 attributes in worklist files being reported
  as incomplete.  Now, the attributes are inserted automatically if required.
  Affects: dcmwlm/include/dcmtk/dcmwlm/wlfsim.h
           dcmwlm/libsrc/wlfsim.cc

**** Changes from 2006.01.25 (riesmeier)

- Updated Doxygen configuration files for version 1.4.6.
  Affects: doxygen/htmldocs.cfg
           doxygen/manpages.cfg

- Added references to additional documentation files.
  Affects: dcmdata/docs/dcmdata.dox
           dcmnet/docs/dcmnet.dox
           dcmtls/docs/dcmtls.dox
           dcmwlm/docs/dcmwlm.dox

- Fixed problem with non-public module dcmjp2k.
  Affects: doxygen/htmldocs.dox

**** Changes from 2006.01.18 (riesmeier)

- Updated version number to 3.5.4a.
  Affects: doxygen/htmldocs.cfg
           doxygen/manpages.cfg

- Translated remaining German comment.
  Affects: dcmdata/libsrc/dcitem.cc

- Added missing reference to new command line tool "pdf2dcm".
  Affects: dcmdata/docs/dcmdata.dox

**** Changes from 2006.01.17 (onken)

- Fixed "--key" option, which was broken when using the optional assignment
  ("=") operation inside the option value.
  Affects: dcmnet/apps/movescu.cc
           dcmnet/apps/findscu.cc

**** Changes from 2006.01.17 (riesmeier)

- Updated version number to 3.5.4(a).
  Affects: INSTALL
           config/configure
           config/configure.in

- Updated sections on installation and HTML documentation.
  Affects: INSTALL

- Updated document to reflect latest changes to the directory structure.
  Affects: dcmdata/docs/datadict.txt

- Reverted to old (pre 3.5.4) directory scheme for the data dictionary.
  Affects: config/configure
           config/configure.in

- Removed superfluous Perl script.
  Removed: config/cleandsp.pl

- Removed mkreport install target from CMake files in order to be consistent
  with Unix makefiles.
  Affects: dcmsr/tests/CMakeLists.txt

- By default, install private data dictionary to <prefix>/lib/private.dic.
  Affects: dcmdata/libsrc/Makefile.in

- Updated private data dictionary for Intelerad Medical Systems tags.
  Affects: dcmdata/libsrc/private.dic
           dcmdata/libsrc/dcdictzz.cc

- Fixed compilation problem with gcc 4.0 on Linux x86_64 (debug mode only).
  Affects: dcmimgle/include/dcmtk/dcmimgle/diinpxt.h

- Fixed syntax errors in debug code.
  Affects: dcmqrdb/apps/dcmqridx.cc
