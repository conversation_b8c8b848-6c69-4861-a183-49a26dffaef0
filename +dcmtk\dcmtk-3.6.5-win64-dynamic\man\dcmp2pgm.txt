dcmp2pgm(1)                       OFFIS DCMTK                      dcmp2pgm(1)



NAME
       dcmp2pgm - Read DICOM image and presentation state and render bitmap


SYNOPSIS
       dcmp2pgm [options] dcmimage-in [bitmap-out]

DESCRIPTION
       The dcmp2pgm utility renders a monochrome DICOM image under the control
       of a grayscale softcopy presentation state  object  into  a  monochrome
       bitmap with 8 bits/pixel. The bitmap is stored either as 'Portable Gray
       Map' (PGM) or  as  a  DICOM  secondary  capture  image  object.  If  no
       presentation  state  is read from file, a default presentation state is
       created. The utility  allows  to  read  a  configuration  file  of  the
       Softcopy  Presentation  State  Viewer  upon  startup. In this case, the
       settings from the configuration file affecting  the  rendering  of  the
       presentation  state are used, e.g. a correction of the gray scale range
       according to <PERSON><PERSON>'s model (DICOM part 14) can  be  performed  if  the
       characteristic  curve of the display system is available and defined in
       the configuration file.

PARAMETERS
       dcmimage-in  input DICOM image

       bitmap-out   output DICOM image or PGM bitmap

OPTIONS
   general options
         -h   --help
                print this help text and exit

              --version
                print version information and exit

              --arguments
                print expanded command line arguments

         -q   --quiet
                quiet mode, print no warnings and errors

         -v   --verbose
                verbose mode, print processing details

         -d   --debug
                debug mode, print debug information

         -ll  --log-level  [l]evel: string constant
                (fatal, error, warn, info, debug, trace)
                use level l for the logger

         -lc  --log-config  [f]ilename: string
                use config file f for the logger

   processing options
         -p   --pstate  [f]ilename: string
                process using presentation state file

         -c   --config  [f]ilename: string
                process using settings from configuration file

         -f   --frame  [f]rame: integer
                process using image frame f (default: 1)

   output format
         -D   --pgm
                save image as PGM (default)

         +D   --dicom
                save image as DICOM secondary capture

   output options
         +S   --save-pstate  [f]ilename: string
                save presentation state to file

NOTES
   Limitations
       Please note that the dcmp2pgm tool does not render all aspects that are
       defined  by the Grayscale Softcopy Presentation State (GSPS) object but
       only those that are supported  by  the  underlying  DCMTK  classes.  In
       particular,  textual  and  graphical annotations will not be visible in
       the output image. The reason is that these aspects of a GSPS object are
       typically  rendered by DICOMscope, the graphical user interface that is
       put on top of the related DCMTK classes and written in Java.

       Also concepts that were introduced after the initial definition of  the
       GSPS  IOD (see Supplement 33) are ignored. This includes Supplement 120
       with its extended features like e.g. Compound Graphics.

LOGGING
       The level of logging output of  the  various  command  line  tools  and
       underlying  libraries  can  be  specified by the user. By default, only
       errors and warnings are written to the  standard  error  stream.  Using
       option  --verbose  also  informational messages like processing details
       are reported. Option --debug can be used to get  more  details  on  the
       internal  activity,  e.g.  for debugging purposes. Other logging levels
       can be selected using option --log-level. In --quiet  mode  only  fatal
       errors  are reported. In such very severe error events, the application
       will usually terminate. For  more  details  on  the  different  logging
       levels, see documentation of module 'oflog'.

       In  case  the logging output should be written to file (optionally with
       logfile rotation), to syslog (Unix) or the event log  (Windows)  option
       --log-config  can  be  used.  This  configuration  file also allows for
       directing only certain messages to a particular output stream  and  for
       filtering  certain  messages  based  on the module or application where
       they are generated.  An  example  configuration  file  is  provided  in
       <etcdir>/logger.cfg.

COMMAND LINE
       All  command  line  tools  use  the  following notation for parameters:
       square brackets enclose optional  values  (0-1),  three  trailing  dots
       indicate  that multiple values are allowed (1-n), a combination of both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or  '-' sign, respectively. Usually, order and position of command line
       options are arbitrary (i.e. they  can  appear  anywhere).  However,  if
       options  are  mutually exclusive the rightmost appearance is used. This
       behavior conforms to the  standard  evaluation  rules  of  common  Unix
       shells.

       In  addition,  one  or more command files can be specified using an '@'
       sign as a prefix to the filename (e.g. @command.txt).  Such  a  command
       argument  is  replaced  by  the  content of the corresponding text file
       (multiple whitespaces are treated as a  single  separator  unless  they
       appear  between  two  quotation marks) prior to any further evaluation.
       Please note that a command file cannot contain  another  command  file.
       This  simple  but  effective  approach  allows  one to summarize common
       combinations of options/parameters and  avoids  longish  and  confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

ENVIRONMENT
       The  dcmp2pgm  utility  will  attempt  to  load DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

FILES
       <etcdir>/dcmpstat.cfg - sample configuration file

COPYRIGHT
       Copyright (C) 1998-2017 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                    dcmp2pgm(1)
