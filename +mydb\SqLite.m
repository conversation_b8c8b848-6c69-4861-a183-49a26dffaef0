classdef SqLite <OptionsMap
    properties
        m_conn; 
    end
    
    methods
        function obj = SqLite(dbfile, varargin)
            obj = obj@OptionsMap(varargin{:});
            obj.setOption('dbfilename', dbfile);
            if ~isempty(dbfile)
                obj.OpenDB();
            end
        end
        
              
        function FromXls(self, xlsfile, names)
            sheets = sheetnames(xlsfile);
            names  = intersect(upper(sheets), upper(names));
            for k=1:numel(names)
                name = names{k}; 
                T    = readtable(xlsfile, 'sheet', name);
                varnames = T.Properties.VariableNames; 
                PRIMARY_KEY = varnames{k};
                self.CreateTable(T, name, PRIMARY_KEY);
            end
        end
        
        function CreateTable(self, T, tablename, PRIMARY_KEY, varargin)
            %str = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" ("SOPInstanceUID" TEXT PRIMARY KEY  NOT NULL  UNIQUE', modality); 
           %str   = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" ("%s" TEXT PRIMARY KEY  NOT NULL  UNIQUE', tablename, PRIMARY_KEY); 
           names = T.Properties.VariableNames; 
           %names = self.ModalityTags(modality);
           str   = sprintf('CREATE  TABLE  IF NOT EXISTS "main"."%s" (', tablename);
           N = numel(names);
           for k=1:N
                name = names{k};
                if strcmpi(name, PRIMARY_KEY)
                    TEXT = 'TEXT PRIMARY KEY  NOT NULL  UNIQUE';
                else
                    TEXT = 'TEXT'; 
                end
                if k<N
                    str1 = [' "' name '" ' TEXT ',']; 
                else
                    str1 = [' "' name '" ' TEXT ')']; 
                end
                str = cat(2, str, str1); 
            end
           
            if isempty(self.m_conn)
                self.OpenDB();
            end
            mksqlite(self.m_conn, str); 
            
            InsertTable(self, T, tablename);
        end
        
        function InsertTable(self, T, tablename)
            if istable(T)
                T = table2struct(T);
            end
            varnames = fieldnames(T); 
            mksqlite(self.m_conn, 'BEGIN');
            for k=1:numel(T)
                mksqlite(self.m_conn, self.struct2sqlInsert(T(k), tablename, varnames));
            end
             mksqlite(self.m_conn, 'COMMIT');
        end
        
        function res = DBFileName(self)
             %res = fullfile(self.rootPath, 'dcmdb1.sqlite');
             res = self.getoptioni('dbfilename');
        end
        
        function OpenDB(self)
            self.CloseDB(); 
            self.m_conn = mksqlite(0, 'open', DBFileName(self));
        end
        
        function CloseDB(self)
            if ~isempty(self.m_conn)
                mksqlite(self.m_conn, 'close');
            end
            self.m_conn = []; 
        end
        
        function res = sqlQuery(self, sqlcmd)
            res = mksqlite(self.m_conn, sqlcmd);
        end
        
        function T = Query(self, tablename, quertstr)
            if ~exist('quertstr', 'var') 
                quertstr=''; 
            end
            cmd = sprintf(['SELECT * FROM main.' tablename  ' ' quertstr]);
            res = self.sqlQuery(cmd);
            T = struct2table(res); 
        end
    end
    
    methods(Static)
        function sql = struct2sqlInsert(s, tableName, fieldnames)
            sql = sprintf('INSERT OR REPLACE INTO "main"."%s" (%s) VALUES (%s)',...
                tableName,...
                strjoin(cellfun(@(x)sprintf('"%s"',x), fieldnames, 'UniformOutput', false), ','),...
                strjoin(cellfun(@(x)sprintf('"%s"',s.(x)), fieldnames, 'UniformOutput', false), ','));
        end
    end
end

