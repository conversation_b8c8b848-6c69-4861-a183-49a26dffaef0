classdef RtPlan < daemon.taskdef.TaskDef
    properties
        
    end

    methods
        function obj = RtPlan(varargin)         
            <EMAIL>(varargin{:});
            obj.SetDefaultCfg;
        end
    end

    methods (Static)
        function SynCT(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            taskdefname  = ['MR_SynCT2Dcm'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder='SynCTm_Unity2mm';
            synctfile =['image.nii.gz'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=struct(...
	     	    "filename",     synctfile,...
	     	    "taskfilename", "../MetsSeg/SynCTm_Unity2mm/MR.[SeriesInstanceUID].tsk");
            tempfname= ['../image_dcm.json'];
            copytags = cat(2, dicom.utils.VhImage2Dcm.Tags_Patient(), dicom.utils.VhImage2Dcm.Tags_Study());
            copytags = xls.TableBase.Content2Str(copytags);
            dcminfos{1} = struct('FileName', tempfname, 'CopyTagName', copytags);
            dcminfos{2} = struct('SeriesDescription', 'MR2SynCT', 'Manufacturer', 'MR2SynCT');
            Process = struct('OperationType','VolImage2Dcm','VolImage',synctfile, 'Modality', 'CT', 'OutputFolder','dcm/');
            %Process = str; 
            Process.DcmInfo=dcminfos; 
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});

            taskdefname  = ['MR_SynCT2TPS'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder='SynCTm_Unity2mm';
            
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=struct(...
	     	    "filename",     ['MR_SynCT2Dcm.tsk'],...
	     	    "taskfilename", 'MR_SynCT2Dcm/MR.[SeriesInstanceUID].tsk');
            Process =struct('OperationType','ExportDcm', ... 
                'InputDcm', ['dcm'],...
                'DcmExportPort', 'TPS');
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'IsCustomTask', 1});
        end

        function Factory_InitPlan(tasksdeffile)
            %tasksdeffile = ['tasks_initplan.def'];
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            taskdefname  = ['RP_FluenceMotion'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            DcmRTPlanFile ='../../../DataBase/DicomData/[PatientID]/[StudyInstanceUID]/RTPlan.[SeriesInstanceUID]/[SOPInstanceUID].dcm';
            Dependency=[];
            Process   = struct('OperationType','DcmRTPlan2FluenceMotion',...
                'DcmRTPlanFile', DcmRTPlanFile, ...
                'PlanMotionFile', 'PlanMotion.json', 'PlanFluenceFile', 'PlanFluence');
            obj.Generate(tasksdeffile, Dependency, Process);

            taskdefname  = ['RP_FluenceMotionAper'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=[];
            Process   = struct('OperationType','DcmRTPlan2FluenceMotion',...
                'DcmRTPlanFile', DcmRTPlanFile, ...
                'PlanMotionFile', 'PlanMotion.json', 'PlanFluenceFile', 'PlanFluence', 'PlanAperFile', 'PlanAper');
            obj.Generate(tasksdeffile, Dependency, Process);


            taskdefname  = ['RP_CT2Density'];
            ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=[];
            Process   = struct('OperationType',"CT2Density", "CTFile", ctfile,...
        	    "DensityFile", "DensityImage");
            obj.Generate(tasksdeffile, Dependency, Process);

            taskdefname  = ['RP_MR2Density'];
            synctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/SynCTm_Unity2mm/image.nii.gz";
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            Dependency=struct(...
	     	    "filename",     synctfile,...
	     	    "taskfilename", "../MetsSeg/SynCTm_Unity2mm/MR.[ReferencedImageSeriesUID].tsk");
            Process   = struct('OperationType',"CT2Density","CTFile", synctfile,...
        	"DensityFile", "DensityImage");
            obj.Generate(tasksdeffile, Dependency, Process);

            taskdefname  = ['RP_Image2Density'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});   
            Filter = struct("FieldName", "ReferencedImageModality",...
                 "MatchMode", "regexpi", "FieldValue",'MR', "Inclusion", true);
            Condition=struct("ConditionType", "InfoFilter",    "InfoSeq", 1,'Filter', Filter);
            process   = struct('OperationType', "CreateTaskFile", 'Condition', Condition,...
                'TaskFileName', 'RP_MR2Density/RP.[SOPInstanceUID].tsk');
            Processes{1} = process;
            process.Condition.Filter.FieldValue='CT';
            process.('TaskFileName')= 'RP_CT2Density/RP.[SOPInstanceUID].tsk';
            Processes{2} = process;
            Dependency=[];
            obj.Generate(tasksdeffile, Dependency, Processes);

            daemon.taskdef.RtPlan.InitPrescription(tasksdeffile);

            daemon.taskdef.RtPlan.CalcInitPlan(tasksdeffile);

            daemon.taskdef.RtPlan.SynCT(tasksdeffile);
        end

        function CalcInitPlan(tasksdeffile)
           taskdeffolder = [fileparts(tasksdeffile) '/'];
           tempdef = utils.json.readJson('C:\Source\matlab\dcmserver\src\+daemon\+taskdef\temp_InitPlan_[RTBeamType].taskdef'); 
           %tasksdeffile = [taskdeffolder 'tasks_initplan.def'];
           taskdefname  = ['temp_CalcInitPlan'];
           obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           Dependency=struct(...
     	    "filename",     '[OptMaskFile]');
           Process = tempdef.Process; 
           DefaultSettings=tempdef.DefaultSettings; 
           obj.Generate(tasksdeffile, Dependency, Process, {'DefaultSettings', DefaultSettings});

           beamtypes    = {'unity'};
           beamcommroot = 'C:\ARTDaemon\distribute\wtkapp\CommBeam\'; 
           for k=1:numel(beamtypes)
               beamtype = beamtypes{k};
               taskdefname  = ['temp_CalcInitPlan_' beamtype];
               vh = VolHeader([beamcommroot beamtype '\resource\oasmap.header']);
               tempdef1 = tempdef; 
               Info=struct('RTBeamType', beamtype, ...
                   "SrcMotionFile", "[SrcMotionFile]",...
                   "OutBixelFile", "InitBixelMap", ...
                   "OutFluenceFile", "InitFluenceMap", ...
                   "OutMotionFile", "InitMotion.json", ...
                   "FieldSizeX", num2str(round(vh.x_dim*vh.x_pixdim, 2)), ...
                   "FieldSizeY", num2str(round(vh.y_dim*vh.y_pixdim, 2)), ...
                   "UseTargetCenter", "0");
               obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
               Dependency=struct("filename",     '[OptMaskFile]');
               
               Process = tempdef1.Process; 
               DefaultSettings=tempdef1.DefaultSettings; 
               Process = DosUtil.WildCardRepStr_recursive(Process, Info);
               Process = DosUtil.WildCardRepStr_recursive(Process, DefaultSettings);
               obj.Generate(tasksdeffile, Dependency, Process);
           end

           for k=1:numel(beamtypes)
               beamtype = beamtypes{k};
               taskdefname  = ['RP_CalcInitPlan_' beamtype];
               obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
               Dependency=[]; m=0; 
               OptMaskFile  = '../InitPrescription_focal/OptMaskImage.nii.gz';
               SrcMotionFile= '../PlanMotion.json';
               CustomSubFolder=taskdefname;

               m=m+1; Dependency{m}=struct(...
     	        "filename",    SrcMotionFile, ...
                "taskfilename", "RP_FluenceMotion/RP.[SOPInstanceUID].tsk");

               m=m+1; Dependency{m}=struct(...
     	        "filename",    OptMaskFile, ...
                "taskfilename", "RP_InitPrescription_focal/RP.[SOPInstanceUID].tsk");
               
               TaskInfo=struct("FileName", ['temp_CalcInitPlan_' beamtype '/RP.[SOPInstanceUID].tskinfo']);
               TaskInfo.Info=struct(...
				        "TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' CustomSubFolder '/'],...
                        'SrcMotionFile', SrcMotionFile,...
                        "OptMaskFile",OptMaskFile);
                m=m+1; Dependency{m}=struct("filename",     "InitMotion.json",...
                    "TaskInfo", TaskInfo);

                TaskInfo=struct("FileName", ['temp_CalcBeamTraceImage/RP.[SOPInstanceUID].tskinfo']);
                TaskInfo.Info=struct(...
                    "RTBeamType", beamtype,...
					"TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' CustomSubFolder '/'],...
					"SrcMotionFile", "InitMotion.json", ...
					"FluenceFile", "InitFluenceMap.nii.gz", ...
                    "DensityFile", "../DensityImage.nii.gz",...
                    "DoseFile", "BeamTraceImage");
                m=m+1; Dependency{m}=struct("filename",     "BeamTraceImage.nii.gz",...
                "TaskInfo", TaskInfo);
               obj.Generate(tasksdeffile, Dependency, [], {'CustomSubFolder', CustomSubFolder});
           end
        end

        function Factory_BBFMO(tasksdeffile)
           taskdeffolder = [fileparts(tasksdeffile) '/'];
           tempdef = utils.json.readJson('C:\Source\matlab\dcmserver\src\+daemon\+taskdef\temp_BBFMO.taskdef'); 
           %tasksdeffile = [taskdeffolder 'tasks_initplan.def'];
           taskdefname  = ['temp_BBFMO'];
           obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           Dependency=struct(...
     	    "filename",     '[OptMaskFile]');
           Process = tempdef.Process; 
           DefaultSettings=tempdef.DefaultSettings; 
           obj.Generate(tasksdeffile, Dependency, Process, {'DefaultSettings', DefaultSettings});

           beamtypes    = {'unity'};
           beamcommroot = 'C:\ARTDaemon\distribute\wtkapp\CommBeam\'; 
           for k=1:numel(beamtypes)
               beamtype = beamtypes{k};
               
               vh = VolHeader([beamcommroot beamtype '\resource\oasmap.header']);
               tempdef1 = tempdef; 
               Info=struct('RTBeamType', beamtype, ...
                   "InitSrcMotionFile", "[InitSrcMotionFile]",...
                   "InitBixelMapFile", "[InitBixelMapFile]", ...
                   "PredDoseFile" ,"[PredDoseFile]", ...
                   "DensityFile" ,"[DensityFile]", ...
                   "FieldSizeX", num2str(round(vh.x_dim*vh.x_pixdim, 2)), ...
                   "FieldSizeY", num2str(round(vh.y_dim*vh.y_pixdim, 2)), ...
                   "UseTargetCenter", "0");
               taskdefname  = ['temp_BBFMO_' beamtype '_preddose'];
               obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
               Dependency=struct(...BeamTraceImage
     	        "filename",     '[OptMaskFile]|[PredDoseFile]|[InitBixelMapFile]|[InitSrcMotionFile]');
               
               Process = tempdef1.Process; 
               DefaultSettings=tempdef1.DefaultSettings; 
               Process = DosUtil.WildCardRepStr_recursive(Process, Info);
               Process = DosUtil.WildCardRepStr_recursive(Process, DefaultSettings);
               obj.Generate(tasksdeffile, Dependency, Process);
           end

           for k=1:numel(beamtypes)
               beamtype = beamtypes{k};
               taskdefname  = ['RP_BBFMO_' beamtype '_preddose'];
               obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
               Dependency=[]; m=0; 
               
               OptMaskFile  = '../InitPrescription_focal/OptMaskImage.nii.gz';
               PredDoseFile = ['../RP_PredDose_nnUNET_unity_initbeam5mm/PredDose.nii.gz'];
               InitSrcMotionFile= ['../RP_CalcInitPlan_unity/InitMotion.json'];
               InitBixelMapFile= ['../RP_CalcInitPlan_unity/InitBixelMap.nii.gz'];
               CustomSubFolder=taskdefname;

               m=m+1; Dependency{m}=struct(...
     	        "filename",    OptMaskFile, ...
                "taskfilename", "RP_InitPrescription_focal/RP.[SOPInstanceUID].tsk");

               m=m+1; Dependency{m}=struct(...
     	        "filename",    PredDoseFile, ...
                "taskfilename", "RP_PredDose_nnUNET_unity_initbeam5mm/RP.[SOPInstanceUID].tsk");

               m=m+1; Dependency{m}=struct(...
     	        "filename",    InitBixelMapFile, ...
                "taskfilename", "RP_CalcInitPlan_unity/RP.[SOPInstanceUID].tsk");

               TaskInfo=struct("FileName", ['temp_BBFMO_' beamtype '_preddose/RP.[SOPInstanceUID].tskinfo']);
               DoseFileName = 'OptDose';
               DoseFile = [DoseFileName '.nii.gz'];
               DVHFile = [DoseFileName '_dvhs.json'];
               TaskInfo.Info=struct(...
					"TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' CustomSubFolder '/'],...
					"InitSrcMotionFile", InitSrcMotionFile, ...
                    "InitBixelMapFile", InitBixelMapFile, ...
					"PredDoseFile", PredDoseFile, ...
                    "OptMaskFile", OptMaskFile, ...
                    "DensityFile", "../DensityImage.nii.gz", ...
                    "OutDoseFile", DoseFileName);
               m=m+1; Dependency{m}=struct("filename",     [DoseFile],...
                "TaskInfo", TaskInfo);

       
               m=0; Process=[];
               ROIMaskFile  = '../../RS.[ReferenceStructureSetUID]/roimask.nii.gz'; 
               m=m+1; Process{m}=struct('OperationType', 'CalcDVH',...
                   'ROIMaskImage', ROIMaskFile, 'DoseImage', [DoseFile], 'DVHFile', [DVHFile] );

               AssociateFileName='../dcmdata.cfg';
               ListName = 'DoseSet';
               Info=struct("ID", taskdefname, "DoseFileName", [CustomSubFolder '/' DoseFile],...
                     "DVHFileName",  [CustomSubFolder '/' DVHFile]);
               m=m+1; Process{m}=struct('OperationType', 'AssociateStruct', 'AssociateFileName', AssociateFileName,...
                    'ListName', ListName, 'Info', Info);
               obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});
           end
        end

        function InitPrescription(tasksdeffile)
            %tasksdeffile = [taskdeffolder 'tasks_initplan.def'];
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            taskdefname='temp_InitPrescription';
            Input=struct('GoalTableFile', '[GoalTableFile]', ...
                'ROIMaskImage', '[ROIMaskFile]', ...
                'BodyMaskImage', '[BodyMaskFile]', ...
                'DensityImage', '[DensityFile]');
            Output=struct('OptMaskImage',  "OptMaskImage",...
                'PrescriptionImage', "PrescriptionImage", ...
                'AvoidanceImage', "AvoidanceImage",...
                'PrescriptionTable', "Prescription.xlsx");
            Dependency=struct("filename",'[GoalTableFile]|[ROIMaskFile]|[DensityFile]');
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            process = struct('OperationType', "InitPrescription",'Input', Input, 'Output', Output);
            obj.Generate(tasksdeffile, Dependency, process);

            taskdefname='RP_InitPrescription_focal';
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            CustomSubFolder= 'InitPrescription_focal';
            obj.SetDef_struct(struct("CustomSubFolder", CustomSubFolder));
            GoalTableFile= '../../../../DataBase/DicomData/[PatientID]/focalGoal.xlsx';
            DensityFile= "../DensityImage.nii.gz";
            ROIMaskFile='../../RS.[ReferenceStructureSetUID]/roimask.nii.gz';
            m=0; Dependency=[];
            m=m+1; Dependency{m}=struct("filename",     DensityFile,...
                "taskfilename", "RP_Image2Density/RP.[SOPInstanceUID].tsk");
            %obj.AddDependency(Dependency);

            m=m+1; Dependency{m}=struct("filename",     ROIMaskFile,...
                "taskfilename", "../DcmConverter/DCMCONVERT_RS_2mm/RS.[ReferenceStructureSetUID].tsk");
            %obj.AddDependency(Dependency);

            TaskInfo=struct("FileName", ['../Focal/Focal_ExtractGoalTable_Patient_init/[PatientID].tskinfo']);
            TaskInfo.Info=struct('PatientID', '[PatientID]');
            m=m+1; Dependency{m}=struct("filename",     GoalTableFile,...
                "TaskInfo", TaskInfo);
 
            TaskInfo=struct("FileName", ['temp_InitPrescription' '/RP.[SOPInstanceUID].tskinfo']);
            TaskInfo.Info=struct(...
					"TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' CustomSubFolder '/'],...
                    'GoalTableFile', GoalTableFile,...
                    "ROIMaskFile",ROIMaskFile,...
                    "DensityFile", DensityFile);
            m=m+1; Dependency{m}=struct("filename",     "PrescriptionImage.nii.gz",...
                "TaskInfo", TaskInfo);
            obj.Generate(tasksdeffile, Dependency, []);
        end

        function RP_CalcDose(taskdeffolder, calcname, ext)
            if ~exist('ext', 'var')
                ext = '';
            end
            FluenceFile= "../PlanFluence.nii.gz";
            if strcmpi(ext, '_aper')
                FluenceFile= "../PlanAper.nii.gz";
            end
            tasksdeffile = [taskdeffolder 'tasks_calcdose.def'];
            taskdefname  = ['RP_CalcDose' '_' calcname ext];
           %TaskDoneIndicator=[taskdefname '.tsk']; 
           obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.SetDef_struct(struct("CustomSubFolder", [calcname ext]));
            m=0; Dependency=[];
            m=m+1; Dependency{m}=struct("filename",     "../DensityImage.nii.gz",...
                "taskfilename", "RP_Image2Density/RP.[SOPInstanceUID].tsk");
            %obj.AddDependency(Dependency);
            m=m+1; Dependency{m}=struct("filename",     "../PlanFluence.nii.gz",...
                "taskfilename", "RP_FluenceMotionAper/RP.[SOPInstanceUID].tsk");
            %obj.AddDependency(Dependency);
            TaskInfo=struct("FileName", ['temp_CalcDose' '_' calcname '/RP.[SOPInstanceUID].tskinfo']);
            TaskInfo.Info=struct(...
					"DoseCalcName", calcname,...
					"TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' calcname ext '/'],...
					"SrcMotionFile", "../PlanMotion.json", ...
					"FluenceFile", FluenceFile, ...
                    "DensityFile", "../DensityImage.nii.gz",...
                    "DoseFile", "PlanDose");
            m=m+1; Dependency{m}=struct("filename",     "PlanDose.nii.gz",...
                "TaskInfo", TaskInfo);
            obj.Generate(tasksdeffile, Dependency, []);
        end

        function Factory_CalcDose(tasksdeffile)
           %tasksdeffile = [taskdeffolder 'tasks_calcdose.def'];
            taskdeffolder = [fileparts(tasksdeffile) '/'];
           calcnames = {'FCBB0', 'FCBB', 'gDPM'};
           for k=1:numel(calcnames)
               calcname = calcnames{k};
               daemon.taskdef.RtPlan.RP_CalcDose(taskdeffolder, calcname, '');
               if k==1
               daemon.taskdef.RtPlan.RP_CalcDose(taskdeffolder, calcname, '_aper');
               end
               Input = struct('SrcMotion', '[SrcMotionFile]', 'Fluence', '[FluenceFile]', 'Density', '[DensityFile]');
               Output= struct('DoseFile', '[DoseFile]');
               Dependency=struct(...
	     	    "filename",     '[SrcMotionFile]|[FluenceFile]|[DensityFile]');
               m=0; AppOptions=[]; 
               m=m+1; AppOptions{m}=struct("OptionName", "exefile","OptionValue", "C:/ARTDaemon/distribute/wtkapp/bin/appDoseFactory.exe");
               m=m+1; AppOptions{m}=struct("OptionName", 'Calculation.DoseUnit',"OptionValue", 'Gy');
               m=m+1; AppOptions{m}=struct("OptionName", "Density.ResizePixdim","OptionValue", [0.2 0.2 0.2]);
               m=m+1; AppOptions{m}=struct("OptionName", "Density.MaxImageDim","OptionValue", [300 300 300]);
               
              
               % AppOptions{2}=struct("OptionName", "in.RTBeamType","OptionValue", "[RTBeamType]");
               %AppOptions{3}=struct("OptionName", 'DosePostProcessing',"OptionValue", '[DosePostProcessing]');
               process   = struct('OperationType', "CalcDose","Input", Input,...
        	    "Output", Output, 'DoseCalcName', calcname); 
               process.("AppOptions")=AppOptions;
               taskdefname  = ['temp_CalcDose' '_' calcname];
               obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
               obj.Generate(tasksdeffile, Dependency, process);
               
               if k==1
                   process.('OperationType')='CalcBeamTraceImage';
                   taskdefname  = ['temp_CalcBeamTraceImage'];
                   obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
                   obj.Generate(tasksdeffile, Dependency, process);
               end
           end

           Dependency=struct(...
	     	    "filename",     '[DoseImage]|[ROIMaskImage]');
           process   = struct('OperationType', "CalcDVH","ROIMaskImage", '[ROIMaskImage]',...
    	    "DoseImage", '[DoseImage]', 'DVHFile', '[DVHFile]'); 
           taskdefname  = ['temp_CalcDVH'];
           obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           obj.Generate(tasksdeffile, Dependency, process); 
        end

        function Factory_PredDose_unity(tasksdeffile)
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.unity20241130\';
            datesetnums={'513', '523', '533'};
            modalities ={'normbeam', 'initbeam', 'initbeam5mm'};
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_unity20241130_prescription-avoidance-' modality '2doseNormRx' TrainerName]), ...
                 datesetnums, modalities, 'UniformOutput',false);
            
            %tasksdeffile = [taskdeffolder 'tasks_PredDose_unity.def'];
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            %subfoldername = 'PredDose_unity';
            InputImage = "[PrescriptionFile]|[AvoidanceFile]|[BeamImageFile]";
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                OutputFile ='PredDose';
                %dependtask ="../DcmConverter/DCMCONVERT_RP/RP.[SOPInstanceUID].tsk";
                dependency = struct("filename", InputImage);
                taskdefname = ['temp_PredDose_nnUNET_unity_' modality];
                obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
                Process=struct('OperationType', 'PredDose_nnUNET', ...
                     'ModelOptionFile', ModelOptionFile, ...
                     'InputImage',  InputImage, ...
                     'OutputFile', OutputFile, ...
                     "PrescriptionTable", "[PrescriptionTable]", ...
                     "ROIMaskFile", "[ROIMaskFile]");
                obj.Generate(tasksdeffile, dependency, Process);
            end

            PrescriptionFile= '../InitPrescription_focal/PrescriptionImage_NormRx.nii.gz';
            PrescriptionTable= '../InitPrescription_focal/Prescription.xlsx';
            AvoidanceFile   = '../InitPrescription_focal/AvoidanceImage.nii.gz';
            %BeamImageFile   = '../FCBB0_aper/PlanDose.nii.gz';
            %ROIMaskFile='../../RS.[ReferenceStructureSetUID]/roimask.nii.gz';
            ROIMaskFile   = '../InitPrescription_focal/OptMaskImage.nii.gz';
            for k=[1 3]
                if k==1
                    BeamImageFile   = '../FCBB0_aper/PlanDose.nii.gz';
                else
                    BeamImageFile = '../RP_CalcInitPlan_unity/BeamTraceImage.nii.gz';
                end
                modality = modalities{k};
                taskdefname = ['RP_PredDose_nnUNET_unity_' modality];
                obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
                %obj.SetDef_struct(struct("CustomSubFolder", taskdefname));
                m=0; Dependency=[];
                m=m+1; Dependency{m}=struct("filename",     ['../doseimage.nii.gz'],...
                "taskfilename", ['../DcmConverter/DCMCONVERT_RP_2mm/RP.[SOPInstanceUID].tsk']);

                m=m+1; Dependency{m}=struct("filename",     [BeamImageFile],...
                "taskfilename", ['RP_CalcDose' '_FCBB0_aper/RP.[SOPInstanceUID].tsk']);
                %obj.AddDependency(Dependency);

                m=m+1; Dependency{m}=struct("filename",     [PrescriptionFile '|' AvoidanceFile],...
                "taskfilename", ['RP_InitPrescription_focal/RP.[SOPInstanceUID].tsk']);
                %obj.AddDependency(Dependency);

                DoseFileName = 'PredDose';
                DoseFile = [DoseFileName '.nii.gz'];
                DVHFile = [DoseFileName '_dvhs.json'];
                TaskInfo=struct("FileName", ['temp_PredDose_nnUNET_unity_' modality '/RP.[SOPInstanceUID].tskinfo']);
                TaskInfo.Info=struct(...
					    "TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' taskdefname '/'],...
					    "PrescriptionTable", PrescriptionTable,...
                        "PrescriptionFile", PrescriptionFile, ...
					    "AvoidanceFile", AvoidanceFile, ...
                        "BeamImageFile", BeamImageFile, ...
                        "ROIMaskFile", ROIMaskFile);
                 m=m+1; Dependency{m}=struct("filename",     DoseFile,...
                "TaskInfo", TaskInfo);
        
                m=0; Process=[];
                ROIMaskFile  = '../../RS.[ReferenceStructureSetUID]/roimask.nii.gz'; 
                m=m+1; Process{m}=struct('OperationType', 'CalcDVH',...
                   'ROIMaskImage', ROIMaskFile, 'DoseImage', [DoseFile], 'DVHFile', [DVHFile] );

                CustomSubFolder= taskdefname; 
                AssociateFileName='../dcmdata.cfg';
                ListName = 'DoseSet';
                Info=struct("ID", taskdefname, "DoseFileName", [CustomSubFolder '/' DoseFile],...
                     "DVHFileName",  [CustomSubFolder '/' DVHFile]);
                m=m+1; Process{m}=struct('OperationType', 'AssociateStruct', 'AssociateFileName', AssociateFileName,...
                    'ListName', ListName, 'Info', Info);
                obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});
            end
        end

        function Factory_tempprocess(tasksdeffile)
            taskdeffolder= [fileparts(tasksdeffile) '/'];
            taskdefname  = ['temp_CalcDVH'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});     m=0; Process=[];
            m=m+1; Process{m}=struct('OperationType', 'CalculateDVH', 'ROIMaskImage', '[ROIMaskImage]',...
                'DoseImage', '[DoseImage]', 'DVHFile', '[DVHFile]');
            Dependency=struct("filename",     '[DoseImage]|[ROIMaskImage]');
            obj.Generate(tasksdeffile, Dependency, Process);

            taskdefname  = ['temp_CT2Density'];
            obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});     m=0; Process=[];
            m=m+1; Process{m}=struct('OperationType', 'CT2Density', 'CTFile', '[CTFile]',...
                'DensityFile', '[DensityFile]');
            Dependency=struct("filename",     '[CTFile]');
            obj.Generate(tasksdeffile, Dependency, Process);
        end

        function Factory_ProjectionApp(tasksdeffile)
            projtypes = {'DRR',  'SSD', 'Attenuation'};
            inputtypes = {'CT', 'Density', 'MR_SynCTCommon', 'MR_SynCTHead', 'PT_SynCT'};
            for m=1:numel(inputtypes)
                inputtype = inputtypes{m};
                for k=1:numel(projtypes)
                    projtype = projtypes{k}; 
                    daemon.taskdef.RtPlan.ProjectionApp(tasksdeffile, projtype, inputtype);
                end
            end
        end

        function ProjectionApp(tasksdeffile, projtype, inputtype)
            taskdeffolder= [fileparts(tasksdeffile) '/'];
            GantryAngle='[GantryAngle]'; 
            RTBeamType ='[RTBeamType]';            
            DefaultSettings = struct('RTBeamType', 'TB-06X', 'GantryAngle', '0 270');

            switch inputtype
                case {'Density', 'CT'}
                    inputimg = ['[' inputtype 'Image]'];
                    Input   = struct(inputtype, inputimg, 'GantryAngle', GantryAngle, 'RTBeamType', RTBeamType );
                    Output  = struct('ProjFile', '[OutputImage]');
        
                    taskdefname  = ['temp_' inputtype '2' projtype];
                    obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});   
                    Dependency = struct("filename",     inputimg);
                    Process  = struct('OperationType', 'CalculateProjection', ...
                        'ProjectionType', projtype, 'Input', Input, 'Output', Output);
                    obj.Generate(tasksdeffile, Dependency, Process, {'DefaultSettings', DefaultSettings});
                    if strcmpi(inputtype, 'CT')
                        angles = [0 270]; ext =  '_AP_RL';
                        taskdefname  = ['CT_CT2' projtype ext];
                        CustomSubFolder = [projtype ext];
                        inputimg1 = '../image.nii.gz';
                        Input1   = struct(inputtype, inputimg1, 'GantryAngle', angles, 'RTBeamType', 'TB-06X');
                        obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});   
                        Process1  = struct('OperationType', 'CalculateProjection', ...
                            'ProjectionType', projtype, 'Input', Input1, ...
                            'Output', struct('ProjFile', [projtype]));
                        Dependency1 = struct("filename",     inputimg1, ...
                             "taskfilename", ['../DcmConverter/DCMCONVERT_CT/CT.[SeriesInstanceUID].tsk']);
                        obj.Generate(tasksdeffile, Dependency1, Process1, {'CustomSubFolder', CustomSubFolder});
                    end

                case {'MR_SynCTCommon', 'MR_SynCTHead', 'PT_SynCT'}
                    modality = extractBefore(inputtype, '_'); 
                    synimagetype= extractAfter(inputtype, '_'); 
                    angles = [0 270]; ext =  '_AP_RL';
                    taskdefname  = [modality '_' synimagetype '2' projtype ext];
                    CustomSubFolder = [synimagetype '_' projtype ext];
                    inputimg1 = ['../' synimagetype '/image.nii.gz'];
                    Input1   = struct('CT', inputimg1, 'GantryAngle', angles, 'RTBeamType', 'TB-06X');
                    obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});   
                    Process1  = struct('OperationType', 'CalculateProjection', ...
                        'ProjectionType', projtype, 'Input', Input1, ...
                        'Output', struct('ProjFile', [projtype]));
                    Dependency1 = struct("filename",     inputimg1, ...
                         "taskfilename", ['../qMRI/' modality '_' synimagetype '/' modality '.[SeriesInstanceUID].tsk']);
                    obj.Generate(tasksdeffile, Dependency1, Process1, {'CustomSubFolder', CustomSubFolder});
            end
        end
    end
end