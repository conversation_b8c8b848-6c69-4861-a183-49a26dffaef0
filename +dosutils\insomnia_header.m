function [methodinfo,structs,enuminfo,ThunkLibName]=insomnia_header
%INSOMNIA_HEADER Create structures to define interfaces found in 'insomnia_header'.

%This function was generated by loadlibrary.m parser version  on Sun May  2 22:39:41 2021
%perl options:'insomnia_header.i -outfile=insomnia_header.m -thunkfile=kernel32_thunk_pcwin64.c -header=insomnia_header.h'
ival={cell(1,0)}; % change 0 to the actual number of functions to preallocate the data.
structs=[];enuminfo=[];fcnNum=1;
fcns=struct('name',ival,'calltype',ival,'LHS',ival,'RHS',ival,'alias',ival,'thunkname', ival);
MfilePath=fileparts(mfilename('fullpath'));
ThunkLibName=fullfile(MfilePath,'kernel32_thunk_pcwin64');
% unsigned int SetThreadExecutionState ( unsigned int esFlags ); 
fcns.thunkname{fcnNum}='uint32uint32Thunk';fcns.name{fcnNum}='SetThreadExecutionState'; fcns.calltype{fcnNum}='Thunk'; fcns.LHS{fcnNum}='uint32'; fcns.RHS{fcnNum}={'uint32'};fcnNum=fcnNum+1;
methodinfo=fcns;