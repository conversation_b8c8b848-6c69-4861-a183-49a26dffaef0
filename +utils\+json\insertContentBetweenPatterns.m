function insertContentBetweenPatterns(fileA, fileB, pattern1, pattern2)
    % INSERTCONTENTBETWEENPATTERNS Inserts content of fileB into fileA
    % between lines matching pattern1 and pattern2.
    %
    % Usage:
    %   insertContentBetweenPatterns('A.txt', 'B.txt', 'startPattern', 'endPattern')

    % Read file A
    fidA = fopen(fileA, 'r');
    if fidA == -1
        error('Could not open file "%s" for reading.', fileA);
    end
    linesA = textscan(fidA, '%s', 'Delimiter', '\n', 'Whitespace', '');
    fclose(fidA);
    linesA = linesA{1};

    % Read file B
    fidB = fopen(fileB, 'r');
    if fidB == -1
        error('Could not open file "%s" for reading.', fileB);
    end
    linesB = textscan(fidB, '%s', 'Delimiter', '\n', 'Whitespace', '');
    fclose(fidB);
    linesB = linesB{1};

    % Find pattern1 and pattern2
    idx1 = find(~cellfun(@isempty, regexp(linesA, pattern1, 'once')), 1, 'first');
    if ~exist('pattern2', 'var')
         idx2=idx1+1; 
    else
        idx2 = find(~cellfun(@isempty, regexp(linesA, pattern2, 'once')), 1, 'first');
    end

    if isempty(idx1) || isempty(idx2) || idx1 >= idx2
        error('Pattern1 and Pattern2 must exist, and Pattern1 must appear before Pattern2.');
    end

    % Insert content from B.txt between idx1 and idx2
    newLines = [linesA(1:idx1); linesB; linesA(idx2:end)];

    % Write modified content back to file A
    fidA = fopen(fileA, 'w');
    if fidA == -1
        error('Could not open file "%s" for writing.', fileA);
    end
    fprintf(fidA, '%s\n', newLines{:});
    fclose(fidA);

    %fprintf('Content of "%s" inserted into "%s" between lines matching "%s" and "%s".\n', fileB, fileA, pattern1, pattern2);
end