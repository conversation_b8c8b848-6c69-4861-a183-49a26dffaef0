#
#  Copyright (C) 2009-2010, OFFIS e.V.
#  All rights reserved.  See COPYRIGHT file for details.
#
#  This software and supporting documentation were developed by
#
#    OFFIS e.V.
#    R&D Division Health
#    Escherweg 2
#    D-26121 Oldenburg, Germany
#
#
#  Module:  oflog
#
#  Author:  <PERSON><PERSON>
#
#  Purpose: Extended sample configuration file for the logger
#

# log to both the console and a file
log4cplus.rootLogger = DEBUG, console, logfile

# log only error and fatal messages to the console
log4cplus.appender.console = log4cplus::ConsoleAppender
log4cplus.appender.console.Threshold = ERROR
log4cplus.appender.console.logToStderr = true
log4cplus.appender.console.ImmediateFlush = true

# log debug messages to a log file (with automatic rotation)
log4cplus.appender.logfile = log4cplus::RollingFileAppender
log4cplus.appender.logfile.File = ${appname}.log
log4cplus.appender.logfile.Append = true
log4cplus.appender.logfile.ImmediateFlush = true
log4cplus.appender.logfile.MaxFileSize = 5MB
log4cplus.appender.logfile.MaxBackupIndex = 10

# use different patterns for the console and the file
log4cplus.appender.console.layout = log4cplus::PatternLayout
log4cplus.appender.console.layout.ConversionPattern = %D{%H:%M:%S} %5p: %m%n

log4cplus.appender.logfile.layout = log4cplus::PatternLayout
log4cplus.appender.logfile.layout.ConversionPattern = %D{%Y-%m-%d %H:%M:%S.%q} %5p - %-8c{1} - %m%n
