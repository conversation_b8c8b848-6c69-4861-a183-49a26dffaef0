echoscu(1)                        OFFIS DCMTK                       echoscu(1)



NAME
       echoscu - DICOM verification (C-ECHO) SCU


SYNOPSIS
       echoscu [options] peer port

DESCRIPTION
       The  echoscu  application implements a Service Class User (SCU) for the
       Verification SOP Class. It sends a DICOM C-ECHO message  to  a  Service
       Class  Provider  (SCP) and waits for a response. The application can be
       used to verify basic DICOM connectivity.

PARAMETERS
       peer  hostname of DICOM peer

       port  tcp/ip port number of peer

OPTIONS
   general options
         -h    --help
                 print this help text and exit

               --version
                 print version information and exit

               --arguments
                 print expanded command line arguments

         -q    --quiet
                 quiet mode, print no warnings and errors

         -v    --verbose
                 verbose mode, print processing details

         -d    --debug
                 debug mode, print debug information

         -ll   --log-level  [l]evel: string constant
                 (fatal, error, warn, info, debug, trace)
                 use level l for the logger

         -lc   --log-config  [f]ilename: string
                 use config file f for the logger

   network options
       application entity titles:

         -aet  --aetitle  [a]etitle: string
                 set my calling AE title (default: ECHOSCU)

         -aec  --call  [a]etitle: string
                 set called AE title of peer (default: ANY-SCP)

       association negotiation debugging:

         -pts  --propose-ts  [n]umber: integer (1..38)
                 propose n transfer syntaxes

         -ppc  --propose-pc  [n]umber: integer (1..128)
                 propose n presentation contexts

       other network options:

         -to   --timeout  [s]econds: integer (default: unlimited)
                 timeout for connection requests

         -ts   --socket-timeout  [s]econds: integer (default: 60)
                 timeout for network socket (0 for none)

         -ta   --acse-timeout  [s]econds: integer (default: 30)
                 timeout for ACSE messages

         -td   --dimse-timeout  [s]econds: integer (default: unlimited)
                 timeout for DIMSE messages

         -pdu  --max-pdu  [n]umber of bytes: integer (4096..131072)
                 set max receive pdu to n bytes (default: 16384)

               --repeat  [n]umber: integer
                 repeat n times

               --abort
                 abort association instead of releasing it

   transport layer security (TLS) options
       transport protocol stack:

         -tls  --disable-tls
                 use normal TCP/IP connection (default)

         +tls  --enable-tls  [p]rivate key file, [c]ertificate file: string
                 use authenticated secure TLS connection

         +tla  --anonymous-tls
                 use secure TLS connection without certificate

       private key password (only with --enable-tls):

         +ps   --std-passwd
                 prompt user to type password on stdin (default)

         +pw   --use-passwd  [p]assword: string
                 use specified password

         -pw   --null-passwd
                 use empty string as password

       key and certificate file format:

         -pem  --pem-keys
                 read keys and certificates as PEM file (default)

         -der  --der-keys
                 read keys and certificates as DER file

       certification authority:

         +cf   --add-cert-file  [c]ertificate filename: string
                 add certificate file to list of certificates

         +cd   --add-cert-dir  [c]ertificate directory: string
                 add certificates in d to list of certificates

       security profile:

         +px   --profile-bcp195
                 BCP 195 TLS Profile (default)

         +py   --profile-bcp195-nd
                 Non-downgrading BCP 195 TLS Profile

         +pz   --profile-bcp195-ex
                 Extended BCP 195 TLS Profile

         +pb   --profile-basic
                 Basic TLS Secure Transport Connection Profile (retired)

         +pa   --profile-aes
                 AES TLS Secure Transport Connection Profile (retired)

         +pn   --profile-null
                 Authenticated unencrypted communication
                 (retired, was used in IHE ATNA)

       ciphersuite:

         +cc   --list-ciphers
                 show list of supported TLS ciphersuites and exit

         +cs   --cipher  [c]iphersuite name: string
                 add ciphersuite to list of negotiated suites

       pseudo random generator:

         +rs   --seed  [f]ilename: string
                 seed random generator with contents of f

         +ws   --write-seed
                 write back modified seed (only with --seed)

         +wf   --write-seed-file  [f]ilename: string (only with --seed)
                 write modified seed to file f

       peer authentication:

         -rc   --require-peer-cert
                 verify peer certificate, fail if absent (default)

         -ic   --ignore-peer-cert
                 don't verify peer certificate

NOTES
   DICOM Conformance
       The echoscu application supports the following SOP Classes as an SCU:

       VerificationSOPClass                1.2.840.10008.1.1

       Unless the --propose-ts option is used, the  echoscu  application  will
       only propose the transfer syntax

       LittleEndianImplicitTransferSyntax  1.2.840.10008.1.2

LOGGING
       The  level  of  logging  output  of  the various command line tools and
       underlying libraries can be specified by the  user.  By  default,  only
       errors  and  warnings  are  written to the standard error stream. Using
       option --verbose also informational messages  like  processing  details
       are  reported.  Option  --debug  can be used to get more details on the
       internal activity, e.g. for debugging purposes.  Other  logging  levels
       can  be  selected  using option --log-level. In --quiet mode only fatal
       errors are reported. In such very severe error events, the  application
       will  usually  terminate.  For  more  details  on the different logging
       levels, see documentation of module 'oflog'.

       In case the logging output should be written to file  (optionally  with
       logfile  rotation),  to syslog (Unix) or the event log (Windows) option
       --log-config can be used.  This  configuration  file  also  allows  for
       directing  only  certain messages to a particular output stream and for
       filtering certain messages based on the  module  or  application  where
       they  are  generated.  An  example  configuration  file  is provided in
       <etcdir>/logger.cfg.

COMMAND LINE
       All command line tools  use  the  following  notation  for  parameters:
       square  brackets  enclose  optional  values  (0-1), three trailing dots
       indicate that multiple values are allowed (1-n), a combination of  both
       means 0 to n values.

       Command line options are distinguished from parameters by a leading '+'
       or '-' sign, respectively. Usually, order and position of command  line
       options  are  arbitrary  (i.e.  they  can appear anywhere). However, if
       options are mutually exclusive the rightmost appearance is  used.  This
       behavior  conforms  to  the  standard  evaluation  rules of common Unix
       shells.

       In addition, one or more command files can be specified  using  an  '@'
       sign  as  a  prefix to the filename (e.g. @command.txt). Such a command
       argument is replaced by the content  of  the  corresponding  text  file
       (multiple  whitespaces  are  treated  as a single separator unless they
       appear between two quotation marks) prior to  any  further  evaluation.
       Please  note  that  a command file cannot contain another command file.
       This simple but effective  approach  allows  one  to  summarize  common
       combinations  of  options/parameters  and  avoids longish and confusing
       command lines (an example is provided in file <datadir>/dumppat.txt).

EXIT CODES
       The echoscu utility uses the following  exit  codes  when  terminating.
       This  enables  the  user  to  check  for the reason why the application
       terminated.

   general
       EXITCODE_NO_ERROR                         0
       EXITCODE_COMMANDLINE_SYNTAX_ERROR         1

   input file errors
       EXITCODE_CANNOT_READ_INPUT_FILE          20 (*)

   output file errors
       EXITCODE_CANNOT_WRITE_OUTPUT_FILE        40 (*)

   network errors
       EXITCODE_CANNOT_INITIALIZE_NETWORK       60 (*)
       EXITCODE_ASSOCIATION_ABORTED             70

       (*) Actually, these codes are currently not used by echoscu  but  serve
       as a placeholder for the corresponding group of exit codes.

ENVIRONMENT
       The  echoscu  utility  will  attempt  to  load  DICOM data dictionaries
       specified in the DCMDICTPATH environment variable. By default, i.e.  if
       the   DCMDICTPATH   environment   variable   is   not   set,  the  file
       <datadir>/dicom.dic will be loaded unless the dictionary is built  into
       the application (default for Windows).

       The   default   behavior   should  be  preferred  and  the  DCMDICTPATH
       environment variable only used when alternative data  dictionaries  are
       required.  The  DCMDICTPATH environment variable has the same format as
       the Unix shell PATH variable in that a colon (':')  separates  entries.
       On  Windows systems, a semicolon (';') is used as a separator. The data
       dictionary code will  attempt  to  load  each  file  specified  in  the
       DCMDICTPATH  environment variable. It is an error if no data dictionary
       can be loaded.

COPYRIGHT
       Copyright (C) 1994-2018 by OFFIS e.V., Escherweg  2,  26121  Oldenburg,
       Germany.



Version 3.6.5                   Mon Oct 28 2019                     echoscu(1)
