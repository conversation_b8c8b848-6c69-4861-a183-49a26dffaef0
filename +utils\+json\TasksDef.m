classdef TasksDef <utils.json.JsonConfig   
    properties
        
    end
    
    methods
        function obj = TasksDef(cfgfile, varargin)
            <EMAIL>(cfgfile, varargin);
            RemoveCommentFileds(obj);
            LoadTaskOptions(obj);
        end
        
        function names = TaskNames(obj)
            names = fieldnames(obj.m_cfg); 
        end
        
        function names = FullTaskNames(obj)
            names0 = TaskNames(obj);
            names = {}; 
            for k=1:numel(names0)
                name0 = names0{k};
                subtasks = obj.getfieldx([name0 '.SubTasks']); 
                if ~isempty(subtasks)
                    names1 = cellfun(@(x)([name0 '.' x]), subtasks, 'uniformoutput', false); 
                    names = cat(1, names, names1); 
                else
                    names = cat(1, names, {name0}); 
                end
            end
        end
        
        function RemoveSubTasksDef(self, fns)
            self.m_cfg=StructBase.rmfields(self.m_cfg, fns);
        end

        function dst = GetSubTasksDef(self, fns)
            dst = utils.json.TasksDef([]);
            for k=1:numel(fns)
                name = fns{k}; 
                dst.SetConfig(name, self.GetConfig(name));
            end
        end

        function flag = ExistTaskDef(obj, taskname)
            flag  = ismember(taskname, TaskNames(obj)); 
        end
        
        function taskdef = GetTaskDef(obj, taskname)
            taskdef      = obj.getfieldx(taskname); 
            if ischar(taskdef)
                fname =  FullFile(obj, taskdef);
                if exist(fname, 'file')
                     cfg = obj.readJsonConfig(fname);
                     taskdef = cfg;
                end
            end
        end
        
        function LoadTaskOptions(obj)
            if isempty(obj.m_cfg)
                return;
            end
            names = TaskNames(obj);
            for k=1:numel(names)
                taskname = names{k}; 
                task = GetTaskDef(obj, taskname);
                if ~isempty(task)
                    optsfile  = StructBase.getfieldx_default(task, 'OptionsFile', '');
                    fname     = obj.FullFile(optsfile);
                    if exist(fname, 'file')==2
                        task.TaskOptions= OptionsMap(fname); 
                        obj.m_cfg.(taskname) = task; 
                    end
                end
            end
        end
        
%         function folder = OutputRootFolder(self)
%             folder = self.getoptioni('OutputRootFolder', ''); 
%         end
%         
%         function folder = TaskFileRootFolder(self)
%             %folder = self.getoptioni('TaskFileRootFolder', ''); 
%             folder = DosUtil.mksubdir(OutputRootFolder(self), 'Tasks');
%         end
%         
%          function fname = TaskFileName(self, taskname, taskid)
%             %folder = self.getoptioni('TaskFileRootFolder', ''); 
%             %taskname = task.GetTaskConfig('TaskName'); 
%             fname = [TaskFileRootFolder(self) taskname '/' taskid '.tsk']; 
%         end
%         
%         function folder = TaskOutputFolder(self, task, info)
%             taskuid  = task.getfieldx('TaskUID'); 
%             folder   = DosUtil.mksubdir(OutputRootFolder(self),[info.PatientID '/' taskuid]);
%             %customsubfolder = task.getfieldx('CustomSubFolder', ''); 
%             customsubfolder = task.GetTaskConfig('CustomSubFolder'); 
%             if ~isempty(customsubfolder)
%                 folder   = DosUtil.mksubdir(folder, customsubfolder);
%             end
%             folder = DosUtil.SimplifyPath(folder); 
%         end
%                 
%         function status = IsTaskDone(self, task, info)
%             %taskuid     = task.getfieldx('TaskUID'); 
%             iscustomtask= task.getfieldx('IsCustomTask', 0); 
%             statusfolder= TaskOutputFolder(self, task, info);
%             donefname   = task.GetTaskConfig('TaskDoneIndicator', ''); 
%             status = 0; 
%             if  ~iscustomtask && exist([statusfolder donefname], 'file')==2
%                 status = 1; 
%                 return; 
%             end
%         end
%         
%         function flag = IsTaskReady(self, task, info1)
%             %taskdef = GetTaskDef(obj, taskname);
%             readyfname = task.GetTaskConfig('TaskReadyIndicator', ''); 
%             statusfolder = DataRootFolder(self);
%             if ~isempty(readyfname) 
%                 readyfname = strsplit(readyfname, '|');
%                 for k=1:numel(readyfname)
%                     tempname = readyfname{k};
%                     matches = regexpi(tempname, '(?<=\[).+?(?=\])', 'match');
%                     if ~isempty(matches)
%                         for m=1:numel(matches)
%                             fn = matches{m};
%                             repstr = StructBase.getfieldx_default(info1, fn);
%                             if ~isempty(repstr)
%                                 tempname = strrep(tempname, ['[' fn ']'], repstr); 
%                             end
%                         end
%                     end
%                     %if exist([statusfolder tempname], 'file')~=2
%                     if ~self.IsFileReady([statusfolder tempname], ageThreshInsecond)
%                         flag = 0; 
%                         return;
%                     end
%                 end
%             end
%             flag = 1; 
%         end
    end
    
    methods (Static)
        function ss = RecursiveWildCardRep(ss, infos)
            if ischar(ss)
                ss = utils.json.TasksDef.WildCardRepStr(ss, infos);
            elseif isstruct(ss)
                fns = fieldnames(ss);
                for k=1:numel(fns)
                    fn = fns{k};
                     ss.(fn)=utils.json.TasksDef.RecursiveWildCardRep(ss.(fn), infos);
                end
            elseif iscell(ss)
                for k=1:numel(ss)
                    ss{k}=utils.json.TasksDef.RecursiveWildCardRep(ss{k}, infos);
                end
            end
        end

        function tempname = WildCardRepStr(tempname, infos)
            if isempty(infos)
                return; 
            end
            %tempname = readyfname{k};
            if isstruct(infos)
                infos = arrayfun(@(x)(x), infos, 'uniformoutput', false); 
            end
            
%             matches = regexpi(tempname, '(?<=\[).+?(?=\])', 'match');
%             if ~isempty(matches)
%                 for m=1:numel(matches)
%                     fn = matches{m};
%                     repstr = StructBase.getfieldx_default(infos{1}, fn);
%                     if ~isempty(repstr)
%                         tempname = strrep(tempname, ['[' fn ']'], repstr); 
%                     end
%                 end
%             end
            if numel(infos)>=1
                %expstr = '(?<=\[).+?(?=\])';
                tempname = utils.json.TasksDef.RepMatchQuoteStr(tempname, infos{1}, {'[', ']'});
            end
            
            if numel(infos)>=2
                %expstr = '(?<=\{).+?(?=\})';
                tempname = utils.json.TasksDef.RepMatchQuoteStr(tempname, infos{2}, {'{', '}'});
            end
            
            if numel(infos)>=3
                %expstr = '(?<=\{).+?(?=\})';
                tempname = utils.json.TasksDef.RepMatchQuoteStr(tempname, infos{3}, {'(', ')'});
            end
        end
        
        function tempname = RepMatchQuoteStr(tempname, info, qutoes)
            if ~exist('qutoes', 'var')
                qutoes = {'[', ']'};
            end
            qutoes1= qutoes; 
            
            specchars  = {'.' , '+' , '*' , '?' , '^' , '$' , '(' , ')' , '[' , ']' , '{' , '}' , '|' , '\'}; 
            for k=1:numel(qutoes1)
                if ismember(qutoes1{k}, specchars)
                    qutoes1{k}=['\' qutoes1{k}];
                end
            end
            
            expstr = ['(?<=' qutoes1{1} ').+?(?=' qutoes1{2} ')'];
            
            matches = regexpi(tempname, expstr, 'match');
            if ~isempty(matches)
                for m=1:numel(matches)
                    fn = matches{m};
                    repstr = StructBase.getfieldx_default(info, fn);
                    if ~isempty(repstr)
                        tempname = strrep(tempname, [qutoes{1} fn qutoes{2}], repstr); 
                    end
                end
            end
        end
        
        function flag = IsFileReady(fname, ageThreshInsecond)
            flag =false; 
            if exist(fname, 'file')~=2
                return;
            end
            res = dir(fname); 
            fileage = (now-res(1).datenum)*24*3600; 
            flag = fileage>=ageThreshInsecond;
        end
       
        function [status, IsBusy] = IsTaskReady(readyfname, info, statusfolder, ageThreshInsecond)
            if ischar(readyfname)
                readyfname = strsplit(readyfname, '|');
            end
            IsBusy = false; 
            for k=1:numel(readyfname)
                tempname = readyfname{k};
                tempname = utils.json.TasksDef.WildCardRepStr(tempname, info);
                %if exist([statusfolder tempname], 'file')~=2
                %fname = DosUtil.SimplifyPath([statusfolder tempname]);
                fname = DosUtil.CatenatePath(tempname,  statusfolder);
                if ~utils.json.TasksDef.IsFileReady(fname, ageThreshInsecond)
                    status = 0; 
                    %%%%2025/07/03 add error check to avoid create task
                    %%%%infinitely
                    errfolder = fileparts(fname);
                    errfile = [errfolder '/status.err']; lockfile = [errfolder '/status.lock'];
                    if exist(errfile, 'file')>0 || exist(lockfile, 'file')>0
                        IsBusy = true; 
                    end
                    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
                    return;
                end
            end
            status = 1; 
        end
    end
end

