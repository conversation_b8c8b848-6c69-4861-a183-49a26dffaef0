classdef DcmServer <dcmtk.DCMTKServer
    properties
        m_DCMPorts;
        
        m_PlanDB;
    end
    
    methods
        function obj = DcmServer(varargin)
            options = OptionsMap();
            options.setOption('Imports.DBNotes', '../DBNotes.json');
            options.setOption('dcmserver.useplandb', 1);

            options.setOption('registered.modality', {'CT', 'MR', 'PT', 'US', 'REG', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'RTIMAGE', 'RTRECORD', 'SR'});
            options.setOption('default.modality', 'MR');
            
            options.setOption('registered.task', {'retrieve', 'query', 'housekeep', 'import', 'dcmsend', 'dcmanon', 'removedcm'});
            %options.setOption('default.task', 'retrieve');
            
            options.setOption('registered.retrievetype', {'RTPatient', 'RPRS','RPRSRD', 'Series', 'Modality', 'Study', 'SOPInstanceUID'});
            %options.setOption('default.retrievetype', 'rtpatient');
            
            
            options.setOption('dcmsrv.storeincoming', 1);            
            options.setOption('default.dcmserver', 'MDD_SCP');

            options.setOption('dcmportstable.file', [fileparts(mfilename('fullpath'))  '\cfg\DCMPorts_UTSWRO.xlsx']);
            
            options.setOption('ANON_DCMSERVER_FOLDER', DosUtil.mksubdir('c:\temp\', 'dcmanon'));
            
            % %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
            % add incoming option
            str = 'DCMSERVER_FOLDER.incoming'; 
            incomingFolders = options.getOption(str, '../DataBase/DicomData/incoming');
            if ischar(incomingFolders)
                incomingFolders=strsplit(incomingFolders, '|'); 
            end
            for k=1:numel(incomingFolders)
                try
                DosUtil.mksubdir(incomingFolders{k});
                catch
                end
            end
            options.setOption(str, incomingFolders);
            % %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

            <EMAIL>(options, varargin{:});
            
            rootfolder = obj.getoptioni('DCMSERVER_ROOT');
            if isempty(rootfolder)
                obj.setOption('DCMSERVER_ROOT', obj.getoptioni('DCMSERVER_FOLDER'));
            end
            
            %obj.setOption('ANON_DCMSERVER_FOLDER', DosUtil.mksubdir(rootfolder, 'dcmanon'));
           
            
            obj.setOption('log.file', [obj.LogFolder() 'dcmserver.log']);
            LoadDCMPorts(obj);
            
            SetLocalePort(obj);
            
            if UsePlanDB(obj)
%                obj.m_PlanDB= xls.TimeStampRecordDB({'table.uniquekey', 'SOPInstanceUID'},...
%                    {'RecordDBName', 'RTPlanDB'}, {'RecordDBFolder', PlanDBFolder(obj)});

%                 filterfile = obj.getoptioni('activeplanfilter.file'); 
%                 dbformat   = obj.getoptioni('FullRecordDBFormat'); 
%                 obj.m_PlanDB= dcmtk.TimeStampPlanDB(PlanDBFolder(obj), {'DCMSERVER_FOLDER', DcmDataFolder(obj)}, ...
%                     {'activeplanfilter.file', filterfile}, {'FullRecordDBFormat', dbformat});
                  
                FullRecordDBFormat = obj.getoptioni('FullRecordDBFormat');
                if strcmpi(FullRecordDBFormat, '.sqlite')
                    RecordDBName = obj.getoptioni('RecordDBName', 'RTPlanDB');
                    dbfile = [PlanDBFolder(obj)  RecordDBName FullRecordDBFormat]; 
                    if ~exist(dbfile, 'file')
                         mydb.sqlite.DcmRTDB.CreateDBTables(dbfile);
                    end
                end
                    
                obj.m_PlanDB= dcmtk.TimeStampPlanDB(PlanDBFolder(obj),obj);  
            end
            
            try
                anonymizer = DcmAnonymizer(self);
                self.m_DB.setOption('DcmServer.Anonymizer', anonymizer); 
            catch
            end
        end
        
        function LoadDCMPorts(obj)
            dcmportsfile   = obj.getoptioni('dcmportstable.file');
            obj.m_DCMPorts = xls.TableBase.table_cellstr(dcmportsfile);
            %obj.m_DCMPorts.AETitle = upper(obj.m_DCMPorts.AETitle);
            %obj.m_DCMPorts.Properties.RowNames=obj.m_DCMPorts.('AETitle');
            obj.m_DCMPorts.Properties.RowNames=obj.m_DCMPorts.('DcmPortName');
        end
        
%         function res = MyAETitle(self)
%             %res = upper(self.getoptioni('AETitle'));
%             res = self.getoptioni('AETitle');
%         end
        
         function res = MyDcmPortName(self)
            %res = upper(self.getoptioni('AETitle'));
            res = self.getoptioni('DcmPortName');
            if isempty(res)
                res = self.getoptioni('AETitle'); %legacy mode
            end
        end
        
        
        
        function res = UsePlanDB(self)
            res = self.getoptioni('dcmserver.useplandb');
        end
        
        function res = PlanDBFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.plandb', DosUtil.mksubdir(RootFolder(self), 'plandb'));
        end
        
        function res = TaskFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.tasks', DosUtil.mksubdir(RootFolder(self), 'tasks'));
        end
        
        function res = PersistentTaskFile(self)
            defaultfile = [RootFolder(self) 'tasks.persistent\persistenttasks.json'];
            res = self.getoptioni('PersistentTaskFile', defaultfile);
        end
        
        function res = LogFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.log', DosUtil.mksubdir(RootFolder(self), 'log'));
        end
        
        function res = HistoryFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.history', DosUtil.mksubdir(RootFolder(self), 'history'));
        end
        
        function res = IncomingFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER.incoming');
        end

        function res = RegisteredModality(self)
            res = self.getoptioni('registered.modality');
            if ischar(res)
                res = strsplit(res, '|');
            end
        end

        function res = RegisteredTask(self)
            res = self.getoptioni('registered.task');
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
        
        function res = RegisteredRetrievetype(self)
            res = self.getoptioni('registered.retrievetype');
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
        
        function HouseKeeping(self, mrns)
            if ischar(mrns) 
                mrns = strsplit(mrns, '|');
            end
            
            rootfolder = DcmDataFolder(self);
            modalities = RegisteredModality(self);
            mrns = unique(mrns);
            for mm=1:numel(mrns)
                try
                    mrn = mrns{mm};
                    self.LogInfo(['HouseKeeping ' mrn]); 

                    %self.HouseKeepMRN(rootfolder, mrn, modalities);
                    HouseKeepPatient(self, mrn);
                    
                catch err
                    LogErr(self, err);
                end
            end
        end
        
        function HouseKeepPatient(self, MRN)
            rtpat = dcmtk.RTPatientTables({'PatientID', MRN}, {'DCMSERVER_FOLDER', DcmDataFolder(self)});
            rtpat.HouseKeep({'plandb', self.m_PlanDB});
        end
        
        function [newInstanceT, instanceT] = StoreDcms(self, inpFiles,  storemode, varargin)
            if isempty(inpFiles)
                newInstanceT=[]; instanceT=[];
                return;
            end
            
            rootfolder = DcmDataFolder(self);
            %[newInstanceT, instanceT] = dcmtk.DcmDb.StoreDCMFiles(rootfolder, inpFiles, {'deleteOnSuccess', deletesource});
            [newInstanceT, instanceT] = self.m_DB.store(inpFiles,  storemode);
            
            if ~isEmpty(newInstanceT)
                try
                rawT = newInstanceT.GetRawTable(); 
                
                dcmtk.RTPatientTables.HouseKeepIncoming(rootfolder, rawT, self.m_PlanDB, varargin{:}); 

                catch err
                    LogErr(self, err);
                end
            end
            
            if ~isEmpty(instanceT) && UsePlanDB(self)
                self.m_PlanDB.AddActivePlans(instanceT.GetRawTable());
            end
        end
                    
        
        function [newInstanceT, instanceT] =  ProcessIncoming(self)
            incomingfolder = IncomingFolder(self);
            
%             res  = DosUtil.rdir(incomingfolder, '~isdir'); 
%             if ~isempty(res)
%                 inpFiles = {res(:).name};
%                 T = self.StoreDcms(inpFiles,  1);
%             end
            if ischar(incomingfolder)
                incomingfolder=strsplit(incomingfolder, '|');
            end
            newInstanceT=[]; instanceT=[];
            for k=1:numel(incomingfolder)
                [newInstanceT1, instanceT1] = self.StoreFolder(incomingfolder{k}, {'storemode', 1});
                newInstanceT=cat(1, newInstanceT, newInstanceT1);
                instanceT=cat(1,instanceT, instanceT1);
            end
        end
        
        function LogTask(self, task,  fname)
            %logfolder  = HistoryFolder(self); 
            logfolder  = DosUtil.mksubdir(HistoryFolder(self), task); 
            try
            [PATHSTR,NAME,EXT] = fileparts(fname);
            dstfile = [logfolder,NAME  EXT];
%             if exist(dstfile, 'file')
%                 %dstfile = [dstfile str];
%                 str = char(datetime); str = strrep(str, ':', '.'); 
%                 dstfile = [logfolder,NAME str  EXT];
%             end
            
            movefile(fname, dstfile, 'f'); 
            %numpatients = numel(res.mrns); 
            self.LogInfo([task ' task file: ', fname ' moved to ' dstfile]);
            catch
                 self.LogInfo(['error: log ' task ' task file: ', fname ' moved to ' dstfile]);
            end
        end

        function SetLocalePort(obj)
            
%             aec = obj.MyAETitle();
%             if isempty(aec)
%                 return;
%             end
            % aec = upper(aec);
            if ~(obj.isOption('DCMTK_Client_AETitle') && obj.isOption('DCMTK_Client_Port'))
            dcmportname = obj.MyDcmPortName; 
            port = obj.m_DCMPorts{dcmportname, 'Port'}{1};
            aec  = obj.m_DCMPorts{dcmportname, 'AETitle'}{1};
            %IP   = obj.m_DCMPorts{aec, 'IP'}{1};
            obj.setOptions({'DCMTK_Client_AETitle', aec}, {'DCMTK_Client_Port', port});
            end
            %obj.openRecvPort;
        end
        
        function SetRemotePort(obj, dcmportname)
%             aec = obj.MyAETitle();
%             clinetport = obj.m_DCMPorts{aec, 'Port'}{1};
            %aet = upper(aet);
            aet        = obj.m_DCMPorts{dcmportname, 'AETitle'}{1};
            serverport = obj.m_DCMPorts{dcmportname, 'Port'}{1};
            serverIP   = obj.m_DCMPorts{dcmportname, 'IP'}{1};
            try
                RetrieveLevel= obj.m_DCMPorts{dcmportname, 'RetrieveLevel'}{1};
            catch
                RetrieveLevel= '';
            end
            
            obj.setOptions({'DCMTK_Server_AETitle', aet}, {'DCMTK_Server_Port', serverport}, {'DCMTK_Server_IP', serverIP}, {'DefaultRetrieveLevel', RetrieveLevel}); 
        end
        
        
        function RunServer(self, servermode)
            if ~exist('servermode', 'var')
                servermode = 1;
            end
            taskfolder = [self.TaskFolder '**\'];
            registeredtasks = self.RegisteredTask; 
            for m=1:numel(registeredtasks)
                DosUtil.mksubdir(self.TaskFolder, registeredtasks{m});
            end
            
            %defaulttask =  self.getoptioni('default.task');
            
            self.LogInfo('*********************************Start DcmServer***********************');
            while(1)
                if self.getoptioni_numeric('dcmsrv.storeincoming')
                    self.ProcessIncoming();
                end
                
                ProcessPersistentTasks(self);
                
                fnames = self.ListTaskFiles(taskfolder);
                
                for k=1:numel(fnames)

                    fname = fnames{k};
                    try
                        %task = self.ParseTaskFile(fname, registeredtasks, defaulttask);
                        tskinfo = self.ExtractInfoFromTaskFileName(fname);
                        task    = StructBase.getfieldx(tskinfo, 'Task'); 
                        if ~isempty(task) && ismember(task, registeredtasks)                           
                            try
                                
                                self.LogInfo(['process ' upper(task) ' in file ', fname]);    
                                
                                self.ProcessTask(task, fname);
                                
                            catch err
                                self.LogErr(err);
                            end
                            self.LogTask(task, fname);
                        end
                    catch err
                        self.LogErr(err);
                    end
                end
                
                if ~servermode
                    break;
                end

                disp(['waitfor new task.....']); 
                
                pause(10);
            end
        end
        
        function ProcessPersistentTasks(self)
            fname = self.PersistentTaskFile();
            if exist(fname, 'file')
                text  = fileread(fname);
                tasks = jsondecode(text);
                if isstruct(tasks)
                    tasks = arrayfun(@(x)(x), tasks, 'uniformoutput', false);
                end
                for k=1:numel(tasks)
                    task = tasks{k};
                    dbnotes = StructBase.getfieldx_default(task, 'DBNotes', '');
                    dcmfilepattern='';
                    if isfield(task, 'dcmfilepattern')
                        dcmfilepattern = task.dcmfilepattern;
                    end
                    try
                    switch lower(task.taskmode)
                        case {'storetasksubfolders'}
                            try
                            rootdcmfolder = task.dcmfolder;
                            subfolders= DosUtils.listDir(rootdcmfolder);
                            storemode = task.storemode; 
                            datetimerange='';
                            if isfield(task, 'datetimerange')
                                datetimerange = task.datetimerange;
                            end
                            tasknote = StructBase.getfieldx(task, 'TaskDBNoteName'); 
                            dbnotes1 = dbnotes; 
                            for mm=1:numel(subfolders)
                                try
                                subfolder = subfolders{mm};
                                if ~isempty(tasknote)
                                    dbnotes1.(tasknote) = subfolder; 
                                end
                                dcmfolder = [rootdcmfolder subfolder '/'];
                                StoreFolder(self, dcmfolder,  {'storemode', storemode},...
                                    {'datetime.range', datetimerange}, {'dcmfilepattern', dcmfilepattern}, {'DBNotes', dbnotes1});
                                catch err
                                    self.LogErr(err);
                                end
                            end
                            catch err
                                self.LogErr(err);
                            end

                        case {'storefolder'}
                            try
                            dcmfolder = task.dcmfolder;
                            storemode = task.storemode; 
                            datetimerange='';
                            if isfield(task, 'datetimerange')
                                datetimerange = task.datetimerange;
                            end
                            
                            StoreFolder(self, dcmfolder,  {'storemode', storemode},...
                                {'datetime.range', datetimerange}, {'dcmfilepattern', dcmfilepattern}, {'DBNotes', dbnotes});
                            
                            catch err
                                self.LogErr(err);
                            end

                      case {'storefolderwithsubtask'}
                            try
                            dcmfolder = task.dcmfolder;
                            storemode = task.storemode; 
                            datetimerange='';
                            if isfield(task, 'datetimerange')
                                datetimerange = task.datetimerange;
                            end
                            dbnotes1 = dbnotes; 
                            try
                            dbinfofile = [dcmfolder self.getoptioni('Imports.DBNotes', '')];
                            if exist([dbinfofile], 'file')
                                dbinfo = utils.json.readJson(dbinfofile);
                                fns = fieldnames(dbinfo);
                                for m=1:numel(fns)
                                    fn = fns{m};
                                    dbnotes1.(fn)=dbinfo.(fn);
                                end
                            end
                            catch
                            end
                            [newInstanceT, instanceT] =StoreFolder(self, dcmfolder,  {'storemode', storemode},...
                                {'datetime.range', datetimerange}, {'dcmfilepattern', dcmfilepattern}, {'DBNotes', dbnotes1});
                            if isempty(newInstanceT)&& isempty(instanceT)
                                continue;
                            end
                            subtasks = StructBase.getfieldx(task, 'subtasks');
                            if isempty(subtasks)
                                continue; 
                            end
                            
                            if isstruct(subtasks)
                                subtasks = arrayfun(@(x)(x), subtasks, 'uniformoutput', false);
                            end
                            
                                for mm=1:numel(subtasks)
                                    try
                                    subtask = subtasks{mm};
                                    IsNewInstance = StructBase.getfieldx_default(subtask, 'IsNewInstance', false);
                                    T =[];
                                    if IsNewInstance
                                        T = newInstanceT.GetRawTable;
                                    else
                                        T = instanceT.GetRawTable;
                                    end
                                    if isempty(T)
                                        continue;
                                    end
                                    Modality =  StructBase.getfieldx_default(subtask,  'Modality', '');
                                    UIDType  =  StructBase.getfieldx_default(subtask,  'UIDType', '');
                                    if isempty(Modality) || isempty(UIDType)
                                       continue;  
                                    end
                                    outfile = StructBase.getfieldx_default(subtask,  'OutputTaskFile', '');
                                    if isempty(outfile)
                                        continue;
                                    end
                                    [I] = ismember( T.Modality, Modality); 
                                    T1 = T(I, :); 
                                    
                                    filterstr   = StructBase.getfieldx_default(subtask,  'InstanceFilter', []);
                                    instancefilter= [];
                                    if ~isempty(filterstr)
                                            instancefilter = xls.TableFilter; 
                                            instancefilter.AddTableFilter(filterstr);
                                            %[~, T1] = myfilter.FilterTable(T1);
                                    end

                                    uids = unique(T1.(UIDType));
                                    for kk=1:numel(uids)
                                        uid = uids{kk};
                                        [~, loc] = ismember(uid, T1.(UIDType));
                                        if loc<=0
                                            continue;
                                        end
                                        info1 = table2struct(T1(loc, :));
                                        if ~isempty(instancefilter)
                                            info1.NumberOfInstances = sum(ismember(T1.(UIDType), uid));
                                            if ~instancefilter.FilterStruct(info1)
                                                continue;
                                            end
                                        end
                                        outfile1 = utils.json.TasksDef.WildCardRepStr(outfile, info1);
                                        
                                        dcmrtdb = self.m_PlanDB.m_FullDB; 
                                        info2 = dcmrtdb.GetTableRow(info1.Modality, uid);
                                        if ~isempty(instancefilter)
                                            if ~instancefilter.FilterStruct(info2)
                                                continue;
                                            end
                                        end
                                        outfile1 = utils.json.TasksDef.WildCardRepStr(outfile, info2);
                                        
                                        fclose(fopen(outfile1, 'wt'));
                                    end
                                    catch
                                    end
                                end

                            catch err
                                self.LogErr(err);
                            end

                        case {'storeactivefolders'}
                            try
                            dcmfolder = task.dcmfolder;
                            storemode = task.storemode; 
                            folderdatetimerange='';
                            if isfield(task, 'folderdatetimerange')
                                folderdatetimerange = task.folderdatetimerange;
                            end
                            filedatetimerange=folderdatetimerange;
                            if isfield(task, 'filedatetimerange')
                                filedatetimerange = task.filedatetimerange;
                            end
                            StoreActiveFolders(self, dcmfolder,  {'storemode', storemode},...
                                {'datetimerange.folder', folderdatetimerange}, ...
                                {'datetimerange.file', filedatetimerange}, {'dcmfilepattern', dcmfilepattern}, {'DBNotes', dbnotes});
                            catch err
                                self.LogErr(err);
                            end
                    end
                    catch err
                        self.LogErr(err);
                    end
                end
            end
        end
        
        
        function ProcessTask(self, task, fname)
           switch lower(task)
               case 'query'
                   self.ProcessTask_query(fname); 
               case 'housekeep'
                   self.ProcessTask_housekeep(fname); 
               case 'retrieve'
                   self.ProcessTask_retrieve(fname);
               case 'import'
                   self.ProcessTask_import(fname);
               case 'dcmsend'
                   self.ProcessTask_dcmsend(fname);
               case 'dcmanon'
                   self.ProcessTask_dcmanon(fname);
               case 'removedcm'
                   self.ProcessTask_removedcm(fname);
           end
        end
        
        function anonymizer = DcmAnonymizer(self)
            anonymizer = self.getoptioni('DcmServer.Anonymizer'); 
            if isempty(anonymizer)
                folder = self.getoptioni('ANON_DCMSERVER_FOLDER');
                dbfile = self.getoptioni('ANON_DB_FILE', [DcmDataFolder(self) 'anonDB.xlsx']);
                anonymizer = anon.DicomInfoAnon({'ANON_DCMSERVER_FOLDER', folder}, {'ANON_DB_FILE', dbfile});
                self.setOption('DcmServer.Anonymizer', anonymizer); 
            end
        end
        
        function ProcessTask_removedcm(self,  fname)
            try
                [~, name, ext] = fileparts(fname);
                if strcmpi(ext, '.tsk')
                    %res      = strsplit(name, '.');
                    dcmlevel = extractBefore(name, '.');
                    uid      = extractAfter(name, '.');
                    db = self.m_PlanDB.GetFullDB();
                    if ~isempty(db)
                        db.RemoveDcmData(uid, dcmlevel, DcmDataFolder(self));
                    end
                elseif strcmpi(ext, '.xlsx')
                    try
                        T     = xls.TableBase.ReadTable(fname);
                        DICOM = T.DICOM;
                        dcmroot=  DcmDataFolder(self);
                        db = self.m_PlanDB.GetFullDB();
                        if ~isempty(db)
                            for k=1:numel(DICOM)
                                name = DICOM{k};
                                dcmlevel = extractBefore(name, '.');
                                uid      = extractAfter(name, '.');
                                db.RemoveDcmData(uid, dcmlevel, dcmroot);
                            end
                        end
                    catch
                    end
                end
            catch
            end
        end
        
        function ProcessTask_dcmanon(self,  fname)
            [~, name, ext] = fileparts(fname);
            switch lower(ext)
                case {'.tsk'}
                    name = strrep(name, '#', '/');
                    T    = table; 
                    %DosUtil.SimplifyPath([DcmDataFolder(self) name]
                    T.DICOM{1} =   DosUtil.SimplifyPath([DcmDataFolder(self) name '/**/*.dcm']);
                case {'.csv', '.xlsx'}
                    T = xls.TableBase.table_cellstr(fname);
            end
            
            withanonfolder = ismember('AnonFolder', T.Properties.VariableNames);
            %'ANON_DCMSERVER_FOLDER'
            %anonymizer = anon.DicomInfoAnon({'ANON_DCMSERVER_FOLDER', self.getoptioni('ANON_DCMSERVER_FOLDER')});
            anonymizer = DcmAnonymizer(self);
            dcms       = T.DICOM; 
            for k=1:size(T, 1)
                if withanonfolder
                    anonymizer.setOption('ANON_DCMSERVER_FOLDER', T.('AnonFolder'){k, 1});
                end
                anonymizer.AnonymizeFiles(dcms{k, 1}); 
            end
        end
        
        function ProcessTask_dcmsend(self,  fname)
           aets = self.m_DCMPorts.AETitle; 
           ports= self.m_DCMPorts.DcmPortName; 
           %defaultaet = self.getoptioni('default.dcmserver');
           %aet = self.ParseTaskFile(fname, aets, defaultaet);
           defaultport = self.getoptioni('default.dcmserver');
           port = self.ParseTaskFile(fname, ports, defaultport);
           [~, loc] = ismember(port, ports);
           aet = aets{loc};
           %self.SetRemotePort(aet);
           self.SetRemotePort(port);
           
           res = self.ParseTaskFile(fname, {'anon'}, '');
           anon = ~isempty(res);
           
           mrns    = regexpi(fname, ['(?<=MRN)\d+'], 'match');
           planuids= regexpi(fname, ['(?<=RP)(\d+.)+\d+'], 'match');
           if numel(mrns)==1 && numel(planuids)==1
               DcmSendRTPlanFiles(self, aet, mrns{1}, planuids{1}, anon);
               return; 
           end
           
           [mrns, T] = self.RetrieveMRNFromTaskFile(fname); 
           if ~isempty(mrns)
               for k=1:numel(mrns)
                   mrn = mrns{k};
                   patfolder = [DcmDataFolder(self) mrn];
                   SendDCM(self, patfolder); 
               end
               return;
           end
           
%            defaltsendtype = 'folder';
%            aet = self.ParseTaskFile(fname, aets, defaultaet);
           

           [~, name, ext] = fileparts(fname);
           switch lower(ext)
                case {'.tsk'}
                    name = strrep(name, '#', '/');
                    T    = table; 
                    T.DICOM{1} =   DosUtil.SimplifyPath([DcmDataFolder(self) name]);
                case {'.csv', '.xlsx'}
                    T = xls.TableBase.table_cellstr(fname);
           end

           dcmfolders = T.DICOM; 
           for k=1:numel(dcmfolders)
               try
               dcmfolder = dcmfolders{k};
               self.SendDCM(dcmfolder, anon);
               catch err
                   self.LogErr(err);
               end
           end
        end
        
        function ProcessTask_housekeep(self,  fname)
            startdate = regexpi(fname, ['(?<=startdate)\d+' ], 'match');
            if ~isempty(startdate) 
                startdate=startdate{1}; 
            end
            enddate   = regexpi(fname, ['(?<=enddate)\d+' ],   'match');
            if ~isempty(enddate) 
                enddate=enddate{1}; 
            end
            if ~isempty(startdate)||~isempty(enddate)
                res = self.FilterSubFolderByDateRange(self.DcmDataFolder, startdate, enddate);
                mrns = {res(:).name};
                HouseKeeping(self, mrns);
                return; 
            end
            
%             T = xls.TableBase.table_cellstr(fname); 
%             mrns = T.MRN;
            [mrns, T] = self.RetrieveMRNFromTaskFile(fname); 
            HouseKeeping(self, mrns);
        end
        
        function ProcessTask_import(self,  fname)
           mode = self.ParseTaskFile(fname, {'move', 'copy', 'symlink'}, 'copy');  
           switch(lower(mode))
               case 'symlink'
                   deleteonsuccess = 2; 
               case 'move'
                   deleteonsuccess = 1; 
               otherwise
                   deleteonsuccess = 0; 
           end
           
           T = xls.TableBase.table_cellstr(fname); 
           dcmfolders = T.DICOM; 
           if ischar(dcmfolders) 
               dcmfolders = {dcmfolders};
           end
           for k=1:numel(dcmfolders)
               try
               
               dcmfolder = dcmfolders{k};
%                while(1)
%                     t = self.FolderIdleTimeInSeconds(dcmfolder);
%                     if t<10
%                         pause(5);
%                     else
%                         break;
%                     end
%                end
               
               res = DosUtil.rdir(dcmfolder, '~isdir'); 
               inpFiles = {res(:).name};
               self.StoreDcms(inpFiles,  deleteonsuccess);
               catch err
                   self.LogErr(err);
               end
           end
        end
        
        function [newInstanceT, instanceT] = StoreFolder(self, dcmfolder,  varargin)
            options   = OptionsMap(varargin{:});
            storemode = options.getoptioni_numeric('storemode', 0); 
            datetimerange = options.getoptioni_numeric('datetimerange', []);
            dcmfilepattern= options.getoptioni_char('dcmfilepattern', '');
            dcmfoldertimeout=self.getoptioni_numeric('dcmfoldertimeout', 10); 
            newInstanceT=[]; instanceT=[];
            try
%                while(1)
%                     t = self.FolderIdleTimeInSeconds(dcmfolder);
%                     if t<dcmfoldertimeout
%                         pause(dcmfoldertimeout/2);
%                     else
%                         break;
%                     end
%                end
               t = self.FolderIdleTimeInSeconds(dcmfolder);
               if t<dcmfoldertimeout
                    %pause(dcmfoldertimeout/2);
                    return; %wait for next cycle to process
               end 
               
               res = DosUtil.rdir([dcmfolder dcmfilepattern], '~isdir'); 
               if isempty(res)
                   return;
               end
               
               inpFiles = {res(:).name};
               
               if numel(datetimerange)==2
                   if max(abs(datetimerange))<1e4
                       datetimerange = datetimerange+now; 
                   end
                   datetime = datenum({res(:).date});
                   I = datetime<datetimerange(1) | datetime>datetimerange(2);
                   inpFiles(I) =[];
               end
               
               if ~isempty(inpFiles)
                    [newInstanceT, instanceT] =  self.StoreDcms(inpFiles,  storemode, options);
               end
               
               if storemode==1
                   delete([dcmfolder '\*']);
               end
           catch err
               self.LogErr(err);
           end
        end
        
        function [newInstanceT, instanceT] = StoreActiveFolders(self, dcmroot,  varargin)
            options   = OptionsMap(varargin{:});
            datetimerange = options.getoptioni_numeric('datetimerange.folder', []);
           
            res = DosUtil.rdir(dcmroot); 
            res(~[res.isdir])=[];
            folders = {res(:).name};
            if numel(datetimerange)==2
               if max(abs(datetimerange))<1e4
                   datetimerange = datetimerange+now; 
               end
               datetime = datenum({res(:).date});
               I = datetime<datetimerange(1) | datetime>datetimerange(2);
               folders(I)=[];
            end
            
            newInstanceT=[]; instanceT=[];
            filedatetimerange = options.getoptioni_numeric('datetimerange.file', datetimerange);
            for k=1:numel(folders)
                dcmfolder = [folders{k} filesep];
                [newInstanceT1, instanceT1] = StoreFolder(self, dcmfolder,  {'datetimerange', filedatetimerange}, options);
%                 newInstanceT=cat(1, newInstanceT, newInstanceT1);
%                 instanceT = cat(1, instanceT, instanceT1);
            end
        end
        
        function ProcessTask_query(self,  fname)
           % portnames= self.m_DCMPorts.DcmPortName;
           % defaultaet = self.getoptioni('default.dcmserver');
           % aet = self.ParseTaskFile(fname, portnames, defaultaet);
           tskinfo = self.ExtractInfoFromTaskFile(fname);
           aet = StructBase.getfieldx(tskinfo, 'Port');
           if isempty(aet)
               return;
           end

           self.SetRemotePort(aet);
           outfolder = DosUtil.mksubdir(self.RootFolder, ['QUERY\' aet]);
           daterange = StructBase.getfieldx(tskinfo, 'DateRange');
           if ~isempty(daterange)
               [uids, Rsps] = self.queryStudy_daterange(daterange);
               if ~isempty(Rsps)
                   T=struct2table(Rsps, 'AsArray', true); 
                   xlsfname = [outfolder 'DateRange.' daterange '.xlsx'];
                   writetable(T, xlsfname, 'sheet', 'study', 'writemode', 'replacefile'); 
                   % xlsfname = [taskfolder '\' daterange '.xlsx'];
                   % writetable(T, xlsfname, 'sheet', 'study', 'writemode', 'replacefile'); 
               end
               return
           end

           %[mrns, T] = self.RetrieveMRNFromTaskFile(fname); 
           mrns = StructBase.getfieldx(tskinfo, 'MRN');
           T = StructBase.getfieldx(tskinfo, 'Table');
           if ~isempty(T) 
               try
                    mrns = T.MRN; 
               catch
                    mrns = T.PatientID; 
               end
           end

           if isempty(mrns)
               return;
           end
           if ischar(mrns)
               mrns = strsplit(mrns, '|');
           end
           for k=1:numel(mrns)
               try
               mrn = mrns{k};
               self.LogInfo(['query ' mrn]);
               T1 = self.QueryPatientSeries(mrn);
               
               if isempty(T1)
                   continue;
               end

               fname = [outfolder mrn '.xlsx'];
               if ~verLessThan('matlab', '9.6')
                   writetable(T1, fname, 'sheet', 'series', 'writemode', 'replacefile'); 
                   self.Series2StudyTable(T1, fname);
               else
                   delete(fname);
                   writetable(T1, fname, 'sheet', 'series');
                   self.Series2StudyTable(T1, fname);
                   excel.RemoveSheet123(fname); 
               end
               
               catch err
                   self.LogErr(err);
               end
           end
        end
        
        
        function ProcessTask_retrieve(self,  fname)
           tskinfo = self.ExtractInfoFromTaskFile(fname);
           aet = StructBase.getfieldx(tskinfo, 'Port');
           if isempty(aet)
               return;
           end
           self.SetRemotePort(aet);
            
           retrievetype = StructBase.getfieldx(tskinfo, 'DcmLevel'); 

           %[mrns, T] = self.RetrieveMRNFromTaskFile(fname);

           mrns = StructBase.getfieldx(tskinfo, 'MRN');
           T = StructBase.getfieldx(tskinfo, 'Table');
           if ~isempty(T) 
               try
                    mrns = T.MRN; 
               catch
                    mrns = T.PatientID; 
               end
           end

           if isempty(mrns)
               return;
           end
           if ischar(mrns)
               mrns = strsplit(mrns, '|');
           end 
           switch lower(retrievetype)
               case {'rtpatient'}
                    mrns = unique(mrns);
                    for k=1:numel(mrns)
                        try
                        mrn = mrns{k};
                        self.LogInfo(['retrieve ' mrn]);
                        self.RetrieveRTPatient(mrn, {'retrieveplanningimage',1});
                        HouseKeeping(self, mrn);
                        catch err
                           self.LogErr(err);
                       end
                    end
               
                case {'rprs'}
                    mrns = unique(mrns);
                    for k=1:numel(mrns)
                        try
                        mrn = mrns{k};
                        self.LogInfo(['retrieve ' mrn]);
                        self.RetrievePatient(mrn, {'RTPLAN', 'RTSTRUCT'});
                        HouseKeeping(self, mrn);
                        catch err
                           self.LogErr(err);
                       end
                    end   
                    
                case {'rprsrd'}
                    mrns = unique(mrns);
                    for k=1:numel(mrns)
                        try
                        mrn = mrns{k};
                        self.LogInfo(['retrieve ' mrn]);
                        self.RetrievePatient(mrn, {'RTPLAN', 'RTSTRUCT', 'RTDOSE'});
                        HouseKeeping(self, mrn);
                        catch err
                           self.LogErr(err);
                       end
                    end     
                    
                    
               case {'modality'}
                    %modality = self.ParseTaskFile(fname, self.RegisteredModality, self.getoptioni('default.modality'));
                    mrns = unique(mrns);
                    modality = StructBase.getfieldx(tskinfo, 'Modality');
                    for k=1:numel(mrns)
                        try
                        mrn = mrns{k};
                        self.LogInfo(['retrieve ' mrn]);
                        self.RetrievePatient(mrn, {modality});
                        HouseKeeping(self, mrn);
                        catch err
                           self.LogErr(err);
                        end
                    end
                    
               case {'series'}
                   % series   = T.SeriesInstanceUID;
                   % studyids = T.StudyInstanceUID;
                   N = size(T,1);
                   for k=1:N
                       try
                       seriresinfo = table2struct(T(k, :));
                       self.RetrieveSeries(seriresinfo);
                       %self.RetrieveSeries(series{k}, studyids{k});
                       catch err
                           self.LogErr(err);
                       end
                   end
                   HouseKeeping(self, unique(mrns));

              case {'study'}
                   modality = StructBase.getfieldx(tskinfo, 'Modality');
                   studyid =  StructBase.getfieldx(tskinfo, 'Study');
                   self.RetrieveStudy(studyid, modality);
                   HouseKeeping(self, unique(mrns));   
              
               case {'sopinstanceuid'}
                   for k=1:size(T, 1)
                       info = table2struct(T(k, :));
                       try
                       self.RetrieveImage(info);
                       catch err
                           self.LogErr(err);
                       end 
                   end
                   mrns = T.PatientID;
                   HouseKeeping(self, unique(mrns));  
           end
           %res  = self.RetrieveDCMs(aet, mrns);
        end
        
        
        %%dcmsend with anon
        function SendDCM(self, imagefolder, anon)
            if ~exist('anon', 'var')|| isempty(anon)
                anon = 0; 
            end
            
            if ~anon
                if exist(imagefolder, 'file')==2 
                    self.dcmsend_sd(imagefolder);
                elseif exist(imagefolder, 'dir')==7
                     if strcmpi(imagefolder(end), '\') || strcmpi(imagefolder(end), '/')
                         imagefolder(end)=[];
                     end
                     self.dcmsend_sd(imagefolder);
                else
                    res = DosUtil.rdir(imagefolder); 
                    infiles = {res(:).name};
                    for k=1:numel(infiles)
                        self.dcmsend_sd(infiles{k});
                    end
                end
            else
                if exist(imagefolder, 'dir')==7
                    res = DosUtil.rdir([imagefolder '\**\*.dcm']); 
                    infiles = {res(:).name};
                elseif exist(imagefolder, 'file')==2 
                    infiles = {imagefolder};
                else
                    res = DosUtil.rdir([imagefolder]); 
                    infiles = {res(:).name};
                end
                
                 anonymizer = DcmAnonymizer(self);
                 outfiles = anonymizer.AnonymizeFiles(infiles); 
                 for k=1:numel(outfiles)
                     self.dcmsend_sd(outfiles{k});
                 end
            end
        end
        
        function T = DcmSendRTPlanFiles(self, portname, MRN, planuid, modalities, anon)
           if ~exist('anon', 'var') || isempty(anon)
               anon = 0; 
           end
           
           if ~exist('modalities', 'var') || isempty(modalities)
               modalities = {'RP', 'RS','PLANDOSE', 'CT'};  
           end
           
           modalities = upper(modalities); 
           self.SetRemotePort(portname);    
           rtpat = dcmtk.RTPatientTables({'PatientID', MRN}, {'DCMSERVER_FOLDER', DcmDataFolder(self)});
           T     = rtpat.ExtendRTPlanTable({'selectedplanuid', planuid});
           K     = size(T, 1); 
           for k=1:K
               try
                   if ismember('CT', modalities)
                        imagefolder = T.ImageFolder{k};
                        if ~isempty(imagefolder)
                            if strcmpi(imagefolder(end), '\') || strcmpi(imagefolder(end), '/')
                                imagefolder(end)=[];
                            end
                            self.SendDCM(imagefolder, anon);
                        end
                   end
               catch err
                   self.LogErr(err);
               end
               
               try
                   if ismember('RS', modalities)
                    rsfile = T.RSFile{k};
                    self.SendDCM(rsfile, anon);
                   end
               catch err
                    self.LogErr(err);
               end      
               
               try
                   if ismember('PLANDOSE', modalities)
                    rdfile = T.PlanDoseFile{k};
                    self.SendDCM(rdfile, anon);
                   end
               catch err
                   self.LogErr(err);
               end   
               
               try
                   if ismember('BEAMDOSE', modalities)
                    rdfiles = strsplit(T.BeamDoseFile{k}, '|');
                    for m=1:numel(rdfiles)
                        self.SendDCM(rdfiles{m}, anon);
                    end
                   end
               catch err
                   self.LogErr(err);
               end   
               
               try
                    if ismember('RP', modalities)
                        rpfile = T.RPFile{k}; 
                        self.SendDCM(rpfile, anon);
                    end
               catch err
                   self.LogErr(err);
               end  
           end
        end

        function info = ExtractInfoFromTaskFile(self, fname)
            info = ExtractInfoFromTaskFileName(self, fname);
            [path, filename, ext] = fileparts(fname);
            if ismember(lower(ext), {'.xlsx', '.csv', 'xlx'})
                info.Table = xls.TableBase.ReadTable(fname);
                % varnames = T.Properties.VariableNames; 
                % if ismember('MRN', varnames)
                %     info.MRN = T.MRN; 
                % end
                % if ismember('PatientID', varnames)
                %     info.MRN = T.PatiendID; 
                % end
            end
        end

        function info = ExtractInfoFromTaskFileName(self, fname)
            fname = DosUtil.SimplifyPath(fname);
            [path, filename, ext] = fileparts(fname);
            
            %to be compatible with old version it will be retired in future
            %version
            strs = strsplit(path, '/');
            modalities = self.RegisteredModality;
            ops   = RegisteredTask(self);
            types = RegisteredRetrievetype(self);
            ports = self.m_DCMPorts.('DcmPortName');
            defaultvals = {modalities, ops, types, ports};
            defaultnames = {'Modality', 'Task', 'DcmLevel', 'Port'};
            info = [];
            for k=1:numel(defaultvals)
                name =  defaultnames{k};
                vals = defaultvals{k};
                [I]=find(ismember(vals, strs));
                if ~isempty(I)
                    info.(name) = vals{I(end)};
                end
            end
            fns  = {'Modality', 'Task', 'DcmLevel', 'Port', 'Import', 'MRN', 'Study', 'Series', 'SOP', 'DateRange'};
            info = DosUtil.ExtractInfoFromFileName(info, filename, fns, '#');
            str = StructBase.getfieldx(info, 'DateRange');
            if ~isempty(str)
               res = strsplit(str, '-');
               if numel(res)==2
                   if ismember('now', res)
                       days      = str2double(res(2));
                       daterange = [datestr(now-days, 'yyyymmdd') '-']; 
                   else
                       daterange = str; 
                   end
                   info.DateRange = daterange;  
               end
            end
            info.TaskFileName = fname; 
        end
    end
    
    methods (Static)
        function [mrns, T] = RetrieveMRNFromTaskFile(fname) 
           mrns = []; T= [];
           
           s = dir(fname);
           if isempty(s) || s.bytes == 0 %empty file
               mrns = regexpi(fname, ['(?<=MRN\.?)\d+' ], 'match');
               return;
           end
    
           try
           T = xls.TableBase.table_cellstr(fname);
           try
                mrns = T.MRN;
           catch
               mrns = T.PatientID;
           end
           mrns = unique(mrns);
           mrns = mrns(end:-1:1);   %in decending order, most recent first
           catch
               
           end
        end
        
        function res = FilterSubFolderByDateRange(rootfolder, startdate, enddate)
            res = dir(rootfolder);
            res(~[res.isdir])=[];
            res(1:2)=[];
            if ~exist('startdate', 'var') || isempty(startdate)
                startdate = '19010101'; 
            end
            if ~exist('enddate', 'var') || isempty(enddate)
                enddate = now; 
            end
            
            if ischar(startdate)
                if numel(startdate)<8
                    startdate = datenum(now)-str2num(startdate);
                else
                    startdate = datenum(startdate,  'yyyymmdd'); 
                end
            end
            
            if ischar(enddate)
                enddate = datenum(enddate,  'yyyymmdd')+1; 
            end
            
            
            dates = cellfun(@(x)(datenum(x)), {res.date}); 
            I = arrayfun(@(x)(x<startdate || x>enddate), dates); 
            
            res(I)=[]; 
        end
        
        
        function fnames = ListTaskFiles(taksfolder)
            patlistfolders= {[taksfolder, '*.tsk'], [taksfolder, '*.csv'], [taksfolder, '*.xlsx']};  
            fnames={};
            for m=1:numel(patlistfolders)
                res = DosUtil.rdir(patlistfolders{m});
                fnames =cat(2, fnames, {res(:).name}); 
            end
        end
        
        function task = ParseTaskFile(fname, registeredtasks0, defaulttask)
            registeredtasks = registeredtasks0(:)';
            registeredtasks = lower(registeredtasks);
            [FILEPATH,NAME,EXT] = fileparts(fname);
            a = lower(regexpi(FILEPATH, filesep, 'split'));
            %b = lower(regexpi(NAME, '\.', 'split'));
            b = lower(regexpi(NAME, '#', 'split'));
            task = defaulttask;
            for k=1:numel(registeredtasks)
                registeredtask = registeredtasks{k};
                if ismember(registeredtask, a) || ismember(registeredtask, b)
                    task = registeredtasks0{k}; return;
                end
            end
        end
        
        function [studyT, seriesT] = Series2StudyTable(seriesT, outfile,varargin)
            if ischar(seriesT)
                seriesT = readtable(seriesT, varargin{:});
            end
            
            Modality  = unique(seriesT.Modality);
            
            StudyInstanceUID = unique(seriesT.StudyInstanceUID);
            for k=1:numel(StudyInstanceUID)
                 I = find(ismember(seriesT.StudyInstanceUID, StudyInstanceUID(k)));
                 indices(k) = I(1);
            end

            T1 = seriesT(indices, :);
            try
            StudyDate        = T1.StudyDate;
            [StudyDate, I ] = sort(StudyDate);
            T1 = T1(I, :);
            catch
            end
            StudyID         = T1.StudyID;            
            StudyInstanceUID= T1.StudyInstanceUID;
            StudyDescription= T1.StudyDescription;
            
            %StudyDate = sort(unique(seriesT.StudyDate));
            
            for k=1:numel(StudyInstanceUID)
                I = find(ismember(seriesT.StudyInstanceUID, StudyInstanceUID(k)));
                Modalities{k, 1} = '';
                for m=1:numel(Modality)
                    mod = Modality{m}; 
                    count = sum(ismember(seriesT{I, 'Modality'}, mod));
                    if count>0
                        if isempty(Modalities{k, 1})
                            Modalities{k, 1} = [mod '_' num2str(count)];
                        else
                            Modalities{k, 1} = cat(2, Modalities{k, 1}, ['|' mod '_' num2str(count)]);
                        end
                    end
                end
            end
            
            studyT = table(StudyDate, StudyID, StudyDescription, StudyInstanceUID,  Modalities); 
            if exist('outfile', 'var') &&~isempty(outfile)
                writetable(studyT, outfile, 'sheet', 'study');
            end
        end

        
        function pattablefile = HouseKeepMRN(rootfolder, mrn, modalities, varargin)
            if ~exist('modalities', 'var') || isempty(modalities)
                 modalities = {'CT', 'MR', 'PT', 'US', 'REG', 'SEG', 'RTPLAN', 'RTSTRUCT', 'RTDOSE', 'RTIMAGE', 'RTRECORD'};
            end
            
            pattablefile = [rootfolder mrn '\patient.' mrn '.xlsx'];
            ai.DcmTable.ProcessRTPatient([rootfolder mrn '\'],modalities, {'rtpatienttable.file', pattablefile}, varargin{:});
            if exist(pattablefile, 'file')
                if verLessThan('Matlab','9.8')
                    excel.RemoveSheet123(pattablefile); 
                end
            end
        end
        
        function [instanceT, newInstanceT,srv] = StoreDcmFiles(srvroot, dcmsrcfiles, deletesource)
            if ~exist('deletesource', 'var')
                deletesource=0;
            end
            
            if ischar(dcmsrcfiles)
                res = DosUtil.rdir(dcmsrcfiles);
                dcmsrcfiles = {res(:).name};
            end
            srvfolder = DosUtil.mksubdir(srvroot, 'DicomData'); 
            srv = dcmtk.DcmServer({'DCMSERVER_ROOT', srvroot}, {'DCMSERVER_FOLDER', srvfolder}, {'openrecvport', 0});
            [newInstanceT, instanceT] = srv.StoreDcms(dcmsrcfiles,  deletesource);
        end
        
        function [instanceT, newInstanceT,srv] = StoreDcmFolder(srvroot, dcmfolder, varargin)
%             if ~exist('storemode', 'var')
%                 storemode=0;
%             end
            %options = OptionsMap(varargin{:});
            %storemode = options.getoptioni_numeric('storemode', 0);
            srvfolder = DosUtil.mksubdir(srvroot, 'DicomData'); 
            srv = dcmtk.DcmServer({'DCMSERVER_ROOT', srvroot}, {'DCMSERVER_FOLDER', srvfolder}, {'openrecvport', 0});
            [newInstanceT, instanceT] = srv.StoreFolder(dcmfolder, varargin{:});
        end
        
        function [instanceT, newInstanceT,srv] = StoreDcmFolders(srvroot, dcmroot, varargin)
            srvfolder = DosUtil.mksubdir(srvroot, 'DicomData'); 
            srv = dcmtk.DcmServer({'DCMSERVER_ROOT', srvroot}, {'DCMSERVER_FOLDER', srvfolder}, {'openrecvport', 0});
            [newInstanceT, instanceT] = srv.StoreActiveFolders(dcmroot, varargin{:});
        end
        
        function t = FolderIdleTimeInSeconds(folder)
            res = dir(folder); 
            if ~isempty(res)
                t= (now - datenum(res(1).date))*3600*24;
            else %folder does not exist
                t= Inf; 
            end
        end
        
        function HouseKeepFullDB(cfgfile, varargin)
%             if ~exist('hkmode', 'var')
%                 hkmode = 0; 
%             end
            path = fileparts(cfgfile);
            if ~isempty(path)
                cd(path);
            end
            
            cfg  = OptionsMap(cfgfile, varargin{:}); 
            dcmfolder    = cfg.getoptioni('DCMSERVER_FOLDER'); 
            hkmode = cfg.getoptioni_numeric('hkmode', 0);
            ext    = datestr(now, 'yyyymmddTHHMMSS');
%                 RecordDBName = ['HouseKeepFullDB-' ext];
            %RecordDBName = ['HouseKeepFullDB'];
            %dbfile = [cfg.getoptioni('DCMSERVER_ROOT') 'plandb\RTPlanDB_' ext '.sqlite']; 
            plandbfolder = DosUtil.mksubdir([dcmfolder '../'], ['plandb-HK' ext]);
            cfg.setOption('DCMSERVER_FOLDER.plandb', plandbfolder);
            dbfile = [plandbfolder 'RTPlanDB.sqlite'];
            
            srvroot   = cfg.getoptioni('DCMSERVER_ROOT');
            srcdbfile = [srvroot  'plandb/RTPlanDB.sqlite'];
            migratetablenames = cfg.getOption('MigrateTableNames', {'User'});
            switch hkmode
                case 0 %database migration
                    srvroot   = cfg.getoptioni('DCMSERVER_ROOT');
                    srcdbfile = [srvroot  'plandb/RTPlanDB.sqlite'];
                    mydb.sqlite.DcmRTDB.MigrateDB(srcdbfile, dbfile,[], cfg);
               
                case 1 %convert xlsfile to SqliteDB
                    mydb.sqlite.DcmRTDB.MigrateDB(srcdbfile, dbfile, migratetablenames, cfg);
                    mydb.sqlite.DcmRTDB.DcmXls2DB(dcmfolder, dbfile);

                case 2
                    mydb.sqlite.DcmRTDB.CreateDBTables(dbfile);
                    mydb.sqlite.DcmRTDB.MigrateDB(srcdbfile, dbfile, migratetablenames, cfg);
                    MRNs = cfg.getoptioni('SelectedPatientIDs'); 
                    if isempty(MRNs)
                        res    = dir(dcmfolder); res(~[res(:).isdir])=[]; res(1:2)=[]; 
                        MRNs   = {res(:).name};
                    end
                    
                    if ischar(MRNs)
                        MRNs = strsplit(MRNs, '|'); 
                    end
                    
                    server = dcmtk.DcmServer(cfg); 
                    server.HouseKeeping(MRNs); 
            end
        end
    end
end

