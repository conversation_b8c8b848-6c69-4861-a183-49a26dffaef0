classdef TaskDef <utils.json.TaskDef
    properties
       
    end

    methods
        function obj = TaskDef(varargin)
            <EMAIL>([], varargin{:});
        end

        function cfg = DefaultCfg(self, str)
            taskdefname = self.TaskDefName;
            TaskDoneIndicator=[taskdefname '.tsk'];
            cfg = struct("TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            if exist('str', 'var')&&~isempty(str)
                fns = fieldnames(str);
                for k=1:numel(fns)
                    name = fns{k};
                    cfg.(name)=str.(name);
                end
            end
        end

        function SetDefaultCfg(self, cfg)
            if ~exist('cfg', 'var')
                cfg=[];
            end
            taskdefname = self.TaskDefName;
            if startsWith(taskdefname, 'rtplan_', 'IgnoreCase',true)||startsWith(taskdefname, 'RP_', 'IgnoreCase',true)
                cfg.("DBTableName")="RTPLANDB";
            elseif startsWith(taskdefname, 'CT_', 'IgnoreCase',true)
                cfg.("DBTableName")="CT";
            elseif startsWith(taskdefname, 'MR_', 'IgnoreCase',true)
                cfg.("DBTableName")="MR";
            elseif startsWith(taskdefname, 'rtstruct_', 'IgnoreCase',true)||startsWith(taskdefname, 'RS_', 'IgnoreCase',true)
                cfg.("DBTableName")="RTSTRUCT";    
            elseif startsWith(taskdefname, 'temp_', 'IgnoreCase',true)
                cfg.("TaskOutputFolder")= "[TaskOutputFolder]";
            end
            cfg = self.DefaultCfg(cfg);
            self.SetDef_struct(cfg);
        end

        function Generate(obj, tasksdeffile, Dependency, Process, varargin)
            if ~isempty(Dependency)
                Dependency=StructBase.toCell(Dependency);
                for k=1:numel(Dependency)
                    obj.AddDependency(Dependency{k});
                end
            end
            if ~isempty(Process)
                Process=StructBase.toCell(Process);
                for k=1:numel(Process)
                    obj.AddConfig2List('Process', Process{k});
                end
            end

            options = OptionsMap(varargin{:});
            obj.SetDef_options(options);
            obj.writeJson;
            if ~isempty(tasksdeffile)
                tasksdeffile = DosUtil.CatenatePath(tasksdeffile,TaskDefFolder(obj));
                obj.AddToTasksDefFile( tasksdeffile);
            end
        end
    end

    methods (Static)
        function words = extractBracketWordsFromFile(filename)
            % Read file content
            fid = fopen(filename, 'r');
            if fid == -1
                error('Could not open file.');
            end
            textData = fread(fid, '*char')';
            fclose(fid);
        
            % Extract words inside square brackets
            matches = regexp(textData, '\[([^\]\n\t\r\f\v"]+)\]', 'tokens');
            
            % Convert from cell of cells to a simple cell array
            words = [matches{:}];
        end

        function [process, defaultsettings]=CreateTemplateArguments(argnames)
            %process = struct('OperationType', opname);
            if ischar(argnames)
                argnames =strsplit(argnames, '|');
            end

            N = numel(argnames);
            for k=1:N
                argname = argnames{k};
                process.(argname)=['[' argname ']'];
                defaultsettings.(argname)='';
            end
            %process = StructBase.mergeStruct(info, process);
            %infoT = struct2table(process);
            %infoT = table('Size', [0, N],'VariableTypes', repmat({'char'}, 1, N),  'VariableNames', argnames);
         end

         function TaskDefFile2TaskTemplate(tskdeffile, exportXlsx)
            fieldNames = unique(daemon.taskdef.TaskDef.extractBracketWordsFromFile(tskdeffile));
            info = cell2struct(cell(size(fieldNames)), fieldNames, 2);
            tskdef=utils.json.readJson(tskdeffile);
            DefaultSettings=StructBase.getfieldx(tskdef, 'DefaultSettings');
            if ~isempty(DefaultSettings)&&isstruct(DefaultSettings)
                fns = fieldnames(DefaultSettings);
                fns = intersect(fns, fieldNames);
                for m=1:numel(fns)
                    fn = fns{m};
                    info.(fn) = DefaultSettings.(fn);
                end
            end
            [taskdeffolder, taskdefname]=fileparts(tskdeffile);
            tempfolder = mksubdir([taskdeffolder '\'], 'temp_tsk');
            utils.json.writeJson(info, [tempfolder taskdefname '.tskinfo']);
            if exist('exportXlsx', 'var') && exportXlsx
                infoT = struct2table(info, 'AsArray', true);
                writetable(infoT, [tempfolder taskdefname '_tsk.xlsx']);
            end
         end

         function TaskDefFiles2TaskTemplate(tskdeffolder, varargin)
            res = DosUtil.rdir(tskdeffolder); 
            fnames = {res(:).name};
            for k=1:numel(fnames)
                try
                 daemon.taskdef.TaskDef.TaskDefFile2TaskTemplate(fnames{k}, varargin{:});
                catch 
                    disp(['error: ' fnames{k}])
                end
            end
        end

        function TaskDef_conditions(tasksdeffile, taskdefname, conditions, taskfiles, varargin)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            for m=1:numel(conditions)
                Process{m} = struct('Condition', conditions{m}, "taskfilename", taskfiles{m});
            end
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end

        function TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin)
            filters = StructBase.toCell(filters);
            taskfiles = StructBase.toCell(taskfiles);
            for m=1:numel(filters)
                condition  = struct("ConditionType",  'InfoFilter',  "InfoSeq", 1, "Filter", filters{m});
                Process{m} = struct('Condition', condition, "taskfilename", taskfiles{m});
            end
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end

        function Process=Process_addfilters(Process, filters, varargin)
            Process = StructBase.toCell(Process);
            filters = StructBase.toCell(filters);
            for m=1:numel(filters)
                condition  = struct("ConditionType",  'InfoFilter',  "InfoSeq", 1, "Filter", filters{m});
                Process{m}.Condition = condition;
            end
        end

        function MR_ClassifyLink(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            CustomSubFolder='./';
            includedseqs={'T1', 'T1c', 'T1nc', 'T2', 'FLAIR', 'DWI'};
            [filters, includedseqs] = daemon.taskdef.TaskDef.MRDescriptionFilters(includedseqs);
            imagefile = 'image.nii.gz';
            patfolder ='../dcm2nifti/[PatientID]/';
            %patfolder = DosUtil.SimplifyPath([taskdeffolder '../../dcm2nifti/[PatientID]/' ]);
            %patfolder = '../';
            %srcfile = [patfolder 'MR.[SeriesInstanceUID]/' imagefile];
            % srcfile = ['../MR.[SeriesInstanceUID]/' imagefile]; %srcfile is relative to destination file
            % srcfile = strrep(srcfile, '/', '\');
            srcfolder = [patfolder 'MR.[SeriesInstanceUID]']; %srcfile is relative to destination file
            srcfolder = strrep(srcfolder, '/', '\');
            for k=1:numel(includedseqs)
                seq=includedseqs{k};
                % dstfile = [patfolder 'STUDY.[StudyInstanceUID]/image_' seq '.nii.gz'];
                % dstfile = strrep(dstfile, '/', '\');
                % Command=['mklink ' dstfile ' ' srcfile];
                studyfolder=[patfolder 'STUDY.[StudyInstanceUID]']; 
                dstfolder = [studyfolder '/MR_' seq];
                dstfolder = strrep(dstfolder, '/', '\');
                %Command=['mklink /J ' dstfolder ' ' srcfolder];
                Command = DosUtils.createFolderLinkCmd(dstfolder, srcfolder);
                Command = ['mkdir "' studyfolder '" & ' Command];
                Process{k} = struct('OperationType', 'ExecuteCommand', 'Command', Command, 'CommandType', 'cmd');
            end
            Process=daemon.taskdef.TaskDef.Process_addfilters(Process, filters);
            taskdefname='MR_ClassifyLink';
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.SetDefaultCfg();
            %dependency= struct("filename", imagefile, "taskfilename", ['DCMCONVERT_MR/MR.[SeriesInstanceUID].tsk']);
            dependency =[];
            %obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'IsCustomTask', 1});
            obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end

        function TaskDef_filterGenders(tasksdeffile, taskdefname, varargin)
            genders = {'M', 'F'};
            for m=1:numel(genders)
                gender = genders{m};
                filters{m}   = struct("FieldName", 'PatientSex', 'MatchMode', 'regexp', "FieldValue", ['^' gender],  "Inclusion", true);           
                taskfiles{m} = [taskdefname '_' gender '/[Modality].[SeriesInstanceUID].tsk'];
            end
            daemon.taskdef.TaskDef.TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin{:});
        end

       function keyword = MRDescription_keyword()
        % MRDescription_keyword
        % Returns a struct of regular expression patterns to match common MRI sequence keywords.
        % Includes T1, T2, FLAIR, T1FLAIR, DWI, and contrast-enhancement (POST) keywords.
        
            keyword.T1 = [ ...
                '(T1(W|WI)?' ...
                '|T1[-_ ]?WEIGHTED' ...
                '|MPRAGE|MP[\s\-_]?RAGE' ...
                '|SPGR|FSPGR|FFE' ...
                '|VIBE|LAVA|THRIVE|STARVIBE' ...
                '|FLASH|GRE|T1FLASH' ...
                '|T1IR|T1SE)' ...
            ];
        
            keyword.T2 = [ ...
                '(T2(W|WI)?' ...
                '|T2[-_ ]?WEIGHTED' ...
                '|STIR' ...
                '|FSE|TSE|CISS|SPACE|VISTA|CUBE' ...
                '|PROP(?:ELLER)?|BLADE|FIESTA|TRUEFISP|BSSFP|DRIVE)' ...
            ];
        
            keyword.FLAIR = '(FLAIR|TIRM|TIR)';
        
            keyword.T1FLAIR = '(T1.*FLAIR|FLAIR.*T1)';
        
            keyword.DWI = '(DWI|DIFFUSION|ADC|TRACE)';
        
            keyword.CONTRAST = '(POST|GAD|CONTRAST|CE|\+C|C\+|VIBE|LAVA|THRIVE|T1C|T1CE|MASTAR|ENH|ENHANCED)';
        end

        function [map, pattern] = MRDescriptionMap_regexp()   
            % T2(?!.*FLAIR)              # T2 not followed later by FLAIR
            % |T2W(?!.*FLAIR)            # T2W not followed by FLAIR
            % |T2WI(?!.*FLAIR)           # T2WI not followed by FLAIR
            % |T2[- ]?weighted(?!.*FLAIR)#
            % |STIR                      # STIR (suppressed fat, still T2W)
            % |FSE                       # Fast spin echo
            % |TSE                       # Turbo spin echo
            % |CISS                      # High-res T2-like
            % |SPACE                     # Siemens 3D T2
            % |VISTA                     # Philips 3D T2
            % |CUBE                      # GE 3D T2
            % |PROP(?:ELLER)?            # Radial T2 (PROP, PROPELLER)
            % |BLADE                     # Siemens radial T2
            % |FIESTA                    # GE bSSFP
            % |TrueFISP                  # Siemens bSSFP
            % |bSSFP                     # Balanced SSFP
            % |DRIVE                     # Philips 3D T2
            
            pattern.T2  = [
              '^(?!.*(FLAIR|T1W|T1WI|T1[-_ ]?WEIGHTED|T1))' ...  % ⛔ exclude entire string if T1-related
              '.*?' ...
              '([_\-\s]|^)' ...
              '(T2(W|WI)?' ...
              '|T2[-_ ]?WEIGHTED' ...
              '|STIR' ...
              '|FSE|TSE|CISS|SPACE|VISTA|CUBE' ...
              '|PROP(?:ELLER)?|BLADE|FIESTA|TRUEFISP|BSSFP|DRIVE)' ...
              '([_\-\s]|$)'
            ];


            pattern.FLAIR = [
              '^(?!.*(T1W|T1WI|T1[-_ ]?WEIGHTED|T1))' ...  % Exclude entire string if any form of T1 is present
              '.*?' ...
              '(FLAIR' ...
              '|T2[\s_\-]?FLAIR' ...
              '|FLAIR[\s_\-]?T2' ...
              '|IR[\s_\-]?(T2|FSE|TSE)?[\s_\-]?FLAIR' ...
              '|FLAIRV\d*' ...
              '|FLUID[\s_\-]?ATTENUATED)' ...
              '([\s_\-]|$)'
            ];

            % pattern_T1 = ['(^|[_\-\s])((T1(W|WI)?|T1[-_ ]?weighted)' ...            % native T1
            %     '|T1[-_ ]?\+[-_ ]?C' ...                                           % T1+C
            %     '|T1[-_ ]?(POST|GAD|CONTRAST|CE)' ...                             % T1 POST/GAD/CE
            %     '|POST[-_ ]?(T1|GAD|CON|CONTRAST)' ...                           % POST T1
            %     '|GAD[-_ ]?T1|GADO|T1C|T1CE' ...                                % GAD variants
            %     '|VIBE|STARVIBE|MP[_\-\s]?RAGE|FLASH|GRE|MP2RAGE|LAVA|SPGR|FSPGR|BRAVO|THRIVE|TFE|FFE|mASTAR)' ... % vendor sequences
            %     '($|[_\-\s])'];

            pattern.T1 =[
              '(^|[_\-\s])' ...
              '(?!.*FLAIR)' ...  % Exclude sequences containing 'FLAIR'
              '[a-zA-Z0-9]*?' ...
              '(T1(W|WI)?' ...
              '|T1[-_ ]?weighted' ...
              '|T1W' ...
              '|MP[_\-\s]?RAGE|MPR' ...
              '|SPGR|FSPGR' ...
              '|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO' ...
              '|VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|mASTAR)' ...
              '([_\-\s]|$)'
            ];
            % Vendor	Sequence Name	Type	Contrast Use
            % Siemens	VIBE|STARVIBE	3D gradient echo (GRE)	Pre- & Post-contrast
            % Siemens	MPRAGE	3D inversion recovery GRE	Mostly pre-contrast
            % Siemens	FLASH	2D/3D GRE	Pre-contrast
            % Siemens	GRE	Generic gradient echo	Pre-contrast
            % Siemens	MP2RAGE	T1-weighted quantitative mapping	Research/Pre-contrast
            % GE	LAVA	3D GRE (similar to VIBE|STARVIBE)	Post-contrast
            % GE	SPGR / FSPGR	3D T1-weighted GRE	Pre- & Post-contrast
            % GE	BRAVO	3D inversion recovery GRE	Pre-contrast
            % Philips	THRIVE	3D GRE (equivalent to VIBE|STARVIBE/LAVA)	Post-contrast
            % Philips	TFE / 3D-TFE	Turbo Field Echo (GRE variant)	Pre-contrast
            % Philips	FFE	Fast Field Echo	Pre-contrast
            % Canon	mASTAR	3D T1 GRE	Post-contrast
            % Canon	3D T1 FFE	Fast Field Echo	Pre- & Post-contrast
            
          pattern.T1c =   [
              '(^|[\s_\-])' ...
              '.*?' ...
              '(' ...
                '(POST|GAD|CONTRAST|CE|\+C).*?(T1W|T1(W|WI)?|T1[-_ ]?WEIGHTED|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
                '|' ...
                '(T1W|T1(W|WI)?|T1[-_ ]?WEIGHTED|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO).*?(POST|GAD|CONTRAST|CE|\+C)' ...
                '|' ...
                'VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|MASTAR' ...
              ')' ...
              '([\s_\-]|$)'
            ];
          
          pattern.T1FLAIR = '(^|[\s_\-]).*?T1.*FLAIR([\s_\-]|$)';

          pattern.T1nc = [
              '^(?!.*(POST|GAD|CONTRAST|CE|\+C|VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|MASTAR))' ...
              '.*?' ...
              '([_\-\s]|^)' ...
              '[A-Z0-9]*?' ...
              '(T1(W|WI)?|T1[-_ ]?WEIGHTED|T1W|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
              '([_\-\s]|$)'
            ];

          pattern.DWI = [
              '(^|[_\-\s])' ...
              '[a-zA-Z0-9]*?' ...
              '(DWI(_?EPI)?' ...
              '|EPI[_\- ]?DWI' ...
              '|Diffusion(_?Weighted)?' ...
              '|DTI(_\d+dir)?' ...
              '|ADC)' ...
              '([_\-\s]|$)'
            ];

          pattern.T1c_or_FLAIR = [
              '(^|[\s_\-])' ...
              '.*?' ...
              '(' ...
                '(' ...  % T1-weighted with contrast
                  '(POST|GAD|CONTRAST|CE|\+C).*?(T1W|T1(W|WI)?|T1[-_ ]?weighted|MP[\s_\-]?RAGE|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
                  '|' ...
                  '(T1W|T1(W|WI)?|T1[-_ ]?weighted|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO).*?(POST|GAD|CONTRAST|CE|\+C)' ...
                  '|' ...
                  'VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|mASTAR' ...
                ')' ...
                '|' ...
                '(' ...  % FLAIR
                  'FLAIR' ...
                  '|T2[\s_\-]?FLAIR' ...
                  '|FLAIR[\s_\-]?T2' ...
                  '|IR[\s_\-]?(T2|FSE|TSE)?[\s_\-]?FLAIR' ...
                  '|FLAIRv\d*' ...
                  '|Fluid[\s_\-]?Attenuated' ...
                ')' ...
              ')' ...
              '([\s_\-]|$)'
            ];
           fns = fieldnames(pattern);
           map = OptionsMap();
           for k=1:numel(fns)
               fn = fns{k};
               map.setOption(fn, pattern.(fn));
           end

            % map.setOption('T1', 'T1|BRAVO|MPRANGE|FSPGR');
            % map.setOption('T1_noce', '^(?!.*(POST|GAD|+C|Flair)).*(T1|BRAVO|MPRANGE|FSPGR).*');
            % map.setOption('T1ce', '^(?!.*Flair).*((T1|BRAVO|MPRANGE).*(POST|GAD|+C))|((POST|GAD|+C).*(T1|BRAVO|MPRANGE)).*');
            % map.setOption('T2', '^(?!.*Flair).*(T2|CISS).*');
            % map.setOption('Flair', '^(?!.*(T1|BRAVO|MPRANGE|FSPGR)).*Flair.*');
        end

        function [filters, includedseqs] = MRDescriptionFilters(includedseqs, varargin)
            map = daemon.taskdef.TaskDef.MRDescriptionMap_regexp();
            if ~exist("includedseqs", 'var')||isempty(includedseqs)
                includedseqs = map.getOptionNames; 
            end
            m=0; filters=[]; 
            for k=1:numel(includedseqs)
                seq = includedseqs{k};
                val = map.getOptioni(seq);
                if ~isempty(val)
                    m=m+1; 
                    filters{m}   = struct("FieldName", 'SeriesDescription', 'MatchMode', 'regexpi', "FieldValue", val,  "Inclusion", true);           
                end
            end
        end

        function TaskDef_filterMRDescriptions(tasksdeffile, taskdefname,includedseqs, varargin)
            map = daemon.taskdef.TaskDef.MRDescriptionMap_regexp();
            m=0; filters=[]; taskfiles=[];
            for k=1:numel(includedseqs)
                seq = includedseqs{k};
                val = map.getoptioni(seq);
                if ~isempty(val)
                    m=m+1; 
                    filters{m}   = struct("FieldName", 'SeriesDescription', 'MatchMode', 'regexpi', "FieldValue", val,  "Inclusion", true);           
                    taskfiles{m} = [taskdefname '_' seq '/[Modality].[SeriesInstanceUID].tsk'];
                end
            end
            daemon.taskdef.TaskDef.TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin{:});
        end

        

        function nnUNETSeg1(tasksdeffile,modality, modelfolder,  modelname, modeloptext)
            label_image_type='labelmask';
            taskdefname0= matlab.lang.makeValidName(modelname);
            %modelfolder = 'C:\ARTDaemon\distribute\nnUNETDatav2.ts-mr\';
            %modeloptext = '#nnUNetTrainerNoMirroring__nnUNetPlans__3d_fullres#fold_0';
            ModelOptionFile=DosUtil.SimplifyPath([modelfolder modelname modeloptext '.opt']);
            
            for astemp=[0 1]
                obj=daemon.taskdef.TaskDef.nnUNETInference(tasksdeffile, modality, modelname, taskdefname0, ModelOptionFile, label_image_type, astemp);
            end
        end

        function obj=nnUNETInference(tasksdeffile, modality, subtaskname, taskdefname0, modeloptfile, label_image_type, astemp, augmentprocess)
              %modality = 'MR';
              modeloptfile=DosUtil.SimplifyPath(modeloptfile) ;
              if ~astemp
                 CustomSubFolder=subtaskname; 
                 InputImage='../image.nii.gz';
                 OutputFile=label_image_type;
                 taskdefname = [modality '_' taskdefname0];
                 dependency= struct("filename", InputImage, "taskfilename", ['../DcmConverter/DCMCONVERT_' modality '/[Modality].[SeriesInstanceUID].tsk']);
                 if strcmpi(label_image_type, 'labelmask')
                      listname = 'StructSet';
                      associateinfo=struct('ID', subtaskname, ...
                     "LabelMaskFileName", [CustomSubFolder, '/'  OutputFile]);
                 else
                     listname = 'ImageSet';
                     associateinfo=struct('ID', subtaskname, "SeriesDescription", subtaskname, "Modality", subtaskname, ...,
                         'SeriesDate', '[SeriesDate]',...
                         "ImageFileName", [CustomSubFolder, '/'  OutputFile]);
                 end
                AssociateStruct2List = struct('OperationType','AssociateStruct',...
                    'AssociateFileName', '../dcmdata.cfg', 'ListName', listname, 'Info', associateinfo);
             else
                 %TaskOutputFolder='[TaskOutputFolder]'; 
                 InputImage='[InputImage]';
                 OutputFile='[OutputFile]';
                 taskdefname = ['temp_' taskdefname0];
                 CustomSubFolder=''; 
                 dependency= struct("filename", InputImage);
                 AssociateStruct2List=[];
              end

            taskdeffolder = [fileparts(tasksdeffile) '/'];
            m=0; Process =[];
            m=m+1; Process{m}   = struct('OperationType','nnUNETInference',...
                    'ModelOptionFile', modeloptfile, ...
                    'InputImage',InputImage, ...
                    'OutputFile', OutputFile, ...
                    'label_image_type', label_image_type);
            
            if exist('augmentprocess', 'var') &&~isempty(augmentprocess)
                Process = cat(2,  Process, StructBase.toCell(augmentprocess)); 
                m=numel(Process);
            end
            if ~isempty(AssociateStruct2List)
                m=m+1; Process{m}   = AssociateStruct2List;
            end
           obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           obj.SetDefaultCfg();
           obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end

        function str = SegmanStr(db, ID)
            str.OrigImageHeaderFile= '../image_dcm.json';
            str.DBTableName = db; 
            str.DcmExport=struct("OrigImageHeaderFile", '../image_dcm.json',...
                "OutRSFileName", 'RS_Segman.dcm',...
                "OutRSSetName", ID);

            str.OutputStructSet=struct(...
                "ID", ID, ...
                "ROIMaskFileName", "roimask", ...
                "LabelMaskFileName", "labelmask", ...
                "LabelMaskWithImageContours", 1, ...
                "ContourSmoothWindow", 3, ...
                "ContourMinNumPoints", 20);
        end

        function CreateSegmanDef(tasksdeffile,taskdefname, db, segmanID, dependency,MergeOperation, PostProcessing, varargin)
           taskdeffolder = [fileparts(tasksdeffile) '/'];
           obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           obj.SetDefaultCfg();
           str = daemon.taskdef.TaskDef.SegmanStr(db, segmanID);
           obj.Generate(tasksdeffile, dependency, [],  {'MergeOperation', MergeOperation}, {'CustomSubFolder', taskdefname}, ...
               {'PostProcessing', PostProcessing},OptionsMap.struct2options(str), varargin{:});
        end
    end
end